{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m10.852 14.772-.383.923\",\n  key: \"11vil6\"\n}], [\"path\", {\n  d: \"M13.148 14.772a3 3 0 1 0-2.296-5.544l-.383-.923\",\n  key: \"1v3clb\"\n}], [\"path\", {\n  d: \"m13.148 9.228.383-.923\",\n  key: \"t2zzyc\"\n}], [\"path\", {\n  d: \"m13.53 15.696-.382-.924a3 3 0 1 1-2.296-5.544\",\n  key: \"1bxfiv\"\n}], [\"path\", {\n  d: \"m14.772 10.852.923-.383\",\n  key: \"k9m8cz\"\n}], [\"path\", {\n  d: \"m14.772 13.148.923.383\",\n  key: \"1xvhww\"\n}], [\"path\", {\n  d: \"M4.5 10H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-.5\",\n  key: \"tn8das\"\n}], [\"path\", {\n  d: \"M4.5 14H4a2 2 0 0 0-2 2v4a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-4a2 2 0 0 0-2-2h-.5\",\n  key: \"1g2pve\"\n}], [\"path\", {\n  d: \"M6 18h.01\",\n  key: \"uhywen\"\n}], [\"path\", {\n  d: \"M6 6h.01\",\n  key: \"1utrut\"\n}], [\"path\", {\n  d: \"m9.228 10.852-.923-.383\",\n  key: \"1wtb30\"\n}], [\"path\", {\n  d: \"m9.228 13.148-.923.383\",\n  key: \"1a830x\"\n}]];\nconst ServerCog = createLucideIcon(\"server-cog\", __iconNode);\nexport { __iconNode, ServerCog as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "ServerCog", "createLucideIcon"], "sources": ["D:\\ASL\\ASL-Training\\node_modules\\lucide-react\\src\\icons\\server-cog.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm10.852 14.772-.383.923', key: '11vil6' }],\n  ['path', { d: 'M13.148 14.772a3 3 0 1 0-2.296-5.544l-.383-.923', key: '1v3clb' }],\n  ['path', { d: 'm13.148 9.228.383-.923', key: 't2zzyc' }],\n  ['path', { d: 'm13.53 15.696-.382-.924a3 3 0 1 1-2.296-5.544', key: '1bxfiv' }],\n  ['path', { d: 'm14.772 10.852.923-.383', key: 'k9m8cz' }],\n  ['path', { d: 'm14.772 13.148.923.383', key: '1xvhww' }],\n  [\n    'path',\n    {\n      d: 'M4.5 10H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-.5',\n      key: 'tn8das',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M4.5 14H4a2 2 0 0 0-2 2v4a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-4a2 2 0 0 0-2-2h-.5',\n      key: '1g2pve',\n    },\n  ],\n  ['path', { d: 'M6 18h.01', key: 'uhywen' }],\n  ['path', { d: 'M6 6h.01', key: '1utrut' }],\n  ['path', { d: 'm9.228 10.852-.923-.383', key: '1wtb30' }],\n  ['path', { d: 'm9.228 13.148-.923.383', key: '1a830x' }],\n];\n\n/**\n * @component @name ServerCog\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTAuODUyIDE0Ljc3Mi0uMzgzLjkyMyIgLz4KICA8cGF0aCBkPSJNMTMuMTQ4IDE0Ljc3MmEzIDMgMCAxIDAtMi4yOTYtNS41NDRsLS4zODMtLjkyMyIgLz4KICA8cGF0aCBkPSJtMTMuMTQ4IDkuMjI4LjM4My0uOTIzIiAvPgogIDxwYXRoIGQ9Im0xMy41MyAxNS42OTYtLjM4Mi0uOTI0YTMgMyAwIDEgMS0yLjI5Ni01LjU0NCIgLz4KICA8cGF0aCBkPSJtMTQuNzcyIDEwLjg1Mi45MjMtLjM4MyIgLz4KICA8cGF0aCBkPSJtMTQuNzcyIDEzLjE0OC45MjMuMzgzIiAvPgogIDxwYXRoIGQ9Ik00LjUgMTBINGEyIDIgMCAwIDEtMi0yVjRhMiAyIDAgMCAxIDItMmgxNmEyIDIgMCAwIDEgMiAydjRhMiAyIDAgMCAxLTIgMmgtLjUiIC8+CiAgPHBhdGggZD0iTTQuNSAxNEg0YTIgMiAwIDAgMC0yIDJ2NGEyIDIgMCAwIDAgMiAyaDE2YTIgMiAwIDAgMCAyLTJ2LTRhMiAyIDAgMCAwLTItMmgtLjUiIC8+CiAgPHBhdGggZD0iTTYgMThoLjAxIiAvPgogIDxwYXRoIGQ9Ik02IDZoLjAxIiAvPgogIDxwYXRoIGQ9Im05LjIyOCAxMC44NTItLjkyMy0uMzgzIiAvPgogIDxwYXRoIGQ9Im05LjIyOCAxMy4xNDgtLjkyMy4zODMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/server-cog\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ServerCog = createLucideIcon('server-cog', __iconNode);\n\nexport default ServerCog;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,iDAAmD;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChF,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,+CAAiD;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAU,GACzD;AAaM,MAAAC,SAAA,GAAYC,gBAAiB,eAAcJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}