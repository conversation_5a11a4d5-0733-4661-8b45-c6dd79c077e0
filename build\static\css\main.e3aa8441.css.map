{"version": 3, "file": "static/css/main.e3aa8441.css", "mappings": "2SAAA,KAEE,mIAKF,CAEA,KACE,uEAEF,CCPA,MAEE,oBAAqB,CACrB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CAGtB,sBAAuB,CACvB,uBAAwB,CACxB,uBAAwB,CACxB,uBAAwB,CACxB,uBAAwB,CACxB,uBAAwB,CACxB,uBAAwB,CACxB,uBAAwB,CACxB,uBAAwB,CACxB,uBAAwB,CAGxB,mBAAoB,CACpB,oBAAqB,CACrB,oBAAqB,CACrB,oBAAqB,CACrB,oBAAqB,CACrB,oBAAqB,CACrB,oBAAqB,CACrB,oBAAqB,CACrB,oBAAqB,CACrB,oBAAqB,CAGrB,mBAAoB,CACpB,oBAAqB,CACrB,oBAAqB,CACrB,oBAAqB,CACrB,oBAAqB,CACrB,oBAAqB,CACrB,oBAAqB,CACrB,oBAAqB,CACrB,oBAAqB,CACrB,oBAAqB,CAGrB,qBAAsB,CACtB,qBAAsB,CACtB,mBAAoB,CACpB,kBAAmB,CAGnB,oBAAqB,CACrB,sDAAiE,CACjE,qDAAgE,CAChE,mDAA8D,CAC9D,gBAAoC,CAGpC,sBAAuB,CACvB,wBAAyB,CACzB,uBAAwB,CACxB,qBAAsB,CACtB,uDAAkE,CAGlE,sBAAuB,CACvB,uBAAwB,CACxB,qBAAsB,CACtB,uBAAwB,CACxB,yBAAwC,CAGxC,iCAA+C,CAC/C,6DAAyF,CACzF,+DAA0F,CAC1F,gEAA4F,CAC5F,2CAA2D,CAC3D,gCAAgD,CAGhD,iBAAkB,CAClB,gBAAiB,CACjB,iBAAkB,CAClB,cAAe,CACf,iBAAkB,CAClB,gBAAiB,CACjB,cAAe,CACf,iBAAkB,CAClB,eAAgB,CAChB,eAAgB,CAChB,eAAgB,CAChB,eAAgB,CAGhB,oBAAqB,CACrB,kBAAmB,CACnB,mBAAoB,CACpB,gBAAiB,CACjB,mBAAoB,CACpB,oBAAqB,CAGrB,sFAA4F,CAC5F,gFAAqF,CACrF,oEAAyE,CAGzE,iDAAqD,CACrD,kDAAsD,CACtD,gDACF,CAEA,EAGE,qBAAsB,CAFtB,QAAS,CACT,SAEF,CAEA,KAEE,cAAe,CADf,sBAEF,CAEA,KAGE,kCAAmC,CACnC,iCAAkC,CAElC,kBAA6B,CAA7B,4BAA6B,CAC7B,aAA0B,CAA1B,yBAA0B,CAL1B,uEAAkC,CAAlC,iCAAkC,CAMlC,eAAgB,CAPhB,QAAS,CAIT,iBAIF,CAEA,KAEE,0BAA2B,CAE3B,qBAA+B,CAA/B,8BAA+B,CAH/B,4DAAyE,CAIzE,iBAAmB,CAFnB,oBAAsC,CAAtC,qCAGF,CAGA,oBACE,SACF,CAEA,0BACE,0BACF,CAEA,0BACE,0BAA2B,CAC3B,oBAAiC,CAAjC,gCACF,CAEA,gCACE,0BACF,CAGA,EACE,0CACF,CAGA,qDAIE,yBAAqC,CAArC,oCAAqC,CACrC,kBACF,CAEA,iCACE,YACF,CAGA,SAAW,gBAAkB,CAAE,gBAAmB,CAClD,SAAW,iBAAmB,CAAE,mBAAsB,CACtD,WAAa,cAAe,CAAE,kBAAqB,CACnD,SAAW,kBAA2C,CACtD,kBADgC,mBACqB,CAArD,SAAW,iBAA0C,CACrD,UAAY,gBAAiB,CAAE,gBAAmB,CAClD,UAAY,kBAAmB,CAAE,mBAAsB,CACvD,UAAY,iBAAkB,CAAE,kBAAqB,CACrD,UAAY,gBAAiB,CAAE,aAAgB,CAC/C,UAAY,cAAe,CAAE,aAAgB,CAE7C,YAAc,eAAkB,CAChC,aAAe,eAAkB,CACjC,aAAe,eAAkB,CACjC,eAAiB,eAAkB,CACnC,WAAa,eAAkB,CAC/B,gBAAkB,eAAkB,CAEpC,cAAgB,6EAAgC,CAAhC,+BAAkC,CAClD,gBAAkB,uEAAkC,CAAlC,iCAAoC,CAGtD,yBACE,KACE,cACF,CAEA,UAAY,kBAAmB,CAAE,mBAAsB,CACvD,UAAY,cAAe,CAAE,kBAAqB,CAClD,UAAY,gBAAiB,CAAE,aAAgB,CACjD,CAGA,OACE,wBAAyB,CAGzB,gBACF,CAGA,YACE,kBAA8B,CAA9B,6BAA8B,CAC9B,aAAyB,CAAzB,wBACF,CAEA,iBACE,kBAA8B,CAA9B,6BAA8B,CAC9B,aAAyB,CAAzB,wBACF,CAGA,kBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,mBACE,GACE,SAAU,CACV,2BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,mBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,mBACE,GACE,SAAU,CACV,mBACF,CACA,GACE,SAAU,CACV,kBACF,CACF,CAEA,iBACE,MACE,SACF,CACA,IACE,UACF,CACF,CAGA,MACE,kBAA6B,CAA7B,4BAA6B,CAG7B,wBAAqC,CAArC,oCAAqC,CAFrC,kBAA+B,CAA/B,8BAA+B,CAC/B,8DAA4B,CAA5B,2BAA4B,CAE5B,0CACF,CAEA,YACE,+DAA4B,CAA5B,2BAA4B,CAC5B,0BACF,CAGA,OAEE,kCAA2B,CAA3B,0BAA2B,CAD3B,gBAAoC,CAEpC,sBACF,CAGA,eAGE,6BAAoC,CAFpC,kDAAqF,CAArF,iFAAqF,CACrF,4BAA6B,CAE7B,oBACF,CAGA,KAEE,kBAAmB,CASnB,WAAY,CALZ,oBAA+B,CAA/B,8BAA+B,CAM/B,cAAe,CAXf,mBAAoB,CAOpB,iBAAmB,CADnB,eAAgB,CAHhB,SAAmB,CAAnB,kBAAmB,CADnB,sBAAuB,CAMvB,mBAAoB,CAMpB,eAAgB,CAVhB,qBAAsC,CAAtC,qCAAsC,CAStC,iBAAkB,CAJlB,oBAAqB,CAGrB,0CAGF,CAEA,aACE,kBAA8B,CAA9B,6BAA8B,CAC9B,UACF,CAEA,mBACE,kBAA8B,CAA9B,6BAA8B,CAE9B,8DAA4B,CAA5B,2BAA4B,CAD5B,0BAEF,CAEA,eACE,kBAA6B,CAA7B,4BAA6B,CAE7B,wBAAsC,CAAtC,qCAAsC,CADtC,aAA0B,CAA1B,yBAEF,CAEA,qBACE,yBAA0B,CAC1B,oBAAgC,CAAhC,+BACF,CAEA,QAEE,cAAe,CACf,kBAAmB,CAFnB,iBAAsC,CAAtC,qCAGF,CAGA,0BACE,aACE,YACF,CAEA,QAEE,iBAAmB,CADnB,qBAAsC,CAAtC,qCAEF,CACF,CAEA,yBACE,aACE,YACF,CAEA,KAEE,eAAiB,CADjB,sBAAsC,CAAtC,qCAEF,CAEA,QAEE,iBAAmB,CADnB,qBAAsC,CAAtC,qCAEF,CACF,CAGA,SAAW,mBAAuB,CAClC,SAAW,mBAAuB,CAClC,SAAW,mBAAuB,CAClC,SAAW,mBAAuB,CAClC,SAAW,mBAAuB,CAGlC,gBACE,sCACF,CAEA,iBACE,uCACF,CAEA,iBACE,uCACF,CAEA,iBACE,uCACF,CAEA,eACE,mDACF,CAGA,WAEE,aAAc,CADd,gBAAiB,CAEjB,cAAyB,CAAzB,wBACF,CAEA,MACE,YACF,CAEA,UACE,qBACF,CAEA,cACE,kBACF,CAEA,gBACE,sBACF,CAEA,iBACE,6BACF,CAEA,aACE,iBACF,CAEA,QACE,UACF,CAEA,QACE,WACF,CAEA,YACE,oBAA+B,CAA/B,8BACF,CAEA,YACE,kBAA+B,CAA/B,8BACF,CAEA,WACE,8DAA4B,CAA5B,2BACF,CAEA,WACE,+DAA4B,CAA5B,2BACF,CAGA,gBAEE,oBAAmC,CACnC,0BAAyC,CAFzC,aAAyB,CAAzB,wBAGF,CAEA,gBAEE,oBAAmC,CACnC,0BAAyC,CAFzC,aAAyB,CAAzB,wBAGF,CAEA,cAEE,oBAAkC,CAClC,0BAAwC,CAFxC,aAAuB,CAAvB,sBAGF", "sources": ["index.css", "App.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n", "@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');\n@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');\n@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap');\n\n/* CSS Variables for AI-Themed Design System */\n:root {\n  /* AI Primary Colors - Neural Network Blue */\n  --primary-50: #eff6ff;\n  --primary-100: #dbeafe;\n  --primary-200: #bfdbfe;\n  --primary-300: #93c5fd;\n  --primary-400: #60a5fa;\n  --primary-500: #3b82f6;\n  --primary-600: #2563eb;\n  --primary-700: #1d4ed8;\n  --primary-800: #1e40af;\n  --primary-900: #1e3a8a;\n\n  /* AI Secondary Colors - Electric Purple */\n  --secondary-50: #faf5ff;\n  --secondary-100: #f3e8ff;\n  --secondary-200: #e9d5ff;\n  --secondary-300: #d8b4fe;\n  --secondary-400: #c084fc;\n  --secondary-500: #a855f7;\n  --secondary-600: #9333ea;\n  --secondary-700: #7c3aed;\n  --secondary-800: #6b21a8;\n  --secondary-900: #581c87;\n\n  /* AI Accent Colors - Cyber Green */\n  --accent-50: #ecfdf5;\n  --accent-100: #d1fae5;\n  --accent-200: #a7f3d0;\n  --accent-300: #6ee7b7;\n  --accent-400: #34d399;\n  --accent-500: #10b981;\n  --accent-600: #059669;\n  --accent-700: #047857;\n  --accent-800: #065f46;\n  --accent-900: #064e3b;\n\n  /* AI Neural Network Grays */\n  --neural-50: #fafbfc;\n  --neural-100: #f4f6f8;\n  --neural-200: #e8ecf0;\n  --neural-300: #d1d9e0;\n  --neural-400: #9aa5b1;\n  --neural-500: #6b7684;\n  --neural-600: #57606a;\n  --neural-700: #424a53;\n  --neural-800: #32383e;\n  --neural-900: #24292e;\n\n  /* Status Colors */\n  --success-500: #10b981;\n  --warning-500: #f59e0b;\n  --error-500: #ef4444;\n  --info-500: #3b82f6;\n\n  /* AI Background Gradients */\n  --bg-primary: #f8fafc;\n  --bg-secondary: linear-gradient(135deg, #fafbfc 0%, #f4f6f8 100%);\n  --bg-tertiary: linear-gradient(135deg, #eff6ff 0%, #faf5ff 100%);\n  --bg-neural: linear-gradient(135deg, #1e3a8a 0%, #7c3aed 100%);\n  --bg-glass: rgba(255, 255, 255, 0.8);\n\n  /* AI Text Colors */\n  --text-primary: #24292e;\n  --text-secondary: #57606a;\n  --text-tertiary: #6b7684;\n  --text-accent: #2563eb;\n  --text-gradient: linear-gradient(135deg, #2563eb 0%, #9333ea 100%);\n\n  /* AI Border Colors */\n  --border-light: #e8ecf0;\n  --border-medium: #d1d9e0;\n  --border-dark: #9aa5b1;\n  --border-accent: #3b82f6;\n  --border-neural: rgba(59, 130, 246, 0.2);\n\n  /* AI Enhanced Shadows */\n  --shadow-sm: 0 1px 2px 0 rgba(36, 41, 46, 0.04);\n  --shadow-md: 0 4px 6px -1px rgba(36, 41, 46, 0.08), 0 2px 4px -2px rgba(36, 41, 46, 0.04);\n  --shadow-lg: 0 10px 15px -3px rgba(36, 41, 46, 0.1), 0 4px 6px -4px rgba(36, 41, 46, 0.05);\n  --shadow-xl: 0 20px 25px -5px rgba(36, 41, 46, 0.12), 0 8px 10px -6px rgba(36, 41, 46, 0.06);\n  --shadow-neural: 0 25px 50px -12px rgba(59, 130, 246, 0.25);\n  --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.15);\n\n  /* Spacing */\n  --space-1: 0.25rem;\n  --space-2: 0.5rem;\n  --space-3: 0.75rem;\n  --space-4: 1rem;\n  --space-5: 1.25rem;\n  --space-6: 1.5rem;\n  --space-8: 2rem;\n  --space-10: 2.5rem;\n  --space-12: 3rem;\n  --space-16: 4rem;\n  --space-20: 5rem;\n  --space-24: 6rem;\n\n  /* Border Radius */\n  --radius-sm: 0.375rem;\n  --radius-md: 0.5rem;\n  --radius-lg: 0.75rem;\n  --radius-xl: 1rem;\n  --radius-2xl: 1.5rem;\n  --radius-full: 9999px;\n\n  /* AI Typography - Updated with better fonts and smaller sizes */\n  --font-primary: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;\n  --font-secondary: 'Open Sans', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n  --font-mono: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;\n\n  /* AI Animations */\n  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);\n  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nhtml {\n  scroll-behavior: smooth;\n  font-size: 16px;\n}\n\nbody {\n  margin: 0;\n  font-family: var(--font-secondary);\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  overflow-x: hidden;\n  background: var(--bg-primary);\n  color: var(--text-primary);\n  line-height: 1.6;\n}\n\ncode {\n  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;\n  background: var(--gray-100);\n  padding: var(--space-1) var(--space-2);\n  border-radius: var(--radius-sm);\n  font-size: 0.875rem;\n}\n\n/* Custom scrollbar */\n::-webkit-scrollbar {\n  width: 6px;\n}\n\n::-webkit-scrollbar-track {\n  background: var(--gray-100);\n}\n\n::-webkit-scrollbar-thumb {\n  background: var(--gray-300);\n  border-radius: var(--radius-full);\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: var(--gray-400);\n}\n\n/* Smooth transitions */\n* {\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n/* Focus styles for accessibility */\nbutton:focus,\ninput:focus,\ntextarea:focus,\nselect:focus {\n  outline: 2px solid var(--primary-500);\n  outline-offset: 2px;\n}\n\nbutton:focus:not(:focus-visible) {\n  outline: none;\n}\n\n/* Typography Classes - Updated with smaller, more readable sizes */\n.text-xs { font-size: 0.75rem; line-height: 1rem; }\n.text-sm { font-size: 0.875rem; line-height: 1.25rem; }\n.text-base { font-size: 1rem; line-height: 1.5rem; }\n.text-lg { font-size: 1.125rem; line-height: 1.75rem; }\n.text-xl { font-size: 1.25rem; line-height: 1.75rem; }\n.text-2xl { font-size: 1.5rem; line-height: 2rem; }\n.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }\n.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }\n.text-5xl { font-size: 2.5rem; line-height: 1; }\n.text-6xl { font-size: 3rem; line-height: 1; }\n\n.font-light { font-weight: 300; }\n.font-normal { font-weight: 400; }\n.font-medium { font-weight: 500; }\n.font-semibold { font-weight: 600; }\n.font-bold { font-weight: 700; }\n.font-extrabold { font-weight: 800; }\n\n.font-primary { font-family: var(--font-primary); }\n.font-secondary { font-family: var(--font-secondary); }\n\n/* Mobile responsiveness */\n@media (max-width: 768px) {\n  html {\n    font-size: 14px;\n  }\n\n  .text-4xl { font-size: 1.875rem; line-height: 2.25rem; }\n  .text-5xl { font-size: 2rem; line-height: 2.5rem; }\n  .text-6xl { font-size: 2.5rem; line-height: 1; }\n}\n\n/* Prevent text selection on buttons */\nbutton {\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n}\n\n/* Selection styles */\n::selection {\n  background: var(--primary-100);\n  color: var(--primary-800);\n}\n\n::-moz-selection {\n  background: var(--primary-100);\n  color: var(--primary-800);\n}\n\n/* Modern Animations */\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateX(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n@keyframes slideUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes scaleIn {\n  from {\n    opacity: 0;\n    transform: scale(0.9);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1);\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n}\n\n/* Modern Card Styles */\n.card {\n  background: var(--bg-primary);\n  border-radius: var(--radius-xl);\n  box-shadow: var(--shadow-lg);\n  border: 1px solid var(--border-light);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.card:hover {\n  box-shadow: var(--shadow-xl);\n  transform: translateY(-2px);\n}\n\n/* Glass Effect for Modern Look */\n.glass {\n  background: rgba(255, 255, 255, 0.8);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n/* Gradient Text Utility */\n.gradient-text {\n  background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n/* Button Styles */\n.btn {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--space-2);\n  padding: var(--space-3) var(--space-6);\n  border-radius: var(--radius-lg);\n  font-weight: 500;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  text-decoration: none;\n  border: none;\n  cursor: pointer;\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n  overflow: hidden;\n}\n\n.btn-primary {\n  background: var(--primary-600);\n  color: white;\n}\n\n.btn-primary:hover {\n  background: var(--primary-700);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-lg);\n}\n\n.btn-secondary {\n  background: var(--bg-primary);\n  color: var(--text-primary);\n  border: 1px solid var(--border-medium);\n}\n\n.btn-secondary:hover {\n  background: var(--gray-50);\n  border-color: var(--border-dark);\n}\n\n.btn-lg {\n  padding: var(--space-4) var(--space-8);\n  font-size: 1rem;\n  line-height: 1.5rem;\n}\n\n/* Responsive utilities */\n@media (max-width: 1024px) {\n  .hide-tablet {\n    display: none;\n  }\n\n  .btn-lg {\n    padding: var(--space-3) var(--space-6);\n    font-size: 0.875rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .hide-mobile {\n    display: none;\n  }\n\n  .btn {\n    padding: var(--space-3) var(--space-5);\n    font-size: 0.8rem;\n  }\n\n  .btn-lg {\n    padding: var(--space-3) var(--space-6);\n    font-size: 0.875rem;\n  }\n}\n\n/* Animation delays */\n.delay-1 { animation-delay: 0.1s; }\n.delay-2 { animation-delay: 0.2s; }\n.delay-3 { animation-delay: 0.3s; }\n.delay-4 { animation-delay: 0.4s; }\n.delay-5 { animation-delay: 0.5s; }\n\n/* Animation classes */\n.animate-fadeIn {\n  animation: fadeIn 0.6s ease-out forwards;\n}\n\n.animate-slideIn {\n  animation: slideIn 0.6s ease-out forwards;\n}\n\n.animate-slideUp {\n  animation: slideUp 0.6s ease-out forwards;\n}\n\n.animate-scaleIn {\n  animation: scaleIn 0.6s ease-out forwards;\n}\n\n.animate-pulse {\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\n\n/* Utility Classes */\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 var(--space-4);\n}\n\n.flex {\n  display: flex;\n}\n\n.flex-col {\n  flex-direction: column;\n}\n\n.items-center {\n  align-items: center;\n}\n\n.justify-center {\n  justify-content: center;\n}\n\n.justify-between {\n  justify-content: space-between;\n}\n\n.text-center {\n  text-align: center;\n}\n\n.w-full {\n  width: 100%;\n}\n\n.h-full {\n  height: 100%;\n}\n\n.rounded-lg {\n  border-radius: var(--radius-lg);\n}\n\n.rounded-xl {\n  border-radius: var(--radius-xl);\n}\n\n.shadow-lg {\n  box-shadow: var(--shadow-lg);\n}\n\n.shadow-xl {\n  box-shadow: var(--shadow-xl);\n}\n\n/* Status indicators */\n.status-success {\n  color: var(--success-500);\n  background: rgba(16, 185, 129, 0.1);\n  border: 1px solid rgba(16, 185, 129, 0.2);\n}\n\n.status-warning {\n  color: var(--warning-500);\n  background: rgba(245, 158, 11, 0.1);\n  border: 1px solid rgba(245, 158, 11, 0.2);\n}\n\n.status-error {\n  color: var(--error-500);\n  background: rgba(239, 68, 68, 0.1);\n  border: 1px solid rgba(239, 68, 68, 0.2);\n}\n"], "names": [], "sourceRoot": ""}