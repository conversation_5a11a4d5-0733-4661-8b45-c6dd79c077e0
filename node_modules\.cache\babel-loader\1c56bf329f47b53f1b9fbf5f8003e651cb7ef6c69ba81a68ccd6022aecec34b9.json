{"ast": null, "code": "import io from'socket.io-client';import axios from'axios';const API_BASE_URL=process.env.REACT_APP_API_URL||'http://localhost:5000';class ASLService{constructor(){this.socket=null;this.isConnected=false;this.recognitionActive=false;this.callbacks={onRecognitionResult:null,onError:null,onConnectionChange:null,onRecognitionStarted:null,onRecognitionStopped:null};}// Initialize WebSocket connection\nconnect(){try{this.socket=io(API_BASE_URL,{transports:['websocket','polling'],timeout:20000,reconnection:true,reconnectionAttempts:5,reconnectionDelay:1000});this.setupSocketListeners();return new Promise((resolve,reject)=>{this.socket.on('connect',()=>{console.log('🔌 Connected to ASL Recognition Backend');this.isConnected=true;this.notifyConnectionChange();resolve();});this.socket.on('connect_error',error=>{console.error('❌ Connection error:',error);this.isConnected=false;this.notifyConnectionChange();reject(error);});});}catch(error){console.error('❌ Failed to connect:',error);throw error;}}// Setup WebSocket event listeners\nsetupSocketListeners(){if(!this.socket)return;this.socket.on('recognitionStarted',data=>{console.log('🎬 Recognition started:',data);this.recognitionActive=true;if(this.callbacks.onRecognitionStarted){this.callbacks.onRecognitionStarted(data);}});this.socket.on('recognitionResult',result=>{console.log('🎯 Recognition result:',result);if(this.callbacks.onRecognitionResult){this.callbacks.onRecognitionResult(result);}});this.socket.on('error',error=>{console.error('❌ Recognition error:',error);if(this.callbacks.onError){this.callbacks.onError(error);}});this.socket.on('disconnect',()=>{console.log('🔌 Disconnected from backend');this.isConnected=false;this.recognitionActive=false;this.notifyConnectionChange();});}// Start real-time recognition\nasync startRecognition(){if(!this.socket||!this.isConnected){throw new Error('Not connected to backend');}try{this.socket.emit('startRecognition');return true;}catch(error){console.error('❌ Failed to start recognition:',error);throw error;}}// Process a video frame\nasync processFrame(frameData){if(!this.socket||!this.isConnected){throw new Error('Not connected to backend');}try{this.socket.emit('processFrame',frameData);}catch(error){console.error('❌ Failed to process frame:',error);throw error;}}// Stop recognition\nstopRecognition(){if(!this.socket||!this.isConnected){return;}try{this.socket.emit('stopRecognition');this.recognitionActive=false;if(this.callbacks.onRecognitionStopped){this.callbacks.onRecognitionStopped();}}catch(error){console.error('❌ Failed to stop recognition:',error);}}// Disconnect from backend\ndisconnect(){if(this.socket){this.socket.disconnect();this.socket=null;this.isConnected=false;this.recognitionActive=false;this.notifyConnectionChange();}}// API calls for sign data\nasync getAvailableSigns(){try{const response=await axios.get(\"\".concat(API_BASE_URL,\"/api/signs\"));return response.data.signs;}catch(error){console.error('❌ Failed to fetch signs:',error);throw error;}}async getSignInfo(signName){try{const response=await axios.get(\"\".concat(API_BASE_URL,\"/api/sign/\").concat(encodeURIComponent(signName)));return response.data;}catch(error){console.error('❌ Failed to fetch sign info:',error);throw error;}}async checkHealth(){try{const response=await axios.get(\"\".concat(API_BASE_URL,\"/api/health\"));return response.data;}catch(error){console.error('❌ Health check failed:',error);throw error;}}// Event callback registration\nonRecognitionResult(callback){this.callbacks.onRecognitionResult=callback;}onError(callback){this.callbacks.onError=callback;}onConnectionChange(callback){this.callbacks.onConnectionChange=callback;}onRecognitionStarted(callback){this.callbacks.onRecognitionStarted=callback;}onRecognitionStopped(callback){this.callbacks.onRecognitionStopped=callback;}// Notify connection change\nnotifyConnectionChange(){if(this.callbacks.onConnectionChange){this.callbacks.onConnectionChange(this.isConnected);}}// Get connection status\ngetConnectionStatus(){return{isConnected:this.isConnected,recognitionActive:this.recognitionActive};}}// Create and export a singleton instance\nconst aslService=new ASLService();export default aslService;", "map": {"version": 3, "names": ["io", "axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "ASLService", "constructor", "socket", "isConnected", "recognitionActive", "callbacks", "onRecognitionResult", "onError", "onConnectionChange", "onRecognitionStarted", "onRecognitionStopped", "connect", "transports", "timeout", "reconnection", "reconnectionAttempts", "reconnectionDelay", "setupSocketListeners", "Promise", "resolve", "reject", "on", "console", "log", "notifyConnectionChange", "error", "data", "result", "startRecognition", "Error", "emit", "processFrame", "frameData", "stopRecognition", "disconnect", "getAvailableSigns", "response", "get", "concat", "signs", "getSignInfo", "signName", "encodeURIComponent", "checkHealth", "callback", "getConnectionStatus", "aslService"], "sources": ["D:/ASL/training-frontend/src/services/aslService.js"], "sourcesContent": ["import io from 'socket.io-client';\r\nimport axios from 'axios';\r\n\r\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\r\n\r\nclass ASLService {\r\n  constructor() {\r\n    this.socket = null;\r\n    this.isConnected = false;\r\n    this.recognitionActive = false;\r\n    this.callbacks = {\r\n      onRecognitionResult: null,\r\n      onError: null,\r\n      onConnectionChange: null,\r\n      onRecognitionStarted: null,\r\n      onRecognitionStopped: null\r\n    };\r\n  }\r\n\r\n  // Initialize WebSocket connection\r\n  connect() {\r\n    try {\r\n      this.socket = io(API_BASE_URL, {\r\n        transports: ['websocket', 'polling'],\r\n        timeout: 20000,\r\n        reconnection: true,\r\n        reconnectionAttempts: 5,\r\n        reconnectionDelay: 1000\r\n      });\r\n\r\n      this.setupSocketListeners();\r\n      \r\n      return new Promise((resolve, reject) => {\r\n        this.socket.on('connect', () => {\r\n          console.log('🔌 Connected to ASL Recognition Backend');\r\n          this.isConnected = true;\r\n          this.notifyConnectionChange();\r\n          resolve();\r\n        });\r\n\r\n        this.socket.on('connect_error', (error) => {\r\n          console.error('❌ Connection error:', error);\r\n          this.isConnected = false;\r\n          this.notifyConnectionChange();\r\n          reject(error);\r\n        });\r\n      });\r\n    } catch (error) {\r\n      console.error('❌ Failed to connect:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Setup WebSocket event listeners\r\n  setupSocketListeners() {\r\n    if (!this.socket) return;\r\n\r\n    this.socket.on('recognitionStarted', (data) => {\r\n      console.log('🎬 Recognition started:', data);\r\n      this.recognitionActive = true;\r\n      if (this.callbacks.onRecognitionStarted) {\r\n        this.callbacks.onRecognitionStarted(data);\r\n      }\r\n    });\r\n\r\n    this.socket.on('recognitionResult', (result) => {\r\n      console.log('🎯 Recognition result:', result);\r\n      if (this.callbacks.onRecognitionResult) {\r\n        this.callbacks.onRecognitionResult(result);\r\n      }\r\n    });\r\n\r\n    this.socket.on('error', (error) => {\r\n      console.error('❌ Recognition error:', error);\r\n      if (this.callbacks.onError) {\r\n        this.callbacks.onError(error);\r\n      }\r\n    });\r\n\r\n    this.socket.on('disconnect', () => {\r\n      console.log('🔌 Disconnected from backend');\r\n      this.isConnected = false;\r\n      this.recognitionActive = false;\r\n      this.notifyConnectionChange();\r\n    });\r\n  }\r\n\r\n  // Start real-time recognition\r\n  async startRecognition() {\r\n    if (!this.socket || !this.isConnected) {\r\n      throw new Error('Not connected to backend');\r\n    }\r\n\r\n    try {\r\n      this.socket.emit('startRecognition');\r\n      return true;\r\n    } catch (error) {\r\n      console.error('❌ Failed to start recognition:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Process a video frame\r\n  async processFrame(frameData) {\r\n    if (!this.socket || !this.isConnected) {\r\n      throw new Error('Not connected to backend');\r\n    }\r\n\r\n    try {\r\n      this.socket.emit('processFrame', frameData);\r\n    } catch (error) {\r\n      console.error('❌ Failed to process frame:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Stop recognition\r\n  stopRecognition() {\r\n    if (!this.socket || !this.isConnected) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      this.socket.emit('stopRecognition');\r\n      this.recognitionActive = false;\r\n      if (this.callbacks.onRecognitionStopped) {\r\n        this.callbacks.onRecognitionStopped();\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Failed to stop recognition:', error);\r\n    }\r\n  }\r\n\r\n  // Disconnect from backend\r\n  disconnect() {\r\n    if (this.socket) {\r\n      this.socket.disconnect();\r\n      this.socket = null;\r\n      this.isConnected = false;\r\n      this.recognitionActive = false;\r\n      this.notifyConnectionChange();\r\n    }\r\n  }\r\n\r\n  // API calls for sign data\r\n  async getAvailableSigns() {\r\n    try {\r\n      const response = await axios.get(`${API_BASE_URL}/api/signs`);\r\n      return response.data.signs;\r\n    } catch (error) {\r\n      console.error('❌ Failed to fetch signs:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async getSignInfo(signName) {\r\n    try {\r\n      const response = await axios.get(`${API_BASE_URL}/api/sign/${encodeURIComponent(signName)}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('❌ Failed to fetch sign info:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async checkHealth() {\r\n    try {\r\n      const response = await axios.get(`${API_BASE_URL}/api/health`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('❌ Health check failed:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Event callback registration\r\n  onRecognitionResult(callback) {\r\n    this.callbacks.onRecognitionResult = callback;\r\n  }\r\n\r\n  onError(callback) {\r\n    this.callbacks.onError = callback;\r\n  }\r\n\r\n  onConnectionChange(callback) {\r\n    this.callbacks.onConnectionChange = callback;\r\n  }\r\n\r\n  onRecognitionStarted(callback) {\r\n    this.callbacks.onRecognitionStarted = callback;\r\n  }\r\n\r\n  onRecognitionStopped(callback) {\r\n    this.callbacks.onRecognitionStopped = callback;\r\n  }\r\n\r\n  // Notify connection change\r\n  notifyConnectionChange() {\r\n    if (this.callbacks.onConnectionChange) {\r\n      this.callbacks.onConnectionChange(this.isConnected);\r\n    }\r\n  }\r\n\r\n  // Get connection status\r\n  getConnectionStatus() {\r\n    return {\r\n      isConnected: this.isConnected,\r\n      recognitionActive: this.recognitionActive\r\n    };\r\n  }\r\n}\r\n\r\n// Create and export a singleton instance\r\nconst aslService = new ASLService();\r\nexport default aslService; "], "mappings": "AAAA,MAAO,CAAAA,EAAE,KAAM,kBAAkB,CACjC,MAAO,CAAAC,KAAK,KAAM,OAAO,CAEzB,KAAM,CAAAC,YAAY,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CAE7E,KAAM,CAAAC,UAAW,CACfC,WAAWA,CAAA,CAAG,CACZ,IAAI,CAACC,MAAM,CAAG,IAAI,CAClB,IAAI,CAACC,WAAW,CAAG,KAAK,CACxB,IAAI,CAACC,iBAAiB,CAAG,KAAK,CAC9B,IAAI,CAACC,SAAS,CAAG,CACfC,mBAAmB,CAAE,IAAI,CACzBC,OAAO,CAAE,IAAI,CACbC,kBAAkB,CAAE,IAAI,CACxBC,oBAAoB,CAAE,IAAI,CAC1BC,oBAAoB,CAAE,IACxB,CAAC,CACH,CAEA;AACAC,OAAOA,CAAA,CAAG,CACR,GAAI,CACF,IAAI,CAACT,MAAM,CAAGR,EAAE,CAACE,YAAY,CAAE,CAC7BgB,UAAU,CAAE,CAAC,WAAW,CAAE,SAAS,CAAC,CACpCC,OAAO,CAAE,KAAK,CACdC,YAAY,CAAE,IAAI,CAClBC,oBAAoB,CAAE,CAAC,CACvBC,iBAAiB,CAAE,IACrB,CAAC,CAAC,CAEF,IAAI,CAACC,oBAAoB,CAAC,CAAC,CAE3B,MAAO,IAAI,CAAAC,OAAO,CAAC,CAACC,OAAO,CAAEC,MAAM,GAAK,CACtC,IAAI,CAAClB,MAAM,CAACmB,EAAE,CAAC,SAAS,CAAE,IAAM,CAC9BC,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC,CACtD,IAAI,CAACpB,WAAW,CAAG,IAAI,CACvB,IAAI,CAACqB,sBAAsB,CAAC,CAAC,CAC7BL,OAAO,CAAC,CAAC,CACX,CAAC,CAAC,CAEF,IAAI,CAACjB,MAAM,CAACmB,EAAE,CAAC,eAAe,CAAGI,KAAK,EAAK,CACzCH,OAAO,CAACG,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC3C,IAAI,CAACtB,WAAW,CAAG,KAAK,CACxB,IAAI,CAACqB,sBAAsB,CAAC,CAAC,CAC7BJ,MAAM,CAACK,KAAK,CAAC,CACf,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAAE,MAAOA,KAAK,CAAE,CACdH,OAAO,CAACG,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACAR,oBAAoBA,CAAA,CAAG,CACrB,GAAI,CAAC,IAAI,CAACf,MAAM,CAAE,OAElB,IAAI,CAACA,MAAM,CAACmB,EAAE,CAAC,oBAAoB,CAAGK,IAAI,EAAK,CAC7CJ,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAEG,IAAI,CAAC,CAC5C,IAAI,CAACtB,iBAAiB,CAAG,IAAI,CAC7B,GAAI,IAAI,CAACC,SAAS,CAACI,oBAAoB,CAAE,CACvC,IAAI,CAACJ,SAAS,CAACI,oBAAoB,CAACiB,IAAI,CAAC,CAC3C,CACF,CAAC,CAAC,CAEF,IAAI,CAACxB,MAAM,CAACmB,EAAE,CAAC,mBAAmB,CAAGM,MAAM,EAAK,CAC9CL,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAEI,MAAM,CAAC,CAC7C,GAAI,IAAI,CAACtB,SAAS,CAACC,mBAAmB,CAAE,CACtC,IAAI,CAACD,SAAS,CAACC,mBAAmB,CAACqB,MAAM,CAAC,CAC5C,CACF,CAAC,CAAC,CAEF,IAAI,CAACzB,MAAM,CAACmB,EAAE,CAAC,OAAO,CAAGI,KAAK,EAAK,CACjCH,OAAO,CAACG,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,GAAI,IAAI,CAACpB,SAAS,CAACE,OAAO,CAAE,CAC1B,IAAI,CAACF,SAAS,CAACE,OAAO,CAACkB,KAAK,CAAC,CAC/B,CACF,CAAC,CAAC,CAEF,IAAI,CAACvB,MAAM,CAACmB,EAAE,CAAC,YAAY,CAAE,IAAM,CACjCC,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC,CAC3C,IAAI,CAACpB,WAAW,CAAG,KAAK,CACxB,IAAI,CAACC,iBAAiB,CAAG,KAAK,CAC9B,IAAI,CAACoB,sBAAsB,CAAC,CAAC,CAC/B,CAAC,CAAC,CACJ,CAEA;AACA,KAAM,CAAAI,gBAAgBA,CAAA,CAAG,CACvB,GAAI,CAAC,IAAI,CAAC1B,MAAM,EAAI,CAAC,IAAI,CAACC,WAAW,CAAE,CACrC,KAAM,IAAI,CAAA0B,KAAK,CAAC,0BAA0B,CAAC,CAC7C,CAEA,GAAI,CACF,IAAI,CAAC3B,MAAM,CAAC4B,IAAI,CAAC,kBAAkB,CAAC,CACpC,MAAO,KAAI,CACb,CAAE,MAAOL,KAAK,CAAE,CACdH,OAAO,CAACG,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtD,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACA,KAAM,CAAAM,YAAYA,CAACC,SAAS,CAAE,CAC5B,GAAI,CAAC,IAAI,CAAC9B,MAAM,EAAI,CAAC,IAAI,CAACC,WAAW,CAAE,CACrC,KAAM,IAAI,CAAA0B,KAAK,CAAC,0BAA0B,CAAC,CAC7C,CAEA,GAAI,CACF,IAAI,CAAC3B,MAAM,CAAC4B,IAAI,CAAC,cAAc,CAAEE,SAAS,CAAC,CAC7C,CAAE,MAAOP,KAAK,CAAE,CACdH,OAAO,CAACG,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACAQ,eAAeA,CAAA,CAAG,CAChB,GAAI,CAAC,IAAI,CAAC/B,MAAM,EAAI,CAAC,IAAI,CAACC,WAAW,CAAE,CACrC,OACF,CAEA,GAAI,CACF,IAAI,CAACD,MAAM,CAAC4B,IAAI,CAAC,iBAAiB,CAAC,CACnC,IAAI,CAAC1B,iBAAiB,CAAG,KAAK,CAC9B,GAAI,IAAI,CAACC,SAAS,CAACK,oBAAoB,CAAE,CACvC,IAAI,CAACL,SAAS,CAACK,oBAAoB,CAAC,CAAC,CACvC,CACF,CAAE,MAAOe,KAAK,CAAE,CACdH,OAAO,CAACG,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACvD,CACF,CAEA;AACAS,UAAUA,CAAA,CAAG,CACX,GAAI,IAAI,CAAChC,MAAM,CAAE,CACf,IAAI,CAACA,MAAM,CAACgC,UAAU,CAAC,CAAC,CACxB,IAAI,CAAChC,MAAM,CAAG,IAAI,CAClB,IAAI,CAACC,WAAW,CAAG,KAAK,CACxB,IAAI,CAACC,iBAAiB,CAAG,KAAK,CAC9B,IAAI,CAACoB,sBAAsB,CAAC,CAAC,CAC/B,CACF,CAEA;AACA,KAAM,CAAAW,iBAAiBA,CAAA,CAAG,CACxB,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAzC,KAAK,CAAC0C,GAAG,IAAAC,MAAA,CAAI1C,YAAY,cAAY,CAAC,CAC7D,MAAO,CAAAwC,QAAQ,CAACV,IAAI,CAACa,KAAK,CAC5B,CAAE,MAAOd,KAAK,CAAE,CACdH,OAAO,CAACG,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,KAAM,CAAAA,KAAK,CACb,CACF,CAEA,KAAM,CAAAe,WAAWA,CAACC,QAAQ,CAAE,CAC1B,GAAI,CACF,KAAM,CAAAL,QAAQ,CAAG,KAAM,CAAAzC,KAAK,CAAC0C,GAAG,IAAAC,MAAA,CAAI1C,YAAY,eAAA0C,MAAA,CAAaI,kBAAkB,CAACD,QAAQ,CAAC,CAAE,CAAC,CAC5F,MAAO,CAAAL,QAAQ,CAACV,IAAI,CACtB,CAAE,MAAOD,KAAK,CAAE,CACdH,OAAO,CAACG,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpD,KAAM,CAAAA,KAAK,CACb,CACF,CAEA,KAAM,CAAAkB,WAAWA,CAAA,CAAG,CAClB,GAAI,CACF,KAAM,CAAAP,QAAQ,CAAG,KAAM,CAAAzC,KAAK,CAAC0C,GAAG,IAAAC,MAAA,CAAI1C,YAAY,eAAa,CAAC,CAC9D,MAAO,CAAAwC,QAAQ,CAACV,IAAI,CACtB,CAAE,MAAOD,KAAK,CAAE,CACdH,OAAO,CAACG,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACAnB,mBAAmBA,CAACsC,QAAQ,CAAE,CAC5B,IAAI,CAACvC,SAAS,CAACC,mBAAmB,CAAGsC,QAAQ,CAC/C,CAEArC,OAAOA,CAACqC,QAAQ,CAAE,CAChB,IAAI,CAACvC,SAAS,CAACE,OAAO,CAAGqC,QAAQ,CACnC,CAEApC,kBAAkBA,CAACoC,QAAQ,CAAE,CAC3B,IAAI,CAACvC,SAAS,CAACG,kBAAkB,CAAGoC,QAAQ,CAC9C,CAEAnC,oBAAoBA,CAACmC,QAAQ,CAAE,CAC7B,IAAI,CAACvC,SAAS,CAACI,oBAAoB,CAAGmC,QAAQ,CAChD,CAEAlC,oBAAoBA,CAACkC,QAAQ,CAAE,CAC7B,IAAI,CAACvC,SAAS,CAACK,oBAAoB,CAAGkC,QAAQ,CAChD,CAEA;AACApB,sBAAsBA,CAAA,CAAG,CACvB,GAAI,IAAI,CAACnB,SAAS,CAACG,kBAAkB,CAAE,CACrC,IAAI,CAACH,SAAS,CAACG,kBAAkB,CAAC,IAAI,CAACL,WAAW,CAAC,CACrD,CACF,CAEA;AACA0C,mBAAmBA,CAAA,CAAG,CACpB,MAAO,CACL1C,WAAW,CAAE,IAAI,CAACA,WAAW,CAC7BC,iBAAiB,CAAE,IAAI,CAACA,iBAC1B,CAAC,CACH,CACF,CAEA;AACA,KAAM,CAAA0C,UAAU,CAAG,GAAI,CAAA9C,UAAU,CAAC,CAAC,CACnC,cAAe,CAAA8C,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}