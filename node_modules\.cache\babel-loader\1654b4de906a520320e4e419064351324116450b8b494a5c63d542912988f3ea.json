{"ast": null, "code": "var _jsxFileName = \"D:\\\\ASL\\\\training-frontend\\\\src\\\\components\\\\TrainingPage.js\",\n  _s = $RefreshSig$();\nimport { useState, useRef, useCallback, useEffect } from 'react';\nimport styled from 'styled-components';\nimport Webcam from 'react-webcam';\nimport { Brain, Camera, ArrowLeft, Play, Square, Download, Eye, Target, Wifi, WifiOff, RefreshCw } from 'lucide-react';\nimport { useSignDetection } from '../hooks/useSignDetection';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TrainingContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow-x: hidden;\n\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n`;\n_c = TrainingContainer;\nconst Navigation = styled.nav`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: var(--bg-glass);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-neural);\n  padding: var(--space-4) 0;\n  transition: var(--transition-normal);\n`;\n_c2 = Navigation;\nconst NavContainer = styled.div`\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  @media (max-width: 768px) {\n    padding: 0 var(--space-4);\n  }\n`;\n_c3 = NavContainer;\nconst Logo = styled.div`\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n    gap: var(--space-2);\n  }\n`;\n_c4 = Logo;\nconst LogoIcon = styled.div`\n  width: 40px;\n  height: 40px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    width: 36px;\n    height: 36px;\n  }\n`;\n_c5 = LogoIcon;\nconst BackButton = styled.button`\n  background: var(--bg-glass);\n  color: var(--text-secondary);\n  border: 1px solid var(--border-neural);\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-xl);\n  font-size: 0.9rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  backdrop-filter: blur(10px);\n\n  &:hover {\n    background: var(--primary-50);\n    color: var(--primary-600);\n    border-color: var(--primary-300);\n    transform: translateY(-1px);\n    box-shadow: var(--shadow-lg);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-2) var(--space-4);\n    font-size: 0.85rem;\n  }\n`;\n_c6 = BackButton;\nconst PageTitle = styled.h1`\n  font-family: var(--font-primary);\n  font-size: 2.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  text-align: center;\n  margin-bottom: var(--space-4);\n  letter-spacing: -0.02em;\n\n  @media (max-width: 768px) {\n    font-size: 2rem;\n  }\n`;\n_c7 = PageTitle;\nconst PageSubtitle = styled.p`\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  text-align: center;\n  margin-bottom: var(--space-16);\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n\n  @media (max-width: 768px) {\n    margin-bottom: var(--space-12);\n    font-size: 1rem;\n  }\n`;\n_c8 = PageSubtitle;\nconst StatusBadge = styled.div`\n  display: inline-flex;\n  align-items: center;\n  gap: var(--space-2);\n  background: var(--bg-glass);\n  border: 1px solid var(--border-neural);\n  border-radius: var(--radius-full);\n  padding: var(--space-2) var(--space-4);\n  margin-bottom: var(--space-8);\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: var(--text-accent);\n  backdrop-filter: blur(10px);\n  box-shadow: var(--shadow-glow);\n\n  @media (max-width: 768px) {\n    font-size: 0.8rem;\n    padding: var(--space-2) var(--space-3);\n  }\n`;\n_c9 = StatusBadge;\nconst MainContent = styled.main`\n  padding: var(--space-20) var(--space-4) var(--space-16);\n  max-width: 1200px;\n  margin: 0 auto;\n\n  @media (max-width: 768px) {\n    padding: var(--space-12) var(--space-3) var(--space-8);\n    max-width: 100%;\n  }\n`;\n_c0 = MainContent;\nconst TrainingGrid = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--space-8);\n  max-width: 1200px;\n  margin: 0 auto var(--space-12);\n\n  @media (max-width: 1024px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-4);\n  }\n\n  @media (max-width: 768px) {\n    gap: var(--space-3);\n    margin: 0 auto var(--space-6);\n  }\n`;\n_c1 = TrainingGrid;\nconst CameraSection = styled.div`\n  background: var(--bg-glass);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-10);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-lg);\n  transition: var(--transition-normal);\n  position: relative;\n  overflow: hidden;\n\n  @media (max-width: 768px) {\n    padding: var(--space-6);\n    border-radius: var(--radius-xl);\n  }\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 4px;\n    background: var(--bg-neural);\n    transform: scaleX(0);\n    transition: var(--transition-normal);\n  }\n\n  &:hover {\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n    border-color: var(--primary-300);\n\n    &::before {\n      transform: scaleX(1);\n    }\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-8);\n  }\n`;\n_c10 = CameraSection;\nconst SectionTitle = styled.h2`\n  font-family: var(--font-primary);\n  font-size: 1.25rem;\n  margin-bottom: var(--space-6);\n  color: var(--text-primary);\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    margin-bottom: var(--space-4);\n  }\n`;\n_c11 = SectionTitle;\nconst SectionIcon = styled.div`\n  width: 36px;\n  height: 36px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    width: 32px;\n    height: 32px;\n  }\n`;\n_c12 = SectionIcon;\nconst WebcamContainer = styled.div`\n  position: relative;\n  border-radius: var(--radius-2xl);\n  overflow: hidden;\n  background: var(--neural-100);\n  aspect-ratio: 4/3;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 3px solid var(--border-neural);\n  margin-bottom: var(--space-6);\n  box-shadow: var(--shadow-lg);\n\n  @media (max-width: 768px) {\n    aspect-ratio: 3/4;\n    margin-bottom: var(--space-4);\n    border-radius: var(--radius-xl);\n    border-width: 2px;\n  }\n`;\n_c13 = WebcamContainer;\nconst StyledWebcam = styled(Webcam)`\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n`;\n_c14 = StyledWebcam;\nconst RecordingOverlay = styled.div`\n  position: absolute;\n  top: var(--space-4);\n  right: var(--space-4);\n  background: ${props => props.isRecording ? 'var(--error-500)' : 'var(--neural-600)'};\n  color: white;\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-full);\n  font-size: 0.9rem;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  box-shadow: var(--shadow-lg);\n  animation: ${props => props.isRecording ? 'pulse 1.5s infinite' : 'none'};\n\n  @keyframes pulse {\n    0%, 100% { opacity: 1; transform: scale(1); }\n    50% { opacity: 0.8; transform: scale(1.05); }\n  }\n`;\n_c15 = RecordingOverlay;\nconst SignSection = styled.div`\n  background: var(--bg-primary);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-8);\n  border: 1px solid var(--border-light);\n  box-shadow: var(--shadow-lg);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  transition: all 0.3s ease;\n\n  &:hover {\n    box-shadow: var(--shadow-xl);\n    border-color: var(--primary-200);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-6);\n  }\n`;\n_c16 = SignSection;\nconst SignSelector = styled.select`\n  width: 100%;\n  padding: var(--space-3) var(--space-4);\n  border: 2px solid var(--border-light);\n  border-radius: var(--radius-lg);\n  background: var(--bg-primary);\n  color: var(--text-primary);\n  font-size: 1rem;\n  font-weight: 500;\n  margin-bottom: var(--space-4);\n  cursor: pointer;\n  transition: var(--transition-normal);\n\n  &:focus {\n    outline: none;\n    border-color: var(--primary-500);\n    box-shadow: 0 0 0 3px var(--primary-100);\n  }\n\n  &:hover {\n    border-color: var(--primary-300);\n  }\n\n  option {\n    padding: var(--space-2);\n    background: var(--bg-primary);\n    color: var(--text-primary);\n  }\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    padding: var(--space-4);\n    margin-bottom: var(--space-3);\n  }\n`;\n_c17 = SignSelector;\nconst SignDisplay = styled.div`\n  width: 300px;\n  height: 300px;\n  background: var(--primary-50);\n  border-radius: var(--radius-2xl);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: var(--space-6);\n  border: 2px solid var(--primary-200);\n  transition: all 0.3s ease;\n  overflow: hidden;\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: var(--radius-xl);\n  }\n\n  &:hover {\n    transform: scale(1.02);\n    border-color: var(--primary-300);\n  }\n\n  @media (max-width: 768px) {\n    width: 250px;\n    height: 250px;\n  }\n`;\n_c18 = SignDisplay;\nconst SignName = styled.h3`\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  margin-bottom: var(--space-3);\n  color: var(--text-primary);\n  font-weight: 700;\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n  }\n`;\n_c19 = SignName;\nconst SignDescription = styled.p`\n  text-align: center;\n  line-height: 1.6;\n  color: var(--text-secondary);\n  font-size: 0.9rem;\n  font-weight: 400;\n  max-width: 280px;\n`;\n_c20 = SignDescription;\nconst TopControlsSection = styled.div`\n  display: flex;\n  justify-content: center;\n  gap: var(--space-4);\n  margin-bottom: var(--space-8);\n  padding: var(--space-4);\n  background: var(--bg-glass);\n  border-radius: var(--radius-xl);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(10px);\n  box-shadow: var(--shadow-lg);\n\n  @media (max-width: 768px) {\n    flex-direction: column;\n    align-items: center;\n    gap: var(--space-3);\n    margin-bottom: var(--space-6);\n    padding: var(--space-3);\n  }\n`;\n_c21 = TopControlsSection;\nconst ControlButton = styled.button`\n  background: ${props => props.variant === 'primary' ? 'var(--primary-600)' : props.variant === 'retry' ? 'var(--warning-500)' : 'var(--bg-primary)'};\n  border: ${props => props.variant === 'primary' || props.variant === 'retry' ? 'none' : '1px solid var(--border-medium)'};\n  color: ${props => props.variant === 'primary' || props.variant === 'retry' ? 'white' : 'var(--text-primary)'};\n  padding: ${props => props.compact ? 'var(--space-2) var(--space-4)' : 'var(--space-3) var(--space-6)'};\n  border-radius: var(--radius-lg);\n  cursor: pointer;\n  font-size: ${props => props.compact ? '0.8rem' : '0.9rem'};\n  font-weight: 600;\n  transition: all 0.2s ease;\n  min-width: ${props => props.compact ? '120px' : '160px'};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--space-2);\n\n  @media (max-width: 768px) {\n    padding: ${props => props.compact ? 'var(--space-3) var(--space-5)' : 'var(--space-4) var(--space-8)'};\n    font-size: ${props => props.compact ? '0.9rem' : '1rem'};\n    min-width: ${props => props.compact ? '140px' : '180px'};\n    border-radius: var(--radius-xl);\n  }\n  box-shadow: ${props => props.variant === 'primary' || props.variant === 'retry' ? 'var(--shadow-lg)' : 'var(--shadow-sm)'};\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: ${props => props.variant === 'primary' || props.variant === 'retry' ? 'var(--shadow-xl)' : 'var(--shadow-md)'};\n    background: ${props => props.variant === 'primary' ? 'var(--primary-700)' : props.variant === 'retry' ? 'var(--warning-600)' : 'var(--gray-50)'};\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none;\n  }\n\n  @media (max-width: 768px) {\n    width: 100%;\n    max-width: ${props => props.compact ? '200px' : '280px'};\n  }\n`;\n_c22 = ControlButton;\nconst StatusMessage = styled.div`\n  text-align: center;\n  margin-top: var(--space-6);\n  padding: var(--space-4) var(--space-6);\n  border-radius: var(--radius-lg);\n  background: ${props => props.type === 'success' ? 'var(--success-500)' : props.type === 'error' ? 'var(--error-500)' : 'var(--primary-600)'};\n  color: white;\n  font-weight: 500;\n  font-size: 0.875rem;\n  max-width: 400px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n_c23 = StatusMessage;\nconst RecordingsSection = styled.div`\n  margin-top: var(--space-16);\n  background: var(--bg-secondary);\n  padding: var(--space-12) var(--space-4);\n  border-radius: var(--radius-2xl);\n  max-width: 1200px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n_c24 = RecordingsSection;\nconst RecordingsTitle = styled.h3`\n  font-family: var(--font-primary);\n  color: var(--text-primary);\n  margin-bottom: var(--space-8);\n  font-size: 1.5rem;\n  font-weight: 600;\n  text-align: center;\n`;\n_c25 = RecordingsTitle;\nconst RecordingsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: var(--space-6);\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-4);\n  }\n`;\n_c26 = RecordingsGrid;\nconst RecordingCard = styled.div`\n  background: var(--bg-primary);\n  padding: var(--space-6);\n  border-radius: var(--radius-xl);\n  border: 1px solid var(--border-light);\n  box-shadow: var(--shadow-sm);\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: translateY(-2px);\n    border-color: var(--primary-200);\n    box-shadow: var(--shadow-lg);\n  }\n`;\n_c27 = RecordingCard;\nconst RecordingTitle = styled.p`\n  margin: 0 0 var(--space-2) 0;\n  color: var(--text-primary);\n  font-weight: 600;\n  font-size: 1rem;\n  font-family: var(--font-primary);\n`;\n_c28 = RecordingTitle;\nconst RecordingTime = styled.p`\n  margin: 0 0 var(--space-4) 0;\n  font-size: 0.8rem;\n  color: var(--text-tertiary);\n`;\n_c29 = RecordingTime;\nconst DownloadButton = styled.button`\n  background: var(--primary-600);\n  border: none;\n  border-radius: var(--radius-lg);\n  padding: var(--space-2) var(--space-4);\n  color: white;\n  cursor: pointer;\n  font-size: 0.8rem;\n  font-weight: 500;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  margin: 0 auto;\n\n  &:hover {\n    background: var(--primary-700);\n    transform: translateY(-1px);\n  }\n`;\n_c30 = DownloadButton;\nconst PredictionDisplay = styled.div`\n  background: var(--bg-glass);\n  border: 2px solid ${props => {\n  if (props.matched) return 'var(--success-400)';\n  if (props.isStale) return 'var(--warning-300)';\n  return 'var(--border-light)';\n}};\n  border-radius: var(--radius-xl);\n  padding: var(--space-4);\n  margin-bottom: var(--space-4);\n  text-align: center;\n  transition: var(--transition-normal);\n  backdrop-filter: blur(10px);\n  opacity: ${props => props.isStale ? 0.7 : 1};\n  min-height: 80px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n\n  ${props => props.matched && `\n    background: var(--success-50);\n    box-shadow: 0 0 20px var(--success-200);\n    animation: pulse 1s ease-in-out;\n  `}\n\n  ${props => props.isStale && `\n    background: var(--warning-50);\n  `}\n\n  @keyframes pulse {\n    0%, 100% { transform: scale(1); }\n    50% { transform: scale(1.02); }\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-3);\n    margin-bottom: var(--space-3);\n    min-height: 70px;\n  }\n`;\n_c31 = PredictionDisplay;\nconst PredictionText = styled.div`\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: ${props => {\n  if (props.matched) return 'var(--success-700)';\n  if (props.isStale) return 'var(--warning-700)';\n  return 'var(--text-primary)';\n}};\n  margin-bottom: var(--space-2);\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n  }\n`;\n_c32 = PredictionText;\nconst ConfidenceBar = styled.div`\n  width: 100%;\n  height: 8px;\n  background: var(--bg-secondary);\n  border-radius: var(--radius-full);\n  overflow: hidden;\n  margin-top: var(--space-2);\n`;\n_c33 = ConfidenceBar;\nconst ConfidenceFill = styled.div`\n  height: 100%;\n  background: ${props => {\n  if (props.confidence > 0.8) return 'var(--success-500)';\n  if (props.confidence > 0.6) return 'var(--warning-500)';\n  return 'var(--error-500)';\n}};\n  width: ${props => props.confidence * 100}%;\n  transition: width 0.3s ease;\n`;\n_c34 = ConfidenceFill;\nconst ConnectionStatus = styled.div`\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  padding: var(--space-2) var(--space-3);\n  border-radius: var(--radius-lg);\n  font-size: 0.875rem;\n  font-weight: 500;\n  background: ${props => props.connected ? 'var(--success-50)' : 'var(--error-50)'};\n  color: ${props => props.connected ? 'var(--success-700)' : 'var(--error-700)'};\n  border: 1px solid ${props => props.connected ? 'var(--success-200)' : 'var(--error-200)'};\n`;\n\n// Sign language data with GIFs (100 signs, model-predictable)\n_c35 = ConnectionStatus;\nconst signLanguageData = {\n  \"TV\": {\n    name: \"TV\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/tv.gif\",\n    description: \"Sign for TV.\"\n  },\n  \"after\": {\n    name: \"After\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/after.gif\",\n    description: \"Sign for after.\"\n  },\n  \"airplane\": {\n    name: \"Airplane\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/airplane.gif\",\n    description: \"Sign for airplane.\"\n  },\n  \"all\": {\n    name: \"All\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/all.gif\",\n    description: \"Sign for all.\"\n  },\n  \"alligator\": {\n    name: \"Alligator\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/alligator.gif\",\n    description: \"Sign for alligator.\"\n  },\n  \"animal\": {\n    name: \"Animal\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/animal.gif\",\n    description: \"Sign for animal.\"\n  },\n  \"another\": {\n    name: \"Another\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/another.gif\",\n    description: \"Sign for another.\"\n  },\n  \"any\": {\n    name: \"Any\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/any.gif\",\n    description: \"Sign for any.\"\n  },\n  \"apple\": {\n    name: \"Apple\",\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\n    description: \"Sign for apple.\"\n  },\n  \"arm\": {\n    name: \"Arm\",\n    gif: \"https://th.bing.com/th/id/OIP.KkJLqfbp6XEHiIe-jOUb2AHaEc?w=251&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for arm.\"\n  },\n  \"aunt\": {\n    name: \"Aunt\",\n    gif: \"https://th.bing.com/th/id/OIP.Yz5UUZdNTrVWXf72we_N6wHaHa?rs=1&pid=ImgDetMain\",\n    description: \"Sign for aunt.\"\n  },\n  \"awake\": {\n    name: \"Awake\",\n    gif: \"https://th.bing.com/th/id/OIP.XcgdjGKBo8LynmiAw-tDCQHaE-?w=235&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for awake.\"\n  },\n  \"backyard\": {\n    name: \"Backyard\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/backyard.gif\",\n    description: \"Sign for backyard.\"\n  },\n  \"bad\": {\n    name: \"Bad\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/bad.gif\",\n    description: \"Sign for bad.\"\n  },\n  \"balloon\": {\n    name: \"Balloon\",\n    gif: \"https://media.giphy.com/media/26FL9yfajyobRXJde/giphy.gif\",\n    description: \"Sign for balloon.\"\n  },\n  \"bath\": {\n    name: \"Bath\",\n    gif: \"https://media.giphy.com/media/l0MYPjjoeJbZVPmNO/giphy.gif\",\n    description: \"Sign for bath.\"\n  },\n  \"because\": {\n    name: \"Because\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/because.gif\",\n    description: \"Sign for because.\"\n  },\n  \"bed\": {\n    name: \"Bed\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/bed.gif\",\n    description: \"Sign for bed.\"\n  },\n  \"bedroom\": {\n    name: \"Bedroom\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/bedroom.gif\",\n    description: \"Sign for bedroom.\"\n  },\n  \"bee\": {\n    name: \"Bee\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/bee.gif\",\n    description: \"Sign for bee.\"\n  },\n  \"before\": {\n    name: \"Before\",\n    gif: \"https://th.bing.com/th/id/OIP.0EvzUY4jH2cDCa4nNcRw4wHaE-?w=267&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for before.\"\n  },\n  \"beside\": {\n    name: \"Beside\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/beside.gif\",\n    description: \"Sign for beside.\"\n  },\n  \"better\": {\n    name: \"Better\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/better.gif\",\n    description: \"Sign for better.\"\n  },\n  \"bird\": {\n    name: \"Bird\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/bird.gif\",\n    description: \"Sign for bird.\"\n  },\n  \"black\": {\n    name: \"Black\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=52tGw7%2fGcx2Htw&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fblack.gif&ehk=jgXbFVBhP%2b5fFrT1%2fE%2fcEq7KnRYjrCKtqEd%2fA5rNhqo%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for black.\"\n  },\n  \"blow\": {\n    name: \"Blow\",\n    gif: \"https://th.bing.com/th/id/OIP.rJg-otMBtvfj1T1HkSKugwHaEc?w=304&h=182&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for blow.\"\n  },\n  \"blue\": {\n    name: \"Blue\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/blue.gif\",\n    description: \"Sign for blue.\"\n  },\n  \"boat\": {\n    name: \"Boat\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/boat.gif\",\n    description: \"Sign for boat.\"\n  },\n  \"book\": {\n    name: \"Book\",\n    gif: \"https://media.giphy.com/media/l0MYL43dl4pQEn3uE/giphy.gif\",\n    description: \"Sign for book.\"\n  },\n  \"boy\": {\n    name: \"Boy\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/boy.gif\",\n    description: \"Sign for boy.\"\n  },\n  \"brother\": {\n    name: \"Brother\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/brother.gif\",\n    description: \"Sign for brother.\"\n  },\n  \"brown\": {\n    name: \"Brown\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/brown.gif\",\n    description: \"Sign for brown.\"\n  },\n  \"bug\": {\n    name: \"Bug\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/bug.gif\",\n    description: \"Sign for bug.\"\n  },\n  \"bye\": {\n    name: \"Bye\",\n    gif: \"https://c.tenor.com/vME77PObDN8AAAAC/asl-bye-asl-goodbye.gif\",\n    description: \"Sign for bye.\"\n  },\n  \"callonphone\": {\n    name: \"Call on phone\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/call.gif\",\n    description: \"Sign for call on phone.\"\n  },\n  \"can\": {\n    name: \"Can\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/can.gif\",\n    description: \"Sign for can.\"\n  },\n  \"car\": {\n    name: \"Car\",\n    gif: \"https://th.bing.com/th/id/OIP.wxw32OaIdqFt8f_ucHVoRgHaEH?w=308&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for car.\"\n  },\n  \"carrot\": {\n    name: \"Carrot\",\n    gif: \"https://media.giphy.com/media/l0HlDdvqxs1jsRtiU/giphy.gif\",\n    description: \"Sign for carrot.\"\n  },\n  \"cat\": {\n    name: \"Cat\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/cat-02.gif\",\n    description: \"Sign for cat.\"\n  },\n  \"cereal\": {\n    name: \"Cereal\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=wPMg%2fK1dYTfR%2bw&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fcereal.gif&ehk=RpDS3wWZM4eryawaxA1wAvWwM0EM%2fdGgJkWY2ce1KFs%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for cereal.\"\n  },\n  \"chair\": {\n    name: \"Chair\",\n    gif: \"https://th.bing.com/th/id/OIP.5kr1MkVLnuN2Z9Jkw-0QpAHaE-?w=237&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for chair.\"\n  },\n  \"cheek\": {\n    name: \"Cheek\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/cheek.gif\",\n    description: \"Sign for cheek.\"\n  },\n  \"child\": {\n    name: \"Child\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/child.gif\",\n    description: \"Sign for child.\"\n  },\n  \"chin\": {\n    name: \"Chin\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/chin.gif\",\n    description: \"Sign for chin.\"\n  },\n  \"chocolate\": {\n    name: \"Chocolate\",\n    gif: \"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fi.pinimg.com%2foriginals%2f9f%2fa2%2fb5%2f9fa2b5064a72b5e46202d20848f1bf21.gif&ehk=izvOlFp25%2fx5NVTCmqVz0UOnZNOWy%2fAJJtzAhkZ8nTg%3d\",\n    description: \"Sign for chocolate.\"\n  },\n  \"clean\": {\n    name: \"Clean\",\n    gif: \"https://media.giphy.com/media/3o7TKoturrdpf5Muwo/giphy.gif\",\n    description: \"Sign for clean.\"\n  },\n  \"close\": {\n    name: \"Close\",\n    gif: \"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia2.giphy.com%2fmedia%2fl4JyZuXNGxS3Yydeo%2fgiphy.gif%3fcid%3d790b7611318eb5b864ad67b3cecb35b9d81240a50d251bb0%26rid%3dgiphy.gif%26ct%3dg&ehk=A6wfp3Afm3rFCPLWSjgQd6JVjmRSBNBlk9vd0jVNgJc%3d\",\n    description: \"Sign for close.\"\n  },\n  \"closet\": {\n    name: \"Closet\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/closet.gif\",\n    description: \"Sign for closet.\"\n  },\n  \"cloud\": {\n    name: \"Cloud\",\n    gif: \"https://th.bing.com/th/id/OIP.hMO89bV2zwVcIVIa7FOT5QHaEc?rs=1&pid=ImgDetMain\",\n    description: \"Sign for cloud.\"\n  },\n  \"clown\": {\n    name: \"Clown\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=OPrV3%2b1Zkelr2A&pid=ImgRaw&r=0\",\n    description: \"Sign for clown.\"\n  },\n  \"cow\": {\n    name: \"Cow\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/cow.gif\",\n    description: \"Sign for cow.\"\n  },\n  \"cowboy\": {\n    name: \"Cowboy\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/cowboy.gif\",\n    description: \"Sign for cowboy.\"\n  },\n  \"cry\": {\n    name: \"Cry\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/cry.gif\",\n    description: \"Sign for cry.\"\n  },\n  \"cut\": {\n    name: \"Cut\",\n    gif: \"https://th.bing.com/th/id/OIP.ZtKu3hlJ6pduArqfgEcyUgHaE-?w=248&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for cut.\"\n  },\n  \"cute\": {\n    name: \"Cute\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/cute.gif\",\n    description: \"Sign for cute.\"\n  },\n  \"dad\": {\n    name: \"Dad\",\n    gif: \"https://media.giphy.com/media/l0MYQcLKwtl5v6H1S/giphy.gif\",\n    description: \"Sign for dad.\"\n  },\n  \"dance\": {\n    name: \"Dance\",\n    gif: \"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia.giphy.com%2fmedia%2f3o7TKMspYQjQTbOz2U%2fgiphy.gif&ehk=h%2bdBHCxuoOT89ovSy5uTk6MCL9acaBEV6ld9lrVDRF4%3d\",\n    description: \"Sign for dance.\"\n  },\n  \"dirty\": {\n    name: \"Dirty\",\n    gif: \"https://th.bing.com/th/id/OIP.wRA7r1OPPUuEoLL4Hds9jAHaHa?rs=1&pid=ImgDetMain\",\n    description: \"Sign for dirty.\"\n  },\n  \"dog\": {\n    name: \"Dog\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=uVshGsOHXgldoQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fd%2fdog1.gif&ehk=Xnfrvg0xwy1u%2fae9vWdKYMT25%2bF6qoteW2FThCbVrOA%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for dog.\"\n  },\n  \"doll\": {\n    name: \"Doll\",\n    gif: \"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fwww.lifeprint.com%2fasl101%2fgifs-animated%2fdoll.gif&ehk=hPI0Fzzl9CGOrgQYS2Z53a5YdYgjxYFeOIGghGAEZYU%3d\",\n    description: \"Sign for doll.\"\n  },\n  \"donkey\": {\n    name: \"Donkey\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/d/donkey-1h.gif\",\n    description: \"Sign for donkey.\"\n  },\n  \"down\": {\n    name: \"Down\",\n    gif: \"https://th.bing.com/th/id/OIP.CZlW6IpZUdgspxVpW6PZ8QHaE-?w=250&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for down.\"\n  },\n  \"drawer\": {\n    name: \"Drawer\",\n    gif: \"https://th.bing.com/th/id/OIP.8yooqOFFixqki7j28PVpYQHaE-?w=234&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for drawer.\"\n  },\n  \"drink\": {\n    name: \"Drink\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/d/drink-c.gif\",\n    description: \"Sign for drink.\"\n  },\n  \"drop\": {\n    name: \"Drop\",\n    gif: \"https://th.bing.com/th/id/OIP.XQJn0tOccOUmG8OZHz8X9gHaE-?w=247&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for drop.\"\n  },\n  \"dry\": {\n    name: \"Dry\",\n    gif: \"https://th.bing.com/th/id/OIP.A0oQgM0IGtwZjfz1Caj-AgHaE-?w=268&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for dry.\"\n  },\n  \"dryer\": {\n    name: \"Dryer\",\n    gif: \"https://lifeprint.com/asl101/gifs/d/dryer.gif\",\n    description: \"Sign for dryer.\"\n  },\n  \"duck\": {\n    name: \"Duck\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=ZetjiJ3WOhOXrQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fd%2fduck.gif&ehk=STeui62x5lieai0VcyeZkX2t8rILR%2f8GR5F3x2xJ5tw%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for duck.\"\n  },\n  \"ear\": {\n    name: \"Ear\",\n    gif: \"https://lifeprint.com/asl101/signjpegs/e/ears.h3.jpg\",\n    description: \"Sign for ear.\"\n  },\n  \"elephant\": {\n    name: \"Elephant\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/elephant.gif\",\n    description: \"Sign for elephant.\"\n  },\n  \"empty\": {\n    name: \"Empty\",\n    gif: \"https://lifeprint.com/images-signs/empty.gif\",\n    description: \"Sign for empty.\"\n  },\n  \"every\": {\n    name: \"Every\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/every.gif\",\n    description: \"Sign for every.\"\n  },\n  \"eye\": {\n    name: \"Eye\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/eye.gif\",\n    description: \"Sign for eye.\"\n  },\n  \"face\": {\n    name: \"Face\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/face.gif\",\n    description: \"Sign for face.\"\n  },\n  \"fall\": {\n    name: \"Fall\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/fall.gif\",\n    description: \"Sign for fall.\"\n  },\n  \"farm\": {\n    name: \"Farm\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=IO%2brRd7xNmCQBQ&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2ffarm.gif&ehk=aOO01Vk8fbE84nLfNNnOVL3kUdyWJtLaTEcwePgbP9A%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for farm.\"\n  },\n  \"fast\": {\n    name: \"Fast\",\n    gif: \"https://th.bing.com/th/id/OIP.YX_BqT1FjGm8HeM4k4WFAgAAAA?rs=1&pid=ImgDetMain\",\n    description: \"Sign for fast.\"\n  },\n  \"feet\": {\n    name: \"Feet\",\n    gif: \"https://th.bing.com/th/id/OIP.RaYFj5lvSS6NeIna8NtmZQHaE-?w=247&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for feet.\"\n  },\n  \"find\": {\n    name: \"Find\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/f/find-pick.gif\",\n    description: \"Sign for find.\"\n  },\n  \"fine\": {\n    name: \"Fine\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=Qpm%2bw3fHTAWj1A&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2ff%2ffine.gif&ehk=mGMZf4l%2bLZMq4atRomNJSvrSjYgFe%2bRVCm1dYLh5J3I%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for fine.\"\n  },\n  \"finger\": {\n    name: \"Finger\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/finger.gif\",\n    description: \"Sign for finger.\"\n  },\n  \"finish\": {\n    name: \"Finish\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=34j4pW2f3E5TtQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2ff%2ffinish.gif&ehk=xNk24Jbe3t0moSmcmUftmZzCRgHIxsarq3W9E7kGmPM%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for finish.\"\n  },\n  \"fireman\": {\n    name: \"Fireman\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/fireman-c2.gif\",\n    description: \"Sign for fireman.\"\n  },\n  \"first\": {\n    name: \"First\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/first.gif\",\n    description: \"Sign for first.\"\n  },\n  \"fish\": {\n    name: \"Fish\",\n    gif: \"https://th.bing.com/th/id/OIP.Lzhd7lIIa-V4H3faS1d3mQHaHa?rs=1&pid=ImgDetMain\",\n    description: \"Sign for fish.\"\n  },\n  \"flag\": {\n    name: \"Flag\",\n    gif: \"https://th.bing.com/th/id/OIP.3LqQWEnK4TG0lohgQ3G5uAHaE-?w=263&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for flag.\"\n  },\n  \"flower\": {\n    name: \"Flower\",\n    gif: \"https://media.giphy.com/media/3o7TKGkqPpLUdFiFPy/giphy.gif\",\n    description: \"Sign for flower.\"\n  },\n  \"food\": {\n    name: \"Food\",\n    gif: \"https://i.pinimg.com/originals/cc/bb/0c/ccbb0c143db0b51e9947a5966db42fd8.gif\",\n    description: \"Sign for food.\"\n  },\n  \"for\": {\n    name: \"For\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/for.gif\",\n    description: \"Sign for for.\"\n  },\n  \"frenchfries\": {\n    name: \"French Fries\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/f/french-fries.gif\",\n    description: \"Sign for french fries.\"\n  },\n  \"frog\": {\n    name: \"Frog\",\n    gif: \"https://media.giphy.com/media/l0HlKl64lIvTjZ7QA/giphy.gif\",\n    description: \"Sign for frog.\"\n  },\n  \"garbage\": {\n    name: \"Garbage\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=78iU%2fDx85Ut9fA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fg%2fgarbage.gif&ehk=lafY%2f1y5WEEfr04p6Uq4waDP9iV7bJB5r2k3RYGOhWY%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for garbage.\"\n  },\n  \"gift\": {\n    name: \"Gift\",\n    gif: \"https://www.babysignlanguage.com/signs/gift.gif\",\n    description: \"Sign for gift.\"\n  },\n  \"giraffe\": {\n    name: \"Giraffe\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/g/giraffe.gif\",\n    description: \"Sign for giraffe.\"\n  },\n  \"girl\": {\n    name: \"Girl\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=yDsGUPEaDyeSlA&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fgirl.gif&ehk=zdVxVSayRBDn67vVCpMhUH6UmzUQE8vaY7%2bv8jedvs8%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for girl.\"\n  },\n  \"give\": {\n    name: \"Give\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/g/give-x-two-handed.gif\",\n    description: \"Sign for give.\"\n  },\n  \"glasswindow\": {\n    name: \"Glass Window\",\n    gif: \"https://lifeprint.com/asl101/gifs/g/glass.gif\",\n    description: \"Sign for glass window.\"\n  },\n  \"go\": {\n    name: \"Go\",\n    gif: \"https://media.giphy.com/media/l3vRdVMMN9VsW5a0w/giphy.gif\",\n    description: \"Sign for go.\"\n  },\n  \"goose\": {\n    name: \"Goose\",\n    gif: \"https://www.babysignlanguage.com/signs/goose.gif\",\n    description: \"Sign for goose.\"\n  },\n  \"grandma\": {\n    name: \"Grandma\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/g/grandma.gif\",\n    description: \"Sign for grandma.\"\n  },\n  \"grandpa\": {\n    name: \"Grandpa\",\n    gif: \"https://th.bing.com/th/id/OIP.yyLPc-rWg0PMNbrwjeQQngHaE-?w=238&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for grandpa.\"\n  },\n  \"grass\": {\n    name: \"Grass\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=uGZNVzt6tISwHA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs-animated%2fgrass.gif&ehk=VKQd9cvBrglo47EhogWYL9rOiZZsEJ7Yqt%2bgJ8N99yQ%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for grass.\"\n  },\n  \"green\": {\n    name: \"Green\",\n    gif: \"https://i.pinimg.com/originals/cb/7f/75/cb7f757ffb79cb3d1309c9ad785e83a1.gif\",\n    description: \"Sign for green.\"\n  },\n  \"gum\": {\n    name: \"Gum\",\n    gif: \"https://lifeprint.com/asl101/gifs/g/gum.gif\",\n    description: \"Sign for gum.\"\n  },\n  \"hair\": {\n    name: \"Hair\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/h/hair-g-version.gif\",\n    description: \"Sign for hair.\"\n  },\n  \"happy\": {\n    name: \"Happy\",\n    gif: \"https://media0.giphy.com/media/3o7TKFpahYpUp4g0N2/giphy.gif?cid=790b7611bca188a92e2e759876756ef62ba95b7cdd337c77&rid=giphy.gif&ct=g\",\n    description: \"Sign for happy.\"\n  },\n  \"hat\": {\n    name: \"Hat\",\n    gif: \"https://th.bing.com/th/id/OIP.QyFdqn-0ZqUwNfE6jbzKWAHaE-?w=258&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for hat.\"\n  },\n  \"hate\": {\n    name: \"Hate\",\n    gif: \"https://media.giphy.com/media/l0MYPiNw8l2LAPJXW/giphy.gif\",\n    description: \"Sign for hate.\"\n  },\n  \"have\": {\n    name: \"Have\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=q5Ei%2b7oJb7Uzyw&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhave.gif&ehk=H9yIaJxFVejkfHpkhTUipBRv9CW63KBFy6QW5cdbkKw%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for have.\"\n  },\n  \"haveto\": {\n    name: \"Have to\",\n    gif: \"https://lifeprint.com/asl101/gifs/h/have-to.gif\",\n    description: \"Sign for have to.\"\n  },\n  \"head\": {\n    name: \"Head\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=OcbJdRbpEFsWXQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fsignjpegs%2fh%2fhead-1.jpg&ehk=RPBV45fSrLDEWYiZvRuZs2c1JNrL4WzdqLSNMFIF3Rs%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for head.\"\n  },\n  \"hear\": {\n    name: \"Hear\",\n    gif: \"https://www.lifeprint.com/asl101/signjpegs/h/hear.h4.jpg\",\n    description: \"Sign for hear.\"\n  },\n  \"helicopter\": {\n    name: \"Helicopter\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=5uhWxBaByliWA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhelicopter.gif&ehk=mwAyT82RBoeYDe7yaHA1jL3%2f30dUksltmv4dF7YGf%2bU%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for helicopter.\"\n  },\n  \"hello\": {\n    name: \"Hello\",\n    gif: \"https://media.giphy.com/media/3o7TKNKOfKlIhbD3gY/giphy.gif\",\n    description: \"Sign for hello.\"\n  },\n  \"hen\": {\n    name: \"Hen\",\n    gif: \"https://media0.giphy.com/media/26hisADhtILiu1J3W/giphy.gif?cid=790b76112d512b94e1647afb111c8d77f92ae31f37864f2&rid=giphy.gif&ct=g\",\n    description: \"Sign for hen.\"\n  },\n  \"hesheit\": {\n    name: \"He/She/It\",\n    gif: \"https://lifeprint.com/asl101/gifs/h/he-she-it.gif\",\n    description: \"Sign for he/she/it.\"\n  },\n  \"hide\": {\n    name: \"Hide\",\n    gif: \"https://lifeprint.com/asl101/gifs/h/hide.gif\",\n    description: \"Sign for hide.\"\n  },\n  \"high\": {\n    name: \"High\",\n    gif: \"https://lifeprint.com/asl101/gifs/h/high.gif\",\n    description: \"Sign for high.\"\n  },\n  \"home\": {\n    name: \"Home\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=%2bnBd%2foQjxnoPfg&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhome-2.gif&ehk=7yD%2f%2fh6JN1Y4D4BOrUjgKW4Jccy2Y4GVYLf%2fzyk%2b5YY%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for home.\"\n  },\n  \"horse\": {\n    name: \"Horse\",\n    gif: \"https://media.giphy.com/media/l0HlM5HffraiQaHUk/giphy.gif\",\n    description: \"Sign for horse.\"\n  },\n  \"hot\": {\n    name: \"Hot\",\n    gif: \"https://media.giphy.com/media/3o6Zt99k5aDok347bG/giphy.gif\",\n    description: \"Sign for hot.\"\n  },\n  \"hungry\": {\n    name: \"Hungry\",\n    gif: \"https://media.giphy.com/media/l3vR0xkdFEz4tnfTq/giphy.gif\",\n    description: \"Sign for hungry.\"\n  },\n  \"icecream\": {\n    name: \"Ice Cream\",\n    gif: \"https://media.giphy.com/media/3o7TKp6yVibVMhBSLu/giphy.gif\",\n    description: \"Sign for ice cream.\"\n  },\n  \"if\": {\n    name: \"If\",\n    gif: \"https://lifeprint.com/asl101/gifs/i/if.gif\",\n    description: \"Sign for if.\"\n  },\n  \"into\": {\n    name: \"Into\",\n    gif: \"https://lifeprint.com/asl101/gifs/i/into.gif\",\n    description: \"Sign for into.\"\n  },\n  \"jacket\": {\n    name: \"Jacket\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/c/coat.gif\",\n    description: \"Sign for jacket.\"\n  },\n  \"jeans\": {\n    name: \"Jeans\",\n    gif: \"https://lifeprint.com/asl101/gifs/j/jeans.gif\",\n    description: \"Sign for jeans.\"\n  },\n  \"jump\": {\n    name: \"Jump\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/jump.gif\",\n    description: \"Sign for jump.\"\n  },\n  \"kiss\": {\n    name: \"Kiss\",\n    gif: \"https://i.gifer.com/PxGY.gif\",\n    description: \"Sign for kiss.\"\n  },\n  \"kitty\": {\n    name: \"Kitty\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/cat-02.gif\",\n    description: \"Sign for kitty.\"\n  },\n  \"lamp\": {\n    name: \"Lamp\",\n    gif: \"https://lifeprint.com/asl101/gifs/l/lamp.gif\",\n    description: \"Sign for lamp.\"\n  },\n  \"later\": {\n    name: \"Later\",\n    gif: \"https://media3.giphy.com/media/l0MYHTyMzMRcikIxi/giphy.gif?cid=790b761128cd39f9baa06dbeb4e099d13e3516763d5f0952&rid=giphy.gif&ct=g\",\n    description: \"Sign for later.\"\n  },\n  \"like\": {\n    name: \"Like\",\n    gif: \"https://lifeprint.com/asl101/gifs/l/like.gif\",\n    description: \"Sign for like.\"\n  },\n  \"lion\": {\n    name: \"Lion\",\n    gif: \"https://th.bing.com/th/id/OIP.8sDkvbXdMKVmCNlDV79WpAHaE-?w=247&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for lion.\"\n  },\n  \"lips\": {\n    name: \"Lips\",\n    gif: \"https://lifeprint.com/asl101/gifs/l/lips.gif\",\n    description: \"Sign for lips.\"\n  },\n  \"listen\": {\n    name: \"Listen\",\n    gif: \"https://th.bing.com/th/id/OIP.VjsXAad6abRwkCla83kbZQHaEc?w=284&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for listen.\"\n  },\n  \"look\": {\n    name: \"Look\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=pYhzip7LqNs7qw&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fl%2flook-at-1.gif&ehk=rFJ7dBrMGFDK0nHLzrOPAzROVE7yqyDEcb%2btLqKqYOI%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for look.\"\n  },\n  \"loud\": {\n    name: \"Loud\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/loud.gif\",\n    description: \"Sign for loud.\"\n  },\n  \"mad\": {\n    name: \"Mad\",\n    gif: \"https://lifeprint.com/asl101/gifs/m/mad.gif\",\n    description: \"Sign for mad.\"\n  },\n  \"make\": {\n    name: \"Make\",\n    gif: \"https://th.bing.com/th/id/OIP.CPz7T2bH107Tu-DBnHvatAHaEc?w=313&h=188&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for make.\"\n  },\n  \"man\": {\n    name: \"Man\",\n    gif: \"https://lifeprint.com/asl101/gifs/m/man.gif\",\n    description: \"Sign for man.\"\n  },\n  \"many\": {\n    name: \"Many\",\n    gif: \"https://lifeprint.com/asl101/gifs/m/many.gif\",\n    description: \"Sign for many.\"\n  },\n  \"milk\": {\n    name: \"Milk\",\n    gif: \"https://lifeprint.com/asl101/gifs/m/milk.gif\",\n    description: \"Sign for milk.\"\n  },\n  \"minemy\": {\n    name: \"Mine/My\",\n    gif: \"https://th.bing.com/th/id/OIP.VBkNZsR_pK7KUoCNiWYMdgHaEc?w=288&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for mine/my.\"\n  },\n  \"mitten\": {\n    name: \"Mitten\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/mittens.gif\",\n    description: \"Sign for mitten.\"\n  },\n  \"mom\": {\n    name: \"Mom\",\n    gif: \"https://lifeprint.com/asl101/gifs/m/mom.gif\",\n    description: \"Sign for mom.\"\n  },\n  \"moon\": {\n    name: \"Moon\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=XbVhBJtkANrG9g&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fm%2fmoon.gif&ehk=YSDvFeUSTa9X1BEJhDjdnLC4c7zWn8z7Hj%2fMkkLUyFE%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for moon.\"\n  },\n  \"morning\": {\n    name: \"Morning\",\n    gif: \"https://media0.giphy.com/media/3o6ZtrcJ9GCXGGw0ww/source.gif\",\n    description: \"Sign for morning.\"\n  },\n  \"mouse\": {\n    name: \"Mouse\",\n    gif: \"https://lifeprint.com/asl101/gifs/m/mouse.gif\",\n    description: \"Sign for mouse.\"\n  },\n  \"mouth\": {\n    name: \"Mouth\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/mouth.gif\",\n    description: \"Sign for mouth.\"\n  },\n  \"nap\": {\n    name: \"Nap\",\n    gif: \"https://lifeprint.com/asl101/gifs/n/nap.gif\",\n    description: \"Sign for nap.\"\n  },\n  \"napkin\": {\n    name: \"Napkin\",\n    gif: \"https://lifeprint.com/asl101/gifs/n/napkin.gif\",\n    description: \"Sign for napkin.\"\n  },\n  \"night\": {\n    name: \"Night\",\n    gif: \"https://lifeprint.com/asl101/gifs/n/night.gif\",\n    description: \"Sign for night.\"\n  },\n  \"no\": {\n    name: \"No\",\n    gif: \"https://lifeprint.com/asl101/gifs/n/no-2-movement.gif\",\n    description: \"Sign for no.\"\n  },\n  \"noisy\": {\n    name: \"Noisy\",\n    gif: \"https://lifeprint.com/asl101/gifs/n/noisy.gif\",\n    description: \"Sign for noisy.\"\n  },\n  \"nose\": {\n    name: \"Nose\",\n    gif: \"https://lifeprint.com/asl101/signjpegs/n/nose.h1.jpg\",\n    description: \"Sign for nose.\"\n  },\n  \"not\": {\n    name: \"Not\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=6%2bbZ2jRA%2famQ4Q&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fn%2fnot-negative.gif&ehk=%2bppuO9P0%2fpdzrrdNO4FXpxdIGs8jgY%2fj%2b1ZCwdbDWO4%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for not.\"\n  },\n  \"now\": {\n    name: \"Now\",\n    gif: \"https://lifeprint.com/asl101/gifs/n/now.gif\",\n    description: \"Sign for now.\"\n  },\n  \"nuts\": {\n    name: \"Nuts\",\n    gif: \"https://th.bing.com/th/id/OIP.wRnQjn9j2vfFfzAnRR205QHaE-?w=276&h=185&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for nuts.\"\n  },\n  \"old\": {\n    name: \"Old\",\n    gif: \"https://lifeprint.com/asl101/gifs/o/old.gif\",\n    description: \"Sign for old.\"\n  },\n  \"on\": {\n    name: \"On\",\n    gif: \"https://lifeprint.com/asl101/gifs/o/on-onto.gif\",\n    description: \"Sign for on.\"\n  },\n  \"open\": {\n    name: \"Open\",\n    gif: \"https://th.bing.com/th/id/OIP.BeMiGXQFuYk_6ZrgG3iqzQHaE-?w=264&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for open.\"\n  },\n  \"orange\": {\n    name: \"Orange\",\n    gif: \"https://lifeprint.com/asl101/gifs/o/orange.gif\",\n    description: \"Sign for orange.\"\n  },\n  \"outside\": {\n    name: \"Outside\",\n    gif: \"https://lifeprint.com/asl101/gifs/o/outside.gif\",\n    description: \"Sign for outside.\"\n  },\n  \"owie\": {\n    name: \"Owie\",\n    gif: \"https://lifeprint.com/asl101/gifs/o/owie.gif\",\n    description: \"Sign for owie.\"\n  },\n  \"owl\": {\n    name: \"Owl\",\n    gif: \"https://lifeprint.com/asl101/gifs/o/owl.gif\",\n    description: \"Sign for owl.\"\n  },\n  \"pajamas\": {\n    name: \"Pajamas\",\n    gif: \"https://lifeprint.com/asl101/gifs/p/pajamas.gif\",\n    description: \"Sign for pajamas.\"\n  },\n  \"pen\": {\n    name: \"Pen\",\n    gif: \"https://lifeprint.com/asl101/gifs/p/pen.gif\",\n    description: \"Sign for pen.\"\n  },\n  \"pencil\": {\n    name: \"Pencil\",\n    gif: \"https://lifeprint.com/asl101/gifs/p/pencil-2.gif\",\n    description: \"Sign for pencil.\"\n  },\n  \"penny\": {\n    name: \"Penny\",\n    gif: \"https://lifeprint.com/asl101/gifs/p/penny.gif\",\n    description: \"Sign for penny.\"\n  },\n  \"person\": {\n    name: \"Person\",\n    gif: \"https://lifeprint.com/asl101/gifs/p/person.gif\",\n    description: \"Sign for person.\"\n  },\n  \"pig\": {\n    name: \"Pig\",\n    gif: \"https://lifeprint.com/asl101/gifs/p/pig.gif\",\n    description: \"Sign for pig.\"\n  },\n  \"pizza\": {\n    name: \"Pizza\",\n    gif: \"https://lifeprint.com/asl101/gifs/p/pizza.gif\",\n    description: \"Sign for pizza.\"\n  },\n  \"please\": {\n    name: \"Please\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/pleasecloseup.gif\",\n    description: \"Sign for please.\"\n  },\n  \"police\": {\n    name: \"Police\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=icjjfUg15cqgLw&pid=ImgRaw&r=0\",\n    description: \"Sign for police.\"\n  },\n  \"pool\": {\n    name: \"Pool\",\n    gif: \"https://th.bing.com/th/id/OIP.dhcMKyW2psDcA5uwsRaRagHaEc?w=276&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for pool.\"\n  },\n  \"potty\": {\n    name: \"Potty\",\n    gif: \"https://th.bing.com/th/id/OIP.YcNMUjCg6f95xdgN5rnenwHaE-?w=264&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for potty.\"\n  },\n  \"pretend\": {\n    name: \"Pretend\",\n    gif: \"https://lifeprint.com/asl101/gifs/p/pretend.gif\",\n    description: \"Sign for pretend.\"\n  },\n  \"pretty\": {\n    name: \"Pretty\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/beautiful.gif\",\n    description: \"Sign for pretty.\"\n  },\n  \"puppy\": {\n    name: \"Puppy\",\n    gif: \"https://lifeprint.com/asl101/gifs/p/puppy.gif\",\n    description: \"Sign for puppy.\"\n  },\n  \"puzzle\": {\n    name: \"Puzzle\",\n    gif: \"https://res.cloudinary.com/spiralyze/image/upload/f_auto,w_auto/BabySignLanguage/DictionaryPages/puzzle.svg\",\n    description: \"Sign for puzzle.\"\n  },\n  \"quiet\": {\n    name: \"Quiet\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/quiet-03.gif\",\n    description: \"Sign for quiet.\"\n  },\n  \"radio\": {\n    name: \"Radio\",\n    gif: \"https://i.pinimg.com/originals/6d/5e/5e/6d5e5e2f78f80e9006293df853a2ba3b.gif\",\n    description: \"Sign for radio.\"\n  },\n  \"rain\": {\n    name: \"Rain\",\n    gif: \"https://lifeprint.com/asl101/gifs/r/rain.gif\",\n    description: \"Sign for rain.\"\n  },\n  \"read\": {\n    name: \"Read\",\n    gif: \"https://lifeprint.com/asl101/gifs/r/read.gif\",\n    description: \"Sign for read.\"\n  },\n  \"red\": {\n    name: \"Red\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/red.gif\",\n    description: \"Sign for red.\"\n  },\n  \"refrigerator\": {\n    name: \"Refrigerator\",\n    gif: \"https://lifeprint.com/asl101/gifs/r/refrigerator-r-e-f.gif\",\n    description: \"Sign for refrigerator.\"\n  },\n  \"ride\": {\n    name: \"Ride\",\n    gif: \"https://lifeprint.com/asl101/gifs/r/ride.gif\",\n    description: \"Sign for ride.\"\n  },\n  \"room\": {\n    name: \"Room\",\n    gif: \"https://lifeprint.com/asl101/gifs/r/room-box.gif\",\n    description: \"Sign for room.\"\n  },\n  \"sad\": {\n    name: \"Sad\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/sad.gif\",\n    description: \"Sign for sad.\"\n  },\n  \"same\": {\n    name: \"Same\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/same-similar.gif\",\n    description: \"Sign for same.\"\n  },\n  \"say\": {\n    name: \"Say\",\n    gif: \"https://asl.signlanguage.io/words/say/say-in-asl-a0a5e00000a44k0.jpg\",\n    description: \"Sign for say.\"\n  },\n  \"scissors\": {\n    name: \"Scissors\",\n    gif: \"https://i.makeagif.com/media/4-17-2023/pl4M4F.gif\",\n    description: \"Sign for scissors.\"\n  },\n  \"see\": {\n    name: \"See\",\n    gif: \"https://lifeprint.com/asl101/gifs/l/look-at-2.gif\",\n    description: \"Sign for see.\"\n  },\n  \"shhh\": {\n    name: \"Shhh\",\n    gif: \"https://lifeprint.com/asl101/signjpegs/s/shhh.jpg\",\n    description: \"Sign for shhh.\"\n  },\n  \"shirt\": {\n    name: \"Shirt\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/shirt-volunteer.gif\",\n    description: \"Sign for shirt.\"\n  },\n  \"shoe\": {\n    name: \"Shoe\",\n    gif: \"https://media.giphy.com/media/3o7TKC4StpZKa6d2y4/giphy.gif\",\n    description: \"Sign for shoe.\"\n  },\n  \"shower\": {\n    name: \"Shower\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/shower.gif\",\n    description: \"Sign for shower.\"\n  },\n  \"sick\": {\n    name: \"Sick\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/sick.gif\",\n    description: \"Sign for sick.\"\n  },\n  \"sleep\": {\n    name: \"Sleep\",\n    gif: \"https://media4.giphy.com/media/3o7TKnRuBdakLslcaI/200.gif?cid=790b76110d8f185a9713f36dd65a0df801576e01b403c95c&rid=200.gif&ct=g\",\n    description: \"Sign for sleep.\"\n  },\n  \"sleepy\": {\n    name: \"Sleepy\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=zdWvzvABcDHTdw&riu=http%3a%2f%2fwww.lifeprint.com%2fasl101%2fgifs-animated%2fsleepy.gif&ehk=zLqDFJMAs2nqG02RbbR6mEMvux4h85JGzls4uwgrePQ%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for sleepy.\"\n  },\n  \"smile\": {\n    name: \"Smile\",\n    gif: \"https://th.bing.com/th/id/OIP.dpce-bMAh-1jorUrPQFW4AHaE-?w=263&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for smile.\"\n  },\n  \"snack\": {\n    name: \"Snack\",\n    gif: \"https://media.giphy.com/media/26ybw1E1GTKzLuKDS/giphy.gif\",\n    description: \"Sign for snack.\"\n  },\n  \"snow\": {\n    name: \"Snow\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/snow.gif\",\n    description: \"Sign for snow.\"\n  },\n  \"stairs\": {\n    name: \"Stairs\",\n    gif: \"https://th.bing.com/th/id/OIP.8BtYhPXXDQHRqodMyyy3HgHaEc?w=288&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for stairs.\"\n  },\n  \"stay\": {\n    name: \"Stay\",\n    gif: \"https://i.pinimg.com/originals/f5/29/8e/f5298eaa46b91cd6de2a32bd76aadffc.gif\",\n    description: \"Sign for stay.\"\n  },\n  \"sticky\": {\n    name: \"Sticky\",\n    gif: \"https://th.bing.com/th/id/OIP.fffIgrX_DBAjxGMkskvTvQHaE-?w=240&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for sticky.\"\n  },\n  \"store\": {\n    name: \"Store\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=x7oUPJGckc7QDg&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fstore.gif&ehk=P7beooAyFUst%2bbVtqIqINeQGP0%2bIUlNSPXc1Du5zWfQ%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for store.\"\n  },\n  \"story\": {\n    name: \"Story\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/story.gif\",\n    description: \"Sign for story.\"\n  },\n  \"stuck\": {\n    name: \"Stuck\",\n    gif: \"https://lifeprint.com/asl101/signjpegs/s/stuck.2.jpg\",\n    description: \"Sign for stuck.\"\n  },\n  \"sun\": {\n    name: \"Sun\",\n    gif: \"https://media.giphy.com/media/3o6Zt7merN2zxEtNRK/giphy.gif\",\n    description: \"Sign for sun.\"\n  },\n  \"table\": {\n    name: \"Table\",\n    gif: \"https://lifeprint.com/asl101/gifs/t/table.gif\",\n    description: \"Sign for table.\"\n  },\n  \"talk\": {\n    name: \"Talk\",\n    gif: \"https://lifeprint.com/asl101/gifs/t/talk.gif\",\n    description: \"Sign for talk.\"\n  },\n  \"taste\": {\n    name: \"Taste\",\n    gif: \"https://lifeprint.com/asl101/gifs/t/taste.gif\",\n    description: \"Sign for taste.\"\n  },\n  \"thankyou\": {\n    name: \"Thank You\",\n    gif: \"https://lifeprint.com/asl101/gifs/t/thank-you.gif\",\n    description: \"Sign for thank you.\"\n  },\n  \"that\": {\n    name: \"That\",\n    gif: \"https://i.ytimg.com/vi/81Wr75AFDnQ/maxresdefault.jpg\",\n    description: \"Sign for that.\"\n  },\n  \"there\": {\n    name: \"There\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/there.gif\",\n    description: \"Sign for there.\"\n  },\n  \"think\": {\n    name: \"Think\",\n    gif: \"https://lifeprint.com/asl101/gifs/t/think.gif\",\n    description: \"Sign for think.\"\n  },\n  \"thirsty\": {\n    name: \"Thirsty\",\n    gif: \"https://media.giphy.com/media/l3vR0sYheBulL1P7W/giphy.gif\",\n    description: \"Sign for thirsty.\"\n  },\n  \"tiger\": {\n    name: \"Tiger\",\n    gif: \"https://lifeprint.com/asl101/gifs/t/tiger.gif\",\n    description: \"Sign for tiger.\"\n  },\n  \"time\": {\n    name: \"Time\",\n    gif: \"https://lifeprint.com/asl101/gifs/t/time-1.gif\",\n    description: \"Sign for time.\"\n  },\n  \"tomorrow\": {\n    name: \"Tomorrow\",\n    gif: \"https://lifeprint.com/asl101/gifs/t/tomorrow.gif\",\n    description: \"Sign for tomorrow.\"\n  },\n  \"tongue\": {\n    name: \"Tongue\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=ZJJ2Ixdj0l0b5A&riu=http%3a%2f%2fwww.aslsearch.com%2fsigns%2fimages%2ftongue.jpg&ehk=MxZVUjfqPa3klIauPGpReg%2fYgnJUyIjlxOOvCYYG0hc%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for tongue.\"\n  },\n  \"tooth\": {\n    name: \"Tooth\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=ZF%2fsFUXvt5czGA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fsignjpegs%2ft%2fteeth1.jpg&ehk=vI5eDlD4HZWXhK1PQOQz4nA5e6oguHgeXqDo%2fcdcWg4%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for tooth.\"\n  },\n  \"toothbrush\": {\n    name: \"Toothbrush\",\n    gif: \"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia.giphy.com%2fmedia%2fl3vR0Rq2HVL2KHLUI%2fgiphy.gif&ehk=eC0Sq9sHjrrOrkyJvOogQbXVkTOL5OPCeyVymejL0RU%3d\",\n    description: \"Sign for toothbrush.\"\n  },\n  \"touch\": {\n    name: \"Touch\",\n    gif: \"https://th.bing.com/th/id/OIP.imGRfqjCtcHhof6Lc_0QJQHaE-?w=230&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for touch.\"\n  },\n  \"toy\": {\n    name: \"Toy\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/play-02.gif\",\n    description: \"Sign for toy.\"\n  },\n  \"tree\": {\n    name: \"Tree\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/tree.gif\",\n    description: \"Sign for tree.\"\n  },\n  \"uncle\": {\n    name: \"Uncle\",\n    gif: \"https://lifeprint.com/asl101/gifs/u/uncle.gif\",\n    description: \"Sign for uncle.\"\n  },\n  \"underwear\": {\n    name: \"Underwear\",\n    gif: \"https://th.bing.com/th/id/OIP.c8g9T_lOhbZWRvKAA12J8wHaEO?pid=ImgDet&w=310&h=177&rs=1\",\n    description: \"Sign for underwear.\"\n  },\n  \"up\": {\n    name: \"Up\",\n    gif: \"https://www.babysignlanguage.com/signs/up.gif\",\n    description: \"Sign for up.\"\n  },\n  \"vacuum\": {\n    name: \"Vacuum\",\n    gif: \"https://www.babysignlanguage.com/signs/vacuum.gif\",\n    description: \"Sign for vacuum.\"\n  },\n  \"wait\": {\n    name: \"Wait\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/wait.gif\",\n    description: \"Sign for wait.\"\n  },\n  \"wake\": {\n    name: \"Wake\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/wake-up.gif\",\n    description: \"Sign for wake.\"\n  },\n  \"water\": {\n    name: \"Water\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/water-2.gif\",\n    description: \"Sign for water.\"\n  },\n  \"wet\": {\n    name: \"Wet\",\n    gif: \"https://www.babysignlanguage.com/signs/wet.gif\",\n    description: \"Sign for wet.\"\n  },\n  \"weus\": {\n    name: \"We/Us\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/we-us.gif\",\n    description: \"Sign for we/us.\"\n  },\n  \"where\": {\n    name: \"Where\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/where.gif\",\n    description: \"Sign for where.\"\n  },\n  \"white\": {\n    name: \"White\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/white.gif\",\n    description: \"Sign for white.\"\n  },\n  \"who\": {\n    name: \"Who\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/who.gif\",\n    description: \"Sign for who.\"\n  },\n  \"why\": {\n    name: \"Why\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/why.gif\",\n    description: \"Sign for why.\"\n  },\n  \"will\": {\n    name: \"Will\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/future.gif\",\n    description: \"Sign for will.\"\n  },\n  \"wolf\": {\n    name: \"Wolf\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/wolf-side-view.gif\",\n    description: \"Sign for wolf.\"\n  },\n  \"yellow\": {\n    name: \"Yellow\",\n    gif: \"https://lifeprint.com/asl101/gifs/y/yellow.gif\",\n    description: \"Sign for yellow.\"\n  },\n  \"yes\": {\n    name: \"Yes\",\n    gif: \"https://media.tenor.com/oYIirlyIih0AAAAC/yes-asl.gif\",\n    description: \"Sign for yes.\"\n  },\n  \"yesterday\": {\n    name: \"Yesterday\",\n    gif: \"https://lifeprint.com/asl101/gifs/y/yesterday.gif\",\n    description: \"Sign for yesterday.\"\n  },\n  \"yourself\": {\n    name: \"Yourself\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/s/self-myself.gif\",\n    description: \"Sign for yourself.\"\n  },\n  \"yucky\": {\n    name: \"Yucky\",\n    gif: \"https://i.pinimg.com/originals/7f/66/7f/7f667f7eeb92c994829dcaf52c5bcf2d.gif\",\n    description: \"Sign for yucky.\"\n  },\n  \"zebra\": {\n    name: \"Zebra\",\n    gif: \"https://lifeprint.com/asl101/gifs/z/zebra-stripes-two-hands.gif\",\n    description: \"Sign for zebra.\"\n  },\n  \"zipper\": {\n    name: \"Zipper\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=qPRTVGd2SzUBxw&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fzipper.gif&ehk=IGx68sSokNwU21zu3Z2D%2blmeehKYxpSNhX2VnrvQqYE%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for zipper.\"\n  }\n};\nconst TrainingPage = ({\n  onBackToHome\n}) => {\n  _s();\n  const [currentSign, setCurrentSign] = useState('hello');\n  const [status, setStatus] = useState('');\n  const [isCapturing, setIsCapturing] = useState(false);\n  // Auto-recording is always on, no need for mode toggle\n  // eslint-disable-next-line no-unused-vars\n  const [recordedVideos, setRecordedVideos] = useState([]);\n  const webcamRef = useRef(null);\n  const autoRecordTimeoutRef = useRef(null);\n  const matchCountRef = useRef(0);\n\n  // Use sign detection hook\n  const {\n    isConnected,\n    prediction,\n    isAIRecording,\n    recordingStatus,\n    signMatched,\n    targetSign,\n    startRecording: startAIRecording,\n    stopRecording: stopAIRecording,\n    startFrameCapture,\n    retryConnection\n  } = useSignDetection();\n  const handleSignChange = useCallback(event => {\n    setCurrentSign(event.target.value);\n  }, []);\n  const startDetection = useCallback(() => {\n    if (!webcamRef.current) {\n      setStatus('Camera not available');\n      return;\n    }\n    setIsCapturing(true);\n    startFrameCapture(webcamRef, 100); // Send frame every 100ms\n    setStatus('AI detection started');\n  }, [startFrameCapture]);\n  const startManualRecording = useCallback(() => {\n    if (!isConnected) {\n      setStatus('AI backend not connected');\n      return;\n    }\n    if (!webcamRef.current) {\n      setStatus('Camera not available');\n      return;\n    }\n    if (isAIRecording) {\n      setStatus('Already recording...');\n      return;\n    }\n\n    // Start manual 3-second recording\n    setStatus(`Manual recording ${signLanguageData[currentSign].name} for 3 seconds...`);\n    startAIRecording(signLanguageData[currentSign].name);\n\n    // Auto-stop after 3 seconds\n    autoRecordTimeoutRef.current = setTimeout(() => {\n      stopAIRecording();\n      setStatus(`Manual recording complete! ${signLanguageData[currentSign].name} saved to recordings folder`);\n    }, 3000);\n\n    // Also start frame capture if not already started\n    if (!isCapturing) {\n      startDetection();\n    }\n  }, [currentSign, isConnected, isCapturing, startDetection, isAIRecording, startAIRecording, stopAIRecording]);\n  const stopManualRecording = useCallback(() => {\n    // Stop current recording\n    if (isAIRecording) {\n      stopAIRecording();\n    }\n    matchCountRef.current = 0;\n    if (autoRecordTimeoutRef.current) {\n      clearTimeout(autoRecordTimeoutRef.current);\n    }\n    setStatus('Manual recording stopped');\n  }, [stopAIRecording, isAIRecording]);\n  const downloadRecording = video => {\n    const a = document.createElement('a');\n    a.href = video.url;\n    a.download = `sign_${video.sign}_${video.timestamp}.webm`;\n    a.click();\n  };\n\n  // Auto-start detection when connected\n  useEffect(() => {\n    if (isConnected && webcamRef.current && !isCapturing) {\n      startDetection();\n    }\n  }, [isConnected, startDetection, isCapturing]);\n\n  // Always-on auto-recording logic - records when confidence >= 50%\n  useEffect(() => {\n    if (!prediction || !isConnected) {\n      matchCountRef.current = 0;\n      return;\n    }\n    const predictedSign = prediction.sign.toLowerCase();\n    const targetSignLower = signLanguageData[currentSign].name.toLowerCase();\n    const confidence = prediction.confidence;\n\n    // Auto-record when sign matches with >= 50% confidence\n    if (predictedSign === targetSignLower && confidence >= 0.5) {\n      matchCountRef.current += 1;\n\n      // Start recording after 2 consecutive matches to avoid false positives\n      if (matchCountRef.current >= 2 && !isAIRecording) {\n        setStatus(`Auto-recording ${signLanguageData[currentSign].name}... (${Math.round(confidence * 100)}% confidence)`);\n        startAIRecording(signLanguageData[currentSign].name);\n\n        // Auto-stop recording after 3 seconds\n        autoRecordTimeoutRef.current = setTimeout(() => {\n          stopAIRecording();\n          setStatus(`Auto-recording complete! ${signLanguageData[currentSign].name} saved to recordings folder`);\n          matchCountRef.current = 0;\n        }, 3000);\n      }\n    } else {\n      // Reset match count if sign doesn't match or confidence is too low\n      matchCountRef.current = 0;\n    }\n    return () => {\n      if (autoRecordTimeoutRef.current) {\n        clearTimeout(autoRecordTimeoutRef.current);\n      }\n    };\n  }, [prediction, currentSign, isAIRecording, startAIRecording, stopAIRecording, isConnected]);\n  return /*#__PURE__*/_jsxDEV(TrainingContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Navigation, {\n      children: /*#__PURE__*/_jsxDEV(NavContainer, {\n        children: [/*#__PURE__*/_jsxDEV(Logo, {\n          children: [/*#__PURE__*/_jsxDEV(LogoIcon, {\n            children: /*#__PURE__*/_jsxDEV(Brain, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1128,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1127,\n            columnNumber: 13\n          }, this), \"ASL Neural\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1126,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(BackButton, {\n          onClick: onBackToHome,\n          children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1133,\n            columnNumber: 13\n          }, this), \"Back to Home\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1132,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1125,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1124,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginBottom: 'var(--space-12)'\n        },\n        children: /*#__PURE__*/_jsxDEV(StatusBadge, {\n          children: [/*#__PURE__*/_jsxDEV(Eye, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1142,\n            columnNumber: 13\n          }, this), \"Neural Vision Active\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1141,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1140,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PageTitle, {\n        children: \"AI Training Session\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PageSubtitle, {\n        children: \"Experience real-time neural network analysis as our AI learns from your sign language practice\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TopControlsSection, {\n        children: [/*#__PURE__*/_jsxDEV(ControlButton, {\n          variant: \"primary\",\n          compact: true,\n          onClick: isAIRecording ? stopManualRecording : startManualRecording,\n          children: isAIRecording ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Square, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1160,\n              columnNumber: 17\n            }, this), \"Stop Recording\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Play, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1165,\n              columnNumber: 17\n            }, this), \"Record 3 Seconds\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1153,\n          columnNumber: 11\n        }, this), !isConnected && /*#__PURE__*/_jsxDEV(ControlButton, {\n          variant: \"retry\",\n          compact: true,\n          onClick: retryConnection,\n          children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1177,\n            columnNumber: 15\n          }, this), \"Retry Connection\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1172,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1152,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TrainingGrid, {\n        children: [/*#__PURE__*/_jsxDEV(CameraSection, {\n          children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n            children: [/*#__PURE__*/_jsxDEV(SectionIcon, {\n              children: /*#__PURE__*/_jsxDEV(Camera, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1187,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1186,\n              columnNumber: 15\n            }, this), \"Neural Vision Feed\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ConnectionStatus, {\n            connected: isConnected,\n            children: [isConnected ? /*#__PURE__*/_jsxDEV(Wifi, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1193,\n              columnNumber: 30\n            }, this) : /*#__PURE__*/_jsxDEV(WifiOff, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1193,\n              columnNumber: 51\n            }, this), isConnected ? 'AI Connected' : 'AI Disconnected']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1192,\n            columnNumber: 13\n          }, this), prediction && /*#__PURE__*/_jsxDEV(PredictionDisplay, {\n            matched: signMatched,\n            isStale: prediction.isStale,\n            children: [/*#__PURE__*/_jsxDEV(PredictionText, {\n              matched: signMatched,\n              isStale: prediction.isStale,\n              children: [\"Detected: \", prediction.sign, prediction.isStale && ' (previous)']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1199,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ConfidenceBar, {\n              children: /*#__PURE__*/_jsxDEV(ConfidenceFill, {\n                confidence: prediction.confidence\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1204,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1203,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                marginTop: '8px',\n                color: 'var(--text-secondary)'\n              },\n              children: [\"Confidence: \", Math.round(prediction.confidence * 100), \"%\", signMatched && targetSign && /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: 'var(--success-600)',\n                  marginLeft: '8px'\n                },\n                children: \"\\u2713 Match! Recording...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1209,\n                columnNumber: 21\n              }, this), !isAIRecording && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: 'var(--primary-600)',\n                  marginTop: '4px'\n                },\n                children: [\"\\uD83C\\uDFAF Auto-recording active: Perform \\\"\", signLanguageData[currentSign].name, \"\\\" sign (\\u226550% confidence)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1214,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1206,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1198,\n            columnNumber: 15\n          }, this), !prediction && /*#__PURE__*/_jsxDEV(PredictionDisplay, {\n            children: [/*#__PURE__*/_jsxDEV(PredictionText, {\n              children: [\"\\uD83C\\uDFAF Ready to detect \\\"\", signLanguageData[currentSign].name, \"\\\"\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1224,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                color: 'var(--text-secondary)'\n              },\n              children: \"Auto-recording is active. Perform the sign with \\u226550% confidence to trigger recording.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1227,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1223,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(WebcamContainer, {\n            children: [/*#__PURE__*/_jsxDEV(StyledWebcam, {\n              ref: webcamRef,\n              audio: false,\n              screenshotFormat: \"image/jpeg\",\n              videoConstraints: {\n                width: 640,\n                height: 480,\n                facingMode: \"user\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1233,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(RecordingOverlay, {\n              isRecording: isAIRecording,\n              children: isAIRecording ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '8px',\n                    height: '8px',\n                    borderRadius: '50%',\n                    backgroundColor: 'white',\n                    marginRight: '4px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1246,\n                  columnNumber: 21\n                }, this), \"Recording\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Eye, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1257,\n                  columnNumber: 21\n                }, this), \"Ready\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1243,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1232,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SignSection, {\n          children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n            children: [/*#__PURE__*/_jsxDEV(SectionIcon, {\n              children: /*#__PURE__*/_jsxDEV(Target, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1268,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1267,\n              columnNumber: 15\n            }, this), \"Select a Sign\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1266,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SignSelector, {\n            value: currentSign,\n            onChange: handleSignChange,\n            disabled: isAIRecording,\n            children: Object.keys(signLanguageData).map(signKey => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: signKey,\n              children: signLanguageData[signKey].name\n            }, signKey, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1278,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1272,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SignDisplay, {\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: signLanguageData[currentSign].gif,\n              alt: signLanguageData[currentSign].name,\n              onError: e => {\n                e.target.style.display = 'none';\n                e.target.nextSibling.style.display = 'flex';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1284,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'none',\n                fontSize: '3rem'\n              },\n              children: \"\\uD83D\\uDCF7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1292,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1283,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SignName, {\n            children: signLanguageData[currentSign].name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1296,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SignDescription, {\n            children: signLanguageData[currentSign].description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1297,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1265,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1183,\n        columnNumber: 9\n      }, this), (status || recordingStatus) && /*#__PURE__*/_jsxDEV(StatusMessage, {\n        type: (status || recordingStatus).includes('error') ? 'error' : (status || recordingStatus).includes('success') ? 'success' : 'info',\n        children: recordingStatus || status\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1306,\n        columnNumber: 11\n      }, this), recordedVideos.length > 0 && /*#__PURE__*/_jsxDEV(RecordingsSection, {\n        children: [/*#__PURE__*/_jsxDEV(RecordingsTitle, {\n          children: \"Your Practice Recordings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1313,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(RecordingsGrid, {\n          children: recordedVideos.map(video => /*#__PURE__*/_jsxDEV(RecordingCard, {\n            children: [/*#__PURE__*/_jsxDEV(RecordingTitle, {\n              children: video.sign\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1317,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(RecordingTime, {\n              children: new Date(video.timestamp).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1318,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(DownloadButton, {\n              onClick: () => downloadRecording(video),\n              children: [/*#__PURE__*/_jsxDEV(Download, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1322,\n                columnNumber: 21\n              }, this), \"Download\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1321,\n              columnNumber: 19\n            }, this)]\n          }, video.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1316,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1314,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1312,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1139,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1123,\n    columnNumber: 5\n  }, this);\n};\n_s(TrainingPage, \"F5b+bytE3bZWeXlz8l7rkfUYaTY=\", false, function () {\n  return [useSignDetection];\n});\n_c36 = TrainingPage;\nexport default TrainingPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32, _c33, _c34, _c35, _c36;\n$RefreshReg$(_c, \"TrainingContainer\");\n$RefreshReg$(_c2, \"Navigation\");\n$RefreshReg$(_c3, \"NavContainer\");\n$RefreshReg$(_c4, \"Logo\");\n$RefreshReg$(_c5, \"LogoIcon\");\n$RefreshReg$(_c6, \"BackButton\");\n$RefreshReg$(_c7, \"PageTitle\");\n$RefreshReg$(_c8, \"PageSubtitle\");\n$RefreshReg$(_c9, \"StatusBadge\");\n$RefreshReg$(_c0, \"MainContent\");\n$RefreshReg$(_c1, \"TrainingGrid\");\n$RefreshReg$(_c10, \"CameraSection\");\n$RefreshReg$(_c11, \"SectionTitle\");\n$RefreshReg$(_c12, \"SectionIcon\");\n$RefreshReg$(_c13, \"WebcamContainer\");\n$RefreshReg$(_c14, \"StyledWebcam\");\n$RefreshReg$(_c15, \"RecordingOverlay\");\n$RefreshReg$(_c16, \"SignSection\");\n$RefreshReg$(_c17, \"SignSelector\");\n$RefreshReg$(_c18, \"SignDisplay\");\n$RefreshReg$(_c19, \"SignName\");\n$RefreshReg$(_c20, \"SignDescription\");\n$RefreshReg$(_c21, \"TopControlsSection\");\n$RefreshReg$(_c22, \"ControlButton\");\n$RefreshReg$(_c23, \"StatusMessage\");\n$RefreshReg$(_c24, \"RecordingsSection\");\n$RefreshReg$(_c25, \"RecordingsTitle\");\n$RefreshReg$(_c26, \"RecordingsGrid\");\n$RefreshReg$(_c27, \"RecordingCard\");\n$RefreshReg$(_c28, \"RecordingTitle\");\n$RefreshReg$(_c29, \"RecordingTime\");\n$RefreshReg$(_c30, \"DownloadButton\");\n$RefreshReg$(_c31, \"PredictionDisplay\");\n$RefreshReg$(_c32, \"PredictionText\");\n$RefreshReg$(_c33, \"ConfidenceBar\");\n$RefreshReg$(_c34, \"ConfidenceFill\");\n$RefreshReg$(_c35, \"ConnectionStatus\");\n$RefreshReg$(_c36, \"TrainingPage\");", "map": {"version": 3, "names": ["useState", "useRef", "useCallback", "useEffect", "styled", "Webcam", "Brain", "Camera", "ArrowLeft", "Play", "Square", "Download", "Eye", "Target", "Wifi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RefreshCw", "useSignDetection", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TrainingContainer", "div", "_c", "Navigation", "nav", "_c2", "NavContainer", "_c3", "Logo", "_c4", "LogoIcon", "_c5", "BackButton", "button", "_c6", "Page<PERSON><PERSON>le", "h1", "_c7", "PageSubtitle", "p", "_c8", "StatusBadge", "_c9", "MainContent", "main", "_c0", "TrainingGrid", "_c1", "CameraSection", "_c10", "SectionTitle", "h2", "_c11", "SectionIcon", "_c12", "WebcamContainer", "_c13", "StyledWebcam", "_c14", "RecordingOverlay", "props", "isRecording", "_c15", "SignSection", "_c16", "SignSelector", "select", "_c17", "SignDisplay", "_c18", "SignName", "h3", "_c19", "SignDescription", "_c20", "TopControlsSection", "_c21", "ControlButton", "variant", "compact", "_c22", "StatusMessage", "type", "_c23", "RecordingsSection", "_c24", "RecordingsTitle", "_c25", "RecordingsGrid", "_c26", "RecordingCard", "_c27", "RecordingTitle", "_c28", "RecordingTime", "_c29", "DownloadButton", "_c30", "PredictionDisplay", "matched", "isStale", "_c31", "PredictionText", "_c32", "ConfidenceBar", "_c33", "ConfidenceFill", "confidence", "_c34", "ConnectionStatus", "connected", "_c35", "signLanguageData", "name", "gif", "description", "TrainingPage", "onBackToHome", "_s", "currentSign", "setCurrentSign", "status", "setStatus", "isCapturing", "setIsCapturing", "recordedVideos", "setRecordedVideos", "webcamRef", "autoRecordTimeoutRef", "matchCountRef", "isConnected", "prediction", "isAIRecording", "recordingStatus", "signMatched", "targetSign", "startRecording", "startAIRecording", "stopRecording", "stopAIRecording", "startFrameCapture", "retryConnection", "handleSignChange", "event", "target", "value", "startDetection", "current", "startManualRecording", "setTimeout", "stopManualRecording", "clearTimeout", "downloadRecording", "video", "a", "document", "createElement", "href", "url", "download", "sign", "timestamp", "click", "predictedSign", "toLowerCase", "targetSignLower", "Math", "round", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "style", "textAlign", "marginBottom", "fontSize", "marginTop", "color", "marginLeft", "ref", "audio", "screenshotFormat", "videoConstraints", "width", "height", "facingMode", "borderRadius", "backgroundColor", "marginRight", "onChange", "disabled", "Object", "keys", "map", "sign<PERSON><PERSON>", "src", "alt", "onError", "e", "display", "nextS<PERSON>ling", "includes", "length", "Date", "toLocaleString", "id", "_c36", "$RefreshReg$"], "sources": ["D:/ASL/training-frontend/src/components/TrainingPage.js"], "sourcesContent": ["import { useState, useRef, useCallback, useEffect } from 'react';\r\nimport styled from 'styled-components';\r\nimport Webcam from 'react-webcam';\r\nimport {\r\n  Brain,\r\n  Camera,\r\n  ArrowLeft,\r\n  Play,\r\n  Square,\r\n  Download,\r\n  Eye,\r\n  Target,\r\n  Wifi,\r\n  WifiOff,\r\n  RefreshCw\r\n} from 'lucide-react';\r\nimport { useSignDetection } from '../hooks/useSignDetection';\r\n\r\nconst TrainingContainer = styled.div`\r\n  min-height: 100vh;\r\n  background: var(--bg-primary);\r\n  position: relative;\r\n  overflow-x: hidden;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background:\r\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\r\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\r\n    pointer-events: none;\r\n    z-index: 0;\r\n  }\r\n`;\r\n\r\nconst Navigation = styled.nav`\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 50;\r\n  background: var(--bg-glass);\r\n  backdrop-filter: blur(20px);\r\n  border-bottom: 1px solid var(--border-neural);\r\n  padding: var(--space-4) 0;\r\n  transition: var(--transition-normal);\r\n`;\r\n\r\nconst NavContainer = styled.div`\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 0 var(--space-6);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n\r\n  @media (max-width: 768px) {\r\n    padding: 0 var(--space-4);\r\n  }\r\n`;\r\n\r\nconst Logo = styled.div`\r\n  font-family: var(--font-primary);\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n  background: var(--text-gradient);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-3);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.25rem;\r\n    gap: var(--space-2);\r\n  }\r\n`;\r\n\r\nconst LogoIcon = styled.div`\r\n  width: 40px;\r\n  height: 40px;\r\n  background: var(--bg-neural);\r\n  border-radius: var(--radius-lg);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  box-shadow: var(--shadow-neural);\r\n\r\n  @media (max-width: 768px) {\r\n    width: 36px;\r\n    height: 36px;\r\n  }\r\n`;\r\n\r\nconst BackButton = styled.button`\r\n  background: var(--bg-glass);\r\n  color: var(--text-secondary);\r\n  border: 1px solid var(--border-neural);\r\n  padding: var(--space-3) var(--space-5);\r\n  border-radius: var(--radius-xl);\r\n  font-size: 0.9rem;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: var(--transition-normal);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  backdrop-filter: blur(10px);\r\n\r\n  &:hover {\r\n    background: var(--primary-50);\r\n    color: var(--primary-600);\r\n    border-color: var(--primary-300);\r\n    transform: translateY(-1px);\r\n    box-shadow: var(--shadow-lg);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-2) var(--space-4);\r\n    font-size: 0.85rem;\r\n  }\r\n`;\r\n\r\nconst PageTitle = styled.h1`\r\n  font-family: var(--font-primary);\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  background: var(--text-gradient);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  text-align: center;\r\n  margin-bottom: var(--space-4);\r\n  letter-spacing: -0.02em;\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 2rem;\r\n  }\r\n`;\r\n\r\nconst PageSubtitle = styled.p`\r\n  font-size: 1.125rem;\r\n  color: var(--text-secondary);\r\n  text-align: center;\r\n  margin-bottom: var(--space-16);\r\n  max-width: 700px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n  line-height: 1.6;\r\n\r\n  @media (max-width: 768px) {\r\n    margin-bottom: var(--space-12);\r\n    font-size: 1rem;\r\n  }\r\n`;\r\n\r\nconst StatusBadge = styled.div`\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  background: var(--bg-glass);\r\n  border: 1px solid var(--border-neural);\r\n  border-radius: var(--radius-full);\r\n  padding: var(--space-2) var(--space-4);\r\n  margin-bottom: var(--space-8);\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  color: var(--text-accent);\r\n  backdrop-filter: blur(10px);\r\n  box-shadow: var(--shadow-glow);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 0.8rem;\r\n    padding: var(--space-2) var(--space-3);\r\n  }\r\n`;\r\n\r\nconst MainContent = styled.main`\r\n  padding: var(--space-20) var(--space-4) var(--space-16);\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-12) var(--space-3) var(--space-8);\r\n    max-width: 100%;\r\n  }\r\n`;\r\n\r\nconst TrainingGrid = styled.div`\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: var(--space-8);\r\n  max-width: 1200px;\r\n  margin: 0 auto var(--space-12);\r\n\r\n  @media (max-width: 1024px) {\r\n    grid-template-columns: 1fr;\r\n    gap: var(--space-4);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    gap: var(--space-3);\r\n    margin: 0 auto var(--space-6);\r\n  }\r\n`;\r\n\r\nconst CameraSection = styled.div`\r\n  background: var(--bg-glass);\r\n  border-radius: var(--radius-2xl);\r\n  padding: var(--space-10);\r\n  border: 1px solid var(--border-neural);\r\n  backdrop-filter: blur(20px);\r\n  box-shadow: var(--shadow-lg);\r\n  transition: var(--transition-normal);\r\n  position: relative;\r\n  overflow: hidden;\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-6);\r\n    border-radius: var(--radius-xl);\r\n  }\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 4px;\r\n    background: var(--bg-neural);\r\n    transform: scaleX(0);\r\n    transition: var(--transition-normal);\r\n  }\r\n\r\n  &:hover {\r\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\r\n    border-color: var(--primary-300);\r\n\r\n    &::before {\r\n      transform: scaleX(1);\r\n    }\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-8);\r\n  }\r\n`;\r\n\r\nconst SectionTitle = styled.h2`\r\n  font-family: var(--font-primary);\r\n  font-size: 1.25rem;\r\n  margin-bottom: var(--space-6);\r\n  color: var(--text-primary);\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-3);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.125rem;\r\n    margin-bottom: var(--space-4);\r\n  }\r\n`;\r\n\r\nconst SectionIcon = styled.div`\r\n  width: 36px;\r\n  height: 36px;\r\n  background: var(--bg-neural);\r\n  border-radius: var(--radius-lg);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  box-shadow: var(--shadow-neural);\r\n\r\n  @media (max-width: 768px) {\r\n    width: 32px;\r\n    height: 32px;\r\n  }\r\n`;\r\n\r\nconst WebcamContainer = styled.div`\r\n  position: relative;\r\n  border-radius: var(--radius-2xl);\r\n  overflow: hidden;\r\n  background: var(--neural-100);\r\n  aspect-ratio: 4/3;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 3px solid var(--border-neural);\r\n  margin-bottom: var(--space-6);\r\n  box-shadow: var(--shadow-lg);\r\n\r\n  @media (max-width: 768px) {\r\n    aspect-ratio: 3/4;\r\n    margin-bottom: var(--space-4);\r\n    border-radius: var(--radius-xl);\r\n    border-width: 2px;\r\n  }\r\n`;\r\n\r\nconst StyledWebcam = styled(Webcam)`\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n`;\r\n\r\nconst RecordingOverlay = styled.div`\r\n  position: absolute;\r\n  top: var(--space-4);\r\n  right: var(--space-4);\r\n  background: ${props => props.isRecording ?\r\n    'var(--error-500)' :\r\n    'var(--neural-600)'\r\n  };\r\n  color: white;\r\n  padding: var(--space-3) var(--space-5);\r\n  border-radius: var(--radius-full);\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  box-shadow: var(--shadow-lg);\r\n  animation: ${props => props.isRecording ? 'pulse 1.5s infinite' : 'none'};\r\n\r\n  @keyframes pulse {\r\n    0%, 100% { opacity: 1; transform: scale(1); }\r\n    50% { opacity: 0.8; transform: scale(1.05); }\r\n  }\r\n`;\r\n\r\nconst SignSection = styled.div`\r\n  background: var(--bg-primary);\r\n  border-radius: var(--radius-2xl);\r\n  padding: var(--space-8);\r\n  border: 1px solid var(--border-light);\r\n  box-shadow: var(--shadow-lg);\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  text-align: center;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    box-shadow: var(--shadow-xl);\r\n    border-color: var(--primary-200);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-6);\r\n  }\r\n`;\r\n\r\nconst SignSelector = styled.select`\r\n  width: 100%;\r\n  padding: var(--space-3) var(--space-4);\r\n  border: 2px solid var(--border-light);\r\n  border-radius: var(--radius-lg);\r\n  background: var(--bg-primary);\r\n  color: var(--text-primary);\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n  margin-bottom: var(--space-4);\r\n  cursor: pointer;\r\n  transition: var(--transition-normal);\r\n\r\n  &:focus {\r\n    outline: none;\r\n    border-color: var(--primary-500);\r\n    box-shadow: 0 0 0 3px var(--primary-100);\r\n  }\r\n\r\n  &:hover {\r\n    border-color: var(--primary-300);\r\n  }\r\n\r\n  option {\r\n    padding: var(--space-2);\r\n    background: var(--bg-primary);\r\n    color: var(--text-primary);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.125rem;\r\n    padding: var(--space-4);\r\n    margin-bottom: var(--space-3);\r\n  }\r\n`;\r\n\r\nconst SignDisplay = styled.div`\r\n  width: 300px;\r\n  height: 300px;\r\n  background: var(--primary-50);\r\n  border-radius: var(--radius-2xl);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-bottom: var(--space-6);\r\n  border: 2px solid var(--primary-200);\r\n  transition: all 0.3s ease;\r\n  overflow: hidden;\r\n\r\n  img {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n    border-radius: var(--radius-xl);\r\n  }\r\n\r\n  &:hover {\r\n    transform: scale(1.02);\r\n    border-color: var(--primary-300);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    width: 250px;\r\n    height: 250px;\r\n  }\r\n`;\r\n\r\nconst SignName = styled.h3`\r\n  font-family: var(--font-primary);\r\n  font-size: 1.5rem;\r\n  margin-bottom: var(--space-3);\r\n  color: var(--text-primary);\r\n  font-weight: 700;\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.25rem;\r\n  }\r\n`;\r\n\r\nconst SignDescription = styled.p`\r\n  text-align: center;\r\n  line-height: 1.6;\r\n  color: var(--text-secondary);\r\n  font-size: 0.9rem;\r\n  font-weight: 400;\r\n  max-width: 280px;\r\n`;\r\n\r\nconst TopControlsSection = styled.div`\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: var(--space-4);\r\n  margin-bottom: var(--space-8);\r\n  padding: var(--space-4);\r\n  background: var(--bg-glass);\r\n  border-radius: var(--radius-xl);\r\n  border: 1px solid var(--border-neural);\r\n  backdrop-filter: blur(10px);\r\n  box-shadow: var(--shadow-lg);\r\n\r\n  @media (max-width: 768px) {\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: var(--space-3);\r\n    margin-bottom: var(--space-6);\r\n    padding: var(--space-3);\r\n  }\r\n`;\r\n\r\n\r\n\r\nconst ControlButton = styled.button`\r\n  background: ${props => props.variant === 'primary'\r\n    ? 'var(--primary-600)'\r\n    : props.variant === 'retry'\r\n    ? 'var(--warning-500)'\r\n    : 'var(--bg-primary)'};\r\n  border: ${props => props.variant === 'primary' || props.variant === 'retry'\r\n    ? 'none'\r\n    : '1px solid var(--border-medium)'};\r\n  color: ${props => props.variant === 'primary' || props.variant === 'retry'\r\n    ? 'white'\r\n    : 'var(--text-primary)'};\r\n  padding: ${props => props.compact\r\n    ? 'var(--space-2) var(--space-4)'\r\n    : 'var(--space-3) var(--space-6)'};\r\n  border-radius: var(--radius-lg);\r\n  cursor: pointer;\r\n  font-size: ${props => props.compact ? '0.8rem' : '0.9rem'};\r\n  font-weight: 600;\r\n  transition: all 0.2s ease;\r\n  min-width: ${props => props.compact ? '120px' : '160px'};\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: var(--space-2);\r\n\r\n  @media (max-width: 768px) {\r\n    padding: ${props => props.compact\r\n      ? 'var(--space-3) var(--space-5)'\r\n      : 'var(--space-4) var(--space-8)'};\r\n    font-size: ${props => props.compact ? '0.9rem' : '1rem'};\r\n    min-width: ${props => props.compact ? '140px' : '180px'};\r\n    border-radius: var(--radius-xl);\r\n  }\r\n  box-shadow: ${props => props.variant === 'primary' || props.variant === 'retry'\r\n    ? 'var(--shadow-lg)'\r\n    : 'var(--shadow-sm)'};\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: ${props => props.variant === 'primary' || props.variant === 'retry'\r\n      ? 'var(--shadow-xl)'\r\n      : 'var(--shadow-md)'};\r\n    background: ${props => props.variant === 'primary'\r\n      ? 'var(--primary-700)'\r\n      : props.variant === 'retry'\r\n      ? 'var(--warning-600)'\r\n      : 'var(--gray-50)'};\r\n  }\r\n\r\n  &:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    width: 100%;\r\n    max-width: ${props => props.compact ? '200px' : '280px'};\r\n  }\r\n`;\r\n\r\nconst StatusMessage = styled.div`\r\n  text-align: center;\r\n  margin-top: var(--space-6);\r\n  padding: var(--space-4) var(--space-6);\r\n  border-radius: var(--radius-lg);\r\n  background: ${props =>\r\n    props.type === 'success' ? 'var(--success-500)' :\r\n    props.type === 'error' ? 'var(--error-500)' :\r\n    'var(--primary-600)'\r\n  };\r\n  color: white;\r\n  font-weight: 500;\r\n  font-size: 0.875rem;\r\n  max-width: 400px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n`;\r\n\r\nconst RecordingsSection = styled.div`\r\n  margin-top: var(--space-16);\r\n  background: var(--bg-secondary);\r\n  padding: var(--space-12) var(--space-4);\r\n  border-radius: var(--radius-2xl);\r\n  max-width: 1200px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n`;\r\n\r\nconst RecordingsTitle = styled.h3`\r\n  font-family: var(--font-primary);\r\n  color: var(--text-primary);\r\n  margin-bottom: var(--space-8);\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n  text-align: center;\r\n`;\r\n\r\nconst RecordingsGrid = styled.div`\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\r\n  gap: var(--space-6);\r\n\r\n  @media (max-width: 768px) {\r\n    grid-template-columns: 1fr;\r\n    gap: var(--space-4);\r\n  }\r\n`;\r\n\r\nconst RecordingCard = styled.div`\r\n  background: var(--bg-primary);\r\n  padding: var(--space-6);\r\n  border-radius: var(--radius-xl);\r\n  border: 1px solid var(--border-light);\r\n  box-shadow: var(--shadow-sm);\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n    border-color: var(--primary-200);\r\n    box-shadow: var(--shadow-lg);\r\n  }\r\n`;\r\n\r\nconst RecordingTitle = styled.p`\r\n  margin: 0 0 var(--space-2) 0;\r\n  color: var(--text-primary);\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  font-family: var(--font-primary);\r\n`;\r\n\r\nconst RecordingTime = styled.p`\r\n  margin: 0 0 var(--space-4) 0;\r\n  font-size: 0.8rem;\r\n  color: var(--text-tertiary);\r\n`;\r\n\r\nconst DownloadButton = styled.button`\r\n  background: var(--primary-600);\r\n  border: none;\r\n  border-radius: var(--radius-lg);\r\n  padding: var(--space-2) var(--space-4);\r\n  color: white;\r\n  cursor: pointer;\r\n  font-size: 0.8rem;\r\n  font-weight: 500;\r\n  transition: all 0.2s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  margin: 0 auto;\r\n\r\n  &:hover {\r\n    background: var(--primary-700);\r\n    transform: translateY(-1px);\r\n  }\r\n`;\r\n\r\nconst PredictionDisplay = styled.div`\r\n  background: var(--bg-glass);\r\n  border: 2px solid ${props => {\r\n    if (props.matched) return 'var(--success-400)';\r\n    if (props.isStale) return 'var(--warning-300)';\r\n    return 'var(--border-light)';\r\n  }};\r\n  border-radius: var(--radius-xl);\r\n  padding: var(--space-4);\r\n  margin-bottom: var(--space-4);\r\n  text-align: center;\r\n  transition: var(--transition-normal);\r\n  backdrop-filter: blur(10px);\r\n  opacity: ${props => props.isStale ? 0.7 : 1};\r\n  min-height: 80px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n\r\n  ${props => props.matched && `\r\n    background: var(--success-50);\r\n    box-shadow: 0 0 20px var(--success-200);\r\n    animation: pulse 1s ease-in-out;\r\n  `}\r\n\r\n  ${props => props.isStale && `\r\n    background: var(--warning-50);\r\n  `}\r\n\r\n  @keyframes pulse {\r\n    0%, 100% { transform: scale(1); }\r\n    50% { transform: scale(1.02); }\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-3);\r\n    margin-bottom: var(--space-3);\r\n    min-height: 70px;\r\n  }\r\n`;\r\n\r\nconst PredictionText = styled.div`\r\n  font-size: 1.25rem;\r\n  font-weight: 600;\r\n  color: ${props => {\r\n    if (props.matched) return 'var(--success-700)';\r\n    if (props.isStale) return 'var(--warning-700)';\r\n    return 'var(--text-primary)';\r\n  }};\r\n  margin-bottom: var(--space-2);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.125rem;\r\n  }\r\n`;\r\n\r\nconst ConfidenceBar = styled.div`\r\n  width: 100%;\r\n  height: 8px;\r\n  background: var(--bg-secondary);\r\n  border-radius: var(--radius-full);\r\n  overflow: hidden;\r\n  margin-top: var(--space-2);\r\n`;\r\n\r\nconst ConfidenceFill = styled.div`\r\n  height: 100%;\r\n  background: ${props => {\r\n    if (props.confidence > 0.8) return 'var(--success-500)';\r\n    if (props.confidence > 0.6) return 'var(--warning-500)';\r\n    return 'var(--error-500)';\r\n  }};\r\n  width: ${props => (props.confidence * 100)}%;\r\n  transition: width 0.3s ease;\r\n`;\r\n\r\nconst ConnectionStatus = styled.div`\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  padding: var(--space-2) var(--space-3);\r\n  border-radius: var(--radius-lg);\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  background: ${props => props.connected ? 'var(--success-50)' : 'var(--error-50)'};\r\n  color: ${props => props.connected ? 'var(--success-700)' : 'var(--error-700)'};\r\n  border: 1px solid ${props => props.connected ? 'var(--success-200)' : 'var(--error-200)'};\r\n`;\r\n\r\n\r\n\r\n// Sign language data with GIFs (100 signs, model-predictable)\r\nconst signLanguageData = {\r\n  \"TV\": { name: \"TV\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/tv.gif\", description: \"Sign for TV.\" },\r\n  \"after\": { name: \"After\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/after.gif\", description: \"Sign for after.\" },\r\n  \"airplane\": { name: \"Airplane\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/airplane.gif\", description: \"Sign for airplane.\" },\r\n  \"all\": { name: \"All\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/all.gif\", description: \"Sign for all.\" },\r\n  \"alligator\": { name: \"Alligator\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/alligator.gif\", description: \"Sign for alligator.\" },\r\n  \"animal\": { name: \"Animal\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/animal.gif\", description: \"Sign for animal.\" },\r\n  \"another\": { name: \"Another\", gif: \"https://lifeprint.com/asl101/gifs-animated/another.gif\", description: \"Sign for another.\" },\r\n  \"any\": { name: \"Any\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/any.gif\", description: \"Sign for any.\" },\r\n  \"apple\": { name: \"Apple\", gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\", description: \"Sign for apple.\" },\r\n  \"arm\": { name: \"Arm\", gif: \"https://th.bing.com/th/id/OIP.KkJLqfbp6XEHiIe-jOUb2AHaEc?w=251&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for arm.\" },\r\n  \"aunt\": { name: \"Aunt\", gif: \"https://th.bing.com/th/id/OIP.Yz5UUZdNTrVWXf72we_N6wHaHa?rs=1&pid=ImgDetMain\", description: \"Sign for aunt.\" },\r\n  \"awake\": { name: \"Awake\", gif: \"https://th.bing.com/th/id/OIP.XcgdjGKBo8LynmiAw-tDCQHaE-?w=235&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for awake.\" },\r\n  \"backyard\": { name: \"Backyard\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/backyard.gif\", description: \"Sign for backyard.\" },\r\n  \"bad\": { name: \"Bad\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/bad.gif\", description: \"Sign for bad.\" },\r\n  \"balloon\": { name: \"Balloon\", gif: \"https://media.giphy.com/media/26FL9yfajyobRXJde/giphy.gif\", description: \"Sign for balloon.\" },\r\n  \"bath\": { name: \"Bath\", gif: \"https://media.giphy.com/media/l0MYPjjoeJbZVPmNO/giphy.gif\", description: \"Sign for bath.\" },\r\n  \"because\": { name: \"Because\", gif: \"https://lifeprint.com/asl101/gifs-animated/because.gif\", description: \"Sign for because.\" },\r\n  \"bed\": { name: \"Bed\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/bed.gif\", description: \"Sign for bed.\" },\r\n  \"bedroom\": { name: \"Bedroom\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/bedroom.gif\", description: \"Sign for bedroom.\" },\r\n  \"bee\": { name: \"Bee\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/bee.gif\", description: \"Sign for bee.\" },\r\n  \"before\": { name: \"Before\", gif: \"https://th.bing.com/th/id/OIP.0EvzUY4jH2cDCa4nNcRw4wHaE-?w=267&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for before.\" },\r\n  \"beside\": { name: \"Beside\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/beside.gif\", description: \"Sign for beside.\" },\r\n  \"better\": { name: \"Better\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/better.gif\", description: \"Sign for better.\" },\r\n  \"bird\": { name: \"Bird\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/bird.gif\", description: \"Sign for bird.\" },\r\n  \"black\": { name: \"Black\", gif: \"https://th.bing.com/th/id/R.********************************?rik=52tGw7%2fGcx2Htw&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fblack.gif&ehk=jgXbFVBhP%2b5fFrT1%2fE%2fcEq7KnRYjrCKtqEd%2fA5rNhqo%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for black.\" },\r\n  \"blow\": { name: \"Blow\", gif: \"https://th.bing.com/th/id/OIP.rJg-otMBtvfj1T1HkSKugwHaEc?w=304&h=182&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for blow.\" },\r\n  \"blue\": { name: \"Blue\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/blue.gif\", description: \"Sign for blue.\" },\r\n  \"boat\": { name: \"Boat\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/boat.gif\", description: \"Sign for boat.\" },\r\n  \"book\": { name: \"Book\", gif: \"https://media.giphy.com/media/l0MYL43dl4pQEn3uE/giphy.gif\", description: \"Sign for book.\" },\r\n  \"boy\": { name: \"Boy\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/boy.gif\", description: \"Sign for boy.\" },\r\n  \"brother\": { name: \"Brother\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/brother.gif\", description: \"Sign for brother.\" },\r\n  \"brown\": { name: \"Brown\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/brown.gif\", description: \"Sign for brown.\" },\r\n  \"bug\": { name: \"Bug\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/bug.gif\", description: \"Sign for bug.\" },\r\n  \"bye\": { name: \"Bye\", gif: \"https://c.tenor.com/vME77PObDN8AAAAC/asl-bye-asl-goodbye.gif\", description: \"Sign for bye.\" },\r\n  \"callonphone\": { name: \"Call on phone\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/call.gif\", description: \"Sign for call on phone.\" },\r\n  \"can\": { name: \"Can\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/can.gif\", description: \"Sign for can.\" },\r\n  \"car\": { name: \"Car\", gif: \"https://th.bing.com/th/id/OIP.wxw32OaIdqFt8f_ucHVoRgHaEH?w=308&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for car.\" },\r\n  \"carrot\": { name: \"Carrot\", gif: \"https://media.giphy.com/media/l0HlDdvqxs1jsRtiU/giphy.gif\", description: \"Sign for carrot.\" },\r\n  \"cat\": { name: \"Cat\", gif: \"https://lifeprint.com/asl101/gifs-animated/cat-02.gif\", description: \"Sign for cat.\" },\r\n  \"cereal\": { name: \"Cereal\", gif: \"https://th.bing.com/th/id/R.********************************?rik=wPMg%2fK1dYTfR%2bw&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fcereal.gif&ehk=RpDS3wWZM4eryawaxA1wAvWwM0EM%2fdGgJkWY2ce1KFs%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for cereal.\" },\r\n  \"chair\": { name: \"Chair\", gif: \"https://th.bing.com/th/id/OIP.5kr1MkVLnuN2Z9Jkw-0QpAHaE-?w=237&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for chair.\" },\r\n  \"cheek\": { name: \"Cheek\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/cheek.gif\", description: \"Sign for cheek.\" },\r\n  \"child\": { name: \"Child\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/child.gif\", description: \"Sign for child.\" },\r\n  \"chin\": { name: \"Chin\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/chin.gif\", description: \"Sign for chin.\" },\r\n  \"chocolate\": { name: \"Chocolate\", gif: \"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fi.pinimg.com%2foriginals%2f9f%2fa2%2fb5%2f9fa2b5064a72b5e46202d20848f1bf21.gif&ehk=izvOlFp25%2fx5NVTCmqVz0UOnZNOWy%2fAJJtzAhkZ8nTg%3d\", description: \"Sign for chocolate.\" },\r\n  \"clean\": { name: \"Clean\", gif: \"https://media.giphy.com/media/3o7TKoturrdpf5Muwo/giphy.gif\", description: \"Sign for clean.\" },\r\n  \"close\": { name: \"Close\", gif: \"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia2.giphy.com%2fmedia%2fl4JyZuXNGxS3Yydeo%2fgiphy.gif%3fcid%3d790b7611318eb5b864ad67b3cecb35b9d81240a50d251bb0%26rid%3dgiphy.gif%26ct%3dg&ehk=A6wfp3Afm3rFCPLWSjgQd6JVjmRSBNBlk9vd0jVNgJc%3d\", description: \"Sign for close.\" },\r\n  \"closet\": { name: \"Closet\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/closet.gif\", description: \"Sign for closet.\" },\r\n  \"cloud\": { name: \"Cloud\", gif: \"https://th.bing.com/th/id/OIP.hMO89bV2zwVcIVIa7FOT5QHaEc?rs=1&pid=ImgDetMain\", description: \"Sign for cloud.\" },\r\n  \"clown\": { name: \"Clown\", gif: \"https://th.bing.com/th/id/R.********************************?rik=OPrV3%2b1Zkelr2A&pid=ImgRaw&r=0\", description: \"Sign for clown.\" },\r\n  \"cow\": { name: \"Cow\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/cow.gif\", description: \"Sign for cow.\" },\r\n  \"cowboy\": { name: \"Cowboy\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/cowboy.gif\", description: \"Sign for cowboy.\" },\r\n  \"cry\": { name: \"Cry\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/cry.gif\", description: \"Sign for cry.\" },\r\n  \"cut\": { name: \"Cut\", gif: \"https://th.bing.com/th/id/OIP.ZtKu3hlJ6pduArqfgEcyUgHaE-?w=248&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for cut.\" },\r\n  \"cute\": { name: \"Cute\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/cute.gif\", description: \"Sign for cute.\" },\r\n  \"dad\": { name: \"Dad\", gif: \"https://media.giphy.com/media/l0MYQcLKwtl5v6H1S/giphy.gif\", description: \"Sign for dad.\" },\r\n  \"dance\": { name: \"Dance\", gif: \"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia.giphy.com%2fmedia%2f3o7TKMspYQjQTbOz2U%2fgiphy.gif&ehk=h%2bdBHCxuoOT89ovSy5uTk6MCL9acaBEV6ld9lrVDRF4%3d\", description: \"Sign for dance.\" },\r\n  \"dirty\": { name: \"Dirty\", gif: \"https://th.bing.com/th/id/OIP.wRA7r1OPPUuEoLL4Hds9jAHaHa?rs=1&pid=ImgDetMain\", description: \"Sign for dirty.\" },\r\n  \"dog\": { name: \"Dog\", gif: \"https://th.bing.com/th/id/R.********************************?rik=uVshGsOHXgldoQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fd%2fdog1.gif&ehk=Xnfrvg0xwy1u%2fae9vWdKYMT25%2bF6qoteW2FThCbVrOA%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for dog.\" },\r\n  \"doll\": { name: \"Doll\", gif: \"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fwww.lifeprint.com%2fasl101%2fgifs-animated%2fdoll.gif&ehk=hPI0Fzzl9CGOrgQYS2Z53a5YdYgjxYFeOIGghGAEZYU%3d\", description: \"Sign for doll.\" },\r\n  \"donkey\": { name: \"Donkey\", gif: \"https://www.lifeprint.com/asl101/gifs/d/donkey-1h.gif\", description: \"Sign for donkey.\" },\r\n  \"down\": { name: \"Down\", gif: \"https://th.bing.com/th/id/OIP.CZlW6IpZUdgspxVpW6PZ8QHaE-?w=250&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for down.\" },\r\n  \"drawer\": { name: \"Drawer\", gif: \"https://th.bing.com/th/id/OIP.8yooqOFFixqki7j28PVpYQHaE-?w=234&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for drawer.\" },\r\n  \"drink\": { name: \"Drink\", gif: \"https://www.lifeprint.com/asl101/gifs/d/drink-c.gif\", description: \"Sign for drink.\" },\r\n  \"drop\": { name: \"Drop\", gif: \"https://th.bing.com/th/id/OIP.XQJn0tOccOUmG8OZHz8X9gHaE-?w=247&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for drop.\" },\r\n  \"dry\": { name: \"Dry\", gif: \"https://th.bing.com/th/id/OIP.A0oQgM0IGtwZjfz1Caj-AgHaE-?w=268&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for dry.\" },\r\n  \"dryer\": { name: \"Dryer\", gif: \"https://lifeprint.com/asl101/gifs/d/dryer.gif\", description: \"Sign for dryer.\" },\r\n  \"duck\": { name: \"Duck\", gif: \"https://th.bing.com/th/id/R.********************************?rik=ZetjiJ3WOhOXrQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fd%2fduck.gif&ehk=STeui62x5lieai0VcyeZkX2t8rILR%2f8GR5F3x2xJ5tw%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for duck.\" },\r\n  \"ear\": { name: \"Ear\", gif: \"https://lifeprint.com/asl101/signjpegs/e/ears.h3.jpg\", description: \"Sign for ear.\" },\r\n  \"elephant\": { name: \"Elephant\", gif: \"https://lifeprint.com/asl101/gifs-animated/elephant.gif\", description: \"Sign for elephant.\" },\r\n  \"empty\": { name: \"Empty\", gif: \"https://lifeprint.com/images-signs/empty.gif\", description: \"Sign for empty.\" },\r\n  \"every\": { name: \"Every\", gif: \"https://lifeprint.com/asl101/gifs-animated/every.gif\", description: \"Sign for every.\" },\r\n  \"eye\": { name: \"Eye\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/eye.gif\", description: \"Sign for eye.\" },\r\n  \"face\": { name: \"Face\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/face.gif\", description: \"Sign for face.\" },\r\n  \"fall\": { name: \"Fall\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/fall.gif\", description: \"Sign for fall.\" },\r\n  \"farm\": { name: \"Farm\", gif: \"https://th.bing.com/th/id/R.********************************?rik=IO%2brRd7xNmCQBQ&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2ffarm.gif&ehk=aOO01Vk8fbE84nLfNNnOVL3kUdyWJtLaTEcwePgbP9A%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for farm.\" },\r\n  \"fast\": { name: \"Fast\", gif: \"https://th.bing.com/th/id/OIP.YX_BqT1FjGm8HeM4k4WFAgAAAA?rs=1&pid=ImgDetMain\", description: \"Sign for fast.\" },\r\n  \"feet\": { name: \"Feet\", gif: \"https://th.bing.com/th/id/OIP.RaYFj5lvSS6NeIna8NtmZQHaE-?w=247&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for feet.\" },\r\n  \"find\": { name: \"Find\", gif: \"https://www.lifeprint.com/asl101/gifs/f/find-pick.gif\", description: \"Sign for find.\" },\r\n  \"fine\": { name: \"Fine\", gif: \"https://th.bing.com/th/id/R.********************************?rik=Qpm%2bw3fHTAWj1A&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2ff%2ffine.gif&ehk=mGMZf4l%2bLZMq4atRomNJSvrSjYgFe%2bRVCm1dYLh5J3I%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for fine.\" },\r\n  \"finger\": { name: \"Finger\", gif: \"https://lifeprint.com/asl101/gifs/f/finger.gif\", description: \"Sign for finger.\" },\r\n  \"finish\": { name: \"Finish\", gif: \"https://th.bing.com/th/id/R.********************************?rik=34j4pW2f3E5TtQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2ff%2ffinish.gif&ehk=xNk24Jbe3t0moSmcmUftmZzCRgHIxsarq3W9E7kGmPM%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for finish.\" },\r\n  \"fireman\": { name: \"Fireman\", gif: \"https://lifeprint.com/asl101/gifs/f/fireman-c2.gif\", description: \"Sign for fireman.\" },\r\n  \"first\": { name: \"First\", gif: \"https://lifeprint.com/asl101/gifs/f/first.gif\", description: \"Sign for first.\" },\r\n  \"fish\": { name: \"Fish\", gif: \"https://th.bing.com/th/id/OIP.Lzhd7lIIa-V4H3faS1d3mQHaHa?rs=1&pid=ImgDetMain\", description: \"Sign for fish.\" },\r\n  \"flag\": { name: \"Flag\", gif: \"https://th.bing.com/th/id/OIP.3LqQWEnK4TG0lohgQ3G5uAHaE-?w=263&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for flag.\" },\r\n  \"flower\": { name: \"Flower\", gif: \"https://media.giphy.com/media/3o7TKGkqPpLUdFiFPy/giphy.gif\", description: \"Sign for flower.\" },\r\n  \"food\": { name: \"Food\", gif: \"https://i.pinimg.com/originals/cc/bb/0c/ccbb0c143db0b51e9947a5966db42fd8.gif\", description: \"Sign for food.\" },\r\n  \"for\": { name: \"For\", gif: \"https://lifeprint.com/asl101/gifs/f/for.gif\", description: \"Sign for for.\" },\r\n  \"frenchfries\": { name: \"French Fries\", gif: \"https://www.lifeprint.com/asl101/gifs/f/french-fries.gif\", description: \"Sign for french fries.\" },\r\n  \"frog\": { name: \"Frog\", gif: \"https://media.giphy.com/media/l0HlKl64lIvTjZ7QA/giphy.gif\", description: \"Sign for frog.\" },\r\n  \"garbage\": { name: \"Garbage\", gif: \"https://th.bing.com/th/id/R.********************************?rik=78iU%2fDx85Ut9fA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fg%2fgarbage.gif&ehk=lafY%2f1y5WEEfr04p6Uq4waDP9iV7bJB5r2k3RYGOhWY%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for garbage.\" },\r\n  \"gift\": { name: \"Gift\", gif: \"https://www.babysignlanguage.com/signs/gift.gif\", description: \"Sign for gift.\" },\r\n  \"giraffe\": { name: \"Giraffe\", gif: \"https://www.lifeprint.com/asl101/gifs/g/giraffe.gif\", description: \"Sign for giraffe.\" },\r\n  \"girl\": { name: \"Girl\", gif: \"https://th.bing.com/th/id/R.********************************?rik=yDsGUPEaDyeSlA&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fgirl.gif&ehk=zdVxVSayRBDn67vVCpMhUH6UmzUQE8vaY7%2bv8jedvs8%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for girl.\" },\r\n  \"give\": { name: \"Give\", gif: \"https://www.lifeprint.com/asl101/gifs/g/give-x-two-handed.gif\", description: \"Sign for give.\" },\r\n  \"glasswindow\": { name: \"Glass Window\", gif: \"https://lifeprint.com/asl101/gifs/g/glass.gif\", description: \"Sign for glass window.\" },\r\n  \"go\": { name: \"Go\", gif: \"https://media.giphy.com/media/l3vRdVMMN9VsW5a0w/giphy.gif\", description: \"Sign for go.\" },\r\n  \"goose\": { name: \"Goose\", gif: \"https://www.babysignlanguage.com/signs/goose.gif\", description: \"Sign for goose.\" },\r\n  \"grandma\": { name: \"Grandma\", gif: \"https://www.lifeprint.com/asl101/gifs/g/grandma.gif\", description: \"Sign for grandma.\" },\r\n  \"grandpa\": { name: \"Grandpa\", gif: \"https://th.bing.com/th/id/OIP.yyLPc-rWg0PMNbrwjeQQngHaE-?w=238&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for grandpa.\" },\r\n  \"grass\": { name: \"Grass\", gif: \"https://th.bing.com/th/id/R.********************************?rik=uGZNVzt6tISwHA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs-animated%2fgrass.gif&ehk=VKQd9cvBrglo47EhogWYL9rOiZZsEJ7Yqt%2bgJ8N99yQ%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for grass.\" },\r\n  \"green\": { name: \"Green\", gif: \"https://i.pinimg.com/originals/cb/7f/75/cb7f757ffb79cb3d1309c9ad785e83a1.gif\", description: \"Sign for green.\" },\r\n  \"gum\": { name: \"Gum\", gif: \"https://lifeprint.com/asl101/gifs/g/gum.gif\", description: \"Sign for gum.\" },\r\n  \"hair\": { name: \"Hair\", gif: \"https://www.lifeprint.com/asl101/gifs/h/hair-g-version.gif\", description: \"Sign for hair.\" },\r\n  \"happy\": { name: \"Happy\", gif: \"https://media0.giphy.com/media/3o7TKFpahYpUp4g0N2/giphy.gif?cid=790b7611bca188a92e2e759876756ef62ba95b7cdd337c77&rid=giphy.gif&ct=g\", description: \"Sign for happy.\" },\r\n  \"hat\": { name: \"Hat\", gif: \"https://th.bing.com/th/id/OIP.QyFdqn-0ZqUwNfE6jbzKWAHaE-?w=258&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for hat.\" },\r\n  \"hate\": { name: \"Hate\", gif: \"https://media.giphy.com/media/l0MYPiNw8l2LAPJXW/giphy.gif\", description: \"Sign for hate.\" },\r\n  \"have\": { name: \"Have\", gif: \"https://th.bing.com/th/id/R.********************************?rik=q5Ei%2b7oJb7Uzyw&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhave.gif&ehk=H9yIaJxFVejkfHpkhTUipBRv9CW63KBFy6QW5cdbkKw%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for have.\" },\r\n  \"haveto\": { name: \"Have to\", gif: \"https://lifeprint.com/asl101/gifs/h/have-to.gif\", description: \"Sign for have to.\" },\r\n  \"head\": { name: \"Head\", gif: \"https://th.bing.com/th/id/R.********************************?rik=OcbJdRbpEFsWXQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fsignjpegs%2fh%2fhead-1.jpg&ehk=RPBV45fSrLDEWYiZvRuZs2c1JNrL4WzdqLSNMFIF3Rs%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for head.\" },\r\n  \"hear\": { name: \"Hear\", gif: \"https://www.lifeprint.com/asl101/signjpegs/h/hear.h4.jpg\", description: \"Sign for hear.\" },\r\n  \"helicopter\": { name: \"Helicopter\", gif: \"https://th.bing.com/th/id/R.********************************?rik=5uhWxBaByliWA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhelicopter.gif&ehk=mwAyT82RBoeYDe7yaHA1jL3%2f30dUksltmv4dF7YGf%2bU%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for helicopter.\" },\r\n  \"hello\": { name: \"Hello\", gif: \"https://media.giphy.com/media/3o7TKNKOfKlIhbD3gY/giphy.gif\", description: \"Sign for hello.\" },\r\n  \"hen\": { name: \"Hen\", gif: \"https://media0.giphy.com/media/26hisADhtILiu1J3W/giphy.gif?cid=790b76112d512b94e1647afb111c8d77f92ae31f37864f2&rid=giphy.gif&ct=g\", description: \"Sign for hen.\" },\r\n  \"hesheit\": { name: \"He/She/It\", gif: \"https://lifeprint.com/asl101/gifs/h/he-she-it.gif\", description: \"Sign for he/she/it.\" },\r\n  \"hide\": { name: \"Hide\", gif: \"https://lifeprint.com/asl101/gifs/h/hide.gif\", description: \"Sign for hide.\" },\r\n  \"high\": { name: \"High\", gif: \"https://lifeprint.com/asl101/gifs/h/high.gif\", description: \"Sign for high.\" },\r\n  \"home\": { name: \"Home\", gif: \"https://th.bing.com/th/id/R.********************************?rik=%2bnBd%2foQjxnoPfg&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhome-2.gif&ehk=7yD%2f%2fh6JN1Y4D4BOrUjgKW4Jccy2Y4GVYLf%2fzyk%2b5YY%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for home.\" },\r\n  \"horse\": { name: \"Horse\", gif: \"https://media.giphy.com/media/l0HlM5HffraiQaHUk/giphy.gif\", description: \"Sign for horse.\" },\r\n  \"hot\": { name: \"Hot\", gif: \"https://media.giphy.com/media/3o6Zt99k5aDok347bG/giphy.gif\", description: \"Sign for hot.\" },\r\n  \"hungry\": { name: \"Hungry\", gif: \"https://media.giphy.com/media/l3vR0xkdFEz4tnfTq/giphy.gif\", description: \"Sign for hungry.\" },\r\n  \"icecream\": { name: \"Ice Cream\", gif: \"https://media.giphy.com/media/3o7TKp6yVibVMhBSLu/giphy.gif\", description: \"Sign for ice cream.\" },\r\n  \"if\": { name: \"If\", gif: \"https://lifeprint.com/asl101/gifs/i/if.gif\", description: \"Sign for if.\" },\r\n  \"into\": { name: \"Into\", gif: \"https://lifeprint.com/asl101/gifs/i/into.gif\", description: \"Sign for into.\" },\r\n  \"jacket\": { name: \"Jacket\", gif: \"https://www.lifeprint.com/asl101/gifs/c/coat.gif\", description: \"Sign for jacket.\" },\r\n  \"jeans\": { name: \"Jeans\", gif: \"https://lifeprint.com/asl101/gifs/j/jeans.gif\", description: \"Sign for jeans.\" },\r\n  \"jump\": { name: \"Jump\", gif: \"https://lifeprint.com/asl101/gifs-animated/jump.gif\", description: \"Sign for jump.\" },\r\n  \"kiss\": { name: \"Kiss\", gif: \"https://i.gifer.com/PxGY.gif\", description: \"Sign for kiss.\" },\r\n  \"kitty\": { name: \"Kitty\", gif: \"https://lifeprint.com/asl101/gifs-animated/cat-02.gif\", description: \"Sign for kitty.\" },\r\n  \"lamp\": { name: \"Lamp\", gif: \"https://lifeprint.com/asl101/gifs/l/lamp.gif\", description: \"Sign for lamp.\" },\r\n  \"later\": { name: \"Later\", gif: \"https://media3.giphy.com/media/l0MYHTyMzMRcikIxi/giphy.gif?cid=790b761128cd39f9baa06dbeb4e099d13e3516763d5f0952&rid=giphy.gif&ct=g\", description: \"Sign for later.\" },\r\n  \"like\": { name: \"Like\", gif: \"https://lifeprint.com/asl101/gifs/l/like.gif\", description: \"Sign for like.\" },\r\n  \"lion\": { name: \"Lion\", gif: \"https://th.bing.com/th/id/OIP.8sDkvbXdMKVmCNlDV79WpAHaE-?w=247&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for lion.\" },\r\n  \"lips\": { name: \"Lips\", gif: \"https://lifeprint.com/asl101/gifs/l/lips.gif\", description: \"Sign for lips.\" },\r\n  \"listen\": { name: \"Listen\", gif: \"https://th.bing.com/th/id/OIP.VjsXAad6abRwkCla83kbZQHaEc?w=284&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for listen.\" },\r\n  \"look\": { name: \"Look\", gif: \"https://th.bing.com/th/id/R.********************************?rik=pYhzip7LqNs7qw&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fl%2flook-at-1.gif&ehk=rFJ7dBrMGFDK0nHLzrOPAzROVE7yqyDEcb%2btLqKqYOI%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for look.\" },\r\n  \"loud\": { name: \"Loud\", gif: \"https://lifeprint.com/asl101/gifs-animated/loud.gif\", description: \"Sign for loud.\" },\r\n  \"mad\": { name: \"Mad\", gif: \"https://lifeprint.com/asl101/gifs/m/mad.gif\", description: \"Sign for mad.\" },\r\n  \"make\": { name: \"Make\", gif: \"https://th.bing.com/th/id/OIP.CPz7T2bH107Tu-DBnHvatAHaEc?w=313&h=188&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for make.\" },\r\n  \"man\": { name: \"Man\", gif: \"https://lifeprint.com/asl101/gifs/m/man.gif\", description: \"Sign for man.\" },\r\n  \"many\": { name: \"Many\", gif: \"https://lifeprint.com/asl101/gifs/m/many.gif\", description: \"Sign for many.\" },\r\n  \"milk\": { name: \"Milk\", gif: \"https://lifeprint.com/asl101/gifs/m/milk.gif\", description: \"Sign for milk.\" },\r\n  \"minemy\": { name: \"Mine/My\", gif: \"https://th.bing.com/th/id/OIP.VBkNZsR_pK7KUoCNiWYMdgHaEc?w=288&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for mine/my.\" },\r\n  \"mitten\": { name: \"Mitten\", gif: \"https://lifeprint.com/asl101/gifs-animated/mittens.gif\", description: \"Sign for mitten.\" },\r\n  \"mom\": { name: \"Mom\", gif: \"https://lifeprint.com/asl101/gifs/m/mom.gif\", description: \"Sign for mom.\" },\r\n  \"moon\": { name: \"Moon\", gif: \"https://th.bing.com/th/id/R.********************************?rik=XbVhBJtkANrG9g&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fm%2fmoon.gif&ehk=YSDvFeUSTa9X1BEJhDjdnLC4c7zWn8z7Hj%2fMkkLUyFE%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for moon.\" },\r\n  \"morning\": { name: \"Morning\", gif: \"https://media0.giphy.com/media/3o6ZtrcJ9GCXGGw0ww/source.gif\", description: \"Sign for morning.\" },\r\n  \"mouse\": { name: \"Mouse\", gif: \"https://lifeprint.com/asl101/gifs/m/mouse.gif\", description: \"Sign for mouse.\" },\r\n  \"mouth\": { name: \"Mouth\", gif: \"https://lifeprint.com/asl101/gifs-animated/mouth.gif\", description: \"Sign for mouth.\" },\r\n  \"nap\": { name: \"Nap\", gif: \"https://lifeprint.com/asl101/gifs/n/nap.gif\", description: \"Sign for nap.\" },\r\n  \"napkin\": { name: \"Napkin\", gif: \"https://lifeprint.com/asl101/gifs/n/napkin.gif\", description: \"Sign for napkin.\" },\r\n  \"night\": { name: \"Night\", gif: \"https://lifeprint.com/asl101/gifs/n/night.gif\", description: \"Sign for night.\" },\r\n  \"no\": { name: \"No\", gif: \"https://lifeprint.com/asl101/gifs/n/no-2-movement.gif\", description: \"Sign for no.\" },\r\n  \"noisy\": { name: \"Noisy\", gif: \"https://lifeprint.com/asl101/gifs/n/noisy.gif\", description: \"Sign for noisy.\" },\r\n  \"nose\": { name: \"Nose\", gif: \"https://lifeprint.com/asl101/signjpegs/n/nose.h1.jpg\", description: \"Sign for nose.\" },\r\n  \"not\": { name: \"Not\", gif: \"https://th.bing.com/th/id/R.********************************?rik=6%2bbZ2jRA%2famQ4Q&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fn%2fnot-negative.gif&ehk=%2bppuO9P0%2fpdzrrdNO4FXpxdIGs8jgY%2fj%2b1ZCwdbDWO4%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for not.\" },\r\n  \"now\": { name: \"Now\", gif: \"https://lifeprint.com/asl101/gifs/n/now.gif\", description: \"Sign for now.\" },\r\n  \"nuts\": { name: \"Nuts\", gif: \"https://th.bing.com/th/id/OIP.wRnQjn9j2vfFfzAnRR205QHaE-?w=276&h=185&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for nuts.\" },\r\n  \"old\": { name: \"Old\", gif: \"https://lifeprint.com/asl101/gifs/o/old.gif\", description: \"Sign for old.\" },\r\n  \"on\": { name: \"On\", gif: \"https://lifeprint.com/asl101/gifs/o/on-onto.gif\", description: \"Sign for on.\" },\r\n  \"open\": { name: \"Open\", gif: \"https://th.bing.com/th/id/OIP.BeMiGXQFuYk_6ZrgG3iqzQHaE-?w=264&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for open.\" },\r\n  \"orange\": { name: \"Orange\", gif: \"https://lifeprint.com/asl101/gifs/o/orange.gif\", description: \"Sign for orange.\" },\r\n  \"outside\": { name: \"Outside\", gif: \"https://lifeprint.com/asl101/gifs/o/outside.gif\", description: \"Sign for outside.\" },\r\n  \"owie\": { name: \"Owie\", gif: \"https://lifeprint.com/asl101/gifs/o/owie.gif\", description: \"Sign for owie.\" },\r\n  \"owl\": { name: \"Owl\", gif: \"https://lifeprint.com/asl101/gifs/o/owl.gif\", description: \"Sign for owl.\" },\r\n  \"pajamas\": { name: \"Pajamas\", gif: \"https://lifeprint.com/asl101/gifs/p/pajamas.gif\", description: \"Sign for pajamas.\" },\r\n  \"pen\": { name: \"Pen\", gif: \"https://lifeprint.com/asl101/gifs/p/pen.gif\", description: \"Sign for pen.\" },\r\n  \"pencil\": { name: \"Pencil\", gif: \"https://lifeprint.com/asl101/gifs/p/pencil-2.gif\", description: \"Sign for pencil.\" },\r\n  \"penny\": { name: \"Penny\", gif: \"https://lifeprint.com/asl101/gifs/p/penny.gif\", description: \"Sign for penny.\" },\r\n  \"person\": { name: \"Person\", gif: \"https://lifeprint.com/asl101/gifs/p/person.gif\", description: \"Sign for person.\" },\r\n  \"pig\": { name: \"Pig\", gif: \"https://lifeprint.com/asl101/gifs/p/pig.gif\", description: \"Sign for pig.\" },\r\n  \"pizza\": { name: \"Pizza\", gif: \"https://lifeprint.com/asl101/gifs/p/pizza.gif\", description: \"Sign for pizza.\" },\r\n  \"please\": { name: \"Please\", gif: \"https://lifeprint.com/asl101/gifs-animated/pleasecloseup.gif\", description: \"Sign for please.\" },\r\n  \"police\": { name: \"Police\", gif: \"https://th.bing.com/th/id/R.********************************?rik=icjjfUg15cqgLw&pid=ImgRaw&r=0\", description: \"Sign for police.\" },\r\n  \"pool\": { name: \"Pool\", gif: \"https://th.bing.com/th/id/OIP.dhcMKyW2psDcA5uwsRaRagHaEc?w=276&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for pool.\" },\r\n  \"potty\": { name: \"Potty\", gif: \"https://th.bing.com/th/id/OIP.YcNMUjCg6f95xdgN5rnenwHaE-?w=264&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for potty.\" },\r\n  \"pretend\": { name: \"Pretend\", gif: \"https://lifeprint.com/asl101/gifs/p/pretend.gif\", description: \"Sign for pretend.\" },\r\n  \"pretty\": { name: \"Pretty\", gif: \"https://lifeprint.com/asl101/gifs/b/beautiful.gif\", description: \"Sign for pretty.\" },\r\n  \"puppy\": { name: \"Puppy\", gif: \"https://lifeprint.com/asl101/gifs/p/puppy.gif\", description: \"Sign for puppy.\" },\r\n  \"puzzle\": { name: \"Puzzle\", gif: \"https://res.cloudinary.com/spiralyze/image/upload/f_auto,w_auto/BabySignLanguage/DictionaryPages/puzzle.svg\", description: \"Sign for puzzle.\" },\r\n  \"quiet\": { name: \"Quiet\", gif: \"https://lifeprint.com/asl101/gifs-animated/quiet-03.gif\", description: \"Sign for quiet.\" },\r\n  \"radio\": { name: \"Radio\", gif: \"https://i.pinimg.com/originals/6d/5e/5e/6d5e5e2f78f80e9006293df853a2ba3b.gif\", description: \"Sign for radio.\" },\r\n  \"rain\": { name: \"Rain\", gif: \"https://lifeprint.com/asl101/gifs/r/rain.gif\", description: \"Sign for rain.\" },\r\n  \"read\": { name: \"Read\", gif: \"https://lifeprint.com/asl101/gifs/r/read.gif\", description: \"Sign for read.\" },\r\n  \"red\": { name: \"Red\", gif: \"https://lifeprint.com/asl101/gifs-animated/red.gif\", description: \"Sign for red.\" },\r\n  \"refrigerator\": { name: \"Refrigerator\", gif: \"https://lifeprint.com/asl101/gifs/r/refrigerator-r-e-f.gif\", description: \"Sign for refrigerator.\" },\r\n  \"ride\": { name: \"Ride\", gif: \"https://lifeprint.com/asl101/gifs/r/ride.gif\", description: \"Sign for ride.\" },\r\n  \"room\": { name: \"Room\", gif: \"https://lifeprint.com/asl101/gifs/r/room-box.gif\", description: \"Sign for room.\" },\r\n  \"sad\": { name: \"Sad\", gif: \"https://lifeprint.com/asl101/gifs/s/sad.gif\", description: \"Sign for sad.\" },\r\n  \"same\": { name: \"Same\", gif: \"https://lifeprint.com/asl101/gifs/s/same-similar.gif\", description: \"Sign for same.\" },\r\n  \"say\": { name: \"Say\", gif: \"https://asl.signlanguage.io/words/say/say-in-asl-a0a5e00000a44k0.jpg\", description: \"Sign for say.\" },\r\n  \"scissors\": { name: \"Scissors\", gif: \"https://i.makeagif.com/media/4-17-2023/pl4M4F.gif\", description: \"Sign for scissors.\" },\r\n  \"see\": { name: \"See\", gif: \"https://lifeprint.com/asl101/gifs/l/look-at-2.gif\", description: \"Sign for see.\" },\r\n  \"shhh\": { name: \"Shhh\", gif: \"https://lifeprint.com/asl101/signjpegs/s/shhh.jpg\", description: \"Sign for shhh.\" },\r\n  \"shirt\": { name: \"Shirt\", gif: \"https://lifeprint.com/asl101/gifs/s/shirt-volunteer.gif\", description: \"Sign for shirt.\" },\r\n  \"shoe\": { name: \"Shoe\", gif: \"https://media.giphy.com/media/3o7TKC4StpZKa6d2y4/giphy.gif\", description: \"Sign for shoe.\" },\r\n  \"shower\": { name: \"Shower\", gif: \"https://lifeprint.com/asl101/gifs/s/shower.gif\", description: \"Sign for shower.\" },\r\n  \"sick\": { name: \"Sick\", gif: \"https://lifeprint.com/asl101/gifs/s/sick.gif\", description: \"Sign for sick.\" },\r\n  \"sleep\": { name: \"Sleep\", gif: \"https://media4.giphy.com/media/3o7TKnRuBdakLslcaI/200.gif?cid=790b76110d8f185a9713f36dd65a0df801576e01b403c95c&rid=200.gif&ct=g\", description: \"Sign for sleep.\" },\r\n  \"sleepy\": { name: \"Sleepy\", gif: \"https://th.bing.com/th/id/R.********************************?rik=zdWvzvABcDHTdw&riu=http%3a%2f%2fwww.lifeprint.com%2fasl101%2fgifs-animated%2fsleepy.gif&ehk=zLqDFJMAs2nqG02RbbR6mEMvux4h85JGzls4uwgrePQ%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for sleepy.\" },\r\n  \"smile\": { name: \"Smile\", gif: \"https://th.bing.com/th/id/OIP.dpce-bMAh-1jorUrPQFW4AHaE-?w=263&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for smile.\" },\r\n  \"snack\": { name: \"Snack\", gif: \"https://media.giphy.com/media/26ybw1E1GTKzLuKDS/giphy.gif\", description: \"Sign for snack.\" },\r\n  \"snow\": { name: \"Snow\", gif: \"https://lifeprint.com/asl101/gifs/s/snow.gif\", description: \"Sign for snow.\" },\r\n  \"stairs\": { name: \"Stairs\", gif: \"https://th.bing.com/th/id/OIP.8BtYhPXXDQHRqodMyyy3HgHaEc?w=288&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for stairs.\" },\r\n  \"stay\": { name: \"Stay\", gif: \"https://i.pinimg.com/originals/f5/29/8e/f5298eaa46b91cd6de2a32bd76aadffc.gif\", description: \"Sign for stay.\" },\r\n  \"sticky\": { name: \"Sticky\", gif: \"https://th.bing.com/th/id/OIP.fffIgrX_DBAjxGMkskvTvQHaE-?w=240&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for sticky.\" },\r\n  \"store\": { name: \"Store\", gif: \"https://th.bing.com/th/id/R.********************************?rik=x7oUPJGckc7QDg&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fstore.gif&ehk=P7beooAyFUst%2bbVtqIqINeQGP0%2bIUlNSPXc1Du5zWfQ%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for store.\" },\r\n  \"story\": { name: \"Story\", gif: \"https://lifeprint.com/asl101/gifs/s/story.gif\", description: \"Sign for story.\" },\r\n  \"stuck\": { name: \"Stuck\", gif: \"https://lifeprint.com/asl101/signjpegs/s/stuck.2.jpg\", description: \"Sign for stuck.\" },\r\n  \"sun\": { name: \"Sun\", gif: \"https://media.giphy.com/media/3o6Zt7merN2zxEtNRK/giphy.gif\", description: \"Sign for sun.\" },\r\n  \"table\": { name: \"Table\", gif: \"https://lifeprint.com/asl101/gifs/t/table.gif\", description: \"Sign for table.\" },\r\n  \"talk\": { name: \"Talk\", gif: \"https://lifeprint.com/asl101/gifs/t/talk.gif\", description: \"Sign for talk.\" },\r\n  \"taste\": { name: \"Taste\", gif: \"https://lifeprint.com/asl101/gifs/t/taste.gif\", description: \"Sign for taste.\" },\r\n  \"thankyou\": { name: \"Thank You\", gif: \"https://lifeprint.com/asl101/gifs/t/thank-you.gif\", description: \"Sign for thank you.\" },\r\n  \"that\": { name: \"That\", gif: \"https://i.ytimg.com/vi/81Wr75AFDnQ/maxresdefault.jpg\", description: \"Sign for that.\" },\r\n  \"there\": { name: \"There\", gif: \"https://lifeprint.com/asl101/gifs-animated/there.gif\", description: \"Sign for there.\" },\r\n  \"think\": { name: \"Think\", gif: \"https://lifeprint.com/asl101/gifs/t/think.gif\", description: \"Sign for think.\" },\r\n  \"thirsty\": { name: \"Thirsty\", gif: \"https://media.giphy.com/media/l3vR0sYheBulL1P7W/giphy.gif\", description: \"Sign for thirsty.\" },\r\n  \"tiger\": { name: \"Tiger\", gif: \"https://lifeprint.com/asl101/gifs/t/tiger.gif\", description: \"Sign for tiger.\" },\r\n  \"time\": { name: \"Time\", gif: \"https://lifeprint.com/asl101/gifs/t/time-1.gif\", description: \"Sign for time.\" },\r\n  \"tomorrow\": { name: \"Tomorrow\", gif: \"https://lifeprint.com/asl101/gifs/t/tomorrow.gif\", description: \"Sign for tomorrow.\" },\r\n  \"tongue\": { name: \"Tongue\", gif: \"https://th.bing.com/th/id/R.********************************?rik=ZJJ2Ixdj0l0b5A&riu=http%3a%2f%2fwww.aslsearch.com%2fsigns%2fimages%2ftongue.jpg&ehk=MxZVUjfqPa3klIauPGpReg%2fYgnJUyIjlxOOvCYYG0hc%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for tongue.\" },\r\n  \"tooth\": { name: \"Tooth\", gif: \"https://th.bing.com/th/id/R.********************************?rik=ZF%2fsFUXvt5czGA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fsignjpegs%2ft%2fteeth1.jpg&ehk=vI5eDlD4HZWXhK1PQOQz4nA5e6oguHgeXqDo%2fcdcWg4%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for tooth.\" },\r\n  \"toothbrush\": { name: \"Toothbrush\", gif: \"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia.giphy.com%2fmedia%2fl3vR0Rq2HVL2KHLUI%2fgiphy.gif&ehk=eC0Sq9sHjrrOrkyJvOogQbXVkTOL5OPCeyVymejL0RU%3d\", description: \"Sign for toothbrush.\" },\r\n  \"touch\": { name: \"Touch\", gif: \"https://th.bing.com/th/id/OIP.imGRfqjCtcHhof6Lc_0QJQHaE-?w=230&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for touch.\" },\r\n  \"toy\": { name: \"Toy\", gif: \"https://lifeprint.com/asl101/gifs-animated/play-02.gif\", description: \"Sign for toy.\" },\r\n  \"tree\": { name: \"Tree\", gif: \"https://lifeprint.com/asl101/gifs-animated/tree.gif\", description: \"Sign for tree.\" },\r\n  \"uncle\": { name: \"Uncle\", gif: \"https://lifeprint.com/asl101/gifs/u/uncle.gif\", description: \"Sign for uncle.\" },\r\n  \"underwear\": { name: \"Underwear\", gif: \"https://th.bing.com/th/id/OIP.c8g9T_lOhbZWRvKAA12J8wHaEO?pid=ImgDet&w=310&h=177&rs=1\", description: \"Sign for underwear.\" },\r\n  \"up\": { name: \"Up\", gif: \"https://www.babysignlanguage.com/signs/up.gif\", description: \"Sign for up.\" },\r\n  \"vacuum\": { name: \"Vacuum\", gif: \"https://www.babysignlanguage.com/signs/vacuum.gif\", description: \"Sign for vacuum.\" },\r\n  \"wait\": { name: \"Wait\", gif: \"https://lifeprint.com/asl101/gifs/w/wait.gif\", description: \"Sign for wait.\" },\r\n  \"wake\": { name: \"Wake\", gif: \"https://lifeprint.com/asl101/gifs/w/wake-up.gif\", description: \"Sign for wake.\" },\r\n  \"water\": { name: \"Water\", gif: \"https://lifeprint.com/asl101/gifs/w/water-2.gif\", description: \"Sign for water.\" },\r\n  \"wet\": { name: \"Wet\", gif: \"https://www.babysignlanguage.com/signs/wet.gif\", description: \"Sign for wet.\" },\r\n  \"weus\": { name: \"We/Us\", gif: \"https://lifeprint.com/asl101/gifs/w/we-us.gif\", description: \"Sign for we/us.\" },\r\n  \"where\": { name: \"Where\", gif: \"https://lifeprint.com/asl101/gifs/w/where.gif\", description: \"Sign for where.\" },\r\n  \"white\": { name: \"White\", gif: \"https://lifeprint.com/asl101/gifs/w/white.gif\", description: \"Sign for white.\" },\r\n  \"who\": { name: \"Who\", gif: \"https://lifeprint.com/asl101/gifs/w/who.gif\", description: \"Sign for who.\" },\r\n  \"why\": { name: \"Why\", gif: \"https://lifeprint.com/asl101/gifs/w/why.gif\", description: \"Sign for why.\" },\r\n  \"will\": { name: \"Will\", gif: \"https://lifeprint.com/asl101/gifs/f/future.gif\", description: \"Sign for will.\" },\r\n  \"wolf\": { name: \"Wolf\", gif: \"https://lifeprint.com/asl101/gifs/w/wolf-side-view.gif\", description: \"Sign for wolf.\" },\r\n  \"yellow\": { name: \"Yellow\", gif: \"https://lifeprint.com/asl101/gifs/y/yellow.gif\", description: \"Sign for yellow.\" },\r\n  \"yes\": { name: \"Yes\", gif: \"https://media.tenor.com/oYIirlyIih0AAAAC/yes-asl.gif\", description: \"Sign for yes.\" },\r\n  \"yesterday\": { name: \"Yesterday\", gif: \"https://lifeprint.com/asl101/gifs/y/yesterday.gif\", description: \"Sign for yesterday.\" },\r\n  \"yourself\": { name: \"Yourself\", gif: \"https://www.lifeprint.com/asl101/gifs/s/self-myself.gif\", description: \"Sign for yourself.\" },\r\n  \"yucky\": { name: \"Yucky\", gif: \"https://i.pinimg.com/originals/7f/66/7f/7f667f7eeb92c994829dcaf52c5bcf2d.gif\", description: \"Sign for yucky.\" },\r\n  \"zebra\": { name: \"Zebra\", gif: \"https://lifeprint.com/asl101/gifs/z/zebra-stripes-two-hands.gif\", description: \"Sign for zebra.\" },\r\n  \"zipper\": { name: \"Zipper\", gif: \"https://th.bing.com/th/id/R.********************************?rik=qPRTVGd2SzUBxw&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fzipper.gif&ehk=IGx68sSokNwU21zu3Z2D%2blmeehKYxpSNhX2VnrvQqYE%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for zipper.\" }\r\n};\r\n\r\nconst TrainingPage = ({ onBackToHome }) => {\r\n  const [currentSign, setCurrentSign] = useState('hello');\r\n  const [status, setStatus] = useState('');\r\n  const [isCapturing, setIsCapturing] = useState(false);\r\n  // Auto-recording is always on, no need for mode toggle\r\n  // eslint-disable-next-line no-unused-vars\r\n  const [recordedVideos, setRecordedVideos] = useState([]);\r\n\r\n\r\n\r\n  const webcamRef = useRef(null);\r\n  const autoRecordTimeoutRef = useRef(null);\r\n  const matchCountRef = useRef(0);\r\n\r\n  // Use sign detection hook\r\n  const {\r\n    isConnected,\r\n    prediction,\r\n    isAIRecording,\r\n    recordingStatus,\r\n    signMatched,\r\n    targetSign,\r\n    startRecording: startAIRecording,\r\n    stopRecording: stopAIRecording,\r\n    startFrameCapture,\r\n    retryConnection\r\n  } = useSignDetection();\r\n\r\n  const handleSignChange = useCallback((event) => {\r\n    setCurrentSign(event.target.value);\r\n  }, []);\r\n\r\n  const startDetection = useCallback(() => {\r\n    if (!webcamRef.current) {\r\n      setStatus('Camera not available');\r\n      return;\r\n    }\r\n\r\n    setIsCapturing(true);\r\n    startFrameCapture(webcamRef, 100); // Send frame every 100ms\r\n    setStatus('AI detection started');\r\n  }, [startFrameCapture]);\r\n\r\n  const startManualRecording = useCallback(() => {\r\n    if (!isConnected) {\r\n      setStatus('AI backend not connected');\r\n      return;\r\n    }\r\n\r\n    if (!webcamRef.current) {\r\n      setStatus('Camera not available');\r\n      return;\r\n    }\r\n\r\n    if (isAIRecording) {\r\n      setStatus('Already recording...');\r\n      return;\r\n    }\r\n\r\n    // Start manual 3-second recording\r\n    setStatus(`Manual recording ${signLanguageData[currentSign].name} for 3 seconds...`);\r\n    startAIRecording(signLanguageData[currentSign].name);\r\n\r\n    // Auto-stop after 3 seconds\r\n    autoRecordTimeoutRef.current = setTimeout(() => {\r\n      stopAIRecording();\r\n      setStatus(`Manual recording complete! ${signLanguageData[currentSign].name} saved to recordings folder`);\r\n    }, 3000);\r\n\r\n    // Also start frame capture if not already started\r\n    if (!isCapturing) {\r\n      startDetection();\r\n    }\r\n  }, [currentSign, isConnected, isCapturing, startDetection, isAIRecording, startAIRecording, stopAIRecording]);\r\n\r\n  const stopManualRecording = useCallback(() => {\r\n    // Stop current recording\r\n    if (isAIRecording) {\r\n      stopAIRecording();\r\n    }\r\n    matchCountRef.current = 0;\r\n    if (autoRecordTimeoutRef.current) {\r\n      clearTimeout(autoRecordTimeoutRef.current);\r\n    }\r\n    setStatus('Manual recording stopped');\r\n  }, [stopAIRecording, isAIRecording]);\r\n\r\n  const downloadRecording = (video) => {\r\n    const a = document.createElement('a');\r\n    a.href = video.url;\r\n    a.download = `sign_${video.sign}_${video.timestamp}.webm`;\r\n    a.click();\r\n  };\r\n\r\n\r\n\r\n  // Auto-start detection when connected\r\n  useEffect(() => {\r\n    if (isConnected && webcamRef.current && !isCapturing) {\r\n      startDetection();\r\n    }\r\n  }, [isConnected, startDetection, isCapturing]);\r\n\r\n\r\n\r\n  // Always-on auto-recording logic - records when confidence >= 50%\r\n  useEffect(() => {\r\n    if (!prediction || !isConnected) {\r\n      matchCountRef.current = 0;\r\n      return;\r\n    }\r\n\r\n    const predictedSign = prediction.sign.toLowerCase();\r\n    const targetSignLower = signLanguageData[currentSign].name.toLowerCase();\r\n    const confidence = prediction.confidence;\r\n\r\n    // Auto-record when sign matches with >= 50% confidence\r\n    if (predictedSign === targetSignLower && confidence >= 0.5) {\r\n      matchCountRef.current += 1;\r\n\r\n      // Start recording after 2 consecutive matches to avoid false positives\r\n      if (matchCountRef.current >= 2 && !isAIRecording) {\r\n        setStatus(`Auto-recording ${signLanguageData[currentSign].name}... (${Math.round(confidence * 100)}% confidence)`);\r\n        startAIRecording(signLanguageData[currentSign].name);\r\n\r\n        // Auto-stop recording after 3 seconds\r\n        autoRecordTimeoutRef.current = setTimeout(() => {\r\n          stopAIRecording();\r\n          setStatus(`Auto-recording complete! ${signLanguageData[currentSign].name} saved to recordings folder`);\r\n          matchCountRef.current = 0;\r\n        }, 3000);\r\n      }\r\n    } else {\r\n      // Reset match count if sign doesn't match or confidence is too low\r\n      matchCountRef.current = 0;\r\n    }\r\n\r\n    return () => {\r\n      if (autoRecordTimeoutRef.current) {\r\n        clearTimeout(autoRecordTimeoutRef.current);\r\n      }\r\n    };\r\n  }, [prediction, currentSign, isAIRecording, startAIRecording, stopAIRecording, isConnected]);\r\n\r\n  return (\r\n    <TrainingContainer>\r\n      <Navigation>\r\n        <NavContainer>\r\n          <Logo>\r\n            <LogoIcon>\r\n              <Brain size={24} />\r\n            </LogoIcon>\r\n            ASL Neural\r\n          </Logo>\r\n          <BackButton onClick={onBackToHome}>\r\n            <ArrowLeft size={18} />\r\n            Back to Home\r\n          </BackButton>\r\n        </NavContainer>\r\n      </Navigation>\r\n\r\n      <MainContent>\r\n        <div style={{ textAlign: 'center', marginBottom: 'var(--space-12)' }}>\r\n          <StatusBadge>\r\n            <Eye size={16} />\r\n            Neural Vision Active\r\n          </StatusBadge>\r\n        </div>\r\n\r\n        <PageTitle>AI Training Session</PageTitle>\r\n        <PageSubtitle>\r\n          Experience real-time neural network analysis as our AI learns from your sign language practice\r\n        </PageSubtitle>\r\n\r\n        <TopControlsSection>\r\n          <ControlButton\r\n            variant=\"primary\"\r\n            compact\r\n            onClick={isAIRecording ? stopManualRecording : startManualRecording}\r\n          >\r\n            {isAIRecording ? (\r\n              <>\r\n                <Square size={16} />\r\n                Stop Recording\r\n              </>\r\n            ) : (\r\n              <>\r\n                <Play size={16} />\r\n                Record 3 Seconds\r\n              </>\r\n            )}\r\n          </ControlButton>\r\n\r\n          {!isConnected && (\r\n            <ControlButton\r\n              variant=\"retry\"\r\n              compact\r\n              onClick={retryConnection}\r\n            >\r\n              <RefreshCw size={16} />\r\n              Retry Connection\r\n            </ControlButton>\r\n          )}\r\n        </TopControlsSection>\r\n\r\n        <TrainingGrid>\r\n          <CameraSection>\r\n            <SectionTitle>\r\n              <SectionIcon>\r\n                <Camera size={24} />\r\n              </SectionIcon>\r\n              Neural Vision Feed\r\n            </SectionTitle>\r\n\r\n            <ConnectionStatus connected={isConnected}>\r\n              {isConnected ? <Wifi size={16} /> : <WifiOff size={16} />}\r\n              {isConnected ? 'AI Connected' : 'AI Disconnected'}\r\n            </ConnectionStatus>\r\n\r\n            {prediction && (\r\n              <PredictionDisplay matched={signMatched} isStale={prediction.isStale}>\r\n                <PredictionText matched={signMatched} isStale={prediction.isStale}>\r\n                  Detected: {prediction.sign}\r\n                  {prediction.isStale && ' (previous)'}\r\n                </PredictionText>\r\n                <ConfidenceBar>\r\n                  <ConfidenceFill confidence={prediction.confidence} />\r\n                </ConfidenceBar>\r\n                <div style={{ fontSize: '0.875rem', marginTop: '8px', color: 'var(--text-secondary)' }}>\r\n                  Confidence: {Math.round(prediction.confidence * 100)}%\r\n                  {signMatched && targetSign && (\r\n                    <span style={{ color: 'var(--success-600)', marginLeft: '8px' }}>\r\n                      ✓ Match! Recording...\r\n                    </span>\r\n                  )}\r\n                  {!isAIRecording && (\r\n                    <div style={{ color: 'var(--primary-600)', marginTop: '4px' }}>\r\n                      🎯 Auto-recording active: Perform \"{signLanguageData[currentSign].name}\" sign (≥50% confidence)\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </PredictionDisplay>\r\n            )}\r\n\r\n            {!prediction && (\r\n              <PredictionDisplay>\r\n                <PredictionText>\r\n                  🎯 Ready to detect \"{signLanguageData[currentSign].name}\"\r\n                </PredictionText>\r\n                <div style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>\r\n                  Auto-recording is active. Perform the sign with ≥50% confidence to trigger recording.\r\n                </div>\r\n              </PredictionDisplay>\r\n            )}\r\n            <WebcamContainer>\r\n              <StyledWebcam\r\n                ref={webcamRef}\r\n                audio={false}\r\n                screenshotFormat=\"image/jpeg\"\r\n                videoConstraints={{\r\n                  width: 640,\r\n                  height: 480,\r\n                  facingMode: \"user\"\r\n                }}\r\n              />\r\n              <RecordingOverlay isRecording={isAIRecording}>\r\n                {isAIRecording ? (\r\n                  <>\r\n                    <div style={{\r\n                      width: '8px',\r\n                      height: '8px',\r\n                      borderRadius: '50%',\r\n                      backgroundColor: 'white',\r\n                      marginRight: '4px'\r\n                    }} />\r\n                    Recording\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <Eye size={16} />\r\n                    Ready\r\n                  </>\r\n                )}\r\n              </RecordingOverlay>\r\n            </WebcamContainer>\r\n          </CameraSection>\r\n\r\n          <SignSection>\r\n            <SectionTitle>\r\n              <SectionIcon>\r\n                <Target size={24} />\r\n              </SectionIcon>\r\n              Select a Sign\r\n            </SectionTitle>\r\n            <SignSelector\r\n              value={currentSign}\r\n              onChange={handleSignChange}\r\n              disabled={isAIRecording}\r\n            >\r\n              {Object.keys(signLanguageData).map(signKey => (\r\n                <option key={signKey} value={signKey}>\r\n                  {signLanguageData[signKey].name}\r\n                </option>\r\n              ))}\r\n            </SignSelector>\r\n            <SignDisplay>\r\n              <img\r\n                src={signLanguageData[currentSign].gif}\r\n                alt={signLanguageData[currentSign].name}\r\n                onError={(e) => {\r\n                  e.target.style.display = 'none';\r\n                  e.target.nextSibling.style.display = 'flex';\r\n                }}\r\n              />\r\n              <div style={{display: 'none', fontSize: '3rem'}}>\r\n                📷\r\n              </div>\r\n            </SignDisplay>\r\n            <SignName>{signLanguageData[currentSign].name}</SignName>\r\n            <SignDescription>\r\n              {signLanguageData[currentSign].description}\r\n            </SignDescription>\r\n          </SignSection>\r\n        </TrainingGrid>\r\n\r\n\r\n\r\n        {(status || recordingStatus) && (\r\n          <StatusMessage type={(status || recordingStatus).includes('error') ? 'error' : (status || recordingStatus).includes('success') ? 'success' : 'info'}>\r\n            {recordingStatus || status}\r\n          </StatusMessage>\r\n        )}\r\n\r\n        {recordedVideos.length > 0 && (\r\n          <RecordingsSection>\r\n            <RecordingsTitle>Your Practice Recordings</RecordingsTitle>\r\n            <RecordingsGrid>\r\n              {recordedVideos.map((video) => (\r\n                <RecordingCard key={video.id}>\r\n                  <RecordingTitle>{video.sign}</RecordingTitle>\r\n                  <RecordingTime>\r\n                    {new Date(video.timestamp).toLocaleString()}\r\n                  </RecordingTime>\r\n                  <DownloadButton onClick={() => downloadRecording(video)}>\r\n                    <Download size={16} />\r\n                    Download\r\n                  </DownloadButton>\r\n                </RecordingCard>\r\n              ))}\r\n            </RecordingsGrid>\r\n          </RecordingsSection>\r\n        )}\r\n      </MainContent>\r\n    </TrainingContainer>\r\n  );\r\n};\r\n\r\nexport default TrainingPage; "], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AAChE,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,cAAc;AACjC,SACEC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,IAAI,EACJC,MAAM,EACNC,QAAQ,EACRC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,OAAO,EACPC,SAAS,QACJ,cAAc;AACrB,SAASC,gBAAgB,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7D,MAAMC,iBAAiB,GAAGlB,MAAM,CAACmB,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAnBIF,iBAAiB;AAqBvB,MAAMG,UAAU,GAAGrB,MAAM,CAACsB,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAXIF,UAAU;AAahB,MAAMG,YAAY,GAAGxB,MAAM,CAACmB,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACM,GAAA,GAXID,YAAY;AAalB,MAAME,IAAI,GAAG1B,MAAM,CAACmB,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GAhBID,IAAI;AAkBV,MAAME,QAAQ,GAAG5B,MAAM,CAACmB,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GAfID,QAAQ;AAiBd,MAAME,UAAU,GAAG9B,MAAM,CAAC+B,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GA3BIF,UAAU;AA6BhB,MAAMG,SAAS,GAAGjC,MAAM,CAACkC,EAAE;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAfIF,SAAS;AAiBf,MAAMG,YAAY,GAAGpC,MAAM,CAACqC,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAdIF,YAAY;AAgBlB,MAAMG,WAAW,GAAGvC,MAAM,CAACmB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACqB,GAAA,GAnBID,WAAW;AAqBjB,MAAME,WAAW,GAAGzC,MAAM,CAAC0C,IAAI;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GATIF,WAAW;AAWjB,MAAMG,YAAY,GAAG5C,MAAM,CAACmB,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC0B,GAAA,GAhBID,YAAY;AAkBlB,MAAME,aAAa,GAAG9C,MAAM,CAACmB,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC4B,IAAA,GAxCID,aAAa;AA0CnB,MAAME,YAAY,GAAGhD,MAAM,CAACiD,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAdIF,YAAY;AAgBlB,MAAMG,WAAW,GAAGnD,MAAM,CAACmB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACiC,IAAA,GAfID,WAAW;AAiBjB,MAAME,eAAe,GAAGrD,MAAM,CAACmB,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmC,IAAA,GAnBID,eAAe;AAqBrB,MAAME,YAAY,GAAGvD,MAAM,CAACC,MAAM,CAAC;AACnC;AACA;AACA;AACA,CAAC;AAACuD,IAAA,GAJID,YAAY;AAMlB,MAAME,gBAAgB,GAAGzD,MAAM,CAACmB,GAAG;AACnC;AACA;AACA;AACA,gBAAgBuC,KAAK,IAAIA,KAAK,CAACC,WAAW,GACtC,kBAAkB,GAClB,mBAAmB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eACeD,KAAK,IAAIA,KAAK,CAACC,WAAW,GAAG,qBAAqB,GAAG,MAAM;AAC1E;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAvBIH,gBAAgB;AAyBtB,MAAMI,WAAW,GAAG7D,MAAM,CAACmB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC2C,IAAA,GApBID,WAAW;AAsBjB,MAAME,YAAY,GAAG/D,MAAM,CAACgE,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAlCIF,YAAY;AAoClB,MAAMG,WAAW,GAAGlE,MAAM,CAACmB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgD,IAAA,GA7BID,WAAW;AA+BjB,MAAME,QAAQ,GAAGpE,MAAM,CAACqE,EAAE;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAVIF,QAAQ;AAYd,MAAMG,eAAe,GAAGvE,MAAM,CAACqC,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmC,IAAA,GAPID,eAAe;AASrB,MAAME,kBAAkB,GAAGzE,MAAM,CAACmB,GAAG;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuD,IAAA,GAnBID,kBAAkB;AAuBxB,MAAME,aAAa,GAAG3E,MAAM,CAAC+B,MAAM;AACnC,gBAAgB2B,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,GAC9C,oBAAoB,GACpBlB,KAAK,CAACkB,OAAO,KAAK,OAAO,GACzB,oBAAoB,GACpB,mBAAmB;AACzB,YAAYlB,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,IAAIlB,KAAK,CAACkB,OAAO,KAAK,OAAO,GACvE,MAAM,GACN,gCAAgC;AACtC,WAAWlB,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,IAAIlB,KAAK,CAACkB,OAAO,KAAK,OAAO,GACtE,OAAO,GACP,qBAAqB;AAC3B,aAAalB,KAAK,IAAIA,KAAK,CAACmB,OAAO,GAC7B,+BAA+B,GAC/B,+BAA+B;AACrC;AACA;AACA,eAAenB,KAAK,IAAIA,KAAK,CAACmB,OAAO,GAAG,QAAQ,GAAG,QAAQ;AAC3D;AACA;AACA,eAAenB,KAAK,IAAIA,KAAK,CAACmB,OAAO,GAAG,OAAO,GAAG,OAAO;AACzD;AACA;AACA;AACA;AACA;AACA;AACA,eAAenB,KAAK,IAAIA,KAAK,CAACmB,OAAO,GAC7B,+BAA+B,GAC/B,+BAA+B;AACvC,iBAAiBnB,KAAK,IAAIA,KAAK,CAACmB,OAAO,GAAG,QAAQ,GAAG,MAAM;AAC3D,iBAAiBnB,KAAK,IAAIA,KAAK,CAACmB,OAAO,GAAG,OAAO,GAAG,OAAO;AAC3D;AACA;AACA,gBAAgBnB,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,IAAIlB,KAAK,CAACkB,OAAO,KAAK,OAAO,GAC3E,kBAAkB,GAClB,kBAAkB;AACxB;AACA;AACA;AACA,kBAAkBlB,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,IAAIlB,KAAK,CAACkB,OAAO,KAAK,OAAO,GAC3E,kBAAkB,GAClB,kBAAkB;AAC1B,kBAAkBlB,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,GAC9C,oBAAoB,GACpBlB,KAAK,CAACkB,OAAO,KAAK,OAAO,GACzB,oBAAoB,GACpB,gBAAgB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiBlB,KAAK,IAAIA,KAAK,CAACmB,OAAO,GAAG,OAAO,GAAG,OAAO;AAC3D;AACA,CAAC;AAACC,IAAA,GA5DIH,aAAa;AA8DnB,MAAMI,aAAa,GAAG/E,MAAM,CAACmB,GAAG;AAChC;AACA;AACA;AACA;AACA,gBAAgBuC,KAAK,IACjBA,KAAK,CAACsB,IAAI,KAAK,SAAS,GAAG,oBAAoB,GAC/CtB,KAAK,CAACsB,IAAI,KAAK,OAAO,GAAG,kBAAkB,GAC3C,oBAAoB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,CACC;AAACC,IAAA,GAhBIF,aAAa;AAkBnB,MAAMG,iBAAiB,GAAGlF,MAAM,CAACmB,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgE,IAAA,GARID,iBAAiB;AAUvB,MAAME,eAAe,GAAGpF,MAAM,CAACqE,EAAE;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgB,IAAA,GAPID,eAAe;AASrB,MAAME,cAAc,GAAGtF,MAAM,CAACmB,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACoE,IAAA,GATID,cAAc;AAWpB,MAAME,aAAa,GAAGxF,MAAM,CAACmB,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsE,IAAA,GAbID,aAAa;AAenB,MAAME,cAAc,GAAG1F,MAAM,CAACqC,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsD,IAAA,GANID,cAAc;AAQpB,MAAME,aAAa,GAAG5F,MAAM,CAACqC,CAAC;AAC9B;AACA;AACA;AACA,CAAC;AAACwD,IAAA,GAJID,aAAa;AAMnB,MAAME,cAAc,GAAG9F,MAAM,CAAC+B,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgE,IAAA,GAnBID,cAAc;AAqBpB,MAAME,iBAAiB,GAAGhG,MAAM,CAACmB,GAAG;AACpC;AACA,sBAAsBuC,KAAK,IAAI;EAC3B,IAAIA,KAAK,CAACuC,OAAO,EAAE,OAAO,oBAAoB;EAC9C,IAAIvC,KAAK,CAACwC,OAAO,EAAE,OAAO,oBAAoB;EAC9C,OAAO,qBAAqB;AAC9B,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA,aAAaxC,KAAK,IAAIA,KAAK,CAACwC,OAAO,GAAG,GAAG,GAAG,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA,IAAIxC,KAAK,IAAIA,KAAK,CAACuC,OAAO,IAAI;AAC9B;AACA;AACA;AACA,GAAG;AACH;AACA,IAAIvC,KAAK,IAAIA,KAAK,CAACwC,OAAO,IAAI;AAC9B;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAvCIH,iBAAiB;AAyCvB,MAAMI,cAAc,GAAGpG,MAAM,CAACmB,GAAG;AACjC;AACA;AACA,WAAWuC,KAAK,IAAI;EAChB,IAAIA,KAAK,CAACuC,OAAO,EAAE,OAAO,oBAAoB;EAC9C,IAAIvC,KAAK,CAACwC,OAAO,EAAE,OAAO,oBAAoB;EAC9C,OAAO,qBAAqB;AAC9B,CAAC;AACH;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,IAAA,GAbID,cAAc;AAepB,MAAME,aAAa,GAAGtG,MAAM,CAACmB,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACoF,IAAA,GAPID,aAAa;AASnB,MAAME,cAAc,GAAGxG,MAAM,CAACmB,GAAG;AACjC;AACA,gBAAgBuC,KAAK,IAAI;EACrB,IAAIA,KAAK,CAAC+C,UAAU,GAAG,GAAG,EAAE,OAAO,oBAAoB;EACvD,IAAI/C,KAAK,CAAC+C,UAAU,GAAG,GAAG,EAAE,OAAO,oBAAoB;EACvD,OAAO,kBAAkB;AAC3B,CAAC;AACH,WAAW/C,KAAK,IAAKA,KAAK,CAAC+C,UAAU,GAAG,GAAI;AAC5C;AACA,CAAC;AAACC,IAAA,GATIF,cAAc;AAWpB,MAAMG,gBAAgB,GAAG3G,MAAM,CAACmB,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBuC,KAAK,IAAIA,KAAK,CAACkD,SAAS,GAAG,mBAAmB,GAAG,iBAAiB;AAClF,WAAWlD,KAAK,IAAIA,KAAK,CAACkD,SAAS,GAAG,oBAAoB,GAAG,kBAAkB;AAC/E,sBAAsBlD,KAAK,IAAIA,KAAK,CAACkD,SAAS,GAAG,oBAAoB,GAAG,kBAAkB;AAC1F,CAAC;;AAID;AAAAC,IAAA,GAfMF,gBAAgB;AAgBtB,MAAMG,gBAAgB,GAAG;EACvB,IAAI,EAAE;IAAEC,IAAI,EAAE,IAAI;IAAEC,GAAG,EAAE,uDAAuD;IAAEC,WAAW,EAAE;EAAe,CAAC;EAC/G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,0DAA0D;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC3H,UAAU,EAAE;IAAEF,IAAI,EAAE,UAAU;IAAEC,GAAG,EAAE,6DAA6D;IAAEC,WAAW,EAAE;EAAqB,CAAC;EACvI,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,wDAAwD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACnH,WAAW,EAAE;IAAEF,IAAI,EAAE,WAAW;IAAEC,GAAG,EAAE,8DAA8D;IAAEC,WAAW,EAAE;EAAsB,CAAC;EAC3I,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAmB,CAAC;EAC/H,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,wDAAwD;IAAEC,WAAW,EAAE;EAAoB,CAAC;EAC/H,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,wDAAwD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACnH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC5H,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC7J,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8EAA8E;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5I,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnK,UAAU,EAAE;IAAEF,IAAI,EAAE,UAAU;IAAEC,GAAG,EAAE,6DAA6D;IAAEC,WAAW,EAAE;EAAqB,CAAC;EACvI,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,wDAAwD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACnH,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAoB,CAAC;EAClI,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACzH,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,wDAAwD;IAAEC,WAAW,EAAE;EAAoB,CAAC;EAC/H,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,wDAAwD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACnH,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,4DAA4D;IAAEC,WAAW,EAAE;EAAoB,CAAC;EACnI,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,wDAAwD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACnH,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACtK,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAmB,CAAC;EAC/H,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAmB,CAAC;EAC/H,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,yDAAyD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACvH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,iOAAiO;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAClS,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAChK,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,yDAAyD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACvH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,yDAAyD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACvH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACzH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,wDAAwD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACnH,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,4DAA4D;IAAEC,WAAW,EAAE;EAAoB,CAAC;EACnI,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,0DAA0D;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC3H,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,wDAAwD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACnH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,8DAA8D;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACzH,aAAa,EAAE;IAAEF,IAAI,EAAE,eAAe;IAAEC,GAAG,EAAE,yDAAyD;IAAEC,WAAW,EAAE;EAA0B,CAAC;EAChJ,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,wDAAwD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACnH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC7J,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAmB,CAAC;EAC/H,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,uDAAuD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAClH,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,8NAA8N;IAAEC,WAAW,EAAE;EAAmB,CAAC;EAClS,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnK,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,0DAA0D;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC3H,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,0DAA0D;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC3H,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,yDAAyD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACvH,WAAW,EAAE;IAAEF,IAAI,EAAE,WAAW;IAAEC,GAAG,EAAE,kOAAkO;IAAEC,WAAW,EAAE;EAAsB,CAAC;EAC/S,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,4DAA4D;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC7H,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,4RAA4R;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC7V,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAmB,CAAC;EAC/H,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,8EAA8E;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC/I,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnK,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,wDAAwD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACnH,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAmB,CAAC;EAC/H,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,wDAAwD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACnH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC7J,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,yDAAyD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACvH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACtH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,0MAA0M;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC3Q,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,8EAA8E;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC/I,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,2NAA2N;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACtR,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,qMAAqM;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACnQ,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,uDAAuD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EAC3H,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAChK,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACtK,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,qDAAqD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACtH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAChK,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC7J,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,yNAAyN;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACvR,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,sDAAsD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACjH,UAAU,EAAE;IAAEF,IAAI,EAAE,UAAU;IAAEC,GAAG,EAAE,yDAAyD;IAAEC,WAAW,EAAE;EAAqB,CAAC;EACnI,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC/G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,sDAAsD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACvH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,wDAAwD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACnH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,yDAAyD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACvH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,yDAAyD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACvH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,wNAAwN;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACtR,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8EAA8E;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5I,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAChK,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,uDAAuD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACrH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,6NAA6N;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC3R,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACpH,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,yNAAyN;IAAEC,WAAW,EAAE;EAAmB,CAAC;EAC7R,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,oDAAoD;IAAEC,WAAW,EAAE;EAAoB,CAAC;EAC3H,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8EAA8E;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5I,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAChK,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,4DAA4D;IAAEC,WAAW,EAAE;EAAmB,CAAC;EAChI,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8EAA8E;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5I,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,aAAa,EAAE;IAAEF,IAAI,EAAE,cAAc;IAAEC,GAAG,EAAE,0DAA0D;IAAEC,WAAW,EAAE;EAAyB,CAAC;EAC/I,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACzH,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,8NAA8N;IAAEC,WAAW,EAAE;EAAoB,CAAC;EACrS,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,iDAAiD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,qDAAqD;IAAEC,WAAW,EAAE;EAAoB,CAAC;EAC5H,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,wNAAwN;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACtR,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,+DAA+D;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC7H,aAAa,EAAE;IAAEF,IAAI,EAAE,cAAc;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAyB,CAAC;EACpI,IAAI,EAAE;IAAEF,IAAI,EAAE,IAAI;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAe,CAAC;EACnH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,kDAAkD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnH,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,qDAAqD;IAAEC,WAAW,EAAE;EAAoB,CAAC;EAC5H,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAoB,CAAC;EACzK,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+NAA+N;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChS,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,8EAA8E;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC/I,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,4DAA4D;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC1H,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,qIAAqI;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACtM,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC7J,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACzH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,yNAAyN;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACvR,QAAQ,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,iDAAiD;IAAEC,WAAW,EAAE;EAAoB,CAAC;EACvH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8NAA8N;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5R,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,0DAA0D;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACxH,YAAY,EAAE;IAAEF,IAAI,EAAE,YAAY;IAAEC,GAAG,EAAE,gOAAgO;IAAEC,WAAW,EAAE;EAAuB,CAAC;EAChT,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,4DAA4D;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC7H,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,mIAAmI;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC9L,SAAS,EAAE;IAAEF,IAAI,EAAE,WAAW;IAAEC,GAAG,EAAE,mDAAmD;IAAEC,WAAW,EAAE;EAAsB,CAAC;EAC9H,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,qOAAqO;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACnS,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC5H,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,4DAA4D;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACvH,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAmB,CAAC;EAC/H,UAAU,EAAE;IAAEF,IAAI,EAAE,WAAW;IAAEC,GAAG,EAAE,4DAA4D;IAAEC,WAAW,EAAE;EAAsB,CAAC;EACxI,IAAI,EAAE;IAAEF,IAAI,EAAE,IAAI;IAAEC,GAAG,EAAE,4CAA4C;IAAEC,WAAW,EAAE;EAAe,CAAC;EACpG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,kDAAkD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACtH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,qDAAqD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACnH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8BAA8B;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5F,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,uDAAuD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACxH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,oIAAoI;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACrM,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAChK,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACtK,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8NAA8N;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5R,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,qDAAqD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACnH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAChK,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,QAAQ,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAoB,CAAC;EACxK,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,wDAAwD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EAC5H,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,yNAAyN;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACvR,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,8DAA8D;IAAEC,WAAW,EAAE;EAAoB,CAAC;EACrI,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,sDAAsD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACvH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACpH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,IAAI,EAAE;IAAEF,IAAI,EAAE,IAAI;IAAEC,GAAG,EAAE,uDAAuD;IAAEC,WAAW,EAAE;EAAe,CAAC;EAC/G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,sDAAsD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACpH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,2OAA2O;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACtS,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAChK,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,IAAI,EAAE;IAAEF,IAAI,EAAE,IAAI;IAAEC,GAAG,EAAE,iDAAiD;IAAEC,WAAW,EAAE;EAAe,CAAC;EACzG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAChK,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACpH,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,iDAAiD;IAAEC,WAAW,EAAE;EAAoB,CAAC;EACxH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,iDAAiD;IAAEC,WAAW,EAAE;EAAoB,CAAC;EACxH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,kDAAkD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACtH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACpH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,8DAA8D;IAAEC,WAAW,EAAE;EAAmB,CAAC;EAClI,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gGAAgG;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACpK,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAChK,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnK,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,iDAAiD;IAAEC,WAAW,EAAE;EAAoB,CAAC;EACxH,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,mDAAmD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACvH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,6GAA6G;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACjL,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,yDAAyD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC1H,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,8EAA8E;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC/I,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,oDAAoD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC/G,cAAc,EAAE;IAAEF,IAAI,EAAE,cAAc;IAAEC,GAAG,EAAE,4DAA4D;IAAEC,WAAW,EAAE;EAAyB,CAAC;EAClJ,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,kDAAkD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAChH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,sDAAsD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACpH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,sEAAsE;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACjI,UAAU,EAAE;IAAEF,IAAI,EAAE,UAAU;IAAEC,GAAG,EAAE,mDAAmD;IAAEC,WAAW,EAAE;EAAqB,CAAC;EAC7H,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,mDAAmD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC9G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,mDAAmD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACjH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,yDAAyD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC1H,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,4DAA4D;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC1H,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACpH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,iIAAiI;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAClM,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,kOAAkO;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACtS,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnK,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC5H,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACtK,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8EAA8E;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5I,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACtK,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,2NAA2N;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC5R,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,sDAAsD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACvH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,4DAA4D;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACvH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,UAAU,EAAE;IAAEF,IAAI,EAAE,WAAW;IAAEC,GAAG,EAAE,mDAAmD;IAAEC,WAAW,EAAE;EAAsB,CAAC;EAC/H,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,sDAAsD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACpH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,sDAAsD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACvH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAoB,CAAC;EAClI,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC9G,UAAU,EAAE;IAAEF,IAAI,EAAE,UAAU;IAAEC,GAAG,EAAE,kDAAkD;IAAEC,WAAW,EAAE;EAAqB,CAAC;EAC5H,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,4NAA4N;IAAEC,WAAW,EAAE;EAAmB,CAAC;EAChS,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,kOAAkO;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnS,YAAY,EAAE;IAAEF,IAAI,EAAE,YAAY;IAAEC,GAAG,EAAE,uMAAuM;IAAEC,WAAW,EAAE;EAAuB,CAAC;EACvR,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnK,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,wDAAwD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACnH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,qDAAqD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACnH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,WAAW,EAAE;IAAEF,IAAI,EAAE,WAAW;IAAEC,GAAG,EAAE,sFAAsF;IAAEC,WAAW,EAAE;EAAsB,CAAC;EACnK,IAAI,EAAE;IAAEF,IAAI,EAAE,IAAI;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAe,CAAC;EACvG,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,mDAAmD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACvH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,iDAAiD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,iDAAiD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAClH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,MAAM,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC/G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC9G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,wDAAwD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACtH,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACpH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,sDAAsD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACjH,WAAW,EAAE;IAAEF,IAAI,EAAE,WAAW;IAAEC,GAAG,EAAE,mDAAmD;IAAEC,WAAW,EAAE;EAAsB,CAAC;EAChI,UAAU,EAAE;IAAEF,IAAI,EAAE,UAAU;IAAEC,GAAG,EAAE,yDAAyD;IAAEC,WAAW,EAAE;EAAqB,CAAC;EACnI,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,8EAA8E;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC/I,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,iEAAiE;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAClI,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,0NAA0N;IAAEC,WAAW,EAAE;EAAmB;AAC/R,CAAC;AAED,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1H,QAAQ,CAAC,OAAO,CAAC;EACvD,MAAM,CAAC2H,MAAM,EAAEC,SAAS,CAAC,GAAG5H,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC6H,WAAW,EAAEC,cAAc,CAAC,GAAG9H,QAAQ,CAAC,KAAK,CAAC;EACrD;EACA;EACA,MAAM,CAAC+H,cAAc,EAAEC,iBAAiB,CAAC,GAAGhI,QAAQ,CAAC,EAAE,CAAC;EAIxD,MAAMiI,SAAS,GAAGhI,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMiI,oBAAoB,GAAGjI,MAAM,CAAC,IAAI,CAAC;EACzC,MAAMkI,aAAa,GAAGlI,MAAM,CAAC,CAAC,CAAC;;EAE/B;EACA,MAAM;IACJmI,WAAW;IACXC,UAAU;IACVC,aAAa;IACbC,eAAe;IACfC,WAAW;IACXC,UAAU;IACVC,cAAc,EAAEC,gBAAgB;IAChCC,aAAa,EAAEC,eAAe;IAC9BC,iBAAiB;IACjBC;EACF,CAAC,GAAG9H,gBAAgB,CAAC,CAAC;EAEtB,MAAM+H,gBAAgB,GAAG9I,WAAW,CAAE+I,KAAK,IAAK;IAC9CvB,cAAc,CAACuB,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACpC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,cAAc,GAAGlJ,WAAW,CAAC,MAAM;IACvC,IAAI,CAAC+H,SAAS,CAACoB,OAAO,EAAE;MACtBzB,SAAS,CAAC,sBAAsB,CAAC;MACjC;IACF;IAEAE,cAAc,CAAC,IAAI,CAAC;IACpBgB,iBAAiB,CAACb,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC;IACnCL,SAAS,CAAC,sBAAsB,CAAC;EACnC,CAAC,EAAE,CAACkB,iBAAiB,CAAC,CAAC;EAEvB,MAAMQ,oBAAoB,GAAGpJ,WAAW,CAAC,MAAM;IAC7C,IAAI,CAACkI,WAAW,EAAE;MAChBR,SAAS,CAAC,0BAA0B,CAAC;MACrC;IACF;IAEA,IAAI,CAACK,SAAS,CAACoB,OAAO,EAAE;MACtBzB,SAAS,CAAC,sBAAsB,CAAC;MACjC;IACF;IAEA,IAAIU,aAAa,EAAE;MACjBV,SAAS,CAAC,sBAAsB,CAAC;MACjC;IACF;;IAEA;IACAA,SAAS,CAAC,oBAAoBV,gBAAgB,CAACO,WAAW,CAAC,CAACN,IAAI,mBAAmB,CAAC;IACpFwB,gBAAgB,CAACzB,gBAAgB,CAACO,WAAW,CAAC,CAACN,IAAI,CAAC;;IAEpD;IACAe,oBAAoB,CAACmB,OAAO,GAAGE,UAAU,CAAC,MAAM;MAC9CV,eAAe,CAAC,CAAC;MACjBjB,SAAS,CAAC,8BAA8BV,gBAAgB,CAACO,WAAW,CAAC,CAACN,IAAI,6BAA6B,CAAC;IAC1G,CAAC,EAAE,IAAI,CAAC;;IAER;IACA,IAAI,CAACU,WAAW,EAAE;MAChBuB,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAC3B,WAAW,EAAEW,WAAW,EAAEP,WAAW,EAAEuB,cAAc,EAAEd,aAAa,EAAEK,gBAAgB,EAAEE,eAAe,CAAC,CAAC;EAE7G,MAAMW,mBAAmB,GAAGtJ,WAAW,CAAC,MAAM;IAC5C;IACA,IAAIoI,aAAa,EAAE;MACjBO,eAAe,CAAC,CAAC;IACnB;IACAV,aAAa,CAACkB,OAAO,GAAG,CAAC;IACzB,IAAInB,oBAAoB,CAACmB,OAAO,EAAE;MAChCI,YAAY,CAACvB,oBAAoB,CAACmB,OAAO,CAAC;IAC5C;IACAzB,SAAS,CAAC,0BAA0B,CAAC;EACvC,CAAC,EAAE,CAACiB,eAAe,EAAEP,aAAa,CAAC,CAAC;EAEpC,MAAMoB,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMC,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGJ,KAAK,CAACK,GAAG;IAClBJ,CAAC,CAACK,QAAQ,GAAG,QAAQN,KAAK,CAACO,IAAI,IAAIP,KAAK,CAACQ,SAAS,OAAO;IACzDP,CAAC,CAACQ,KAAK,CAAC,CAAC;EACX,CAAC;;EAID;EACAjK,SAAS,CAAC,MAAM;IACd,IAAIiI,WAAW,IAAIH,SAAS,CAACoB,OAAO,IAAI,CAACxB,WAAW,EAAE;MACpDuB,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAChB,WAAW,EAAEgB,cAAc,EAAEvB,WAAW,CAAC,CAAC;;EAI9C;EACA1H,SAAS,CAAC,MAAM;IACd,IAAI,CAACkI,UAAU,IAAI,CAACD,WAAW,EAAE;MAC/BD,aAAa,CAACkB,OAAO,GAAG,CAAC;MACzB;IACF;IAEA,MAAMgB,aAAa,GAAGhC,UAAU,CAAC6B,IAAI,CAACI,WAAW,CAAC,CAAC;IACnD,MAAMC,eAAe,GAAGrD,gBAAgB,CAACO,WAAW,CAAC,CAACN,IAAI,CAACmD,WAAW,CAAC,CAAC;IACxE,MAAMzD,UAAU,GAAGwB,UAAU,CAACxB,UAAU;;IAExC;IACA,IAAIwD,aAAa,KAAKE,eAAe,IAAI1D,UAAU,IAAI,GAAG,EAAE;MAC1DsB,aAAa,CAACkB,OAAO,IAAI,CAAC;;MAE1B;MACA,IAAIlB,aAAa,CAACkB,OAAO,IAAI,CAAC,IAAI,CAACf,aAAa,EAAE;QAChDV,SAAS,CAAC,kBAAkBV,gBAAgB,CAACO,WAAW,CAAC,CAACN,IAAI,QAAQqD,IAAI,CAACC,KAAK,CAAC5D,UAAU,GAAG,GAAG,CAAC,eAAe,CAAC;QAClH8B,gBAAgB,CAACzB,gBAAgB,CAACO,WAAW,CAAC,CAACN,IAAI,CAAC;;QAEpD;QACAe,oBAAoB,CAACmB,OAAO,GAAGE,UAAU,CAAC,MAAM;UAC9CV,eAAe,CAAC,CAAC;UACjBjB,SAAS,CAAC,4BAA4BV,gBAAgB,CAACO,WAAW,CAAC,CAACN,IAAI,6BAA6B,CAAC;UACtGgB,aAAa,CAACkB,OAAO,GAAG,CAAC;QAC3B,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,MAAM;MACL;MACAlB,aAAa,CAACkB,OAAO,GAAG,CAAC;IAC3B;IAEA,OAAO,MAAM;MACX,IAAInB,oBAAoB,CAACmB,OAAO,EAAE;QAChCI,YAAY,CAACvB,oBAAoB,CAACmB,OAAO,CAAC;MAC5C;IACF,CAAC;EACH,CAAC,EAAE,CAAChB,UAAU,EAAEZ,WAAW,EAAEa,aAAa,EAAEK,gBAAgB,EAAEE,eAAe,EAAET,WAAW,CAAC,CAAC;EAE5F,oBACEjH,OAAA,CAACG,iBAAiB;IAAAoJ,QAAA,gBAChBvJ,OAAA,CAACM,UAAU;MAAAiJ,QAAA,eACTvJ,OAAA,CAACS,YAAY;QAAA8I,QAAA,gBACXvJ,OAAA,CAACW,IAAI;UAAA4I,QAAA,gBACHvJ,OAAA,CAACa,QAAQ;YAAA0I,QAAA,eACPvJ,OAAA,CAACb,KAAK;cAACqK,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,cAEb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP5J,OAAA,CAACe,UAAU;UAAC8I,OAAO,EAAEzD,YAAa;UAAAmD,QAAA,gBAChCvJ,OAAA,CAACX,SAAS;YAACmK,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEzB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEb5J,OAAA,CAAC0B,WAAW;MAAA6H,QAAA,gBACVvJ,OAAA;QAAK8J,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAkB,CAAE;QAAAT,QAAA,eACnEvJ,OAAA,CAACwB,WAAW;UAAA+H,QAAA,gBACVvJ,OAAA,CAACP,GAAG;YAAC+J,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,wBAEnB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eAEN5J,OAAA,CAACkB,SAAS;QAAAqI,QAAA,EAAC;MAAmB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eAC1C5J,OAAA,CAACqB,YAAY;QAAAkI,QAAA,EAAC;MAEd;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eAEf5J,OAAA,CAAC0D,kBAAkB;QAAA6F,QAAA,gBACjBvJ,OAAA,CAAC4D,aAAa;UACZC,OAAO,EAAC,SAAS;UACjBC,OAAO;UACP+F,OAAO,EAAE1C,aAAa,GAAGkB,mBAAmB,GAAGF,oBAAqB;UAAAoB,QAAA,EAEnEpC,aAAa,gBACZnH,OAAA,CAAAE,SAAA;YAAAqJ,QAAA,gBACEvJ,OAAA,CAACT,MAAM;cAACiK,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kBAEtB;UAAA,eAAE,CAAC,gBAEH5J,OAAA,CAAAE,SAAA;YAAAqJ,QAAA,gBACEvJ,OAAA,CAACV,IAAI;cAACkK,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,oBAEpB;UAAA,eAAE;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,EAEf,CAAC3C,WAAW,iBACXjH,OAAA,CAAC4D,aAAa;UACZC,OAAO,EAAC,OAAO;UACfC,OAAO;UACP+F,OAAO,EAAEjC,eAAgB;UAAA2B,QAAA,gBAEzBvJ,OAAA,CAACH,SAAS;YAAC2J,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAEzB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAChB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACiB,CAAC,eAErB5J,OAAA,CAAC6B,YAAY;QAAA0H,QAAA,gBACXvJ,OAAA,CAAC+B,aAAa;UAAAwH,QAAA,gBACZvJ,OAAA,CAACiC,YAAY;YAAAsH,QAAA,gBACXvJ,OAAA,CAACoC,WAAW;cAAAmH,QAAA,eACVvJ,OAAA,CAACZ,MAAM;gBAACoK,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,sBAEhB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eAEf5J,OAAA,CAAC4F,gBAAgB;YAACC,SAAS,EAAEoB,WAAY;YAAAsC,QAAA,GACtCtC,WAAW,gBAAGjH,OAAA,CAACL,IAAI;cAAC6J,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG5J,OAAA,CAACJ,OAAO;cAAC4J,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACxD3C,WAAW,GAAG,cAAc,GAAG,iBAAiB;UAAA;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,EAElB1C,UAAU,iBACTlH,OAAA,CAACiF,iBAAiB;YAACC,OAAO,EAAEmC,WAAY;YAAClC,OAAO,EAAE+B,UAAU,CAAC/B,OAAQ;YAAAoE,QAAA,gBACnEvJ,OAAA,CAACqF,cAAc;cAACH,OAAO,EAAEmC,WAAY;cAAClC,OAAO,EAAE+B,UAAU,CAAC/B,OAAQ;cAAAoE,QAAA,GAAC,YACvD,EAACrC,UAAU,CAAC6B,IAAI,EACzB7B,UAAU,CAAC/B,OAAO,IAAI,aAAa;YAAA;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACjB5J,OAAA,CAACuF,aAAa;cAAAgE,QAAA,eACZvJ,OAAA,CAACyF,cAAc;gBAACC,UAAU,EAAEwB,UAAU,CAACxB;cAAW;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eAChB5J,OAAA;cAAK8J,KAAK,EAAE;gBAAEG,QAAQ,EAAE,UAAU;gBAAEC,SAAS,EAAE,KAAK;gBAAEC,KAAK,EAAE;cAAwB,CAAE;cAAAZ,QAAA,GAAC,cAC1E,EAACF,IAAI,CAACC,KAAK,CAACpC,UAAU,CAACxB,UAAU,GAAG,GAAG,CAAC,EAAC,GACrD,EAAC2B,WAAW,IAAIC,UAAU,iBACxBtH,OAAA;gBAAM8J,KAAK,EAAE;kBAAEK,KAAK,EAAE,oBAAoB;kBAAEC,UAAU,EAAE;gBAAM,CAAE;gBAAAb,QAAA,EAAC;cAEjE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP,EACA,CAACzC,aAAa,iBACbnH,OAAA;gBAAK8J,KAAK,EAAE;kBAAEK,KAAK,EAAE,oBAAoB;kBAAED,SAAS,EAAE;gBAAM,CAAE;gBAAAX,QAAA,GAAC,gDAC1B,EAACxD,gBAAgB,CAACO,WAAW,CAAC,CAACN,IAAI,EAAC,gCACzE;cAAA;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACW,CACpB,EAEA,CAAC1C,UAAU,iBACVlH,OAAA,CAACiF,iBAAiB;YAAAsE,QAAA,gBAChBvJ,OAAA,CAACqF,cAAc;cAAAkE,QAAA,GAAC,iCACM,EAACxD,gBAAgB,CAACO,WAAW,CAAC,CAACN,IAAI,EAAC,IAC1D;YAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAgB,CAAC,eACjB5J,OAAA;cAAK8J,KAAK,EAAE;gBAAEG,QAAQ,EAAE,UAAU;gBAAEE,KAAK,EAAE;cAAwB,CAAE;cAAAZ,QAAA,EAAC;YAEtE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACW,CACpB,eACD5J,OAAA,CAACsC,eAAe;YAAAiH,QAAA,gBACdvJ,OAAA,CAACwC,YAAY;cACX6H,GAAG,EAAEvD,SAAU;cACfwD,KAAK,EAAE,KAAM;cACbC,gBAAgB,EAAC,YAAY;cAC7BC,gBAAgB,EAAE;gBAChBC,KAAK,EAAE,GAAG;gBACVC,MAAM,EAAE,GAAG;gBACXC,UAAU,EAAE;cACd;YAAE;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF5J,OAAA,CAAC0C,gBAAgB;cAACE,WAAW,EAAEuE,aAAc;cAAAoC,QAAA,EAC1CpC,aAAa,gBACZnH,OAAA,CAAAE,SAAA;gBAAAqJ,QAAA,gBACEvJ,OAAA;kBAAK8J,KAAK,EAAE;oBACVW,KAAK,EAAE,KAAK;oBACZC,MAAM,EAAE,KAAK;oBACbE,YAAY,EAAE,KAAK;oBACnBC,eAAe,EAAE,OAAO;oBACxBC,WAAW,EAAE;kBACf;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,aAEP;cAAA,eAAE,CAAC,gBAEH5J,OAAA,CAAAE,SAAA;gBAAAqJ,QAAA,gBACEvJ,OAAA,CAACP,GAAG;kBAAC+J,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,SAEnB;cAAA,eAAE;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEhB5J,OAAA,CAAC8C,WAAW;UAAAyG,QAAA,gBACVvJ,OAAA,CAACiC,YAAY;YAAAsH,QAAA,gBACXvJ,OAAA,CAACoC,WAAW;cAAAmH,QAAA,eACVvJ,OAAA,CAACN,MAAM;gBAAC8J,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,iBAEhB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACf5J,OAAA,CAACgD,YAAY;YACXgF,KAAK,EAAE1B,WAAY;YACnByE,QAAQ,EAAElD,gBAAiB;YAC3BmD,QAAQ,EAAE7D,aAAc;YAAAoC,QAAA,EAEvB0B,MAAM,CAACC,IAAI,CAACnF,gBAAgB,CAAC,CAACoF,GAAG,CAACC,OAAO,iBACxCpL,OAAA;cAAsBgI,KAAK,EAAEoD,OAAQ;cAAA7B,QAAA,EAClCxD,gBAAgB,CAACqF,OAAO,CAAC,CAACpF;YAAI,GADpBoF,OAAO;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEZ,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,eACf5J,OAAA,CAACmD,WAAW;YAAAoG,QAAA,gBACVvJ,OAAA;cACEqL,GAAG,EAAEtF,gBAAgB,CAACO,WAAW,CAAC,CAACL,GAAI;cACvCqF,GAAG,EAAEvF,gBAAgB,CAACO,WAAW,CAAC,CAACN,IAAK;cACxCuF,OAAO,EAAGC,CAAC,IAAK;gBACdA,CAAC,CAACzD,MAAM,CAAC+B,KAAK,CAAC2B,OAAO,GAAG,MAAM;gBAC/BD,CAAC,CAACzD,MAAM,CAAC2D,WAAW,CAAC5B,KAAK,CAAC2B,OAAO,GAAG,MAAM;cAC7C;YAAE;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF5J,OAAA;cAAK8J,KAAK,EAAE;gBAAC2B,OAAO,EAAE,MAAM;gBAAExB,QAAQ,EAAE;cAAM,CAAE;cAAAV,QAAA,EAAC;YAEjD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eACd5J,OAAA,CAACqD,QAAQ;YAAAkG,QAAA,EAAExD,gBAAgB,CAACO,WAAW,CAAC,CAACN;UAAI;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACzD5J,OAAA,CAACwD,eAAe;YAAA+F,QAAA,EACbxD,gBAAgB,CAACO,WAAW,CAAC,CAACJ;UAAW;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAId,CAACpD,MAAM,IAAIY,eAAe,kBACzBpH,OAAA,CAACgE,aAAa;QAACC,IAAI,EAAE,CAACuC,MAAM,IAAIY,eAAe,EAAEuE,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG,CAACnF,MAAM,IAAIY,eAAe,EAAEuE,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,MAAO;QAAApC,QAAA,EACjJnC,eAAe,IAAIZ;MAAM;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAChB,EAEAhD,cAAc,CAACgF,MAAM,GAAG,CAAC,iBACxB5L,OAAA,CAACmE,iBAAiB;QAAAoF,QAAA,gBAChBvJ,OAAA,CAACqE,eAAe;UAAAkF,QAAA,EAAC;QAAwB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAiB,CAAC,eAC3D5J,OAAA,CAACuE,cAAc;UAAAgF,QAAA,EACZ3C,cAAc,CAACuE,GAAG,CAAE3C,KAAK,iBACxBxI,OAAA,CAACyE,aAAa;YAAA8E,QAAA,gBACZvJ,OAAA,CAAC2E,cAAc;cAAA4E,QAAA,EAAEf,KAAK,CAACO;YAAI;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiB,CAAC,eAC7C5J,OAAA,CAAC6E,aAAa;cAAA0E,QAAA,EACX,IAAIsC,IAAI,CAACrD,KAAK,CAACQ,SAAS,CAAC,CAAC8C,cAAc,CAAC;YAAC;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eAChB5J,OAAA,CAAC+E,cAAc;cAAC8E,OAAO,EAAEA,CAAA,KAAMtB,iBAAiB,CAACC,KAAK,CAAE;cAAAe,QAAA,gBACtDvJ,OAAA,CAACR,QAAQ;gBAACgK,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAExB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAgB,CAAC;UAAA,GARCpB,KAAK,CAACuD,EAAE;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASb,CAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACpB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAExB,CAAC;AAACvD,EAAA,CAnWIF,YAAY;EAAA,QA0BZrG,gBAAgB;AAAA;AAAAkM,IAAA,GA1BhB7F,YAAY;AAqWlB,eAAeA,YAAY;AAAC,IAAA9F,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAkG,IAAA;AAAAC,YAAA,CAAA5L,EAAA;AAAA4L,YAAA,CAAAzL,GAAA;AAAAyL,YAAA,CAAAvL,GAAA;AAAAuL,YAAA,CAAArL,GAAA;AAAAqL,YAAA,CAAAnL,GAAA;AAAAmL,YAAA,CAAAhL,GAAA;AAAAgL,YAAA,CAAA7K,GAAA;AAAA6K,YAAA,CAAA1K,GAAA;AAAA0K,YAAA,CAAAxK,GAAA;AAAAwK,YAAA,CAAArK,GAAA;AAAAqK,YAAA,CAAAnK,GAAA;AAAAmK,YAAA,CAAAjK,IAAA;AAAAiK,YAAA,CAAA9J,IAAA;AAAA8J,YAAA,CAAA5J,IAAA;AAAA4J,YAAA,CAAA1J,IAAA;AAAA0J,YAAA,CAAAxJ,IAAA;AAAAwJ,YAAA,CAAApJ,IAAA;AAAAoJ,YAAA,CAAAlJ,IAAA;AAAAkJ,YAAA,CAAA/I,IAAA;AAAA+I,YAAA,CAAA7I,IAAA;AAAA6I,YAAA,CAAA1I,IAAA;AAAA0I,YAAA,CAAAxI,IAAA;AAAAwI,YAAA,CAAAtI,IAAA;AAAAsI,YAAA,CAAAlI,IAAA;AAAAkI,YAAA,CAAA/H,IAAA;AAAA+H,YAAA,CAAA7H,IAAA;AAAA6H,YAAA,CAAA3H,IAAA;AAAA2H,YAAA,CAAAzH,IAAA;AAAAyH,YAAA,CAAAvH,IAAA;AAAAuH,YAAA,CAAArH,IAAA;AAAAqH,YAAA,CAAAnH,IAAA;AAAAmH,YAAA,CAAAjH,IAAA;AAAAiH,YAAA,CAAA7G,IAAA;AAAA6G,YAAA,CAAA3G,IAAA;AAAA2G,YAAA,CAAAzG,IAAA;AAAAyG,YAAA,CAAAtG,IAAA;AAAAsG,YAAA,CAAAnG,IAAA;AAAAmG,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}