{"ast": null, "code": "var _jsxFileName = \"D:\\\\ASL\\\\ASL-Training\\\\src\\\\components\\\\LevelSelector.js\";\nimport React from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport { Play, Lock, CheckCircle, Star, Trophy, Target, Sparkles, Zap } from 'lucide-react';\nimport { SIGN_LEVELS, getTotalLevels } from '../data/signLevels';\nimport { theme } from '../styles/theme';\nimport { Container, Section, Grid, Card, Button, Heading, Text, ProgressBar, ProgressFill, Badge } from './ui/ModernComponents';\n\n// Modern Animations\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst modernFadeInUp = keyframes`\n  from {\n    opacity: 0;\n    transform: translateY(30px) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n`;\nconst modernPulse = keyframes`\n  0%, 100% {\n    transform: scale(1);\n    box-shadow: ${theme.shadows.lg};\n  }\n  50% {\n    transform: scale(1.02);\n    box-shadow: ${theme.shadows.glow};\n  }\n`;\nconst floatingAnimation = keyframes`\n  0%, 100% { transform: translateY(0px); }\n  50% { transform: translateY(-10px); }\n`;\n\n// Modern Styled Components\nconst ModernContainer = styled(Container)`\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  min-height: 100vh;\n  padding: ${theme.spacing[8]} ${theme.spacing[4]};\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    padding: ${theme.spacing[6]} ${theme.spacing[3]};\n  }\n`;\nconst ModernHeader = styled(Section)`\n  text-align: center;\n  padding: ${theme.spacing[8]} 0 ${theme.spacing[12]};\n  animation: ${modernFadeInUp} 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    padding: ${theme.spacing[6]} 0 ${theme.spacing[8]};\n  }\n`;\nconst HeroTitle = styled(Heading)`\n  margin-bottom: ${theme.spacing[4]};\n  position: relative;\n\n  &::after {\n    content: '✨';\n    position: absolute;\n    top: -10px;\n    right: -20px;\n    font-size: 2rem;\n    animation: ${floatingAnimation} 3s ease-in-out infinite;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    &::after {\n      display: none;\n    }\n  }\n`;\nconst HeroSubtitle = styled(Text)`\n  max-width: 600px;\n  margin: 0 auto ${theme.spacing[8]};\n`;\nconst ModernLevelsGrid = styled(Grid)`\n  margin-bottom: ${theme.spacing[8]};\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    grid-template-columns: 1fr;\n  }\n`;\nconst ModernLevelCard = styled(Card)`\n  cursor: ${props => props.isLocked ? 'not-allowed' : 'pointer'};\n  opacity: ${props => props.isLocked ? 0.6 : 1};\n  animation: ${modernFadeInUp} 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);\n  animation-delay: ${props => props.index * 0.1}s;\n  animation-fill-mode: both;\n  position: relative;\n  overflow: hidden;\n  border: 2px solid ${props => {\n  if (props.isCompleted) return theme.colors.success[200];\n  if (props.isLocked) return theme.colors.neutral[200];\n  if (props.isActive) return theme.colors.primary[300];\n  return theme.colors.neutral[100];\n}};\n\n  &:hover {\n    transform: ${props => props.isLocked ? 'none' : 'translateY(-8px) scale(1.02)'};\n    box-shadow: ${props => {\n  if (props.isLocked) return theme.shadows.lg;\n  if (props.isCompleted) return theme.shadows.glowSuccess;\n  return theme.shadows.glow;\n}};\n  }\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 6px;\n    background: ${props => {\n  if (props.isCompleted) return theme.colors.gradients.success;\n  if (props.isLocked) return theme.colors.gradients.secondary;\n  return theme.colors.gradients.primary;\n}};\n    border-radius: ${theme.borderRadius['2xl']} ${theme.borderRadius['2xl']} 0 0;\n  }\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: -50%;\n    left: -50%;\n    width: 200%;\n    height: 200%;\n    background: ${props => {\n  if (props.isCompleted) return `radial-gradient(circle, ${theme.colors.success[50]} 0%, transparent 70%)`;\n  if (props.isActive) return `radial-gradient(circle, ${theme.colors.primary[50]} 0%, transparent 70%)`;\n  return 'transparent';\n}};\n    opacity: 0.5;\n    pointer-events: none;\n    z-index: 0;\n  }\n\n  > * {\n    position: relative;\n    z-index: 1;\n  }\n\n  ${props => props.isActive && `\n    animation: ${modernPulse} 2s ease infinite;\n    box-shadow: 0 0 0 4px ${theme.colors.primary[200]};\n  `}\n\n  ${props => props.isCompleted && `\n    &::before {\n      background: ${theme.colors.gradients.success};\n      box-shadow: 0 2px 10px ${theme.colors.success[300]};\n    }\n  `}\n`;\nconst ModernLevelHeader = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${theme.spacing[4]};\n`;\nconst ModernLevelNumber = styled.div`\n  background: ${props => {\n  if (props.isCompleted) return theme.colors.gradients.success;\n  if (props.isLocked) return theme.colors.gradients.secondary;\n  return theme.colors.gradients.primary;\n}};\n  color: ${theme.colors.text.inverse};\n  width: 60px;\n  height: 60px;\n  border-radius: ${theme.borderRadius.full};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: ${theme.typography.fontWeight.bold};\n  font-size: ${theme.typography.fontSize.xl};\n  box-shadow: ${theme.shadows.md};\n  position: relative;\n\n  ${props => props.isCompleted && `\n    &::after {\n      content: '✨';\n      position: absolute;\n      top: -5px;\n      right: -5px;\n      font-size: 1rem;\n      animation: ${floatingAnimation} 2s ease-in-out infinite;\n    }\n  `}\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    width: 50px;\n    height: 50px;\n    font-size: ${theme.typography.fontSize.lg};\n  }\n`;\nconst ModernLevelStatus = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${theme.spacing[2]};\n  color: ${props => {\n  if (props.isCompleted) return theme.colors.success[600];\n  if (props.isLocked) return theme.colors.neutral[400];\n  return theme.colors.primary[600];\n}};\n  font-weight: ${theme.typography.fontWeight.medium};\n`;\nconst ModernLevelTitle = styled(Heading)`\n  margin-bottom: ${theme.spacing[2]};\n  color: ${theme.colors.text.primary};\n`;\nconst ModernLevelTheme = styled.div`\n  font-size: ${theme.typography.fontSize.xl};\n  margin-bottom: ${theme.spacing[3]};\n  text-align: center;\n  padding: ${theme.spacing[2]};\n  background: ${theme.colors.gradients.surface};\n  border-radius: ${theme.borderRadius.lg};\n  border: 1px solid ${theme.colors.neutral[100]};\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    font-size: ${theme.typography.fontSize.lg};\n  }\n`;\nconst ModernLevelDescription = styled(Text)`\n  margin-bottom: ${theme.spacing[4]};\n  text-align: center;\n`;\nconst ModernLevelProgress = styled.div`\n  margin-bottom: ${theme.spacing[5]};\n`;\nconst ProgressHeader = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${theme.spacing[2]};\n`;\nconst ProgressText = styled(Text)`\n  font-weight: ${theme.typography.fontWeight.medium};\n`;\n_c = ProgressText;\nconst ProgressPercentage = styled(Badge)`\n  font-weight: ${theme.typography.fontWeight.bold};\n`;\nconst ModernProgressBar = styled(ProgressBar)`\n  height: 12px;\n  background: ${theme.colors.neutral[200]};\n  margin-bottom: ${theme.spacing[3]};\n  position: relative;\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n    animation: shimmer 2s infinite;\n  }\n`;\nconst ModernProgressFill = styled(ProgressFill)`\n  background: ${props => {\n  if (props.isCompleted) return theme.colors.gradients.success;\n  return theme.colors.gradients.primary;\n}};\n  position: relative;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\n    animation: shimmer 3s infinite;\n  }\n`;\nconst ModernActionButton = styled(Button)`\n  width: 100%;\n  height: ${theme.components.button.height.lg};\n  font-size: ${theme.typography.fontSize.base};\n  font-weight: ${theme.typography.fontWeight.semibold};\n\n  ${props => {\n  if (props.disabled) {\n    return `\n        background: ${theme.colors.neutral[200]};\n        color: ${theme.colors.neutral[400]};\n        cursor: not-allowed;\n\n        &:hover {\n          transform: none;\n          box-shadow: ${theme.shadows.base};\n        }\n      `;\n  }\n  if (props.isCompleted) {\n    return `\n        variant: success;\n      `;\n  }\n  return `\n      variant: primary;\n    `;\n}}\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    height: ${theme.components.button.height.xl};\n    font-size: ${theme.typography.fontSize.lg};\n  }\n`;\nconst shimmer = keyframes`\n  0% { transform: translateX(-100%); }\n  100% { transform: translateX(100%); }\n`;\nconst LevelSelector = ({\n  currentLevel,\n  userProgress = {},\n  onLevelSelect,\n  className\n}) => {\n  const totalLevels = getTotalLevels();\n  const getLevelProgress = level => {\n    const progress = userProgress[level] || {\n      completed: 0,\n      total: 20\n    };\n    return progress.completed / progress.total * 100;\n  };\n  const isLevelLocked = level => {\n    if (level === 1) return false;\n    const prevLevelProgress = userProgress[level - 1] || {\n      completed: 0,\n      total: 20\n    };\n    return prevLevelProgress.completed < prevLevelProgress.total;\n  };\n  const isLevelCompleted = level => {\n    const progress = userProgress[level] || {\n      completed: 0,\n      total: 20\n    };\n    return progress.completed >= progress.total;\n  };\n  const getStatusIcon = level => {\n    if (isLevelCompleted(level)) return /*#__PURE__*/_jsxDEV(CheckCircle, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 41\n    }, this);\n    if (isLevelLocked(level)) return /*#__PURE__*/_jsxDEV(Lock, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 38\n    }, this);\n    if (level === currentLevel) return /*#__PURE__*/_jsxDEV(Target, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 40\n    }, this);\n    return /*#__PURE__*/_jsxDEV(Star, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 12\n    }, this);\n  };\n  const getButtonText = level => {\n    if (isLevelLocked(level)) return 'Locked';\n    if (isLevelCompleted(level)) return 'Review Level';\n    if (level === currentLevel) return 'Continue';\n    return 'Start Level';\n  };\n  const getButtonIcon = level => {\n    if (isLevelLocked(level)) return /*#__PURE__*/_jsxDEV(Lock, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 38\n    }, this);\n    if (isLevelCompleted(level)) return /*#__PURE__*/_jsxDEV(Trophy, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 380,\n      columnNumber: 41\n    }, this);\n    return /*#__PURE__*/_jsxDEV(Play, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 12\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    className: className,\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        children: \"ASL Learning Journey\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Subtitle, {\n        children: \"Master American Sign Language through interactive flash cards. Complete each level to unlock the next challenge!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(LevelsGrid, {\n      children: Object.entries(SIGN_LEVELS).map(([levelNum, levelData], index) => {\n        var _userProgress$level;\n        const level = parseInt(levelNum);\n        const isLocked = isLevelLocked(level);\n        const isCompleted = isLevelCompleted(level);\n        const isActive = level === currentLevel;\n        const progress = getLevelProgress(level);\n        return /*#__PURE__*/_jsxDEV(LevelCard, {\n          index: index,\n          isLocked: isLocked,\n          isCompleted: isCompleted,\n          isActive: isActive,\n          onClick: () => !isLocked && onLevelSelect(level),\n          children: [/*#__PURE__*/_jsxDEV(LevelHeader, {\n            children: [/*#__PURE__*/_jsxDEV(LevelNumber, {\n              isCompleted: isCompleted,\n              isLocked: isLocked,\n              children: level\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(LevelStatus, {\n              isCompleted: isCompleted,\n              isLocked: isLocked,\n              children: getStatusIcon(level)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(LevelTheme, {\n            children: levelData.theme\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(LevelTitle, {\n            children: levelData.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(LevelDescription, {\n            children: levelData.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(LevelProgress, {\n            children: [/*#__PURE__*/_jsxDEV(ProgressText, {\n              children: [((_userProgress$level = userProgress[level]) === null || _userProgress$level === void 0 ? void 0 : _userProgress$level.completed) || 0, \" / 20 signs\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n              children: /*#__PURE__*/_jsxDEV(ProgressFill, {\n                style: {\n                  width: `${progress}%`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ProgressText, {\n              children: [Math.round(progress), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n            disabled: isLocked,\n            isCompleted: isCompleted,\n            onClick: e => {\n              e.stopPropagation();\n              if (!isLocked) onLevelSelect(level);\n            },\n            children: [getButtonIcon(level), getButtonText(level)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 15\n          }, this)]\n        }, level, true, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 385,\n    columnNumber: 5\n  }, this);\n};\n_c2 = LevelSelector;\nexport default LevelSelector;\nvar _c, _c2;\n$RefreshReg$(_c, \"ProgressText\");\n$RefreshReg$(_c2, \"LevelSelector\");", "map": {"version": 3, "names": ["React", "styled", "keyframes", "Play", "Lock", "CheckCircle", "Star", "Trophy", "Target", "<PERSON><PERSON><PERSON>", "Zap", "SIGN_LEVELS", "getTotalLevels", "theme", "Container", "Section", "Grid", "Card", "<PERSON><PERSON>", "Heading", "Text", "ProgressBar", "ProgressFill", "Badge", "jsxDEV", "_jsxDEV", "modernFadeInUp", "modernPulse", "shadows", "lg", "glow", "floatingAnimation", "ModernContainer", "spacing", "breakpoints", "md", "ModernHeader", "<PERSON><PERSON><PERSON><PERSON>", "sm", "HeroSubtitle", "ModernLevelsGrid", "ModernLevelCard", "props", "isLocked", "index", "isCompleted", "colors", "success", "neutral", "isActive", "primary", "glowSuccess", "gradients", "secondary", "borderRadius", "ModernLevelHeader", "div", "ModernLevelNumber", "text", "inverse", "full", "typography", "fontWeight", "bold", "fontSize", "xl", "ModernLevelStatus", "medium", "ModernLevelTitle", "ModernLevelTheme", "surface", "ModernLevelDescription", "ModernLevelProgress", "ProgressHeader", "ProgressText", "_c", "ProgressPercentage", "ModernProgressBar", "ModernProgressFill", "ModernActionButton", "components", "button", "height", "base", "semibold", "disabled", "shimmer", "LevelSelector", "currentLevel", "userProgress", "onLevelSelect", "className", "totalLevels", "getLevelProgress", "level", "progress", "completed", "total", "isLevelLocked", "prevLevelProgress", "isLevelCompleted", "getStatusIcon", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getButtonText", "getButtonIcon", "children", "Header", "Title", "Subtitle", "LevelsGrid", "Object", "entries", "map", "levelNum", "levelData", "_userProgress$level", "parseInt", "LevelCard", "onClick", "<PERSON><PERSON><PERSON><PERSON>", "LevelNumber", "LevelStatus", "LevelTheme", "LevelTitle", "name", "LevelDescription", "description", "LevelProgress", "style", "width", "Math", "round", "ActionButton", "e", "stopPropagation", "_c2", "$RefreshReg$"], "sources": ["D:/ASL/ASL-Training/src/components/LevelSelector.js"], "sourcesContent": ["import React from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport { Play, Lock, CheckCircle, Star, Trophy, Target, Sparkles, Zap } from 'lucide-react';\nimport { SIGN_LEVELS, getTotalLevels } from '../data/signLevels';\nimport { theme } from '../styles/theme';\nimport { Container, Section, Grid, Card, Button, Heading, Text, ProgressBar, ProgressFill, Badge } from './ui/ModernComponents';\n\n// Modern Animations\nconst modernFadeInUp = keyframes`\n  from {\n    opacity: 0;\n    transform: translateY(30px) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n`;\n\nconst modernPulse = keyframes`\n  0%, 100% {\n    transform: scale(1);\n    box-shadow: ${theme.shadows.lg};\n  }\n  50% {\n    transform: scale(1.02);\n    box-shadow: ${theme.shadows.glow};\n  }\n`;\n\nconst floatingAnimation = keyframes`\n  0%, 100% { transform: translateY(0px); }\n  50% { transform: translateY(-10px); }\n`;\n\n// Modern Styled Components\nconst ModernContainer = styled(Container)`\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  min-height: 100vh;\n  padding: ${theme.spacing[8]} ${theme.spacing[4]};\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    padding: ${theme.spacing[6]} ${theme.spacing[3]};\n  }\n`;\n\nconst ModernHeader = styled(Section)`\n  text-align: center;\n  padding: ${theme.spacing[8]} 0 ${theme.spacing[12]};\n  animation: ${modernFadeInUp} 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    padding: ${theme.spacing[6]} 0 ${theme.spacing[8]};\n  }\n`;\n\nconst HeroTitle = styled(Heading)`\n  margin-bottom: ${theme.spacing[4]};\n  position: relative;\n\n  &::after {\n    content: '✨';\n    position: absolute;\n    top: -10px;\n    right: -20px;\n    font-size: 2rem;\n    animation: ${floatingAnimation} 3s ease-in-out infinite;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    &::after {\n      display: none;\n    }\n  }\n`;\n\nconst HeroSubtitle = styled(Text)`\n  max-width: 600px;\n  margin: 0 auto ${theme.spacing[8]};\n`;\n\nconst ModernLevelsGrid = styled(Grid)`\n  margin-bottom: ${theme.spacing[8]};\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    grid-template-columns: 1fr;\n  }\n`;\n\nconst ModernLevelCard = styled(Card)`\n  cursor: ${props => props.isLocked ? 'not-allowed' : 'pointer'};\n  opacity: ${props => props.isLocked ? 0.6 : 1};\n  animation: ${modernFadeInUp} 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);\n  animation-delay: ${props => props.index * 0.1}s;\n  animation-fill-mode: both;\n  position: relative;\n  overflow: hidden;\n  border: 2px solid ${props => {\n    if (props.isCompleted) return theme.colors.success[200];\n    if (props.isLocked) return theme.colors.neutral[200];\n    if (props.isActive) return theme.colors.primary[300];\n    return theme.colors.neutral[100];\n  }};\n\n  &:hover {\n    transform: ${props => props.isLocked ? 'none' : 'translateY(-8px) scale(1.02)'};\n    box-shadow: ${props => {\n      if (props.isLocked) return theme.shadows.lg;\n      if (props.isCompleted) return theme.shadows.glowSuccess;\n      return theme.shadows.glow;\n    }};\n  }\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 6px;\n    background: ${props => {\n      if (props.isCompleted) return theme.colors.gradients.success;\n      if (props.isLocked) return theme.colors.gradients.secondary;\n      return theme.colors.gradients.primary;\n    }};\n    border-radius: ${theme.borderRadius['2xl']} ${theme.borderRadius['2xl']} 0 0;\n  }\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: -50%;\n    left: -50%;\n    width: 200%;\n    height: 200%;\n    background: ${props => {\n      if (props.isCompleted) return `radial-gradient(circle, ${theme.colors.success[50]} 0%, transparent 70%)`;\n      if (props.isActive) return `radial-gradient(circle, ${theme.colors.primary[50]} 0%, transparent 70%)`;\n      return 'transparent';\n    }};\n    opacity: 0.5;\n    pointer-events: none;\n    z-index: 0;\n  }\n\n  > * {\n    position: relative;\n    z-index: 1;\n  }\n\n  ${props => props.isActive && `\n    animation: ${modernPulse} 2s ease infinite;\n    box-shadow: 0 0 0 4px ${theme.colors.primary[200]};\n  `}\n\n  ${props => props.isCompleted && `\n    &::before {\n      background: ${theme.colors.gradients.success};\n      box-shadow: 0 2px 10px ${theme.colors.success[300]};\n    }\n  `}\n`;\n\nconst ModernLevelHeader = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${theme.spacing[4]};\n`;\n\nconst ModernLevelNumber = styled.div`\n  background: ${props => {\n    if (props.isCompleted) return theme.colors.gradients.success;\n    if (props.isLocked) return theme.colors.gradients.secondary;\n    return theme.colors.gradients.primary;\n  }};\n  color: ${theme.colors.text.inverse};\n  width: 60px;\n  height: 60px;\n  border-radius: ${theme.borderRadius.full};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: ${theme.typography.fontWeight.bold};\n  font-size: ${theme.typography.fontSize.xl};\n  box-shadow: ${theme.shadows.md};\n  position: relative;\n\n  ${props => props.isCompleted && `\n    &::after {\n      content: '✨';\n      position: absolute;\n      top: -5px;\n      right: -5px;\n      font-size: 1rem;\n      animation: ${floatingAnimation} 2s ease-in-out infinite;\n    }\n  `}\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    width: 50px;\n    height: 50px;\n    font-size: ${theme.typography.fontSize.lg};\n  }\n`;\n\nconst ModernLevelStatus = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${theme.spacing[2]};\n  color: ${props => {\n    if (props.isCompleted) return theme.colors.success[600];\n    if (props.isLocked) return theme.colors.neutral[400];\n    return theme.colors.primary[600];\n  }};\n  font-weight: ${theme.typography.fontWeight.medium};\n`;\n\nconst ModernLevelTitle = styled(Heading)`\n  margin-bottom: ${theme.spacing[2]};\n  color: ${theme.colors.text.primary};\n`;\n\nconst ModernLevelTheme = styled.div`\n  font-size: ${theme.typography.fontSize.xl};\n  margin-bottom: ${theme.spacing[3]};\n  text-align: center;\n  padding: ${theme.spacing[2]};\n  background: ${theme.colors.gradients.surface};\n  border-radius: ${theme.borderRadius.lg};\n  border: 1px solid ${theme.colors.neutral[100]};\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    font-size: ${theme.typography.fontSize.lg};\n  }\n`;\n\nconst ModernLevelDescription = styled(Text)`\n  margin-bottom: ${theme.spacing[4]};\n  text-align: center;\n`;\n\nconst ModernLevelProgress = styled.div`\n  margin-bottom: ${theme.spacing[5]};\n`;\n\nconst ProgressHeader = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${theme.spacing[2]};\n`;\n\nconst ProgressText = styled(Text)`\n  font-weight: ${theme.typography.fontWeight.medium};\n`;\n\nconst ProgressPercentage = styled(Badge)`\n  font-weight: ${theme.typography.fontWeight.bold};\n`;\n\nconst ModernProgressBar = styled(ProgressBar)`\n  height: 12px;\n  background: ${theme.colors.neutral[200]};\n  margin-bottom: ${theme.spacing[3]};\n  position: relative;\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n    animation: shimmer 2s infinite;\n  }\n`;\n\nconst ModernProgressFill = styled(ProgressFill)`\n  background: ${props => {\n    if (props.isCompleted) return theme.colors.gradients.success;\n    return theme.colors.gradients.primary;\n  }};\n  position: relative;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\n    animation: shimmer 3s infinite;\n  }\n`;\n\nconst ModernActionButton = styled(Button)`\n  width: 100%;\n  height: ${theme.components.button.height.lg};\n  font-size: ${theme.typography.fontSize.base};\n  font-weight: ${theme.typography.fontWeight.semibold};\n\n  ${props => {\n    if (props.disabled) {\n      return `\n        background: ${theme.colors.neutral[200]};\n        color: ${theme.colors.neutral[400]};\n        cursor: not-allowed;\n\n        &:hover {\n          transform: none;\n          box-shadow: ${theme.shadows.base};\n        }\n      `;\n    }\n    if (props.isCompleted) {\n      return `\n        variant: success;\n      `;\n    }\n    return `\n      variant: primary;\n    `;\n  }}\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    height: ${theme.components.button.height.xl};\n    font-size: ${theme.typography.fontSize.lg};\n  }\n`;\n\nconst shimmer = keyframes`\n  0% { transform: translateX(-100%); }\n  100% { transform: translateX(100%); }\n`;\n\nconst LevelSelector = ({ \n  currentLevel, \n  userProgress = {}, \n  onLevelSelect,\n  className \n}) => {\n  const totalLevels = getTotalLevels();\n\n  const getLevelProgress = (level) => {\n    const progress = userProgress[level] || { completed: 0, total: 20 };\n    return (progress.completed / progress.total) * 100;\n  };\n\n  const isLevelLocked = (level) => {\n    if (level === 1) return false;\n    const prevLevelProgress = userProgress[level - 1] || { completed: 0, total: 20 };\n    return prevLevelProgress.completed < prevLevelProgress.total;\n  };\n\n  const isLevelCompleted = (level) => {\n    const progress = userProgress[level] || { completed: 0, total: 20 };\n    return progress.completed >= progress.total;\n  };\n\n  const getStatusIcon = (level) => {\n    if (isLevelCompleted(level)) return <CheckCircle size={20} />;\n    if (isLevelLocked(level)) return <Lock size={20} />;\n    if (level === currentLevel) return <Target size={20} />;\n    return <Star size={20} />;\n  };\n\n  const getButtonText = (level) => {\n    if (isLevelLocked(level)) return 'Locked';\n    if (isLevelCompleted(level)) return 'Review Level';\n    if (level === currentLevel) return 'Continue';\n    return 'Start Level';\n  };\n\n  const getButtonIcon = (level) => {\n    if (isLevelLocked(level)) return <Lock size={20} />;\n    if (isLevelCompleted(level)) return <Trophy size={20} />;\n    return <Play size={20} />;\n  };\n\n  return (\n    <Container className={className}>\n      <Header>\n        <Title>ASL Learning Journey</Title>\n        <Subtitle>\n          Master American Sign Language through interactive flash cards. \n          Complete each level to unlock the next challenge!\n        </Subtitle>\n      </Header>\n\n      <LevelsGrid>\n        {Object.entries(SIGN_LEVELS).map(([levelNum, levelData], index) => {\n          const level = parseInt(levelNum);\n          const isLocked = isLevelLocked(level);\n          const isCompleted = isLevelCompleted(level);\n          const isActive = level === currentLevel;\n          const progress = getLevelProgress(level);\n\n          return (\n            <LevelCard\n              key={level}\n              index={index}\n              isLocked={isLocked}\n              isCompleted={isCompleted}\n              isActive={isActive}\n              onClick={() => !isLocked && onLevelSelect(level)}\n            >\n              <LevelHeader>\n                <LevelNumber \n                  isCompleted={isCompleted}\n                  isLocked={isLocked}\n                >\n                  {level}\n                </LevelNumber>\n                <LevelStatus \n                  isCompleted={isCompleted}\n                  isLocked={isLocked}\n                >\n                  {getStatusIcon(level)}\n                </LevelStatus>\n              </LevelHeader>\n\n              <LevelTheme>{levelData.theme}</LevelTheme>\n              <LevelTitle>{levelData.name}</LevelTitle>\n              <LevelDescription>{levelData.description}</LevelDescription>\n\n              <LevelProgress>\n                <ProgressText>\n                  {userProgress[level]?.completed || 0} / 20 signs\n                </ProgressText>\n                <ProgressBar>\n                  <ProgressFill style={{ width: `${progress}%` }} />\n                </ProgressBar>\n                <ProgressText>{Math.round(progress)}%</ProgressText>\n              </LevelProgress>\n\n              <ActionButton\n                disabled={isLocked}\n                isCompleted={isCompleted}\n                onClick={(e) => {\n                  e.stopPropagation();\n                  if (!isLocked) onLevelSelect(level);\n                }}\n              >\n                {getButtonIcon(level)}\n                {getButtonText(level)}\n              </ActionButton>\n            </LevelCard>\n          );\n        })}\n      </LevelsGrid>\n    </Container>\n  );\n};\n\nexport default LevelSelector;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,IAAIC,SAAS,QAAQ,mBAAmB;AACrD,SAASC,IAAI,EAAEC,IAAI,EAAEC,WAAW,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,cAAc;AAC3F,SAASC,WAAW,EAAEC,cAAc,QAAQ,oBAAoB;AAChE,SAASC,KAAK,QAAQ,iBAAiB;AACvC,SAASC,SAAS,EAAEC,OAAO,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAEC,WAAW,EAAEC,YAAY,EAAEC,KAAK,QAAQ,uBAAuB;;AAE/H;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAGxB,SAAS;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMyB,WAAW,GAAGzB,SAAS;AAC7B;AACA;AACA,kBAAkBW,KAAK,CAACe,OAAO,CAACC,EAAE;AAClC;AACA;AACA;AACA,kBAAkBhB,KAAK,CAACe,OAAO,CAACE,IAAI;AACpC;AACA,CAAC;AAED,MAAMC,iBAAiB,GAAG7B,SAAS;AACnC;AACA;AACA,CAAC;;AAED;AACA,MAAM8B,eAAe,GAAG/B,MAAM,CAACa,SAAS,CAAC;AACzC;AACA;AACA,aAAaD,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC,IAAIpB,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACjD;AACA,uBAAuBpB,KAAK,CAACqB,WAAW,CAACC,EAAE;AAC3C,eAAetB,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC,IAAIpB,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACnD;AACA,CAAC;AAED,MAAMG,YAAY,GAAGnC,MAAM,CAACc,OAAO,CAAC;AACpC;AACA,aAAaF,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC,MAAMpB,KAAK,CAACoB,OAAO,CAAC,EAAE,CAAC;AACpD,eAAeP,cAAc;AAC7B;AACA,uBAAuBb,KAAK,CAACqB,WAAW,CAACC,EAAE;AAC3C,eAAetB,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC,MAAMpB,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACrD;AACA,CAAC;AAED,MAAMI,SAAS,GAAGpC,MAAM,CAACkB,OAAO,CAAC;AACjC,mBAAmBN,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiBF,iBAAiB;AAClC;AACA;AACA,uBAAuBlB,KAAK,CAACqB,WAAW,CAACI,EAAE;AAC3C;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,YAAY,GAAGtC,MAAM,CAACmB,IAAI,CAAC;AACjC;AACA,mBAAmBP,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACnC,CAAC;AAED,MAAMO,gBAAgB,GAAGvC,MAAM,CAACe,IAAI,CAAC;AACrC,mBAAmBH,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACnC;AACA;AACA,uBAAuBpB,KAAK,CAACqB,WAAW,CAACI,EAAE;AAC3C;AACA;AACA,CAAC;AAED,MAAMG,eAAe,GAAGxC,MAAM,CAACgB,IAAI,CAAC;AACpC,YAAYyB,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG,aAAa,GAAG,SAAS;AAC/D,aAAaD,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG,GAAG,GAAG,CAAC;AAC9C,eAAejB,cAAc;AAC7B,qBAAqBgB,KAAK,IAAIA,KAAK,CAACE,KAAK,GAAG,GAAG;AAC/C;AACA;AACA;AACA,sBAAsBF,KAAK,IAAI;EAC3B,IAAIA,KAAK,CAACG,WAAW,EAAE,OAAOhC,KAAK,CAACiC,MAAM,CAACC,OAAO,CAAC,GAAG,CAAC;EACvD,IAAIL,KAAK,CAACC,QAAQ,EAAE,OAAO9B,KAAK,CAACiC,MAAM,CAACE,OAAO,CAAC,GAAG,CAAC;EACpD,IAAIN,KAAK,CAACO,QAAQ,EAAE,OAAOpC,KAAK,CAACiC,MAAM,CAACI,OAAO,CAAC,GAAG,CAAC;EACpD,OAAOrC,KAAK,CAACiC,MAAM,CAACE,OAAO,CAAC,GAAG,CAAC;AAClC,CAAC;AACH;AACA;AACA,iBAAiBN,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG,MAAM,GAAG,8BAA8B;AAClF,kBAAkBD,KAAK,IAAI;EACrB,IAAIA,KAAK,CAACC,QAAQ,EAAE,OAAO9B,KAAK,CAACe,OAAO,CAACC,EAAE;EAC3C,IAAIa,KAAK,CAACG,WAAW,EAAE,OAAOhC,KAAK,CAACe,OAAO,CAACuB,WAAW;EACvD,OAAOtC,KAAK,CAACe,OAAO,CAACE,IAAI;AAC3B,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBY,KAAK,IAAI;EACrB,IAAIA,KAAK,CAACG,WAAW,EAAE,OAAOhC,KAAK,CAACiC,MAAM,CAACM,SAAS,CAACL,OAAO;EAC5D,IAAIL,KAAK,CAACC,QAAQ,EAAE,OAAO9B,KAAK,CAACiC,MAAM,CAACM,SAAS,CAACC,SAAS;EAC3D,OAAOxC,KAAK,CAACiC,MAAM,CAACM,SAAS,CAACF,OAAO;AACvC,CAAC;AACL,qBAAqBrC,KAAK,CAACyC,YAAY,CAAC,KAAK,CAAC,IAAIzC,KAAK,CAACyC,YAAY,CAAC,KAAK,CAAC;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBZ,KAAK,IAAI;EACrB,IAAIA,KAAK,CAACG,WAAW,EAAE,OAAO,2BAA2BhC,KAAK,CAACiC,MAAM,CAACC,OAAO,CAAC,EAAE,CAAC,uBAAuB;EACxG,IAAIL,KAAK,CAACO,QAAQ,EAAE,OAAO,2BAA2BpC,KAAK,CAACiC,MAAM,CAACI,OAAO,CAAC,EAAE,CAAC,uBAAuB;EACrG,OAAO,aAAa;AACtB,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIR,KAAK,IAAIA,KAAK,CAACO,QAAQ,IAAI;AAC/B,iBAAiBtB,WAAW;AAC5B,4BAA4Bd,KAAK,CAACiC,MAAM,CAACI,OAAO,CAAC,GAAG,CAAC;AACrD,GAAG;AACH;AACA,IAAIR,KAAK,IAAIA,KAAK,CAACG,WAAW,IAAI;AAClC;AACA,oBAAoBhC,KAAK,CAACiC,MAAM,CAACM,SAAS,CAACL,OAAO;AAClD,+BAA+BlC,KAAK,CAACiC,MAAM,CAACC,OAAO,CAAC,GAAG,CAAC;AACxD;AACA,GAAG;AACH,CAAC;AAED,MAAMQ,iBAAiB,GAAGtD,MAAM,CAACuD,GAAG;AACpC;AACA;AACA;AACA,mBAAmB3C,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACnC,CAAC;AAED,MAAMwB,iBAAiB,GAAGxD,MAAM,CAACuD,GAAG;AACpC,gBAAgBd,KAAK,IAAI;EACrB,IAAIA,KAAK,CAACG,WAAW,EAAE,OAAOhC,KAAK,CAACiC,MAAM,CAACM,SAAS,CAACL,OAAO;EAC5D,IAAIL,KAAK,CAACC,QAAQ,EAAE,OAAO9B,KAAK,CAACiC,MAAM,CAACM,SAAS,CAACC,SAAS;EAC3D,OAAOxC,KAAK,CAACiC,MAAM,CAACM,SAAS,CAACF,OAAO;AACvC,CAAC;AACH,WAAWrC,KAAK,CAACiC,MAAM,CAACY,IAAI,CAACC,OAAO;AACpC;AACA;AACA,mBAAmB9C,KAAK,CAACyC,YAAY,CAACM,IAAI;AAC1C;AACA;AACA;AACA,iBAAiB/C,KAAK,CAACgD,UAAU,CAACC,UAAU,CAACC,IAAI;AACjD,eAAelD,KAAK,CAACgD,UAAU,CAACG,QAAQ,CAACC,EAAE;AAC3C,gBAAgBpD,KAAK,CAACe,OAAO,CAACO,EAAE;AAChC;AACA;AACA,IAAIO,KAAK,IAAIA,KAAK,CAACG,WAAW,IAAI;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmBd,iBAAiB;AACpC;AACA,GAAG;AACH;AACA,uBAAuBlB,KAAK,CAACqB,WAAW,CAACI,EAAE;AAC3C;AACA;AACA,iBAAiBzB,KAAK,CAACgD,UAAU,CAACG,QAAQ,CAACnC,EAAE;AAC7C;AACA,CAAC;AAED,MAAMqC,iBAAiB,GAAGjE,MAAM,CAACuD,GAAG;AACpC;AACA;AACA,SAAS3C,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACzB,WAAWS,KAAK,IAAI;EAChB,IAAIA,KAAK,CAACG,WAAW,EAAE,OAAOhC,KAAK,CAACiC,MAAM,CAACC,OAAO,CAAC,GAAG,CAAC;EACvD,IAAIL,KAAK,CAACC,QAAQ,EAAE,OAAO9B,KAAK,CAACiC,MAAM,CAACE,OAAO,CAAC,GAAG,CAAC;EACpD,OAAOnC,KAAK,CAACiC,MAAM,CAACI,OAAO,CAAC,GAAG,CAAC;AAClC,CAAC;AACH,iBAAiBrC,KAAK,CAACgD,UAAU,CAACC,UAAU,CAACK,MAAM;AACnD,CAAC;AAED,MAAMC,gBAAgB,GAAGnE,MAAM,CAACkB,OAAO,CAAC;AACxC,mBAAmBN,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACnC,WAAWpB,KAAK,CAACiC,MAAM,CAACY,IAAI,CAACR,OAAO;AACpC,CAAC;AAED,MAAMmB,gBAAgB,GAAGpE,MAAM,CAACuD,GAAG;AACnC,eAAe3C,KAAK,CAACgD,UAAU,CAACG,QAAQ,CAACC,EAAE;AAC3C,mBAAmBpD,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACnC;AACA,aAAapB,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AAC7B,gBAAgBpB,KAAK,CAACiC,MAAM,CAACM,SAAS,CAACkB,OAAO;AAC9C,mBAAmBzD,KAAK,CAACyC,YAAY,CAACzB,EAAE;AACxC,sBAAsBhB,KAAK,CAACiC,MAAM,CAACE,OAAO,CAAC,GAAG,CAAC;AAC/C;AACA,uBAAuBnC,KAAK,CAACqB,WAAW,CAACI,EAAE;AAC3C,iBAAiBzB,KAAK,CAACgD,UAAU,CAACG,QAAQ,CAACnC,EAAE;AAC7C;AACA,CAAC;AAED,MAAM0C,sBAAsB,GAAGtE,MAAM,CAACmB,IAAI,CAAC;AAC3C,mBAAmBP,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACnC;AACA,CAAC;AAED,MAAMuC,mBAAmB,GAAGvE,MAAM,CAACuD,GAAG;AACtC,mBAAmB3C,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACnC,CAAC;AAED,MAAMwC,cAAc,GAAGxE,MAAM,CAACuD,GAAG;AACjC;AACA;AACA;AACA,mBAAmB3C,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACnC,CAAC;AAED,MAAMyC,YAAY,GAAGzE,MAAM,CAACmB,IAAI,CAAC;AACjC,iBAAiBP,KAAK,CAACgD,UAAU,CAACC,UAAU,CAACK,MAAM;AACnD,CAAC;AAACQ,EAAA,GAFID,YAAY;AAIlB,MAAME,kBAAkB,GAAG3E,MAAM,CAACsB,KAAK,CAAC;AACxC,iBAAiBV,KAAK,CAACgD,UAAU,CAACC,UAAU,CAACC,IAAI;AACjD,CAAC;AAED,MAAMc,iBAAiB,GAAG5E,MAAM,CAACoB,WAAW,CAAC;AAC7C;AACA,gBAAgBR,KAAK,CAACiC,MAAM,CAACE,OAAO,CAAC,GAAG,CAAC;AACzC,mBAAmBnC,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAM6C,kBAAkB,GAAG7E,MAAM,CAACqB,YAAY,CAAC;AAC/C,gBAAgBoB,KAAK,IAAI;EACrB,IAAIA,KAAK,CAACG,WAAW,EAAE,OAAOhC,KAAK,CAACiC,MAAM,CAACM,SAAS,CAACL,OAAO;EAC5D,OAAOlC,KAAK,CAACiC,MAAM,CAACM,SAAS,CAACF,OAAO;AACvC,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAM6B,kBAAkB,GAAG9E,MAAM,CAACiB,MAAM,CAAC;AACzC;AACA,YAAYL,KAAK,CAACmE,UAAU,CAACC,MAAM,CAACC,MAAM,CAACrD,EAAE;AAC7C,eAAehB,KAAK,CAACgD,UAAU,CAACG,QAAQ,CAACmB,IAAI;AAC7C,iBAAiBtE,KAAK,CAACgD,UAAU,CAACC,UAAU,CAACsB,QAAQ;AACrD;AACA,IAAI1C,KAAK,IAAI;EACT,IAAIA,KAAK,CAAC2C,QAAQ,EAAE;IAClB,OAAO;AACb,sBAAsBxE,KAAK,CAACiC,MAAM,CAACE,OAAO,CAAC,GAAG,CAAC;AAC/C,iBAAiBnC,KAAK,CAACiC,MAAM,CAACE,OAAO,CAAC,GAAG,CAAC;AAC1C;AACA;AACA;AACA;AACA,wBAAwBnC,KAAK,CAACe,OAAO,CAACuD,IAAI;AAC1C;AACA,OAAO;EACH;EACA,IAAIzC,KAAK,CAACG,WAAW,EAAE;IACrB,OAAO;AACb;AACA,OAAO;EACH;EACA,OAAO;AACX;AACA,KAAK;AACH,CAAC;AACH;AACA,uBAAuBhC,KAAK,CAACqB,WAAW,CAACI,EAAE;AAC3C,cAAczB,KAAK,CAACmE,UAAU,CAACC,MAAM,CAACC,MAAM,CAACjB,EAAE;AAC/C,iBAAiBpD,KAAK,CAACgD,UAAU,CAACG,QAAQ,CAACnC,EAAE;AAC7C;AACA,CAAC;AAED,MAAMyD,OAAO,GAAGpF,SAAS;AACzB;AACA;AACA,CAAC;AAED,MAAMqF,aAAa,GAAGA,CAAC;EACrBC,YAAY;EACZC,YAAY,GAAG,CAAC,CAAC;EACjBC,aAAa;EACbC;AACF,CAAC,KAAK;EACJ,MAAMC,WAAW,GAAGhF,cAAc,CAAC,CAAC;EAEpC,MAAMiF,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,QAAQ,GAAGN,YAAY,CAACK,KAAK,CAAC,IAAI;MAAEE,SAAS,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAG,CAAC;IACnE,OAAQF,QAAQ,CAACC,SAAS,GAAGD,QAAQ,CAACE,KAAK,GAAI,GAAG;EACpD,CAAC;EAED,MAAMC,aAAa,GAAIJ,KAAK,IAAK;IAC/B,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,KAAK;IAC7B,MAAMK,iBAAiB,GAAGV,YAAY,CAACK,KAAK,GAAG,CAAC,CAAC,IAAI;MAAEE,SAAS,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAG,CAAC;IAChF,OAAOE,iBAAiB,CAACH,SAAS,GAAGG,iBAAiB,CAACF,KAAK;EAC9D,CAAC;EAED,MAAMG,gBAAgB,GAAIN,KAAK,IAAK;IAClC,MAAMC,QAAQ,GAAGN,YAAY,CAACK,KAAK,CAAC,IAAI;MAAEE,SAAS,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAG,CAAC;IACnE,OAAOF,QAAQ,CAACC,SAAS,IAAID,QAAQ,CAACE,KAAK;EAC7C,CAAC;EAED,MAAMI,aAAa,GAAIP,KAAK,IAAK;IAC/B,IAAIM,gBAAgB,CAACN,KAAK,CAAC,EAAE,oBAAOrE,OAAA,CAACpB,WAAW;MAACiG,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7D,IAAIR,aAAa,CAACJ,KAAK,CAAC,EAAE,oBAAOrE,OAAA,CAACrB,IAAI;MAACkG,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnD,IAAIZ,KAAK,KAAKN,YAAY,EAAE,oBAAO/D,OAAA,CAACjB,MAAM;MAAC8F,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvD,oBAAOjF,OAAA,CAACnB,IAAI;MAACgG,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3B,CAAC;EAED,MAAMC,aAAa,GAAIb,KAAK,IAAK;IAC/B,IAAII,aAAa,CAACJ,KAAK,CAAC,EAAE,OAAO,QAAQ;IACzC,IAAIM,gBAAgB,CAACN,KAAK,CAAC,EAAE,OAAO,cAAc;IAClD,IAAIA,KAAK,KAAKN,YAAY,EAAE,OAAO,UAAU;IAC7C,OAAO,aAAa;EACtB,CAAC;EAED,MAAMoB,aAAa,GAAId,KAAK,IAAK;IAC/B,IAAII,aAAa,CAACJ,KAAK,CAAC,EAAE,oBAAOrE,OAAA,CAACrB,IAAI;MAACkG,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnD,IAAIN,gBAAgB,CAACN,KAAK,CAAC,EAAE,oBAAOrE,OAAA,CAAClB,MAAM;MAAC+F,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxD,oBAAOjF,OAAA,CAACtB,IAAI;MAACmG,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3B,CAAC;EAED,oBACEjF,OAAA,CAACX,SAAS;IAAC6E,SAAS,EAAEA,SAAU;IAAAkB,QAAA,gBAC9BpF,OAAA,CAACqF,MAAM;MAAAD,QAAA,gBACLpF,OAAA,CAACsF,KAAK;QAAAF,QAAA,EAAC;MAAoB;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnCjF,OAAA,CAACuF,QAAQ;QAAAH,QAAA,EAAC;MAGV;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAETjF,OAAA,CAACwF,UAAU;MAAAJ,QAAA,EACRK,MAAM,CAACC,OAAO,CAACxG,WAAW,CAAC,CAACyG,GAAG,CAAC,CAAC,CAACC,QAAQ,EAAEC,SAAS,CAAC,EAAE1E,KAAK,KAAK;QAAA,IAAA2E,mBAAA;QACjE,MAAMzB,KAAK,GAAG0B,QAAQ,CAACH,QAAQ,CAAC;QAChC,MAAM1E,QAAQ,GAAGuD,aAAa,CAACJ,KAAK,CAAC;QACrC,MAAMjD,WAAW,GAAGuD,gBAAgB,CAACN,KAAK,CAAC;QAC3C,MAAM7C,QAAQ,GAAG6C,KAAK,KAAKN,YAAY;QACvC,MAAMO,QAAQ,GAAGF,gBAAgB,CAACC,KAAK,CAAC;QAExC,oBACErE,OAAA,CAACgG,SAAS;UAER7E,KAAK,EAAEA,KAAM;UACbD,QAAQ,EAAEA,QAAS;UACnBE,WAAW,EAAEA,WAAY;UACzBI,QAAQ,EAAEA,QAAS;UACnByE,OAAO,EAAEA,CAAA,KAAM,CAAC/E,QAAQ,IAAI+C,aAAa,CAACI,KAAK,CAAE;UAAAe,QAAA,gBAEjDpF,OAAA,CAACkG,WAAW;YAAAd,QAAA,gBACVpF,OAAA,CAACmG,WAAW;cACV/E,WAAW,EAAEA,WAAY;cACzBF,QAAQ,EAAEA,QAAS;cAAAkE,QAAA,EAElBf;YAAK;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACdjF,OAAA,CAACoG,WAAW;cACVhF,WAAW,EAAEA,WAAY;cACzBF,QAAQ,EAAEA,QAAS;cAAAkE,QAAA,EAElBR,aAAa,CAACP,KAAK;YAAC;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEdjF,OAAA,CAACqG,UAAU;YAAAjB,QAAA,EAAES,SAAS,CAACzG;UAAK;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC1CjF,OAAA,CAACsG,UAAU;YAAAlB,QAAA,EAAES,SAAS,CAACU;UAAI;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACzCjF,OAAA,CAACwG,gBAAgB;YAAApB,QAAA,EAAES,SAAS,CAACY;UAAW;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAmB,CAAC,eAE5DjF,OAAA,CAAC0G,aAAa;YAAAtB,QAAA,gBACZpF,OAAA,CAACiD,YAAY;cAAAmC,QAAA,GACV,EAAAU,mBAAA,GAAA9B,YAAY,CAACK,KAAK,CAAC,cAAAyB,mBAAA,uBAAnBA,mBAAA,CAAqBvB,SAAS,KAAI,CAAC,EAAC,aACvC;YAAA;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC,eACfjF,OAAA,CAACJ,WAAW;cAAAwF,QAAA,eACVpF,OAAA,CAACH,YAAY;gBAAC8G,KAAK,EAAE;kBAAEC,KAAK,EAAE,GAAGtC,QAAQ;gBAAI;cAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACdjF,OAAA,CAACiD,YAAY;cAAAmC,QAAA,GAAEyB,IAAI,CAACC,KAAK,CAACxC,QAAQ,CAAC,EAAC,GAAC;YAAA;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eAEhBjF,OAAA,CAAC+G,YAAY;YACXnD,QAAQ,EAAE1C,QAAS;YACnBE,WAAW,EAAEA,WAAY;YACzB6E,OAAO,EAAGe,CAAC,IAAK;cACdA,CAAC,CAACC,eAAe,CAAC,CAAC;cACnB,IAAI,CAAC/F,QAAQ,EAAE+C,aAAa,CAACI,KAAK,CAAC;YACrC,CAAE;YAAAe,QAAA,GAEDD,aAAa,CAACd,KAAK,CAAC,EACpBa,aAAa,CAACb,KAAK,CAAC;UAAA;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA,GA9CVZ,KAAK;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA+CD,CAAC;MAEhB,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEhB,CAAC;AAACiC,GAAA,GArHIpD,aAAa;AAuHnB,eAAeA,aAAa;AAAC,IAAAZ,EAAA,EAAAgE,GAAA;AAAAC,YAAA,CAAAjE,EAAA;AAAAiE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}