{"ast": null, "code": "var _jsxFileName = \"D:\\\\ASL\\\\training-frontend\\\\src\\\\components\\\\SignupPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { UserPlus } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PageContainer = styled.div`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: var(--bg-tertiary);\n`;\n_c = PageContainer;\nconst Card = styled.div`\n  background: var(--bg-primary);\n  border-radius: var(--radius-2xl);\n  box-shadow: var(--shadow-xl);\n  padding: 3rem 2.5rem 2.5rem;\n  min-width: 350px;\n  max-width: 95vw;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n`;\n_c2 = Card;\nconst Title = styled.h1`\n  font-family: var(--font-primary);\n  font-size: 2rem;\n  font-weight: 700;\n  color: var(--primary-700);\n  margin-bottom: 1.5rem;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n`;\n_c3 = Title;\nconst Form = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 1.25rem;\n  width: 100%;\n`;\n_c4 = Form;\nconst Input = styled.input`\n  padding: 0.75rem 1rem;\n  border-radius: var(--radius-md);\n  border: 1px solid var(--border-light);\n  font-size: 1rem;\n  background: var(--bg-secondary);\n  color: var(--text-primary);\n`;\n_c5 = Input;\nconst SubmitButton = styled.button`\n  background: var(--primary-600);\n  color: white;\n  border: none;\n  border-radius: var(--radius-lg);\n  padding: 0.75rem 1.5rem;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  margin-top: 0.5rem;\n  box-shadow: var(--shadow-md);\n  transition: background 0.2s;\n  &:hover {\n    background: var(--primary-700);\n  }\n`;\n_c6 = SubmitButton;\nconst SwitchText = styled.div`\n  margin-top: 1.5rem;\n  color: var(--text-secondary);\n  font-size: 1rem;\n  text-align: center;\n`;\n_c7 = SwitchText;\nconst LinkButton = styled.button`\n  background: none;\n  border: none;\n  color: var(--primary-600);\n  font-weight: 600;\n  cursor: pointer;\n  font-size: 1rem;\n  margin-left: 0.25rem;\n  text-decoration: underline;\n`;\n_c8 = LinkButton;\nconst BackButton = styled.button`\n  background: none;\n  border: none;\n  color: var(--text-accent);\n  font-size: 1rem;\n  cursor: pointer;\n  margin-bottom: 2rem;\n  align-self: flex-start;\n  text-decoration: underline;\n`;\n_c9 = BackButton;\nconst SignupPage = ({\n  onSignup,\n  onLogin,\n  onBack\n}) => {\n  _s();\n  const [form, setForm] = useState({\n    name: '',\n    email: '',\n    password: ''\n  });\n  const handleChange = e => setForm({\n    ...form,\n    [e.target.name]: e.target.value\n  });\n  const handleSubmit = e => {\n    e.preventDefault();\n    onSignup({\n      name: form.name,\n      email: form.email,\n      signsLearned: 0,\n      signsRecorded: 0\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(PageContainer, {\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(BackButton, {\n        onClick: onBack,\n        children: \"\\u2190 Back\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Title, {\n        children: [/*#__PURE__*/_jsxDEV(UserPlus, {\n          size: 28\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 16\n        }, this), \" Sign Up for ASL Neural\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Input, {\n          name: \"name\",\n          type: \"text\",\n          placeholder: \"Full Name\",\n          value: form.name,\n          onChange: handleChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          name: \"email\",\n          type: \"email\",\n          placeholder: \"Email\",\n          value: form.email,\n          onChange: handleChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          name: \"password\",\n          type: \"password\",\n          placeholder: \"Password\",\n          value: form.password,\n          onChange: handleChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SubmitButton, {\n          type: \"submit\",\n          children: \"Sign Up\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SwitchText, {\n        children: [\"Already have an account?\", /*#__PURE__*/_jsxDEV(LinkButton, {\n          type: \"button\",\n          onClick: onLogin,\n          children: \"Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 98,\n    columnNumber: 5\n  }, this);\n};\n_s(SignupPage, \"5Nmj1/FXHSGB0eBG7cEdNY4WAbs=\");\n_c0 = SignupPage;\nexport default SignupPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0;\n$RefreshReg$(_c, \"PageContainer\");\n$RefreshReg$(_c2, \"Card\");\n$RefreshReg$(_c3, \"Title\");\n$RefreshReg$(_c4, \"Form\");\n$RefreshReg$(_c5, \"Input\");\n$RefreshReg$(_c6, \"SubmitButton\");\n$RefreshReg$(_c7, \"SwitchText\");\n$RefreshReg$(_c8, \"LinkButton\");\n$RefreshReg$(_c9, \"BackButton\");\n$RefreshReg$(_c0, \"SignupPage\");", "map": {"version": 3, "names": ["React", "useState", "styled", "UserPlus", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON>", "div", "_c", "Card", "_c2", "Title", "h1", "_c3", "Form", "form", "_c4", "Input", "input", "_c5", "SubmitButton", "button", "_c6", "SwitchText", "_c7", "LinkButton", "_c8", "BackButton", "_c9", "SignupPage", "onSignup", "onLogin", "onBack", "_s", "setForm", "name", "email", "password", "handleChange", "e", "target", "value", "handleSubmit", "preventDefault", "signsLearned", "signsRecorded", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "onSubmit", "type", "placeholder", "onChange", "required", "_c0", "$RefreshReg$"], "sources": ["D:/ASL/training-frontend/src/components/SignupPage.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport styled from 'styled-components';\r\nimport { UserPlus } from 'lucide-react';\r\n\r\nconst PageContainer = styled.div`\r\n  min-height: 100vh;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: var(--bg-tertiary);\r\n`;\r\nconst Card = styled.div`\r\n  background: var(--bg-primary);\r\n  border-radius: var(--radius-2xl);\r\n  box-shadow: var(--shadow-xl);\r\n  padding: 3rem 2.5rem 2.5rem;\r\n  min-width: 350px;\r\n  max-width: 95vw;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n`;\r\nconst Title = styled.h1`\r\n  font-family: var(--font-primary);\r\n  font-size: 2rem;\r\n  font-weight: 700;\r\n  color: var(--primary-700);\r\n  margin-bottom: 1.5rem;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.75rem;\r\n`;\r\nconst Form = styled.form`\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1.25rem;\r\n  width: 100%;\r\n`;\r\nconst Input = styled.input`\r\n  padding: 0.75rem 1rem;\r\n  border-radius: var(--radius-md);\r\n  border: 1px solid var(--border-light);\r\n  font-size: 1rem;\r\n  background: var(--bg-secondary);\r\n  color: var(--text-primary);\r\n`;\r\nconst SubmitButton = styled.button`\r\n  background: var(--primary-600);\r\n  color: white;\r\n  border: none;\r\n  border-radius: var(--radius-lg);\r\n  padding: 0.75rem 1.5rem;\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  margin-top: 0.5rem;\r\n  box-shadow: var(--shadow-md);\r\n  transition: background 0.2s;\r\n  &:hover {\r\n    background: var(--primary-700);\r\n  }\r\n`;\r\nconst SwitchText = styled.div`\r\n  margin-top: 1.5rem;\r\n  color: var(--text-secondary);\r\n  font-size: 1rem;\r\n  text-align: center;\r\n`;\r\nconst LinkButton = styled.button`\r\n  background: none;\r\n  border: none;\r\n  color: var(--primary-600);\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  font-size: 1rem;\r\n  margin-left: 0.25rem;\r\n  text-decoration: underline;\r\n`;\r\nconst BackButton = styled.button`\r\n  background: none;\r\n  border: none;\r\n  color: var(--text-accent);\r\n  font-size: 1rem;\r\n  cursor: pointer;\r\n  margin-bottom: 2rem;\r\n  align-self: flex-start;\r\n  text-decoration: underline;\r\n`;\r\n\r\nconst SignupPage = ({ onSignup, onLogin, onBack }) => {\r\n  const [form, setForm] = useState({ name: '', email: '', password: '' });\r\n  const handleChange = e => setForm({ ...form, [e.target.name]: e.target.value });\r\n  const handleSubmit = e => {\r\n    e.preventDefault();\r\n    onSignup({ name: form.name, email: form.email, signsLearned: 0, signsRecorded: 0 });\r\n  };\r\n  return (\r\n    <PageContainer>\r\n      <Card>\r\n        <BackButton onClick={onBack}>&larr; Back</BackButton>\r\n        <Title><UserPlus size={28} /> Sign Up for ASL Neural</Title>\r\n        <Form onSubmit={handleSubmit}>\r\n          <Input name=\"name\" type=\"text\" placeholder=\"Full Name\" value={form.name} onChange={handleChange} required />\r\n          <Input name=\"email\" type=\"email\" placeholder=\"Email\" value={form.email} onChange={handleChange} required />\r\n          <Input name=\"password\" type=\"password\" placeholder=\"Password\" value={form.password} onChange={handleChange} required />\r\n          <SubmitButton type=\"submit\">Sign Up</SubmitButton>\r\n        </Form>\r\n        <SwitchText>\r\n          Already have an account?\r\n          <LinkButton type=\"button\" onClick={onLogin}>Login</LinkButton>\r\n        </SwitchText>\r\n      </Card>\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default SignupPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,QAAQ,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,aAAa,GAAGJ,MAAM,CAACK,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,aAAa;AAOnB,MAAMG,IAAI,GAAGP,MAAM,CAACK,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAVID,IAAI;AAWV,MAAME,KAAK,GAAGT,MAAM,CAACU,EAAE;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GATIF,KAAK;AAUX,MAAMG,IAAI,GAAGZ,MAAM,CAACa,IAAI;AACxB;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,IAAI;AAMV,MAAMG,KAAK,GAAGf,MAAM,CAACgB,KAAK;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAPIF,KAAK;AAQX,MAAMG,YAAY,GAAGlB,MAAM,CAACmB,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAfIF,YAAY;AAgBlB,MAAMG,UAAU,GAAGrB,MAAM,CAACK,GAAG;AAC7B;AACA;AACA;AACA;AACA,CAAC;AAACiB,GAAA,GALID,UAAU;AAMhB,MAAME,UAAU,GAAGvB,MAAM,CAACmB,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GATID,UAAU;AAUhB,MAAME,UAAU,GAAGzB,MAAM,CAACmB,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACO,GAAA,GATID,UAAU;AAWhB,MAAME,UAAU,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,OAAO;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EACpD,MAAM,CAAClB,IAAI,EAAEmB,OAAO,CAAC,GAAGjC,QAAQ,CAAC;IAAEkC,IAAI,EAAE,EAAE;IAAEC,KAAK,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAG,CAAC,CAAC;EACvE,MAAMC,YAAY,GAAGC,CAAC,IAAIL,OAAO,CAAC;IAAE,GAAGnB,IAAI;IAAE,CAACwB,CAAC,CAACC,MAAM,CAACL,IAAI,GAAGI,CAAC,CAACC,MAAM,CAACC;EAAM,CAAC,CAAC;EAC/E,MAAMC,YAAY,GAAGH,CAAC,IAAI;IACxBA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClBb,QAAQ,CAAC;MAAEK,IAAI,EAAEpB,IAAI,CAACoB,IAAI;MAAEC,KAAK,EAAErB,IAAI,CAACqB,KAAK;MAAEQ,YAAY,EAAE,CAAC;MAAEC,aAAa,EAAE;IAAE,CAAC,CAAC;EACrF,CAAC;EACD,oBACExC,OAAA,CAACC,aAAa;IAAAwC,QAAA,eACZzC,OAAA,CAACI,IAAI;MAAAqC,QAAA,gBACHzC,OAAA,CAACsB,UAAU;QAACoB,OAAO,EAAEf,MAAO;QAAAc,QAAA,EAAC;MAAW;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACrD9C,OAAA,CAACM,KAAK;QAAAmC,QAAA,gBAACzC,OAAA,CAACF,QAAQ;UAACiD,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,2BAAuB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC5D9C,OAAA,CAACS,IAAI;QAACuC,QAAQ,EAAEX,YAAa;QAAAI,QAAA,gBAC3BzC,OAAA,CAACY,KAAK;UAACkB,IAAI,EAAC,MAAM;UAACmB,IAAI,EAAC,MAAM;UAACC,WAAW,EAAC,WAAW;UAACd,KAAK,EAAE1B,IAAI,CAACoB,IAAK;UAACqB,QAAQ,EAAElB,YAAa;UAACmB,QAAQ;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5G9C,OAAA,CAACY,KAAK;UAACkB,IAAI,EAAC,OAAO;UAACmB,IAAI,EAAC,OAAO;UAACC,WAAW,EAAC,OAAO;UAACd,KAAK,EAAE1B,IAAI,CAACqB,KAAM;UAACoB,QAAQ,EAAElB,YAAa;UAACmB,QAAQ;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3G9C,OAAA,CAACY,KAAK;UAACkB,IAAI,EAAC,UAAU;UAACmB,IAAI,EAAC,UAAU;UAACC,WAAW,EAAC,UAAU;UAACd,KAAK,EAAE1B,IAAI,CAACsB,QAAS;UAACmB,QAAQ,EAAElB,YAAa;UAACmB,QAAQ;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvH9C,OAAA,CAACe,YAAY;UAACkC,IAAI,EAAC,QAAQ;UAAAR,QAAA,EAAC;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,eACP9C,OAAA,CAACkB,UAAU;QAAAuB,QAAA,GAAC,0BAEV,eAAAzC,OAAA,CAACoB,UAAU;UAAC6B,IAAI,EAAC,QAAQ;UAACP,OAAO,EAAEhB,OAAQ;UAAAe,QAAA,EAAC;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACM,CAAC;AAEpB,CAAC;AAAClB,EAAA,CAzBIJ,UAAU;AAAA6B,GAAA,GAAV7B,UAAU;AA2BhB,eAAeA,UAAU;AAAC,IAAArB,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAA8B,GAAA;AAAAC,YAAA,CAAAnD,EAAA;AAAAmD,YAAA,CAAAjD,GAAA;AAAAiD,YAAA,CAAA9C,GAAA;AAAA8C,YAAA,CAAA3C,GAAA;AAAA2C,YAAA,CAAAxC,GAAA;AAAAwC,YAAA,CAAArC,GAAA;AAAAqC,YAAA,CAAAnC,GAAA;AAAAmC,YAAA,CAAAjC,GAAA;AAAAiC,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}