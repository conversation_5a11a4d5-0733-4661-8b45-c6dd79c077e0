{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13.67 8H18a2 2 0 0 1 2 2v4.33\",\n  key: \"7az073\"\n}], [\"path\", {\n  d: \"M2 14h2\",\n  key: \"vft8re\"\n}], [\"path\", {\n  d: \"M20 14h2\",\n  key: \"4cs60a\"\n}], [\"path\", {\n  d: \"M22 22 2 2\",\n  key: \"1r8tn9\"\n}], [\"path\", {\n  d: \"M8 8H6a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h12a2 2 0 0 0 1.414-.586\",\n  key: \"s09a7a\"\n}], [\"path\", {\n  d: \"M9 13v2\",\n  key: \"rq6x2g\"\n}], [\"path\", {\n  d: \"M9.67 4H12v2.33\",\n  key: \"110xot\"\n}]];\nconst BotOff = createLucideIcon(\"bot-off\", __iconNode);\nexport { __iconNode, BotOff as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "<PERSON><PERSON><PERSON><PERSON>", "createLucideIcon"], "sources": ["D:\\ASL\\ASL-Training\\node_modules\\lucide-react\\src\\icons\\bot-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M13.67 8H18a2 2 0 0 1 2 2v4.33', key: '7az073' }],\n  ['path', { d: 'M2 14h2', key: 'vft8re' }],\n  ['path', { d: 'M20 14h2', key: '4cs60a' }],\n  ['path', { d: 'M22 22 2 2', key: '1r8tn9' }],\n  ['path', { d: 'M8 8H6a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h12a2 2 0 0 0 1.414-.586', key: 's09a7a' }],\n  ['path', { d: 'M9 13v2', key: 'rq6x2g' }],\n  ['path', { d: 'M9.67 4H12v2.33', key: '110xot' }],\n];\n\n/**\n * @component @name BotOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTMuNjcgOEgxOGEyIDIgMCAwIDEgMiAydjQuMzMiIC8+CiAgPHBhdGggZD0iTTIgMTRoMiIgLz4KICA8cGF0aCBkPSJNMjAgMTRoMiIgLz4KICA8cGF0aCBkPSJNMjIgMjIgMiAyIiAvPgogIDxwYXRoIGQ9Ik04IDhINmEyIDIgMCAwIDAtMiAydjhhMiAyIDAgMCAwIDIgMmgxMmEyIDIgMCAwIDAgMS40MTQtLjU4NiIgLz4KICA8cGF0aCBkPSJNOSAxM3YyIiAvPgogIDxwYXRoIGQ9Ik05LjY3IDRIMTJ2Mi4zMyIgLz4KPC9zdmc+) - https://lucide.dev/icons/bot-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BotOff = createLucideIcon('bot-off', __iconNode);\n\nexport default BotOff;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,gCAAkC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,8DAAgE;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7F,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAU,GAClD;AAaM,MAAAC,MAAA,GAASC,gBAAiB,YAAWJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}