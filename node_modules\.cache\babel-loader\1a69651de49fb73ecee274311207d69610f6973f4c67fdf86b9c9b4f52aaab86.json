{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.293 2.293a1 1 0 0 1 1.414 0l2.5 2.5 5.994 1.227a1 1 0 0 1 .506 1.687l-7 7a1 1 0 0 1-1.687-.506l-1.227-5.994-2.5-2.5a1 1 0 0 1 0-1.414z\",\n  key: \"sb8slu\"\n}], [\"path\", {\n  d: \"m14.207 4.793-3.414 3.414\",\n  key: \"m2x3oj\"\n}], [\"path\", {\n  d: \"M3 20a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v1a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1z\",\n  key: \"8b3myj\"\n}], [\"path\", {\n  d: \"m9.086 6.5-4.793 4.793a1 1 0 0 0-.18 1.17L7 18\",\n  key: \"43s6cu\"\n}]];\nconst LampDesk = createLucideIcon(\"lamp-desk\", __iconNode);\nexport { __iconNode, LampDesk as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "LampDesk", "createLucideIcon"], "sources": ["D:\\ASL\\training-frontend\\node_modules\\lucide-react\\src\\icons\\lamp-desk.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M10.293 2.293a1 1 0 0 1 1.414 0l2.5 2.5 5.994 1.227a1 1 0 0 1 .506 1.687l-7 7a1 1 0 0 1-1.687-.506l-1.227-5.994-2.5-2.5a1 1 0 0 1 0-1.414z',\n      key: 'sb8slu',\n    },\n  ],\n  ['path', { d: 'm14.207 4.793-3.414 3.414', key: 'm2x3oj' }],\n  [\n    'path',\n    { d: 'M3 20a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v1a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1z', key: '8b3myj' },\n  ],\n  ['path', { d: 'm9.086 6.5-4.793 4.793a1 1 0 0 0-.18 1.17L7 18', key: '43s6cu' }],\n];\n\n/**\n * @component @name LampDesk\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAuMjkzIDIuMjkzYTEgMSAwIDAgMSAxLjQxNCAwbDIuNSAyLjUgNS45OTQgMS4yMjdhMSAxIDAgMCAxIC41MDYgMS42ODdsLTcgN2ExIDEgMCAwIDEtMS42ODctLjUwNmwtMS4yMjctNS45OTQtMi41LTIuNWExIDEgMCAwIDEgMC0xLjQxNHoiIC8+CiAgPHBhdGggZD0ibTE0LjIwNyA0Ljc5My0zLjQxNCAzLjQxNCIgLz4KICA8cGF0aCBkPSJNMyAyMGEyIDIgMCAwIDEgMi0yaDRhMiAyIDAgMCAxIDIgMnYxYTEgMSAwIDAgMS0xIDFINGExIDEgMCAwIDEtMS0xeiIgLz4KICA8cGF0aCBkPSJtOS4wODYgNi41LTQuNzkzIDQuNzkzYTEgMSAwIDAgMC0uMTggMS4xN0w3IDE4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/lamp-desk\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LampDesk = createLucideIcon('lamp-desk', __iconNode);\n\nexport default LampDesk;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,2BAA6B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CACE,QACA;EAAED,CAAA,EAAG,sEAAwE;EAAAC,GAAA,EAAK;AAAS,EAC7F,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,gDAAkD;EAAAC,GAAA,EAAK;AAAU,GACjF;AAaM,MAAAC,QAAA,GAAWC,gBAAiB,cAAaJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}