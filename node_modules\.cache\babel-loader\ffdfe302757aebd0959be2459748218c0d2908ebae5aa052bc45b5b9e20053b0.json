{"ast": null, "code": "// ASL Sign Language Learning Levels\n// Each level contains 20 signs organized by difficulty and theme\n// Updated to only include signs that exist in signLanguageData\nexport const SIGN_LEVELS={1:{name:\"Basic Greetings & Family\",description:\"Start with essential greetings and family members\",theme:\"👋 Greetings & Family\",signs:[{key:\"hello\",name:\"Hello\",gif:\"https://media.giphy.com/media/3o7TKNKOfKlIhbD3gY/giphy.gif\",description:\"Basic greeting sign\"},{key:\"bye\",name:\"Bye\",gif:\"https://c.tenor.com/vME77PObDN8AAAAC/asl-bye-asl-goodbye.gif\",description:\"Sign for goodbye\"},{key:\"please\",name:\"Please\",gif:\"https://lifeprint.com/asl101/gifs-animated/pleasecloseup.gif\",description:\"Polite request sign\"},{key:\"yes\",name:\"Yes\",gif:\"https://media.tenor.com/oYIirlyIih0AAAAC/yes-asl.gif\",description:\"Affirmative response\"},{key:\"no\",name:\"No\",gif:\"https://lifeprint.com/asl101/gifs/n/no-2-movement.gif\",description:\"Negative response\"},{key:\"mom\",name:\"Mom\",gif:\"https://lifeprint.com/asl101/gifs/m/mom.gif\",description:\"Sign for mother\"},{key:\"dad\",name:\"Dad\",gif:\"https://media.giphy.com/media/l0MYQcLKwtl5v6H1S/giphy.gif\",description:\"Sign for father\"},{key:\"brother\",name:\"Brother\",gif:\"https://lifeprint.com/asl101/gifs/b/brother.gif\",description:\"Sign for brother\"},{key:\"happy\",name:\"Happy\",gif:\"https://media0.giphy.com/media/3o7TKFpahYpUp4g0N2/giphy.gif?cid=790b7611bca188a92e2e759876756ef62ba95b7cdd337c77&rid=giphy.gif&ct=g\",description:\"Expression of joy\"},{key:\"sad\",name:\"Sad\",gif:\"https://lifeprint.com/asl101/gifs/s/sad.gif\",description:\"Expression of sadness\"},{key:\"bad\",name:\"Bad\",gif:\"https://media.giphy.com/media/v1.Y2lkPTc5MGI3NjExeThwMm9rZGVkejE2N2RhMWp0NTY3Z2pwNHBqZXFlOTF3ZGd0cG1zZSZlcD12MV9naWZzX3NlYXJjaCZjdD1n/l0MYL34CVEqLK27yU/giphy.gif\",description:\"Negative evaluation\"},{key:\"good\",name:\"Good\",gif:\"https://lifeprint.com/asl101/gifs/g/good.gif\",description:\"Positive evaluation\"},{key:\"help\",name:\"Help\",gif:\"https://lifeprint.com/asl101/gifs/h/help.gif\",description:\"Request for assistance\"},{key:\"sorry\",name:\"Sorry\",gif:\"https://lifeprint.com/asl101/gifs/s/sorry.gif\",description:\"Apology sign\"},{key:\"love\",name:\"Love\",gif:\"https://lifeprint.com/asl101/gifs/l/love.gif\",description:\"Expression of love\"},{key:\"friend\",name:\"Friend\",gif:\"https://lifeprint.com/asl101/gifs/f/friend.gif\",description:\"Sign for friend\"},{key:\"family\",name:\"Family\",gif:\"https://lifeprint.com/asl101/gifs/f/family.gif\",description:\"Sign for family\"},{key:\"sister\",name:\"Sister\",gif:\"https://lifeprint.com/asl101/gifs/s/sister.gif\",description:\"Sign for sister\"},{key:\"thank_you\",name:\"Thank You\",gif:\"https://media.giphy.com/media/l0MYL34CVEqLK27yU/giphy.gif\",description:\"Expression of gratitude\"},{key:\"name\",name:\"Name\",gif:\"https://lifeprint.com/asl101/gifs/n/name.gif\",description:\"Personal identifier\"}]},2:{name:\"Colors & Basic Objects\",description:\"Learn essential colors and everyday objects\",theme:\"🎨 Colors & Objects\",signs:[{key:\"red\",name:\"Red\",gif:\"https://lifeprint.com/asl101/gifs/r/red.gif\",description:\"Color red\"},{key:\"blue\",name:\"Blue\",gif:\"https://lifeprint.com/asl101/gifs/b/blue-1.gif\",description:\"Color blue\"},{key:\"green\",name:\"Green\",gif:\"https://lifeprint.com/asl101/gifs/g/green.gif\",description:\"Color green\"},{key:\"yellow\",name:\"Yellow\",gif:\"https://lifeprint.com/asl101/gifs/y/yellow.gif\",description:\"Color yellow\"},{key:\"black\",name:\"Black\",gif:\"https://th.bing.com/th/id/R.********************************?rik=52tGw7%2fGcx2HtwMm9rZGVkejE2N2RhMWp0NTY3Z2pwNHBqZXFlOTF3ZGd0cG1zZSZlcD12MV9naWZzX3NlYXJjaCZjdD1n/l0MYL34CVEqLK27yU/giphy.gif\",description:\"Color black\"},{key:\"white\",name:\"White\",gif:\"https://lifeprint.com/asl101/gifs/w/white.gif\",description:\"Color white\"},{key:\"brown\",name:\"Brown\",gif:\"https://lifeprint.com/asl101/gifs/b/brown.gif\",description:\"Color brown\"},{key:\"orange\",name:\"Orange\",gif:\"https://lifeprint.com/asl101/gifs/o/orange.gif\",description:\"Color orange\"},{key:\"book\",name:\"Book\",gif:\"https://media.giphy.com/media/l0MYL43dl4pQEn3uE/giphy.gif\",description:\"Reading material\"},{key:\"chair\",name:\"Chair\",gif:\"https://th.bing.com/th/id/OIP.5kr1MkVLnuN2Z9Jkw-0QpAHaE-?w=237&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",description:\"Furniture for sitting\"},{key:\"table\",name:\"Table\",gif:\"https://lifeprint.com/asl101/gifs/t/table.gif\",description:\"Furniture surface\"},{key:\"bed\",name:\"Bed\",gif:\"https://lifeprint.com/asl101/gifs/b/bed-1.gif\",description:\"Sleeping furniture\"},{key:\"door\",name:\"Door\",gif:\"https://lifeprint.com/asl101/gifs/d/door.gif\",description:\"Entry/exit\"},{key:\"window\",name:\"Window\",gif:\"https://lifeprint.com/asl101/gifs/w/window.gif\",description:\"Glass opening\"},{key:\"car\",name:\"Car\",gif:\"https://th.bing.com/th/id/OIP.wxw32OaIdqFt8f_ucHVoRgHaEH?w=308&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",description:\"Vehicle\"},{key:\"house\",name:\"House\",gif:\"https://lifeprint.com/asl101/gifs/h/house.gif\",description:\"Home building\"},{key:\"school\",name:\"School\",gif:\"https://lifeprint.com/asl101/gifs/s/school.gif\",description:\"Educational institution\"},{key:\"water\",name:\"Water\",gif:\"https://lifeprint.com/asl101/gifs/w/water-2.gif\",description:\"Clear liquid\"},{key:\"food\",name:\"Food\",gif:\"https://i.pinimg.com/originals/cc/bb/0c/ccbb0c143db0b51e9947a5966db42fd8.gif\",description:\"Nourishment\"},{key:\"milk\",name:\"Milk\",gif:\"https://lifeprint.com/asl101/gifs/m/milk.gif\",description:\"Dairy beverage\"}]},3:{name:\"Animals & Nature\",description:\"Discover animals and nature signs\",theme:\"🐾 Animals & Nature\",signs:[{key:\"cat\",name:\"Cat\",gif:\"https://lifeprint.com/asl101/gifs-animated/cat-02.gif\",description:\"Feline pet\"},{key:\"dog\",name:\"Dog\",gif:\"https://th.bing.com/th/id/R.********************************?rik=uVshGsOHXgldoQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fd%2fdog1.gif&ehk=Xnfrvg0xwy1u%2fae9vWdKYMT25%2bF6qoteW2FThCbVrOA%3d&risl=&pid=ImgRaw&r=0\",description:\"Canine pet\"},{key:\"bird\",name:\"Bird\",gif:\"https://lifeprint.com/asl101/gifs/b/bird.gif\",description:\"Flying animal\"},{key:\"fish\",name:\"Fish\",gif:\"https://th.bing.com/th/id/OIP.Lzhd7lIIa-V4H3faS1d3mQHaHa?rs=1&pid=ImgDetMain\",description:\"Aquatic animal\"},{key:\"cow\",name:\"Cow\",gif:\"https://lifeprint.com/asl101/gifs/c/cow.gif\",description:\"Farm animal\"},{key:\"horse\",name:\"Horse\",gif:\"https://lifeprint.com/asl101/gifs/h/horse.gif\",description:\"Riding animal\"},{key:\"pig\",name:\"Pig\",gif:\"https://lifeprint.com/asl101/gifs/p/pig.gif\",description:\"Farm animal\"},{key:\"chicken\",name:\"Chicken\",gif:\"https://lifeprint.com/asl101/gifs/c/chicken.gif\",description:\"Poultry\"},{key:\"rabbit\",name:\"Rabbit\",gif:\"https://lifeprint.com/asl101/gifs/r/rabbit.gif\",description:\"Small mammal\"},{key:\"bear\",name:\"Bear\",gif:\"https://lifeprint.com/asl101/gifs/b/bear.gif\",description:\"Large mammal\"},{key:\"tree\",name:\"Tree\",gif:\"https://lifeprint.com/asl101/gifs/t/tree.gif\",description:\"Tall plant\"},{key:\"flower\",name:\"Flower\",gif:\"https://media.giphy.com/media/3o7TKGkqPpLUdFiFPy/giphy.gif\",description:\"Blooming plant\"},{key:\"grass\",name:\"Grass\",gif:\"https://lifeprint.com/asl101/gifs/g/grass.gif\",description:\"Ground cover\"},{key:\"sun\",name:\"Sun\",gif:\"https://lifeprint.com/asl101/gifs/s/sun.gif\",description:\"Solar star\"},{key:\"moon\",name:\"Moon\",gif:\"https://lifeprint.com/asl101/gifs/m/moon.gif\",description:\"Night satellite\"},{key:\"star\",name:\"Star\",gif:\"https://lifeprint.com/asl101/gifs/s/star.gif\",description:\"Celestial body\"},{key:\"cloud\",name:\"Cloud\",gif:\"https://th.bing.com/th/id/OIP.hMO89bV2zwVcIVIa7FOT5QHaEc?rs=1&pid=ImgDetMain\",description:\"Sky formation\"},{key:\"rain\",name:\"Rain\",gif:\"https://lifeprint.com/asl101/gifs/r/rain.gif\",description:\"Water precipitation\"},{key:\"snow\",name:\"Snow\",gif:\"https://lifeprint.com/asl101/gifs/s/snow.gif\",description:\"Frozen precipitation\"},{key:\"wind\",name:\"Wind\",gif:\"https://lifeprint.com/asl101/gifs/w/wind.gif\",description:\"Air movement\"}]},4:{name:\"Body Parts & Actions\",description:\"Learn body parts and common actions\",theme:\"👤 Body & Actions\",signs:[{key:\"head\",name:\"Head\",gif:\"https://lifeprint.com/asl101/gifs/h/head.gif\",description:\"Top body part\"},{key:\"face\",name:\"Face\",gif:\"https://lifeprint.com/asl101/gifs/f/face.gif\",description:\"Front of head\"},{key:\"eye\",name:\"Eye\",gif:\"https://lifeprint.com/asl101/gifs/e/eyes.gif\",description:\"Vision organ\"},{key:\"nose\",name:\"Nose\",gif:\"https://lifeprint.com/asl101/gifs/n/nose.gif\",description:\"Smell organ\"},{key:\"mouth\",name:\"Mouth\",gif:\"https://lifeprint.com/asl101/gifs/m/mouth.gif\",description:\"Eating/speaking organ\"},{key:\"ear\",name:\"Ear\",gif:\"https://lifeprint.com/asl101/signjpegs/e/ears.h3.jpg\",description:\"Hearing organ\"},{key:\"hand\",name:\"Hand\",gif:\"https://lifeprint.com/asl101/gifs/h/hand.gif\",description:\"Grasping appendage\"},{key:\"arm\",name:\"Arm\",gif:\"https://th.bing.com/th/id/OIP.KkJLqfbp6XEHiIe-jOUb2AHaEc?w=251&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",description:\"Upper limb\"},{key:\"leg\",name:\"Leg\",gif:\"https://lifeprint.com/asl101/gifs/l/leg.gif\",description:\"Lower limb\"},{key:\"foot\",name:\"Foot\",gif:\"https://lifeprint.com/asl101/gifs/f/foot.gif\",description:\"Walking appendage\"},{key:\"walk\",name:\"Walk\",gif:\"https://lifeprint.com/asl101/gifs/w/walk.gif\",description:\"Move on foot\"},{key:\"run\",name:\"Run\",gif:\"https://lifeprint.com/asl101/gifs/r/run.gif\",description:\"Move quickly\"},{key:\"sit\",name:\"Sit\",gif:\"https://lifeprint.com/asl101/gifs/s/sit.gif\",description:\"Rest position\"},{key:\"stand\",name:\"Stand\",gif:\"https://lifeprint.com/asl101/gifs/s/stand.gif\",description:\"Upright position\"},{key:\"sleep\",name:\"Sleep\",gif:\"https://lifeprint.com/asl101/gifs/s/sleep.gif\",description:\"Rest state\"},{key:\"eat\",name:\"Eat\",gif:\"https://lifeprint.com/asl101/gifs/e/eat.gif\",description:\"Consume food\"},{key:\"drink\",name:\"Drink\",gif:\"https://www.lifeprint.com/asl101/gifs/d/drink-c.gif\",description:\"Consume liquid\"},{key:\"see\",name:\"See\",gif:\"https://lifeprint.com/asl101/gifs/s/see.gif\",description:\"Use vision\"},{key:\"hear\",name:\"Hear\",gif:\"https://lifeprint.com/asl101/gifs/h/hear.gif\",description:\"Use hearing\"},{key:\"touch\",name:\"Touch\",gif:\"https://lifeprint.com/asl101/gifs/t/touch.gif\",description:\"Physical contact\"}]},5:{name:\"Numbers & Time\",description:\"Master numbers and time concepts\",theme:\"🔢 Numbers & Time\",signs:[{key:\"one\",name:\"One\",gif:\"https://lifeprint.com/asl101/gifs/numbers/1.gif\",description:\"Number 1\"},{key:\"two\",name:\"Two\",gif:\"https://lifeprint.com/asl101/gifs/numbers/2.gif\",description:\"Number 2\"},{key:\"three\",name:\"Three\",gif:\"https://lifeprint.com/asl101/gifs/numbers/3.gif\",description:\"Number 3\"},{key:\"four\",name:\"Four\",gif:\"https://lifeprint.com/asl101/gifs/numbers/4.gif\",description:\"Number 4\"},{key:\"five\",name:\"Five\",gif:\"https://lifeprint.com/asl101/gifs/numbers/5.gif\",description:\"Number 5\"},{key:\"six\",name:\"Six\",gif:\"https://lifeprint.com/asl101/gifs/numbers/6.gif\",description:\"Number 6\"},{key:\"seven\",name:\"Seven\",gif:\"https://lifeprint.com/asl101/gifs/numbers/7.gif\",description:\"Number 7\"},{key:\"eight\",name:\"Eight\",gif:\"https://lifeprint.com/asl101/gifs/numbers/8.gif\",description:\"Number 8\"},{key:\"nine\",name:\"Nine\",gif:\"https://lifeprint.com/asl101/gifs/numbers/9.gif\",description:\"Number 9\"},{key:\"ten\",name:\"Ten\",gif:\"https://lifeprint.com/asl101/gifs/numbers/10.gif\",description:\"Number 10\"},{key:\"today\",name:\"Today\",gif:\"https://lifeprint.com/asl101/gifs/t/today.gif\",description:\"Current day\"},{key:\"tomorrow\",name:\"Tomorrow\",gif:\"https://lifeprint.com/asl101/gifs/t/tomorrow.gif\",description:\"Next day\"},{key:\"yesterday\",name:\"Yesterday\",gif:\"https://lifeprint.com/asl101/gifs/y/yesterday.gif\",description:\"Previous day\"},{key:\"morning\",name:\"Morning\",gif:\"https://lifeprint.com/asl101/gifs/m/morning.gif\",description:\"Early day\"},{key:\"afternoon\",name:\"Afternoon\",gif:\"https://lifeprint.com/asl101/gifs/a/afternoon.gif\",description:\"Mid day\"},{key:\"night\",name:\"Night\",gif:\"https://lifeprint.com/asl101/gifs/n/night.gif\",description:\"Dark time\"},{key:\"week\",name:\"Week\",gif:\"https://lifeprint.com/asl101/gifs/w/week.gif\",description:\"7-day period\"},{key:\"month\",name:\"Month\",gif:\"https://lifeprint.com/asl101/gifs/m/month.gif\",description:\"Calendar period\"},{key:\"year\",name:\"Year\",gif:\"https://lifeprint.com/asl101/gifs/y/year.gif\",description:\"Annual period\"},{key:\"time\",name:\"Time\",gif:\"https://lifeprint.com/asl101/gifs/t/time.gif\",description:\"Clock measurement\"}]}};// Helper functions\nexport const getSignsForLevel=level=>{var _SIGN_LEVELS$level;return((_SIGN_LEVELS$level=SIGN_LEVELS[level])===null||_SIGN_LEVELS$level===void 0?void 0:_SIGN_LEVELS$level.signs)||[];};export const getLevelInfo=level=>{return SIGN_LEVELS[level]||null;};export const getTotalLevels=()=>{return Object.keys(SIGN_LEVELS).length;};export const isValidLevel=level=>{return SIGN_LEVELS.hasOwnProperty(level);};", "map": {"version": 3, "names": ["SIGN_LEVELS", "name", "description", "theme", "signs", "key", "gif", "getSignsForLevel", "level", "_SIGN_LEVELS$level", "getLevelInfo", "getTotalLevels", "Object", "keys", "length", "isValidLevel", "hasOwnProperty"], "sources": ["D:/ASL/ASL-Training/src/data/signLevels.js"], "sourcesContent": ["// ASL Sign Language Learning Levels\n// Each level contains 20 signs organized by difficulty and theme\n// Updated to only include signs that exist in signLanguageData\n\nexport const SIGN_LEVELS = {\n  1: {\n    name: \"Basic Greetings & Family\",\n    description: \"Start with essential greetings and family members\",\n    theme: \"👋 Greetings & Family\",\n    signs: [\n      { key: \"hello\", name: \"Hello\", gif: \"https://media.giphy.com/media/3o7TKNKOfKlIhbD3gY/giphy.gif\", description: \"Basic greeting sign\" },\n      { key: \"bye\", name: \"Bye\", gif: \"https://c.tenor.com/vME77PObDN8AAAAC/asl-bye-asl-goodbye.gif\", description: \"Sign for goodbye\" },\n      { key: \"please\", name: \"Please\", gif: \"https://lifeprint.com/asl101/gifs-animated/pleasecloseup.gif\", description: \"Polite request sign\" },\n      { key: \"yes\", name: \"Yes\", gif: \"https://media.tenor.com/oYIirlyIih0AAAAC/yes-asl.gif\", description: \"Affirmative response\" },\n      { key: \"no\", name: \"No\", gif: \"https://lifeprint.com/asl101/gifs/n/no-2-movement.gif\", description: \"Negative response\" },\n      { key: \"mom\", name: \"Mom\", gif: \"https://lifeprint.com/asl101/gifs/m/mom.gif\", description: \"Sign for mother\" },\n      { key: \"dad\", name: \"Dad\", gif: \"https://media.giphy.com/media/l0MYQcLKwtl5v6H1S/giphy.gif\", description: \"Sign for father\" },\n      { key: \"brother\", name: \"Brother\", gif: \"https://lifeprint.com/asl101/gifs/b/brother.gif\", description: \"Sign for brother\" },\n      { key: \"happy\", name: \"Happy\", gif: \"https://media0.giphy.com/media/3o7TKFpahYpUp4g0N2/giphy.gif?cid=790b7611bca188a92e2e759876756ef62ba95b7cdd337c77&rid=giphy.gif&ct=g\", description: \"Expression of joy\" },\n      { key: \"sad\", name: \"Sad\", gif: \"https://lifeprint.com/asl101/gifs/s/sad.gif\", description: \"Expression of sadness\" },\n      { key: \"bad\", name: \"Bad\", gif: \"https://media.giphy.com/media/v1.Y2lkPTc5MGI3NjExeThwMm9rZGVkejE2N2RhMWp0NTY3Z2pwNHBqZXFlOTF3ZGd0cG1zZSZlcD12MV9naWZzX3NlYXJjaCZjdD1n/l0MYL34CVEqLK27yU/giphy.gif\", description: \"Negative evaluation\" },\n      { key: \"good\", name: \"Good\", gif: \"https://lifeprint.com/asl101/gifs/g/good.gif\", description: \"Positive evaluation\" },\n      { key: \"help\", name: \"Help\", gif: \"https://lifeprint.com/asl101/gifs/h/help.gif\", description: \"Request for assistance\" },\n      { key: \"sorry\", name: \"Sorry\", gif: \"https://lifeprint.com/asl101/gifs/s/sorry.gif\", description: \"Apology sign\" },\n      { key: \"love\", name: \"Love\", gif: \"https://lifeprint.com/asl101/gifs/l/love.gif\", description: \"Expression of love\" },\n      { key: \"friend\", name: \"Friend\", gif: \"https://lifeprint.com/asl101/gifs/f/friend.gif\", description: \"Sign for friend\" },\n      { key: \"family\", name: \"Family\", gif: \"https://lifeprint.com/asl101/gifs/f/family.gif\", description: \"Sign for family\" },\n      { key: \"sister\", name: \"Sister\", gif: \"https://lifeprint.com/asl101/gifs/s/sister.gif\", description: \"Sign for sister\" },\n      { key: \"thank_you\", name: \"Thank You\", gif: \"https://media.giphy.com/media/l0MYL34CVEqLK27yU/giphy.gif\", description: \"Expression of gratitude\" },\n      { key: \"name\", name: \"Name\", gif: \"https://lifeprint.com/asl101/gifs/n/name.gif\", description: \"Personal identifier\" }\n    ]\n  },\n  2: {\n    name: \"Colors & Basic Objects\",\n    description: \"Learn essential colors and everyday objects\",\n    theme: \"🎨 Colors & Objects\",\n    signs: [\n      { key: \"red\", name: \"Red\", gif: \"https://lifeprint.com/asl101/gifs/r/red.gif\", description: \"Color red\" },\n      { key: \"blue\", name: \"Blue\", gif: \"https://lifeprint.com/asl101/gifs/b/blue-1.gif\", description: \"Color blue\" },\n      { key: \"green\", name: \"Green\", gif: \"https://lifeprint.com/asl101/gifs/g/green.gif\", description: \"Color green\" },\n      { key: \"yellow\", name: \"Yellow\", gif: \"https://lifeprint.com/asl101/gifs/y/yellow.gif\", description: \"Color yellow\" },\n      { key: \"black\", name: \"Black\", gif: \"https://th.bing.com/th/id/R.********************************?rik=52tGw7%2fGcx2HtwMm9rZGVkejE2N2RhMWp0NTY3Z2pwNHBqZXFlOTF3ZGd0cG1zZSZlcD12MV9naWZzX3NlYXJjaCZjdD1n/l0MYL34CVEqLK27yU/giphy.gif\", description: \"Color black\" },\n      { key: \"white\", name: \"White\", gif: \"https://lifeprint.com/asl101/gifs/w/white.gif\", description: \"Color white\" },\n      { key: \"brown\", name: \"Brown\", gif: \"https://lifeprint.com/asl101/gifs/b/brown.gif\", description: \"Color brown\" },\n      { key: \"orange\", name: \"Orange\", gif: \"https://lifeprint.com/asl101/gifs/o/orange.gif\", description: \"Color orange\" },\n      { key: \"book\", name: \"Book\", gif: \"https://media.giphy.com/media/l0MYL43dl4pQEn3uE/giphy.gif\", description: \"Reading material\" },\n      { key: \"chair\", name: \"Chair\", gif: \"https://th.bing.com/th/id/OIP.5kr1MkVLnuN2Z9Jkw-0QpAHaE-?w=237&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Furniture for sitting\" },\n      { key: \"table\", name: \"Table\", gif: \"https://lifeprint.com/asl101/gifs/t/table.gif\", description: \"Furniture surface\" },\n      { key: \"bed\", name: \"Bed\", gif: \"https://lifeprint.com/asl101/gifs/b/bed-1.gif\", description: \"Sleeping furniture\" },\n      { key: \"door\", name: \"Door\", gif: \"https://lifeprint.com/asl101/gifs/d/door.gif\", description: \"Entry/exit\" },\n      { key: \"window\", name: \"Window\", gif: \"https://lifeprint.com/asl101/gifs/w/window.gif\", description: \"Glass opening\" },\n      { key: \"car\", name: \"Car\", gif: \"https://th.bing.com/th/id/OIP.wxw32OaIdqFt8f_ucHVoRgHaEH?w=308&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Vehicle\" },\n      { key: \"house\", name: \"House\", gif: \"https://lifeprint.com/asl101/gifs/h/house.gif\", description: \"Home building\" },\n      { key: \"school\", name: \"School\", gif: \"https://lifeprint.com/asl101/gifs/s/school.gif\", description: \"Educational institution\" },\n      { key: \"water\", name: \"Water\", gif: \"https://lifeprint.com/asl101/gifs/w/water-2.gif\", description: \"Clear liquid\" },\n      { key: \"food\", name: \"Food\", gif: \"https://i.pinimg.com/originals/cc/bb/0c/ccbb0c143db0b51e9947a5966db42fd8.gif\", description: \"Nourishment\" },\n      { key: \"milk\", name: \"Milk\", gif: \"https://lifeprint.com/asl101/gifs/m/milk.gif\", description: \"Dairy beverage\" }\n    ]\n  },\n  3: {\n    name: \"Animals & Nature\",\n    description: \"Discover animals and nature signs\",\n    theme: \"🐾 Animals & Nature\",\n    signs: [\n      { key: \"cat\", name: \"Cat\", gif: \"https://lifeprint.com/asl101/gifs-animated/cat-02.gif\", description: \"Feline pet\" },\n      { key: \"dog\", name: \"Dog\", gif: \"https://th.bing.com/th/id/R.********************************?rik=uVshGsOHXgldoQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fd%2fdog1.gif&ehk=Xnfrvg0xwy1u%2fae9vWdKYMT25%2bF6qoteW2FThCbVrOA%3d&risl=&pid=ImgRaw&r=0\", description: \"Canine pet\" },\n      { key: \"bird\", name: \"Bird\", gif: \"https://lifeprint.com/asl101/gifs/b/bird.gif\", description: \"Flying animal\" },\n      { key: \"fish\", name: \"Fish\", gif: \"https://th.bing.com/th/id/OIP.Lzhd7lIIa-V4H3faS1d3mQHaHa?rs=1&pid=ImgDetMain\", description: \"Aquatic animal\" },\n      { key: \"cow\", name: \"Cow\", gif: \"https://lifeprint.com/asl101/gifs/c/cow.gif\", description: \"Farm animal\" },\n      { key: \"horse\", name: \"Horse\", gif: \"https://lifeprint.com/asl101/gifs/h/horse.gif\", description: \"Riding animal\" },\n      { key: \"pig\", name: \"Pig\", gif: \"https://lifeprint.com/asl101/gifs/p/pig.gif\", description: \"Farm animal\" },\n      { key: \"chicken\", name: \"Chicken\", gif: \"https://lifeprint.com/asl101/gifs/c/chicken.gif\", description: \"Poultry\" },\n      { key: \"rabbit\", name: \"Rabbit\", gif: \"https://lifeprint.com/asl101/gifs/r/rabbit.gif\", description: \"Small mammal\" },\n      { key: \"bear\", name: \"Bear\", gif: \"https://lifeprint.com/asl101/gifs/b/bear.gif\", description: \"Large mammal\" },\n      { key: \"tree\", name: \"Tree\", gif: \"https://lifeprint.com/asl101/gifs/t/tree.gif\", description: \"Tall plant\" },\n      { key: \"flower\", name: \"Flower\", gif: \"https://media.giphy.com/media/3o7TKGkqPpLUdFiFPy/giphy.gif\", description: \"Blooming plant\" },\n      { key: \"grass\", name: \"Grass\", gif: \"https://lifeprint.com/asl101/gifs/g/grass.gif\", description: \"Ground cover\" },\n      { key: \"sun\", name: \"Sun\", gif: \"https://lifeprint.com/asl101/gifs/s/sun.gif\", description: \"Solar star\" },\n      { key: \"moon\", name: \"Moon\", gif: \"https://lifeprint.com/asl101/gifs/m/moon.gif\", description: \"Night satellite\" },\n      { key: \"star\", name: \"Star\", gif: \"https://lifeprint.com/asl101/gifs/s/star.gif\", description: \"Celestial body\" },\n      { key: \"cloud\", name: \"Cloud\", gif: \"https://th.bing.com/th/id/OIP.hMO89bV2zwVcIVIa7FOT5QHaEc?rs=1&pid=ImgDetMain\", description: \"Sky formation\" },\n      { key: \"rain\", name: \"Rain\", gif: \"https://lifeprint.com/asl101/gifs/r/rain.gif\", description: \"Water precipitation\" },\n      { key: \"snow\", name: \"Snow\", gif: \"https://lifeprint.com/asl101/gifs/s/snow.gif\", description: \"Frozen precipitation\" },\n      { key: \"wind\", name: \"Wind\", gif: \"https://lifeprint.com/asl101/gifs/w/wind.gif\", description: \"Air movement\" }\n    ]\n  },\n  4: {\n    name: \"Body Parts & Actions\",\n    description: \"Learn body parts and common actions\",\n    theme: \"👤 Body & Actions\",\n    signs: [\n      { key: \"head\", name: \"Head\", gif: \"https://lifeprint.com/asl101/gifs/h/head.gif\", description: \"Top body part\" },\n      { key: \"face\", name: \"Face\", gif: \"https://lifeprint.com/asl101/gifs/f/face.gif\", description: \"Front of head\" },\n      { key: \"eye\", name: \"Eye\", gif: \"https://lifeprint.com/asl101/gifs/e/eyes.gif\", description: \"Vision organ\" },\n      { key: \"nose\", name: \"Nose\", gif: \"https://lifeprint.com/asl101/gifs/n/nose.gif\", description: \"Smell organ\" },\n      { key: \"mouth\", name: \"Mouth\", gif: \"https://lifeprint.com/asl101/gifs/m/mouth.gif\", description: \"Eating/speaking organ\" },\n      { key: \"ear\", name: \"Ear\", gif: \"https://lifeprint.com/asl101/signjpegs/e/ears.h3.jpg\", description: \"Hearing organ\" },\n      { key: \"hand\", name: \"Hand\", gif: \"https://lifeprint.com/asl101/gifs/h/hand.gif\", description: \"Grasping appendage\" },\n      { key: \"arm\", name: \"Arm\", gif: \"https://th.bing.com/th/id/OIP.KkJLqfbp6XEHiIe-jOUb2AHaEc?w=251&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Upper limb\" },\n      { key: \"leg\", name: \"Leg\", gif: \"https://lifeprint.com/asl101/gifs/l/leg.gif\", description: \"Lower limb\" },\n      { key: \"foot\", name: \"Foot\", gif: \"https://lifeprint.com/asl101/gifs/f/foot.gif\", description: \"Walking appendage\" },\n      { key: \"walk\", name: \"Walk\", gif: \"https://lifeprint.com/asl101/gifs/w/walk.gif\", description: \"Move on foot\" },\n      { key: \"run\", name: \"Run\", gif: \"https://lifeprint.com/asl101/gifs/r/run.gif\", description: \"Move quickly\" },\n      { key: \"sit\", name: \"Sit\", gif: \"https://lifeprint.com/asl101/gifs/s/sit.gif\", description: \"Rest position\" },\n      { key: \"stand\", name: \"Stand\", gif: \"https://lifeprint.com/asl101/gifs/s/stand.gif\", description: \"Upright position\" },\n      { key: \"sleep\", name: \"Sleep\", gif: \"https://lifeprint.com/asl101/gifs/s/sleep.gif\", description: \"Rest state\" },\n      { key: \"eat\", name: \"Eat\", gif: \"https://lifeprint.com/asl101/gifs/e/eat.gif\", description: \"Consume food\" },\n      { key: \"drink\", name: \"Drink\", gif: \"https://www.lifeprint.com/asl101/gifs/d/drink-c.gif\", description: \"Consume liquid\" },\n      { key: \"see\", name: \"See\", gif: \"https://lifeprint.com/asl101/gifs/s/see.gif\", description: \"Use vision\" },\n      { key: \"hear\", name: \"Hear\", gif: \"https://lifeprint.com/asl101/gifs/h/hear.gif\", description: \"Use hearing\" },\n      { key: \"touch\", name: \"Touch\", gif: \"https://lifeprint.com/asl101/gifs/t/touch.gif\", description: \"Physical contact\" }\n    ]\n  },\n  5: {\n    name: \"Numbers & Time\",\n    description: \"Master numbers and time concepts\",\n    theme: \"🔢 Numbers & Time\",\n    signs: [\n      { key: \"one\", name: \"One\", gif: \"https://lifeprint.com/asl101/gifs/numbers/1.gif\", description: \"Number 1\" },\n      { key: \"two\", name: \"Two\", gif: \"https://lifeprint.com/asl101/gifs/numbers/2.gif\", description: \"Number 2\" },\n      { key: \"three\", name: \"Three\", gif: \"https://lifeprint.com/asl101/gifs/numbers/3.gif\", description: \"Number 3\" },\n      { key: \"four\", name: \"Four\", gif: \"https://lifeprint.com/asl101/gifs/numbers/4.gif\", description: \"Number 4\" },\n      { key: \"five\", name: \"Five\", gif: \"https://lifeprint.com/asl101/gifs/numbers/5.gif\", description: \"Number 5\" },\n      { key: \"six\", name: \"Six\", gif: \"https://lifeprint.com/asl101/gifs/numbers/6.gif\", description: \"Number 6\" },\n      { key: \"seven\", name: \"Seven\", gif: \"https://lifeprint.com/asl101/gifs/numbers/7.gif\", description: \"Number 7\" },\n      { key: \"eight\", name: \"Eight\", gif: \"https://lifeprint.com/asl101/gifs/numbers/8.gif\", description: \"Number 8\" },\n      { key: \"nine\", name: \"Nine\", gif: \"https://lifeprint.com/asl101/gifs/numbers/9.gif\", description: \"Number 9\" },\n      { key: \"ten\", name: \"Ten\", gif: \"https://lifeprint.com/asl101/gifs/numbers/10.gif\", description: \"Number 10\" },\n      { key: \"today\", name: \"Today\", gif: \"https://lifeprint.com/asl101/gifs/t/today.gif\", description: \"Current day\" },\n      { key: \"tomorrow\", name: \"Tomorrow\", gif: \"https://lifeprint.com/asl101/gifs/t/tomorrow.gif\", description: \"Next day\" },\n      { key: \"yesterday\", name: \"Yesterday\", gif: \"https://lifeprint.com/asl101/gifs/y/yesterday.gif\", description: \"Previous day\" },\n      { key: \"morning\", name: \"Morning\", gif: \"https://lifeprint.com/asl101/gifs/m/morning.gif\", description: \"Early day\" },\n      { key: \"afternoon\", name: \"Afternoon\", gif: \"https://lifeprint.com/asl101/gifs/a/afternoon.gif\", description: \"Mid day\" },\n      { key: \"night\", name: \"Night\", gif: \"https://lifeprint.com/asl101/gifs/n/night.gif\", description: \"Dark time\" },\n      { key: \"week\", name: \"Week\", gif: \"https://lifeprint.com/asl101/gifs/w/week.gif\", description: \"7-day period\" },\n      { key: \"month\", name: \"Month\", gif: \"https://lifeprint.com/asl101/gifs/m/month.gif\", description: \"Calendar period\" },\n      { key: \"year\", name: \"Year\", gif: \"https://lifeprint.com/asl101/gifs/y/year.gif\", description: \"Annual period\" },\n      { key: \"time\", name: \"Time\", gif: \"https://lifeprint.com/asl101/gifs/t/time.gif\", description: \"Clock measurement\" }\n    ]\n  }\n};\n\n// Helper functions\nexport const getSignsForLevel = (level) => {\n  return SIGN_LEVELS[level]?.signs || [];\n};\n\nexport const getLevelInfo = (level) => {\n  return SIGN_LEVELS[level] || null;\n};\n\nexport const getTotalLevels = () => {\n  return Object.keys(SIGN_LEVELS).length;\n};\n\nexport const isValidLevel = (level) => {\n  return SIGN_LEVELS.hasOwnProperty(level);\n};\n"], "mappings": "AAAA;AACA;AACA;AAEA,MAAO,MAAM,CAAAA,WAAW,CAAG,CACzB,CAAC,CAAE,CACDC,IAAI,CAAE,0BAA0B,CAChCC,WAAW,CAAE,mDAAmD,CAChEC,KAAK,CAAE,uBAAuB,CAC9BC,KAAK,CAAE,CACL,CAAEC,GAAG,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAO,CAAEK,GAAG,CAAE,4DAA4D,CAAEJ,WAAW,CAAE,qBAAsB,CAAC,CACtI,CAAEG,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,8DAA8D,CAAEJ,WAAW,CAAE,kBAAmB,CAAC,CACjI,CAAEG,GAAG,CAAE,QAAQ,CAAEJ,IAAI,CAAE,QAAQ,CAAEK,GAAG,CAAE,8DAA8D,CAAEJ,WAAW,CAAE,qBAAsB,CAAC,CAC1I,CAAEG,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,sDAAsD,CAAEJ,WAAW,CAAE,sBAAuB,CAAC,CAC7H,CAAEG,GAAG,CAAE,IAAI,CAAEJ,IAAI,CAAE,IAAI,CAAEK,GAAG,CAAE,uDAAuD,CAAEJ,WAAW,CAAE,mBAAoB,CAAC,CACzH,CAAEG,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,6CAA6C,CAAEJ,WAAW,CAAE,iBAAkB,CAAC,CAC/G,CAAEG,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,2DAA2D,CAAEJ,WAAW,CAAE,iBAAkB,CAAC,CAC7H,CAAEG,GAAG,CAAE,SAAS,CAAEJ,IAAI,CAAE,SAAS,CAAEK,GAAG,CAAE,iDAAiD,CAAEJ,WAAW,CAAE,kBAAmB,CAAC,CAC5H,CAAEG,GAAG,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAO,CAAEK,GAAG,CAAE,qIAAqI,CAAEJ,WAAW,CAAE,mBAAoB,CAAC,CAC7M,CAAEG,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,6CAA6C,CAAEJ,WAAW,CAAE,uBAAwB,CAAC,CACrH,CAAEG,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,mKAAmK,CAAEJ,WAAW,CAAE,qBAAsB,CAAC,CACzO,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8CAA8C,CAAEJ,WAAW,CAAE,qBAAsB,CAAC,CACtH,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8CAA8C,CAAEJ,WAAW,CAAE,wBAAyB,CAAC,CACzH,CAAEG,GAAG,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAO,CAAEK,GAAG,CAAE,+CAA+C,CAAEJ,WAAW,CAAE,cAAe,CAAC,CAClH,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8CAA8C,CAAEJ,WAAW,CAAE,oBAAqB,CAAC,CACrH,CAAEG,GAAG,CAAE,QAAQ,CAAEJ,IAAI,CAAE,QAAQ,CAAEK,GAAG,CAAE,gDAAgD,CAAEJ,WAAW,CAAE,iBAAkB,CAAC,CACxH,CAAEG,GAAG,CAAE,QAAQ,CAAEJ,IAAI,CAAE,QAAQ,CAAEK,GAAG,CAAE,gDAAgD,CAAEJ,WAAW,CAAE,iBAAkB,CAAC,CACxH,CAAEG,GAAG,CAAE,QAAQ,CAAEJ,IAAI,CAAE,QAAQ,CAAEK,GAAG,CAAE,gDAAgD,CAAEJ,WAAW,CAAE,iBAAkB,CAAC,CACxH,CAAEG,GAAG,CAAE,WAAW,CAAEJ,IAAI,CAAE,WAAW,CAAEK,GAAG,CAAE,2DAA2D,CAAEJ,WAAW,CAAE,yBAA0B,CAAC,CACjJ,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8CAA8C,CAAEJ,WAAW,CAAE,qBAAsB,CAAC,CAE1H,CAAC,CACD,CAAC,CAAE,CACDD,IAAI,CAAE,wBAAwB,CAC9BC,WAAW,CAAE,6CAA6C,CAC1DC,KAAK,CAAE,qBAAqB,CAC5BC,KAAK,CAAE,CACL,CAAEC,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,6CAA6C,CAAEJ,WAAW,CAAE,WAAY,CAAC,CACzG,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,gDAAgD,CAAEJ,WAAW,CAAE,YAAa,CAAC,CAC/G,CAAEG,GAAG,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAO,CAAEK,GAAG,CAAE,+CAA+C,CAAEJ,WAAW,CAAE,aAAc,CAAC,CACjH,CAAEG,GAAG,CAAE,QAAQ,CAAEJ,IAAI,CAAE,QAAQ,CAAEK,GAAG,CAAE,gDAAgD,CAAEJ,WAAW,CAAE,cAAe,CAAC,CACrH,CAAEG,GAAG,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAO,CAAEK,GAAG,CAAE,+LAA+L,CAAEJ,WAAW,CAAE,aAAc,CAAC,CACjQ,CAAEG,GAAG,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAO,CAAEK,GAAG,CAAE,+CAA+C,CAAEJ,WAAW,CAAE,aAAc,CAAC,CACjH,CAAEG,GAAG,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAO,CAAEK,GAAG,CAAE,+CAA+C,CAAEJ,WAAW,CAAE,aAAc,CAAC,CACjH,CAAEG,GAAG,CAAE,QAAQ,CAAEJ,IAAI,CAAE,QAAQ,CAAEK,GAAG,CAAE,gDAAgD,CAAEJ,WAAW,CAAE,cAAe,CAAC,CACrH,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,2DAA2D,CAAEJ,WAAW,CAAE,kBAAmB,CAAC,CAChI,CAAEG,GAAG,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAO,CAAEK,GAAG,CAAE,kGAAkG,CAAEJ,WAAW,CAAE,uBAAwB,CAAC,CAC9K,CAAEG,GAAG,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAO,CAAEK,GAAG,CAAE,+CAA+C,CAAEJ,WAAW,CAAE,mBAAoB,CAAC,CACvH,CAAEG,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,+CAA+C,CAAEJ,WAAW,CAAE,oBAAqB,CAAC,CACpH,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8CAA8C,CAAEJ,WAAW,CAAE,YAAa,CAAC,CAC7G,CAAEG,GAAG,CAAE,QAAQ,CAAEJ,IAAI,CAAE,QAAQ,CAAEK,GAAG,CAAE,gDAAgD,CAAEJ,WAAW,CAAE,eAAgB,CAAC,CACtH,CAAEG,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,kGAAkG,CAAEJ,WAAW,CAAE,SAAU,CAAC,CAC5J,CAAEG,GAAG,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAO,CAAEK,GAAG,CAAE,+CAA+C,CAAEJ,WAAW,CAAE,eAAgB,CAAC,CACnH,CAAEG,GAAG,CAAE,QAAQ,CAAEJ,IAAI,CAAE,QAAQ,CAAEK,GAAG,CAAE,gDAAgD,CAAEJ,WAAW,CAAE,yBAA0B,CAAC,CAChI,CAAEG,GAAG,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAO,CAAEK,GAAG,CAAE,iDAAiD,CAAEJ,WAAW,CAAE,cAAe,CAAC,CACpH,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8EAA8E,CAAEJ,WAAW,CAAE,aAAc,CAAC,CAC9I,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8CAA8C,CAAEJ,WAAW,CAAE,gBAAiB,CAAC,CAErH,CAAC,CACD,CAAC,CAAE,CACDD,IAAI,CAAE,kBAAkB,CACxBC,WAAW,CAAE,mCAAmC,CAChDC,KAAK,CAAE,qBAAqB,CAC5BC,KAAK,CAAE,CACL,CAAEC,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,uDAAuD,CAAEJ,WAAW,CAAE,YAAa,CAAC,CACpH,CAAEG,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,2NAA2N,CAAEJ,WAAW,CAAE,YAAa,CAAC,CACxR,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8CAA8C,CAAEJ,WAAW,CAAE,eAAgB,CAAC,CAChH,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8EAA8E,CAAEJ,WAAW,CAAE,gBAAiB,CAAC,CACjJ,CAAEG,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,6CAA6C,CAAEJ,WAAW,CAAE,aAAc,CAAC,CAC3G,CAAEG,GAAG,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAO,CAAEK,GAAG,CAAE,+CAA+C,CAAEJ,WAAW,CAAE,eAAgB,CAAC,CACnH,CAAEG,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,6CAA6C,CAAEJ,WAAW,CAAE,aAAc,CAAC,CAC3G,CAAEG,GAAG,CAAE,SAAS,CAAEJ,IAAI,CAAE,SAAS,CAAEK,GAAG,CAAE,iDAAiD,CAAEJ,WAAW,CAAE,SAAU,CAAC,CACnH,CAAEG,GAAG,CAAE,QAAQ,CAAEJ,IAAI,CAAE,QAAQ,CAAEK,GAAG,CAAE,gDAAgD,CAAEJ,WAAW,CAAE,cAAe,CAAC,CACrH,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8CAA8C,CAAEJ,WAAW,CAAE,cAAe,CAAC,CAC/G,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8CAA8C,CAAEJ,WAAW,CAAE,YAAa,CAAC,CAC7G,CAAEG,GAAG,CAAE,QAAQ,CAAEJ,IAAI,CAAE,QAAQ,CAAEK,GAAG,CAAE,4DAA4D,CAAEJ,WAAW,CAAE,gBAAiB,CAAC,CACnI,CAAEG,GAAG,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAO,CAAEK,GAAG,CAAE,+CAA+C,CAAEJ,WAAW,CAAE,cAAe,CAAC,CAClH,CAAEG,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,6CAA6C,CAAEJ,WAAW,CAAE,YAAa,CAAC,CAC1G,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8CAA8C,CAAEJ,WAAW,CAAE,iBAAkB,CAAC,CAClH,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8CAA8C,CAAEJ,WAAW,CAAE,gBAAiB,CAAC,CACjH,CAAEG,GAAG,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAO,CAAEK,GAAG,CAAE,8EAA8E,CAAEJ,WAAW,CAAE,eAAgB,CAAC,CAClJ,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8CAA8C,CAAEJ,WAAW,CAAE,qBAAsB,CAAC,CACtH,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8CAA8C,CAAEJ,WAAW,CAAE,sBAAuB,CAAC,CACvH,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8CAA8C,CAAEJ,WAAW,CAAE,cAAe,CAAC,CAEnH,CAAC,CACD,CAAC,CAAE,CACDD,IAAI,CAAE,sBAAsB,CAC5BC,WAAW,CAAE,qCAAqC,CAClDC,KAAK,CAAE,mBAAmB,CAC1BC,KAAK,CAAE,CACL,CAAEC,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8CAA8C,CAAEJ,WAAW,CAAE,eAAgB,CAAC,CAChH,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8CAA8C,CAAEJ,WAAW,CAAE,eAAgB,CAAC,CAChH,CAAEG,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,8CAA8C,CAAEJ,WAAW,CAAE,cAAe,CAAC,CAC7G,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8CAA8C,CAAEJ,WAAW,CAAE,aAAc,CAAC,CAC9G,CAAEG,GAAG,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAO,CAAEK,GAAG,CAAE,+CAA+C,CAAEJ,WAAW,CAAE,uBAAwB,CAAC,CAC3H,CAAEG,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,sDAAsD,CAAEJ,WAAW,CAAE,eAAgB,CAAC,CACtH,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8CAA8C,CAAEJ,WAAW,CAAE,oBAAqB,CAAC,CACrH,CAAEG,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,kGAAkG,CAAEJ,WAAW,CAAE,YAAa,CAAC,CAC/J,CAAEG,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,6CAA6C,CAAEJ,WAAW,CAAE,YAAa,CAAC,CAC1G,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8CAA8C,CAAEJ,WAAW,CAAE,mBAAoB,CAAC,CACpH,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8CAA8C,CAAEJ,WAAW,CAAE,cAAe,CAAC,CAC/G,CAAEG,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,6CAA6C,CAAEJ,WAAW,CAAE,cAAe,CAAC,CAC5G,CAAEG,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,6CAA6C,CAAEJ,WAAW,CAAE,eAAgB,CAAC,CAC7G,CAAEG,GAAG,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAO,CAAEK,GAAG,CAAE,+CAA+C,CAAEJ,WAAW,CAAE,kBAAmB,CAAC,CACtH,CAAEG,GAAG,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAO,CAAEK,GAAG,CAAE,+CAA+C,CAAEJ,WAAW,CAAE,YAAa,CAAC,CAChH,CAAEG,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,6CAA6C,CAAEJ,WAAW,CAAE,cAAe,CAAC,CAC5G,CAAEG,GAAG,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAO,CAAEK,GAAG,CAAE,qDAAqD,CAAEJ,WAAW,CAAE,gBAAiB,CAAC,CAC1H,CAAEG,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,6CAA6C,CAAEJ,WAAW,CAAE,YAAa,CAAC,CAC1G,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8CAA8C,CAAEJ,WAAW,CAAE,aAAc,CAAC,CAC9G,CAAEG,GAAG,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAO,CAAEK,GAAG,CAAE,+CAA+C,CAAEJ,WAAW,CAAE,kBAAmB,CAAC,CAE1H,CAAC,CACD,CAAC,CAAE,CACDD,IAAI,CAAE,gBAAgB,CACtBC,WAAW,CAAE,kCAAkC,CAC/CC,KAAK,CAAE,mBAAmB,CAC1BC,KAAK,CAAE,CACL,CAAEC,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,iDAAiD,CAAEJ,WAAW,CAAE,UAAW,CAAC,CAC5G,CAAEG,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,iDAAiD,CAAEJ,WAAW,CAAE,UAAW,CAAC,CAC5G,CAAEG,GAAG,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAO,CAAEK,GAAG,CAAE,iDAAiD,CAAEJ,WAAW,CAAE,UAAW,CAAC,CAChH,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,iDAAiD,CAAEJ,WAAW,CAAE,UAAW,CAAC,CAC9G,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,iDAAiD,CAAEJ,WAAW,CAAE,UAAW,CAAC,CAC9G,CAAEG,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,iDAAiD,CAAEJ,WAAW,CAAE,UAAW,CAAC,CAC5G,CAAEG,GAAG,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAO,CAAEK,GAAG,CAAE,iDAAiD,CAAEJ,WAAW,CAAE,UAAW,CAAC,CAChH,CAAEG,GAAG,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAO,CAAEK,GAAG,CAAE,iDAAiD,CAAEJ,WAAW,CAAE,UAAW,CAAC,CAChH,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,iDAAiD,CAAEJ,WAAW,CAAE,UAAW,CAAC,CAC9G,CAAEG,GAAG,CAAE,KAAK,CAAEJ,IAAI,CAAE,KAAK,CAAEK,GAAG,CAAE,kDAAkD,CAAEJ,WAAW,CAAE,WAAY,CAAC,CAC9G,CAAEG,GAAG,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAO,CAAEK,GAAG,CAAE,+CAA+C,CAAEJ,WAAW,CAAE,aAAc,CAAC,CACjH,CAAEG,GAAG,CAAE,UAAU,CAAEJ,IAAI,CAAE,UAAU,CAAEK,GAAG,CAAE,kDAAkD,CAAEJ,WAAW,CAAE,UAAW,CAAC,CACvH,CAAEG,GAAG,CAAE,WAAW,CAAEJ,IAAI,CAAE,WAAW,CAAEK,GAAG,CAAE,mDAAmD,CAAEJ,WAAW,CAAE,cAAe,CAAC,CAC9H,CAAEG,GAAG,CAAE,SAAS,CAAEJ,IAAI,CAAE,SAAS,CAAEK,GAAG,CAAE,iDAAiD,CAAEJ,WAAW,CAAE,WAAY,CAAC,CACrH,CAAEG,GAAG,CAAE,WAAW,CAAEJ,IAAI,CAAE,WAAW,CAAEK,GAAG,CAAE,mDAAmD,CAAEJ,WAAW,CAAE,SAAU,CAAC,CACzH,CAAEG,GAAG,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAO,CAAEK,GAAG,CAAE,+CAA+C,CAAEJ,WAAW,CAAE,WAAY,CAAC,CAC/G,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8CAA8C,CAAEJ,WAAW,CAAE,cAAe,CAAC,CAC/G,CAAEG,GAAG,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAO,CAAEK,GAAG,CAAE,+CAA+C,CAAEJ,WAAW,CAAE,iBAAkB,CAAC,CACrH,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8CAA8C,CAAEJ,WAAW,CAAE,eAAgB,CAAC,CAChH,CAAEG,GAAG,CAAE,MAAM,CAAEJ,IAAI,CAAE,MAAM,CAAEK,GAAG,CAAE,8CAA8C,CAAEJ,WAAW,CAAE,mBAAoB,CAAC,CAExH,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAK,gBAAgB,CAAIC,KAAK,EAAK,KAAAC,kBAAA,CACzC,MAAO,EAAAA,kBAAA,CAAAT,WAAW,CAACQ,KAAK,CAAC,UAAAC,kBAAA,iBAAlBA,kBAAA,CAAoBL,KAAK,GAAI,EAAE,CACxC,CAAC,CAED,MAAO,MAAM,CAAAM,YAAY,CAAIF,KAAK,EAAK,CACrC,MAAO,CAAAR,WAAW,CAACQ,KAAK,CAAC,EAAI,IAAI,CACnC,CAAC,CAED,MAAO,MAAM,CAAAG,cAAc,CAAGA,CAAA,GAAM,CAClC,MAAO,CAAAC,MAAM,CAACC,IAAI,CAACb,WAAW,CAAC,CAACc,MAAM,CACxC,CAAC,CAED,MAAO,MAAM,CAAAC,YAAY,CAAIP,KAAK,EAAK,CACrC,MAAO,CAAAR,WAAW,CAACgB,cAAc,CAACR,KAAK,CAAC,CAC1C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}