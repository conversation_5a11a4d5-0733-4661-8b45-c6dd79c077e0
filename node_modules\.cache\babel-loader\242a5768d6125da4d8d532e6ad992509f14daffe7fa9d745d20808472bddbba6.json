{"ast": null, "code": "var _jsxFileName = \"D:\\\\ASL\\\\ASL-Training\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport HomePage from './components/HomePage';\nimport TrainingPage from './components/TrainingPage';\nimport LevelsPage from './components/LevelsPage';\nimport FlashCardsPage from './components/FlashCardsPage';\nimport AboutPage from './components/AboutPage';\nimport ContactPage from './components/ContactPage';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  font-family: var(--font-secondary);\n  overflow-x: hidden;\n  position: relative;\n\n  /* Subtle background pattern for visual interest */\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 25% 25%, var(--primary-50) 0%, transparent 50%),\n      radial-gradient(circle at 75% 75%, var(--secondary-50) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n`;\n_c = AppContainer;\nconst PageWrapper = styled.div`\n  position: relative;\n  z-index: 1;\n  min-height: 100vh;\n`;\n_c2 = PageWrapper;\nfunction App() {\n  _s();\n  const [currentPage, setCurrentPage] = useState('home');\n  const navigateToTraining = () => {\n    setCurrentPage('training');\n  };\n  const navigateToAbout = () => {\n    setCurrentPage('about');\n  };\n  const navigateToContact = () => {\n    setCurrentPage('contact');\n  };\n  const navigateToHome = () => {\n    setCurrentPage('home');\n  };\n  const renderCurrentPage = () => {\n    switch (currentPage) {\n      case 'training':\n        return /*#__PURE__*/_jsxDEV(TrainingPage, {\n          onBackToHome: navigateToHome\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 16\n        }, this);\n      case 'about':\n        return /*#__PURE__*/_jsxDEV(AboutPage, {\n          onBackToHome: navigateToHome\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 16\n        }, this);\n      case 'contact':\n        return /*#__PURE__*/_jsxDEV(ContactPage, {\n          onBackToHome: navigateToHome\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(HomePage, {\n          onStartTraining: navigateToTraining,\n          onNavigateToAbout: navigateToAbout,\n          onNavigateToContact: navigateToContact\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(AppContainer, {\n    children: /*#__PURE__*/_jsxDEV(PageWrapper, {\n      children: renderCurrentPage()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"KLlrbvIFn6o4dTsrFf/Szg7G3bM=\");\n_c3 = App;\nexport default App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"AppContainer\");\n$RefreshReg$(_c2, \"PageWrapper\");\n$RefreshReg$(_c3, \"App\");", "map": {"version": 3, "names": ["React", "useState", "styled", "HomePage", "TrainingPage", "LevelsPage", "FlashCardsPage", "AboutPage", "ContactPage", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "PageWrapper", "_c2", "App", "_s", "currentPage", "setCurrentPage", "navigateToTraining", "navigateToAbout", "navigateToContact", "navigateToHome", "renderCurrentPage", "onBackToHome", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onStartTraining", "onNavigateToAbout", "onNavigateToContact", "children", "_c3", "$RefreshReg$"], "sources": ["D:/ASL/ASL-Training/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport HomePage from './components/HomePage';\nimport TrainingPage from './components/TrainingPage';\nimport LevelsPage from './components/LevelsPage';\nimport FlashCardsPage from './components/FlashCardsPage';\nimport AboutPage from './components/AboutPage';\nimport ContactPage from './components/ContactPage';\nimport './App.css';\n\nconst AppContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  font-family: var(--font-secondary);\n  overflow-x: hidden;\n  position: relative;\n\n  /* Subtle background pattern for visual interest */\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 25% 25%, var(--primary-50) 0%, transparent 50%),\n      radial-gradient(circle at 75% 75%, var(--secondary-50) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n`;\n\nconst PageWrapper = styled.div`\n  position: relative;\n  z-index: 1;\n  min-height: 100vh;\n`;\n\nfunction App() {\n  const [currentPage, setCurrentPage] = useState('home');\n\n  const navigateToTraining = () => {\n    setCurrentPage('training');\n  };\n\n  const navigateToAbout = () => {\n    setCurrentPage('about');\n  };\n\n  const navigateToContact = () => {\n    setCurrentPage('contact');\n  };\n\n  const navigateToHome = () => {\n    setCurrentPage('home');\n  };\n\n  const renderCurrentPage = () => {\n    switch (currentPage) {\n      case 'training':\n        return <TrainingPage onBackToHome={navigateToHome} />;\n      case 'about':\n        return <AboutPage onBackToHome={navigateToHome} />;\n      case 'contact':\n        return <ContactPage onBackToHome={navigateToHome} />;\n      default:\n        return (\n          <HomePage\n            onStartTraining={navigateToTraining}\n            onNavigateToAbout={navigateToAbout}\n            onNavigateToContact={navigateToContact}\n          />\n        );\n    }\n  };\n\n  return (\n    <AppContainer>\n      <PageWrapper>\n        {renderCurrentPage()}\n      </PageWrapper>\n    </AppContainer>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,MAAMC,YAAY,GAAGT,MAAM,CAACU,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GArBIF,YAAY;AAuBlB,MAAMG,WAAW,GAAGZ,MAAM,CAACU,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAJID,WAAW;AAMjB,SAASE,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,MAAM,CAAC;EAEtD,MAAMmB,kBAAkB,GAAGA,CAAA,KAAM;IAC/BD,cAAc,CAAC,UAAU,CAAC;EAC5B,CAAC;EAED,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5BF,cAAc,CAAC,OAAO,CAAC;EACzB,CAAC;EAED,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;IAC9BH,cAAc,CAAC,SAAS,CAAC;EAC3B,CAAC;EAED,MAAMI,cAAc,GAAGA,CAAA,KAAM;IAC3BJ,cAAc,CAAC,MAAM,CAAC;EACxB,CAAC;EAED,MAAMK,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQN,WAAW;MACjB,KAAK,UAAU;QACb,oBAAOR,OAAA,CAACN,YAAY;UAACqB,YAAY,EAAEF;QAAe;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvD,KAAK,OAAO;QACV,oBAAOnB,OAAA,CAACH,SAAS;UAACkB,YAAY,EAAEF;QAAe;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpD,KAAK,SAAS;QACZ,oBAAOnB,OAAA,CAACF,WAAW;UAACiB,YAAY,EAAEF;QAAe;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD;QACE,oBACEnB,OAAA,CAACP,QAAQ;UACP2B,eAAe,EAAEV,kBAAmB;UACpCW,iBAAiB,EAAEV,eAAgB;UACnCW,mBAAmB,EAAEV;QAAkB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;IAER;EACF,CAAC;EAED,oBACEnB,OAAA,CAACC,YAAY;IAAAsB,QAAA,eACXvB,OAAA,CAACI,WAAW;MAAAmB,QAAA,EACTT,iBAAiB,CAAC;IAAC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEnB;AAACZ,EAAA,CA7CQD,GAAG;AAAAkB,GAAA,GAAHlB,GAAG;AA+CZ,eAAeA,GAAG;AAAC,IAAAH,EAAA,EAAAE,GAAA,EAAAmB,GAAA;AAAAC,YAAA,CAAAtB,EAAA;AAAAsB,YAAA,CAAApB,GAAA;AAAAoB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}