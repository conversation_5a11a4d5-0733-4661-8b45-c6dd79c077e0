{"ast": null, "code": "import { __spreadArray as e, __assign as t } from \"tslib\";\nimport n from \"@emotion/is-prop-valid\";\nimport o, { useRef as r, useState as s, useMemo as i, useEffect as a, useContext as c, useDebugValue as l, createElement as u } from \"react\";\nimport p from \"shallowequal\";\nimport * as d from \"stylis\";\nimport h from \"@emotion/unitless\";\nvar f = \"undefined\" != typeof process && void 0 !== process.env && (process.env.REACT_APP_SC_ATTR || process.env.SC_ATTR) || \"data-styled\",\n  m = \"active\",\n  y = \"data-styled-version\",\n  v = \"6.1.19\",\n  g = \"/*!sc*/\\n\",\n  S = \"undefined\" != typeof window && \"undefined\" != typeof document,\n  w = Boolean(\"boolean\" == typeof SC_DISABLE_SPEEDY ? SC_DISABLE_SPEEDY : \"undefined\" != typeof process && void 0 !== process.env && void 0 !== process.env.REACT_APP_SC_DISABLE_SPEEDY && \"\" !== process.env.REACT_APP_SC_DISABLE_SPEEDY ? \"false\" !== process.env.REACT_APP_SC_DISABLE_SPEEDY && process.env.REACT_APP_SC_DISABLE_SPEEDY : \"undefined\" != typeof process && void 0 !== process.env && void 0 !== process.env.SC_DISABLE_SPEEDY && \"\" !== process.env.SC_DISABLE_SPEEDY ? \"false\" !== process.env.SC_DISABLE_SPEEDY && process.env.SC_DISABLE_SPEEDY : \"production\" !== process.env.NODE_ENV),\n  b = {},\n  E = /invalid hook call/i,\n  N = new Set(),\n  P = function (t, n) {\n    if (\"production\" !== process.env.NODE_ENV) {\n      var o = n ? ' with the id of \"'.concat(n, '\"') : \"\",\n        s = \"The component \".concat(t).concat(o, \" has been created dynamically.\\n\") + \"You may see this warning because you've called styled inside another component.\\nTo resolve this only create new StyledComponents outside of any render method and function component.\\nSee https://styled-components.com/docs/basics#define-styled-components-outside-of-the-render-method for more info.\\n\",\n        i = console.error;\n      try {\n        var a = !0;\n        console.error = function (t) {\n          for (var n = [], o = 1; o < arguments.length; o++) n[o - 1] = arguments[o];\n          E.test(t) ? (a = !1, N.delete(s)) : i.apply(void 0, e([t], n, !1));\n        }, r(), a && !N.has(s) && (console.warn(s), N.add(s));\n      } catch (e) {\n        E.test(e.message) && N.delete(s);\n      } finally {\n        console.error = i;\n      }\n    }\n  },\n  _ = Object.freeze([]),\n  C = Object.freeze({});\nfunction I(e, t, n) {\n  return void 0 === n && (n = C), e.theme !== n.theme && e.theme || t || n.theme;\n}\nvar A = new Set([\"a\", \"abbr\", \"address\", \"area\", \"article\", \"aside\", \"audio\", \"b\", \"base\", \"bdi\", \"bdo\", \"big\", \"blockquote\", \"body\", \"br\", \"button\", \"canvas\", \"caption\", \"cite\", \"code\", \"col\", \"colgroup\", \"data\", \"datalist\", \"dd\", \"del\", \"details\", \"dfn\", \"dialog\", \"div\", \"dl\", \"dt\", \"em\", \"embed\", \"fieldset\", \"figcaption\", \"figure\", \"footer\", \"form\", \"h1\", \"h2\", \"h3\", \"h4\", \"h5\", \"h6\", \"header\", \"hgroup\", \"hr\", \"html\", \"i\", \"iframe\", \"img\", \"input\", \"ins\", \"kbd\", \"keygen\", \"label\", \"legend\", \"li\", \"link\", \"main\", \"map\", \"mark\", \"menu\", \"menuitem\", \"meta\", \"meter\", \"nav\", \"noscript\", \"object\", \"ol\", \"optgroup\", \"option\", \"output\", \"p\", \"param\", \"picture\", \"pre\", \"progress\", \"q\", \"rp\", \"rt\", \"ruby\", \"s\", \"samp\", \"script\", \"section\", \"select\", \"small\", \"source\", \"span\", \"strong\", \"style\", \"sub\", \"summary\", \"sup\", \"table\", \"tbody\", \"td\", \"textarea\", \"tfoot\", \"th\", \"thead\", \"time\", \"tr\", \"track\", \"u\", \"ul\", \"use\", \"var\", \"video\", \"wbr\", \"circle\", \"clipPath\", \"defs\", \"ellipse\", \"foreignObject\", \"g\", \"image\", \"line\", \"linearGradient\", \"marker\", \"mask\", \"path\", \"pattern\", \"polygon\", \"polyline\", \"radialGradient\", \"rect\", \"stop\", \"svg\", \"text\", \"tspan\"]),\n  O = /[!\"#$%&'()*+,./:;<=>?@[\\\\\\]^`{|}~-]+/g,\n  D = /(^-|-$)/g;\nfunction R(e) {\n  return e.replace(O, \"-\").replace(D, \"\");\n}\nvar T = /(a)(d)/gi,\n  k = 52,\n  j = function (e) {\n    return String.fromCharCode(e + (e > 25 ? 39 : 97));\n  };\nfunction x(e) {\n  var t,\n    n = \"\";\n  for (t = Math.abs(e); t > k; t = t / k | 0) n = j(t % k) + n;\n  return (j(t % k) + n).replace(T, \"$1-$2\");\n}\nvar V,\n  F = 5381,\n  M = function (e, t) {\n    for (var n = t.length; n;) e = 33 * e ^ t.charCodeAt(--n);\n    return e;\n  },\n  z = function (e) {\n    return M(F, e);\n  };\nfunction $(e) {\n  return x(z(e) >>> 0);\n}\nfunction B(e) {\n  return \"production\" !== process.env.NODE_ENV && \"string\" == typeof e && e || e.displayName || e.name || \"Component\";\n}\nfunction L(e) {\n  return \"string\" == typeof e && (\"production\" === process.env.NODE_ENV || e.charAt(0) === e.charAt(0).toLowerCase());\n}\nvar G = \"function\" == typeof Symbol && Symbol.for,\n  Y = G ? Symbol.for(\"react.memo\") : 60115,\n  W = G ? Symbol.for(\"react.forward_ref\") : 60112,\n  q = {\n    childContextTypes: !0,\n    contextType: !0,\n    contextTypes: !0,\n    defaultProps: !0,\n    displayName: !0,\n    getDefaultProps: !0,\n    getDerivedStateFromError: !0,\n    getDerivedStateFromProps: !0,\n    mixins: !0,\n    propTypes: !0,\n    type: !0\n  },\n  H = {\n    name: !0,\n    length: !0,\n    prototype: !0,\n    caller: !0,\n    callee: !0,\n    arguments: !0,\n    arity: !0\n  },\n  U = {\n    $$typeof: !0,\n    compare: !0,\n    defaultProps: !0,\n    displayName: !0,\n    propTypes: !0,\n    type: !0\n  },\n  J = ((V = {})[W] = {\n    $$typeof: !0,\n    render: !0,\n    defaultProps: !0,\n    displayName: !0,\n    propTypes: !0\n  }, V[Y] = U, V);\nfunction X(e) {\n  return (\"type\" in (t = e) && t.type.$$typeof) === Y ? U : \"$$typeof\" in e ? J[e.$$typeof] : q;\n  var t;\n}\nvar Z = Object.defineProperty,\n  K = Object.getOwnPropertyNames,\n  Q = Object.getOwnPropertySymbols,\n  ee = Object.getOwnPropertyDescriptor,\n  te = Object.getPrototypeOf,\n  ne = Object.prototype;\nfunction oe(e, t, n) {\n  if (\"string\" != typeof t) {\n    if (ne) {\n      var o = te(t);\n      o && o !== ne && oe(e, o, n);\n    }\n    var r = K(t);\n    Q && (r = r.concat(Q(t)));\n    for (var s = X(e), i = X(t), a = 0; a < r.length; ++a) {\n      var c = r[a];\n      if (!(c in H || n && n[c] || i && c in i || s && c in s)) {\n        var l = ee(t, c);\n        try {\n          Z(e, c, l);\n        } catch (e) {}\n      }\n    }\n  }\n  return e;\n}\nfunction re(e) {\n  return \"function\" == typeof e;\n}\nfunction se(e) {\n  return \"object\" == typeof e && \"styledComponentId\" in e;\n}\nfunction ie(e, t) {\n  return e && t ? \"\".concat(e, \" \").concat(t) : e || t || \"\";\n}\nfunction ae(e, t) {\n  if (0 === e.length) return \"\";\n  for (var n = e[0], o = 1; o < e.length; o++) n += t ? t + e[o] : e[o];\n  return n;\n}\nfunction ce(e) {\n  return null !== e && \"object\" == typeof e && e.constructor.name === Object.name && !(\"props\" in e && e.$$typeof);\n}\nfunction le(e, t, n) {\n  if (void 0 === n && (n = !1), !n && !ce(e) && !Array.isArray(e)) return t;\n  if (Array.isArray(t)) for (var o = 0; o < t.length; o++) e[o] = le(e[o], t[o]);else if (ce(t)) for (var o in t) e[o] = le(e[o], t[o]);\n  return e;\n}\nfunction ue(e, t) {\n  Object.defineProperty(e, \"toString\", {\n    value: t\n  });\n}\nvar pe = \"production\" !== process.env.NODE_ENV ? {\n  1: \"Cannot create styled-component for component: %s.\\n\\n\",\n  2: \"Can't collect styles once you've consumed a `ServerStyleSheet`'s styles! `ServerStyleSheet` is a one off instance for each server-side render cycle.\\n\\n- Are you trying to reuse it across renders?\\n- Are you accidentally calling collectStyles twice?\\n\\n\",\n  3: \"Streaming SSR is only supported in a Node.js environment; Please do not try to call this method in the browser.\\n\\n\",\n  4: \"The `StyleSheetManager` expects a valid target or sheet prop!\\n\\n- Does this error occur on the client and is your target falsy?\\n- Does this error occur on the server and is the sheet falsy?\\n\\n\",\n  5: \"The clone method cannot be used on the client!\\n\\n- Are you running in a client-like environment on the server?\\n- Are you trying to run SSR on the client?\\n\\n\",\n  6: \"Trying to insert a new style tag, but the given Node is unmounted!\\n\\n- Are you using a custom target that isn't mounted?\\n- Does your document not have a valid head element?\\n- Have you accidentally removed a style tag manually?\\n\\n\",\n  7: 'ThemeProvider: Please return an object from your \"theme\" prop function, e.g.\\n\\n```js\\ntheme={() => ({})}\\n```\\n\\n',\n  8: 'ThemeProvider: Please make your \"theme\" prop an object.\\n\\n',\n  9: \"Missing document `<head>`\\n\\n\",\n  10: \"Cannot find a StyleSheet instance. Usually this happens if there are multiple copies of styled-components loaded at once. Check out this issue for how to troubleshoot and fix the common cases where this situation can happen: https://github.com/styled-components/styled-components/issues/1941#issuecomment-417862021\\n\\n\",\n  11: \"_This error was replaced with a dev-time warning, it will be deleted for v4 final._ [createGlobalStyle] received children which will not be rendered. Please use the component without passing children elements.\\n\\n\",\n  12: \"It seems you are interpolating a keyframe declaration (%s) into an untagged string. This was supported in styled-components v3, but is not longer supported in v4 as keyframes are now injected on-demand. Please wrap your string in the css\\\\`\\\\` helper which ensures the styles are injected correctly. See https://www.styled-components.com/docs/api#css\\n\\n\",\n  13: \"%s is not a styled component and cannot be referred to via component selector. See https://www.styled-components.com/docs/advanced#referring-to-other-components for more details.\\n\\n\",\n  14: 'ThemeProvider: \"theme\" prop is required.\\n\\n',\n  15: \"A stylis plugin has been supplied that is not named. We need a name for each plugin to be able to prevent styling collisions between different stylis configurations within the same app. Before you pass your plugin to `<StyleSheetManager stylisPlugins={[]}>`, please make sure each plugin is uniquely-named, e.g.\\n\\n```js\\nObject.defineProperty(importedPlugin, 'name', { value: 'some-unique-name' });\\n```\\n\\n\",\n  16: \"Reached the limit of how many styled components may be created at group %s.\\nYou may only create up to 1,073,741,824 components. If you're creating components dynamically,\\nas for instance in your render method then you may be running into this limitation.\\n\\n\",\n  17: \"CSSStyleSheet could not be found on HTMLStyleElement.\\nHas styled-components' style tag been unmounted or altered by another script?\\n\",\n  18: \"ThemeProvider: Please make sure your useTheme hook is within a `<ThemeProvider>`\"\n} : {};\nfunction de() {\n  for (var e = [], t = 0; t < arguments.length; t++) e[t] = arguments[t];\n  for (var n = e[0], o = [], r = 1, s = e.length; r < s; r += 1) o.push(e[r]);\n  return o.forEach(function (e) {\n    n = n.replace(/%[a-z]/, e);\n  }), n;\n}\nfunction he(t) {\n  for (var n = [], o = 1; o < arguments.length; o++) n[o - 1] = arguments[o];\n  return \"production\" === process.env.NODE_ENV ? new Error(\"An error occurred. See https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#\".concat(t, \" for more information.\").concat(n.length > 0 ? \" Args: \".concat(n.join(\", \")) : \"\")) : new Error(de.apply(void 0, e([pe[t]], n, !1)).trim());\n}\nvar fe = function () {\n    function e(e) {\n      this.groupSizes = new Uint32Array(512), this.length = 512, this.tag = e;\n    }\n    return e.prototype.indexOfGroup = function (e) {\n      for (var t = 0, n = 0; n < e; n++) t += this.groupSizes[n];\n      return t;\n    }, e.prototype.insertRules = function (e, t) {\n      if (e >= this.groupSizes.length) {\n        for (var n = this.groupSizes, o = n.length, r = o; e >= r;) if ((r <<= 1) < 0) throw he(16, \"\".concat(e));\n        this.groupSizes = new Uint32Array(r), this.groupSizes.set(n), this.length = r;\n        for (var s = o; s < r; s++) this.groupSizes[s] = 0;\n      }\n      for (var i = this.indexOfGroup(e + 1), a = (s = 0, t.length); s < a; s++) this.tag.insertRule(i, t[s]) && (this.groupSizes[e]++, i++);\n    }, e.prototype.clearGroup = function (e) {\n      if (e < this.length) {\n        var t = this.groupSizes[e],\n          n = this.indexOfGroup(e),\n          o = n + t;\n        this.groupSizes[e] = 0;\n        for (var r = n; r < o; r++) this.tag.deleteRule(n);\n      }\n    }, e.prototype.getGroup = function (e) {\n      var t = \"\";\n      if (e >= this.length || 0 === this.groupSizes[e]) return t;\n      for (var n = this.groupSizes[e], o = this.indexOfGroup(e), r = o + n, s = o; s < r; s++) t += \"\".concat(this.tag.getRule(s)).concat(g);\n      return t;\n    }, e;\n  }(),\n  me = 1 << 30,\n  ye = new Map(),\n  ve = new Map(),\n  ge = 1,\n  Se = function (e) {\n    if (ye.has(e)) return ye.get(e);\n    for (; ve.has(ge);) ge++;\n    var t = ge++;\n    if (\"production\" !== process.env.NODE_ENV && ((0 | t) < 0 || t > me)) throw he(16, \"\".concat(t));\n    return ye.set(e, t), ve.set(t, e), t;\n  },\n  we = function (e, t) {\n    ge = t + 1, ye.set(e, t), ve.set(t, e);\n  },\n  be = \"style[\".concat(f, \"][\").concat(y, '=\"').concat(v, '\"]'),\n  Ee = new RegExp(\"^\".concat(f, '\\\\.g(\\\\d+)\\\\[id=\"([\\\\w\\\\d-]+)\"\\\\].*?\"([^\"]*)')),\n  Ne = function (e, t, n) {\n    for (var o, r = n.split(\",\"), s = 0, i = r.length; s < i; s++) (o = r[s]) && e.registerName(t, o);\n  },\n  Pe = function (e, t) {\n    for (var n, o = (null !== (n = t.textContent) && void 0 !== n ? n : \"\").split(g), r = [], s = 0, i = o.length; s < i; s++) {\n      var a = o[s].trim();\n      if (a) {\n        var c = a.match(Ee);\n        if (c) {\n          var l = 0 | parseInt(c[1], 10),\n            u = c[2];\n          0 !== l && (we(u, l), Ne(e, u, c[3]), e.getTag().insertRules(l, r)), r.length = 0;\n        } else r.push(a);\n      }\n    }\n  },\n  _e = function (e) {\n    for (var t = document.querySelectorAll(be), n = 0, o = t.length; n < o; n++) {\n      var r = t[n];\n      r && r.getAttribute(f) !== m && (Pe(e, r), r.parentNode && r.parentNode.removeChild(r));\n    }\n  };\nfunction Ce() {\n  return \"undefined\" != typeof __webpack_nonce__ ? __webpack_nonce__ : null;\n}\nvar Ie = function (e) {\n    var t = document.head,\n      n = e || t,\n      o = document.createElement(\"style\"),\n      r = function (e) {\n        var t = Array.from(e.querySelectorAll(\"style[\".concat(f, \"]\")));\n        return t[t.length - 1];\n      }(n),\n      s = void 0 !== r ? r.nextSibling : null;\n    o.setAttribute(f, m), o.setAttribute(y, v);\n    var i = Ce();\n    return i && o.setAttribute(\"nonce\", i), n.insertBefore(o, s), o;\n  },\n  Ae = function () {\n    function e(e) {\n      this.element = Ie(e), this.element.appendChild(document.createTextNode(\"\")), this.sheet = function (e) {\n        if (e.sheet) return e.sheet;\n        for (var t = document.styleSheets, n = 0, o = t.length; n < o; n++) {\n          var r = t[n];\n          if (r.ownerNode === e) return r;\n        }\n        throw he(17);\n      }(this.element), this.length = 0;\n    }\n    return e.prototype.insertRule = function (e, t) {\n      try {\n        return this.sheet.insertRule(t, e), this.length++, !0;\n      } catch (e) {\n        return !1;\n      }\n    }, e.prototype.deleteRule = function (e) {\n      this.sheet.deleteRule(e), this.length--;\n    }, e.prototype.getRule = function (e) {\n      var t = this.sheet.cssRules[e];\n      return t && t.cssText ? t.cssText : \"\";\n    }, e;\n  }(),\n  Oe = function () {\n    function e(e) {\n      this.element = Ie(e), this.nodes = this.element.childNodes, this.length = 0;\n    }\n    return e.prototype.insertRule = function (e, t) {\n      if (e <= this.length && e >= 0) {\n        var n = document.createTextNode(t);\n        return this.element.insertBefore(n, this.nodes[e] || null), this.length++, !0;\n      }\n      return !1;\n    }, e.prototype.deleteRule = function (e) {\n      this.element.removeChild(this.nodes[e]), this.length--;\n    }, e.prototype.getRule = function (e) {\n      return e < this.length ? this.nodes[e].textContent : \"\";\n    }, e;\n  }(),\n  De = function () {\n    function e(e) {\n      this.rules = [], this.length = 0;\n    }\n    return e.prototype.insertRule = function (e, t) {\n      return e <= this.length && (this.rules.splice(e, 0, t), this.length++, !0);\n    }, e.prototype.deleteRule = function (e) {\n      this.rules.splice(e, 1), this.length--;\n    }, e.prototype.getRule = function (e) {\n      return e < this.length ? this.rules[e] : \"\";\n    }, e;\n  }(),\n  Re = S,\n  Te = {\n    isServer: !S,\n    useCSSOMInjection: !w\n  },\n  ke = function () {\n    function e(e, n, o) {\n      void 0 === e && (e = C), void 0 === n && (n = {});\n      var r = this;\n      this.options = t(t({}, Te), e), this.gs = n, this.names = new Map(o), this.server = !!e.isServer, !this.server && S && Re && (Re = !1, _e(this)), ue(this, function () {\n        return function (e) {\n          for (var t = e.getTag(), n = t.length, o = \"\", r = function (n) {\n              var r = function (e) {\n                return ve.get(e);\n              }(n);\n              if (void 0 === r) return \"continue\";\n              var s = e.names.get(r),\n                i = t.getGroup(n);\n              if (void 0 === s || !s.size || 0 === i.length) return \"continue\";\n              var a = \"\".concat(f, \".g\").concat(n, '[id=\"').concat(r, '\"]'),\n                c = \"\";\n              void 0 !== s && s.forEach(function (e) {\n                e.length > 0 && (c += \"\".concat(e, \",\"));\n              }), o += \"\".concat(i).concat(a, '{content:\"').concat(c, '\"}').concat(g);\n            }, s = 0; s < n; s++) r(s);\n          return o;\n        }(r);\n      });\n    }\n    return e.registerId = function (e) {\n      return Se(e);\n    }, e.prototype.rehydrate = function () {\n      !this.server && S && _e(this);\n    }, e.prototype.reconstructWithOptions = function (n, o) {\n      return void 0 === o && (o = !0), new e(t(t({}, this.options), n), this.gs, o && this.names || void 0);\n    }, e.prototype.allocateGSInstance = function (e) {\n      return this.gs[e] = (this.gs[e] || 0) + 1;\n    }, e.prototype.getTag = function () {\n      return this.tag || (this.tag = (e = function (e) {\n        var t = e.useCSSOMInjection,\n          n = e.target;\n        return e.isServer ? new De(n) : t ? new Ae(n) : new Oe(n);\n      }(this.options), new fe(e)));\n      var e;\n    }, e.prototype.hasNameForId = function (e, t) {\n      return this.names.has(e) && this.names.get(e).has(t);\n    }, e.prototype.registerName = function (e, t) {\n      if (Se(e), this.names.has(e)) this.names.get(e).add(t);else {\n        var n = new Set();\n        n.add(t), this.names.set(e, n);\n      }\n    }, e.prototype.insertRules = function (e, t, n) {\n      this.registerName(e, t), this.getTag().insertRules(Se(e), n);\n    }, e.prototype.clearNames = function (e) {\n      this.names.has(e) && this.names.get(e).clear();\n    }, e.prototype.clearRules = function (e) {\n      this.getTag().clearGroup(Se(e)), this.clearNames(e);\n    }, e.prototype.clearTag = function () {\n      this.tag = void 0;\n    }, e;\n  }(),\n  je = /&/g,\n  xe = /^\\s*\\/\\/.*$/gm;\nfunction Ve(e, t) {\n  return e.map(function (e) {\n    return \"rule\" === e.type && (e.value = \"\".concat(t, \" \").concat(e.value), e.value = e.value.replaceAll(\",\", \",\".concat(t, \" \")), e.props = e.props.map(function (e) {\n      return \"\".concat(t, \" \").concat(e);\n    })), Array.isArray(e.children) && \"@keyframes\" !== e.type && (e.children = Ve(e.children, t)), e;\n  });\n}\nfunction Fe(e) {\n  var t,\n    n,\n    o,\n    r = void 0 === e ? C : e,\n    s = r.options,\n    i = void 0 === s ? C : s,\n    a = r.plugins,\n    c = void 0 === a ? _ : a,\n    l = function (e, o, r) {\n      return r.startsWith(n) && r.endsWith(n) && r.replaceAll(n, \"\").length > 0 ? \".\".concat(t) : e;\n    },\n    u = c.slice();\n  u.push(function (e) {\n    e.type === d.RULESET && e.value.includes(\"&\") && (e.props[0] = e.props[0].replace(je, n).replace(o, l));\n  }), i.prefix && u.push(d.prefixer), u.push(d.stringify);\n  var p = function (e, r, s, a) {\n    void 0 === r && (r = \"\"), void 0 === s && (s = \"\"), void 0 === a && (a = \"&\"), t = a, n = r, o = new RegExp(\"\\\\\".concat(n, \"\\\\b\"), \"g\");\n    var c = e.replace(xe, \"\"),\n      l = d.compile(s || r ? \"\".concat(s, \" \").concat(r, \" { \").concat(c, \" }\") : c);\n    i.namespace && (l = Ve(l, i.namespace));\n    var p = [];\n    return d.serialize(l, d.middleware(u.concat(d.rulesheet(function (e) {\n      return p.push(e);\n    })))), p;\n  };\n  return p.hash = c.length ? c.reduce(function (e, t) {\n    return t.name || he(15), M(e, t.name);\n  }, F).toString() : \"\", p;\n}\nvar Me = new ke(),\n  ze = Fe(),\n  $e = o.createContext({\n    shouldForwardProp: void 0,\n    styleSheet: Me,\n    stylis: ze\n  }),\n  Be = $e.Consumer,\n  Le = o.createContext(void 0);\nfunction Ge() {\n  return c($e);\n}\nfunction Ye(e) {\n  var t = s(e.stylisPlugins),\n    n = t[0],\n    r = t[1],\n    c = Ge().styleSheet,\n    l = i(function () {\n      var t = c;\n      return e.sheet ? t = e.sheet : e.target && (t = t.reconstructWithOptions({\n        target: e.target\n      }, !1)), e.disableCSSOMInjection && (t = t.reconstructWithOptions({\n        useCSSOMInjection: !1\n      })), t;\n    }, [e.disableCSSOMInjection, e.sheet, e.target, c]),\n    u = i(function () {\n      return Fe({\n        options: {\n          namespace: e.namespace,\n          prefix: e.enableVendorPrefixes\n        },\n        plugins: n\n      });\n    }, [e.enableVendorPrefixes, e.namespace, n]);\n  a(function () {\n    p(n, e.stylisPlugins) || r(e.stylisPlugins);\n  }, [e.stylisPlugins]);\n  var d = i(function () {\n    return {\n      shouldForwardProp: e.shouldForwardProp,\n      styleSheet: l,\n      stylis: u\n    };\n  }, [e.shouldForwardProp, l, u]);\n  return o.createElement($e.Provider, {\n    value: d\n  }, o.createElement(Le.Provider, {\n    value: u\n  }, e.children));\n}\nvar We = function () {\n    function e(e, t) {\n      var n = this;\n      this.inject = function (e, t) {\n        void 0 === t && (t = ze);\n        var o = n.name + t.hash;\n        e.hasNameForId(n.id, o) || e.insertRules(n.id, o, t(n.rules, o, \"@keyframes\"));\n      }, this.name = e, this.id = \"sc-keyframes-\".concat(e), this.rules = t, ue(this, function () {\n        throw he(12, String(n.name));\n      });\n    }\n    return e.prototype.getName = function (e) {\n      return void 0 === e && (e = ze), this.name + e.hash;\n    }, e;\n  }(),\n  qe = function (e) {\n    return e >= \"A\" && e <= \"Z\";\n  };\nfunction He(e) {\n  for (var t = \"\", n = 0; n < e.length; n++) {\n    var o = e[n];\n    if (1 === n && \"-\" === o && \"-\" === e[0]) return e;\n    qe(o) ? t += \"-\" + o.toLowerCase() : t += o;\n  }\n  return t.startsWith(\"ms-\") ? \"-\" + t : t;\n}\nvar Ue = function (e) {\n    return null == e || !1 === e || \"\" === e;\n  },\n  Je = function (t) {\n    var n,\n      o,\n      r = [];\n    for (var s in t) {\n      var i = t[s];\n      t.hasOwnProperty(s) && !Ue(i) && (Array.isArray(i) && i.isCss || re(i) ? r.push(\"\".concat(He(s), \":\"), i, \";\") : ce(i) ? r.push.apply(r, e(e([\"\".concat(s, \" {\")], Je(i), !1), [\"}\"], !1)) : r.push(\"\".concat(He(s), \": \").concat((n = s, null == (o = i) || \"boolean\" == typeof o || \"\" === o ? \"\" : \"number\" != typeof o || 0 === o || n in h || n.startsWith(\"--\") ? String(o).trim() : \"\".concat(o, \"px\")), \";\")));\n    }\n    return r;\n  };\nfunction Xe(e, t, n, o) {\n  if (Ue(e)) return [];\n  if (se(e)) return [\".\".concat(e.styledComponentId)];\n  if (re(e)) {\n    if (!re(s = e) || s.prototype && s.prototype.isReactComponent || !t) return [e];\n    var r = e(t);\n    return \"production\" === process.env.NODE_ENV || \"object\" != typeof r || Array.isArray(r) || r instanceof We || ce(r) || null === r || console.error(\"\".concat(B(e), \" is not a styled component and cannot be referred to via component selector. See https://www.styled-components.com/docs/advanced#referring-to-other-components for more details.\")), Xe(r, t, n, o);\n  }\n  var s;\n  return e instanceof We ? n ? (e.inject(n, o), [e.getName(o)]) : [e] : ce(e) ? Je(e) : Array.isArray(e) ? Array.prototype.concat.apply(_, e.map(function (e) {\n    return Xe(e, t, n, o);\n  })) : [e.toString()];\n}\nfunction Ze(e) {\n  for (var t = 0; t < e.length; t += 1) {\n    var n = e[t];\n    if (re(n) && !se(n)) return !1;\n  }\n  return !0;\n}\nvar Ke = z(v),\n  Qe = function () {\n    function e(e, t, n) {\n      this.rules = e, this.staticRulesId = \"\", this.isStatic = \"production\" === process.env.NODE_ENV && (void 0 === n || n.isStatic) && Ze(e), this.componentId = t, this.baseHash = M(Ke, t), this.baseStyle = n, ke.registerId(t);\n    }\n    return e.prototype.generateAndInjectStyles = function (e, t, n) {\n      var o = this.baseStyle ? this.baseStyle.generateAndInjectStyles(e, t, n) : \"\";\n      if (this.isStatic && !n.hash) {\n        if (this.staticRulesId && t.hasNameForId(this.componentId, this.staticRulesId)) o = ie(o, this.staticRulesId);else {\n          var r = ae(Xe(this.rules, e, t, n)),\n            s = x(M(this.baseHash, r) >>> 0);\n          if (!t.hasNameForId(this.componentId, s)) {\n            var i = n(r, \".\".concat(s), void 0, this.componentId);\n            t.insertRules(this.componentId, s, i);\n          }\n          o = ie(o, s), this.staticRulesId = s;\n        }\n      } else {\n        for (var a = M(this.baseHash, n.hash), c = \"\", l = 0; l < this.rules.length; l++) {\n          var u = this.rules[l];\n          if (\"string\" == typeof u) c += u, \"production\" !== process.env.NODE_ENV && (a = M(a, u));else if (u) {\n            var p = ae(Xe(u, e, t, n));\n            a = M(a, p + l), c += p;\n          }\n        }\n        if (c) {\n          var d = x(a >>> 0);\n          t.hasNameForId(this.componentId, d) || t.insertRules(this.componentId, d, n(c, \".\".concat(d), void 0, this.componentId)), o = ie(o, d);\n        }\n      }\n      return o;\n    }, e;\n  }(),\n  et = o.createContext(void 0),\n  tt = et.Consumer;\nfunction nt() {\n  var e = c(et);\n  if (!e) throw he(18);\n  return e;\n}\nfunction ot(e) {\n  var n = o.useContext(et),\n    r = i(function () {\n      return function (e, n) {\n        if (!e) throw he(14);\n        if (re(e)) {\n          var o = e(n);\n          if (\"production\" !== process.env.NODE_ENV && (null === o || Array.isArray(o) || \"object\" != typeof o)) throw he(7);\n          return o;\n        }\n        if (Array.isArray(e) || \"object\" != typeof e) throw he(8);\n        return n ? t(t({}, n), e) : e;\n      }(e.theme, n);\n    }, [e.theme, n]);\n  return e.children ? o.createElement(et.Provider, {\n    value: r\n  }, e.children) : null;\n}\nvar rt = {},\n  st = new Set();\nfunction it(e, r, s) {\n  var i = se(e),\n    a = e,\n    c = !L(e),\n    p = r.attrs,\n    d = void 0 === p ? _ : p,\n    h = r.componentId,\n    f = void 0 === h ? function (e, t) {\n      var n = \"string\" != typeof e ? \"sc\" : R(e);\n      rt[n] = (rt[n] || 0) + 1;\n      var o = \"\".concat(n, \"-\").concat($(v + n + rt[n]));\n      return t ? \"\".concat(t, \"-\").concat(o) : o;\n    }(r.displayName, r.parentComponentId) : h,\n    m = r.displayName,\n    y = void 0 === m ? function (e) {\n      return L(e) ? \"styled.\".concat(e) : \"Styled(\".concat(B(e), \")\");\n    }(e) : m,\n    g = r.displayName && r.componentId ? \"\".concat(R(r.displayName), \"-\").concat(r.componentId) : r.componentId || f,\n    S = i && a.attrs ? a.attrs.concat(d).filter(Boolean) : d,\n    w = r.shouldForwardProp;\n  if (i && a.shouldForwardProp) {\n    var b = a.shouldForwardProp;\n    if (r.shouldForwardProp) {\n      var E = r.shouldForwardProp;\n      w = function (e, t) {\n        return b(e, t) && E(e, t);\n      };\n    } else w = b;\n  }\n  var N = new Qe(s, g, i ? a.componentStyle : void 0);\n  function O(e, r) {\n    return function (e, r, s) {\n      var i = e.attrs,\n        a = e.componentStyle,\n        c = e.defaultProps,\n        p = e.foldedComponentIds,\n        d = e.styledComponentId,\n        h = e.target,\n        f = o.useContext(et),\n        m = Ge(),\n        y = e.shouldForwardProp || m.shouldForwardProp;\n      \"production\" !== process.env.NODE_ENV && l(d);\n      var v = I(r, f, c) || C,\n        g = function (e, n, o) {\n          for (var r, s = t(t({}, n), {\n              className: void 0,\n              theme: o\n            }), i = 0; i < e.length; i += 1) {\n            var a = re(r = e[i]) ? r(s) : r;\n            for (var c in a) s[c] = \"className\" === c ? ie(s[c], a[c]) : \"style\" === c ? t(t({}, s[c]), a[c]) : a[c];\n          }\n          return n.className && (s.className = ie(s.className, n.className)), s;\n        }(i, r, v),\n        S = g.as || h,\n        w = {};\n      for (var b in g) void 0 === g[b] || \"$\" === b[0] || \"as\" === b || \"theme\" === b && g.theme === v || (\"forwardedAs\" === b ? w.as = g.forwardedAs : y && !y(b, S) || (w[b] = g[b], y || \"development\" !== process.env.NODE_ENV || n(b) || st.has(b) || !A.has(S) || (st.add(b), console.warn('styled-components: it looks like an unknown prop \"'.concat(b, '\" is being sent through to the DOM, which will likely trigger a React console error. If you would like automatic filtering of unknown props, you can opt-into that behavior via `<StyleSheetManager shouldForwardProp={...}>` (connect an API like `@emotion/is-prop-valid`) or consider using transient props (`$` prefix for automatic filtering.)')))));\n      var E = function (e, t) {\n        var n = Ge(),\n          o = e.generateAndInjectStyles(t, n.styleSheet, n.stylis);\n        return \"production\" !== process.env.NODE_ENV && l(o), o;\n      }(a, g);\n      \"production\" !== process.env.NODE_ENV && e.warnTooManyClasses && e.warnTooManyClasses(E);\n      var N = ie(p, d);\n      return E && (N += \" \" + E), g.className && (N += \" \" + g.className), w[L(S) && !A.has(S) ? \"class\" : \"className\"] = N, s && (w.ref = s), u(S, w);\n    }(D, e, r);\n  }\n  O.displayName = y;\n  var D = o.forwardRef(O);\n  return D.attrs = S, D.componentStyle = N, D.displayName = y, D.shouldForwardProp = w, D.foldedComponentIds = i ? ie(a.foldedComponentIds, a.styledComponentId) : \"\", D.styledComponentId = g, D.target = i ? a.target : e, Object.defineProperty(D, \"defaultProps\", {\n    get: function () {\n      return this._foldedDefaultProps;\n    },\n    set: function (e) {\n      this._foldedDefaultProps = i ? function (e) {\n        for (var t = [], n = 1; n < arguments.length; n++) t[n - 1] = arguments[n];\n        for (var o = 0, r = t; o < r.length; o++) le(e, r[o], !0);\n        return e;\n      }({}, a.defaultProps, e) : e;\n    }\n  }), \"production\" !== process.env.NODE_ENV && (P(y, g), D.warnTooManyClasses = function (e, t) {\n    var n = {},\n      o = !1;\n    return function (r) {\n      if (!o && (n[r] = !0, Object.keys(n).length >= 200)) {\n        var s = t ? ' with the id of \"'.concat(t, '\"') : \"\";\n        console.warn(\"Over \".concat(200, \" classes were generated for component \").concat(e).concat(s, \".\\n\") + \"Consider using the attrs method, together with a style object for frequently changed styles.\\nExample:\\n  const Component = styled.div.attrs(props => ({\\n    style: {\\n      background: props.background,\\n    },\\n  }))`width: 100%;`\\n\\n  <Component />\"), o = !0, n = {};\n      }\n    };\n  }(y, g)), ue(D, function () {\n    return \".\".concat(D.styledComponentId);\n  }), c && oe(D, e, {\n    attrs: !0,\n    componentStyle: !0,\n    displayName: !0,\n    foldedComponentIds: !0,\n    shouldForwardProp: !0,\n    styledComponentId: !0,\n    target: !0\n  }), D;\n}\nfunction at(e, t) {\n  for (var n = [e[0]], o = 0, r = t.length; o < r; o += 1) n.push(t[o], e[o + 1]);\n  return n;\n}\nvar ct = function (e) {\n  return Object.assign(e, {\n    isCss: !0\n  });\n};\nfunction lt(t) {\n  for (var n = [], o = 1; o < arguments.length; o++) n[o - 1] = arguments[o];\n  if (re(t) || ce(t)) return ct(Xe(at(_, e([t], n, !0))));\n  var r = t;\n  return 0 === n.length && 1 === r.length && \"string\" == typeof r[0] ? Xe(r) : ct(Xe(at(r, n)));\n}\nfunction ut(n, o, r) {\n  if (void 0 === r && (r = C), !o) throw he(1, o);\n  var s = function (t) {\n    for (var s = [], i = 1; i < arguments.length; i++) s[i - 1] = arguments[i];\n    return n(o, r, lt.apply(void 0, e([t], s, !1)));\n  };\n  return s.attrs = function (e) {\n    return ut(n, o, t(t({}, r), {\n      attrs: Array.prototype.concat(r.attrs, e).filter(Boolean)\n    }));\n  }, s.withConfig = function (e) {\n    return ut(n, o, t(t({}, r), e));\n  }, s;\n}\nvar pt = function (e) {\n    return ut(it, e);\n  },\n  dt = pt;\nA.forEach(function (e) {\n  dt[e] = pt(e);\n});\nvar ht = function () {\n  function e(e, t) {\n    this.rules = e, this.componentId = t, this.isStatic = Ze(e), ke.registerId(this.componentId + 1);\n  }\n  return e.prototype.createStyles = function (e, t, n, o) {\n    var r = o(ae(Xe(this.rules, t, n, o)), \"\"),\n      s = this.componentId + e;\n    n.insertRules(s, s, r);\n  }, e.prototype.removeStyles = function (e, t) {\n    t.clearRules(this.componentId + e);\n  }, e.prototype.renderStyles = function (e, t, n, o) {\n    e > 2 && ke.registerId(this.componentId + e), this.removeStyles(e, n), this.createStyles(e, t, n, o);\n  }, e;\n}();\nfunction ft(n) {\n  for (var r = [], s = 1; s < arguments.length; s++) r[s - 1] = arguments[s];\n  var i = lt.apply(void 0, e([n], r, !1)),\n    a = \"sc-global-\".concat($(JSON.stringify(i))),\n    c = new ht(i, a);\n  \"production\" !== process.env.NODE_ENV && P(a);\n  var l = function (e) {\n    var t = Ge(),\n      n = o.useContext(et),\n      r = o.useRef(t.styleSheet.allocateGSInstance(a)).current;\n    return \"production\" !== process.env.NODE_ENV && o.Children.count(e.children) && console.warn(\"The global style component \".concat(a, \" was given child JSX. createGlobalStyle does not render children.\")), \"production\" !== process.env.NODE_ENV && i.some(function (e) {\n      return \"string\" == typeof e && -1 !== e.indexOf(\"@import\");\n    }) && console.warn(\"Please do not use @import CSS syntax in createGlobalStyle at this time, as the CSSOM APIs we use in production do not handle it well. Instead, we recommend using a library such as react-helmet to inject a typical <link> meta tag to the stylesheet, or simply embedding it manually in your index.html <head> section for a simpler app.\"), t.styleSheet.server && u(r, e, t.styleSheet, n, t.stylis), o.useLayoutEffect(function () {\n      if (!t.styleSheet.server) return u(r, e, t.styleSheet, n, t.stylis), function () {\n        return c.removeStyles(r, t.styleSheet);\n      };\n    }, [r, e, t.styleSheet, n, t.stylis]), null;\n  };\n  function u(e, n, o, r, s) {\n    if (c.isStatic) c.renderStyles(e, b, o, s);else {\n      var i = t(t({}, n), {\n        theme: I(n, r, l.defaultProps)\n      });\n      c.renderStyles(e, i, o, s);\n    }\n  }\n  return o.memo(l);\n}\nfunction mt(t) {\n  for (var n = [], o = 1; o < arguments.length; o++) n[o - 1] = arguments[o];\n  \"production\" !== process.env.NODE_ENV && \"undefined\" != typeof navigator && \"ReactNative\" === navigator.product && console.warn(\"`keyframes` cannot be used on ReactNative, only on the web. To do animation in ReactNative please use Animated.\");\n  var r = ae(lt.apply(void 0, e([t], n, !1))),\n    s = $(r);\n  return new We(s, r);\n}\nfunction yt(e) {\n  var n = o.forwardRef(function (n, r) {\n    var s = I(n, o.useContext(et), e.defaultProps);\n    return \"production\" !== process.env.NODE_ENV && void 0 === s && console.warn('[withTheme] You are not using a ThemeProvider nor passing a theme prop or a theme in defaultProps in component class \"'.concat(B(e), '\"')), o.createElement(e, t({}, n, {\n      theme: s,\n      ref: r\n    }));\n  });\n  return n.displayName = \"WithTheme(\".concat(B(e), \")\"), oe(n, e);\n}\nvar vt = function () {\n    function e() {\n      var e = this;\n      this._emitSheetCSS = function () {\n        var t = e.instance.toString();\n        if (!t) return \"\";\n        var n = Ce(),\n          o = ae([n && 'nonce=\"'.concat(n, '\"'), \"\".concat(f, '=\"true\"'), \"\".concat(y, '=\"').concat(v, '\"')].filter(Boolean), \" \");\n        return \"<style \".concat(o, \">\").concat(t, \"</style>\");\n      }, this.getStyleTags = function () {\n        if (e.sealed) throw he(2);\n        return e._emitSheetCSS();\n      }, this.getStyleElement = function () {\n        var n;\n        if (e.sealed) throw he(2);\n        var r = e.instance.toString();\n        if (!r) return [];\n        var s = ((n = {})[f] = \"\", n[y] = v, n.dangerouslySetInnerHTML = {\n            __html: r\n          }, n),\n          i = Ce();\n        return i && (s.nonce = i), [o.createElement(\"style\", t({}, s, {\n          key: \"sc-0-0\"\n        }))];\n      }, this.seal = function () {\n        e.sealed = !0;\n      }, this.instance = new ke({\n        isServer: !0\n      }), this.sealed = !1;\n    }\n    return e.prototype.collectStyles = function (e) {\n      if (this.sealed) throw he(2);\n      return o.createElement(Ye, {\n        sheet: this.instance\n      }, e);\n    }, e.prototype.interleaveWithNodeStream = function (e) {\n      throw he(3);\n    }, e;\n  }(),\n  gt = {\n    StyleSheet: ke,\n    mainSheet: Me\n  };\n\"production\" !== process.env.NODE_ENV && \"undefined\" != typeof navigator && \"ReactNative\" === navigator.product && console.warn(\"It looks like you've imported 'styled-components' on React Native.\\nPerhaps you're looking to import 'styled-components/native'?\\nRead more about this at https://www.styled-components.com/docs/basics#react-native\");\nvar St = \"__sc-\".concat(f, \"__\");\n\"production\" !== process.env.NODE_ENV && \"test\" !== process.env.NODE_ENV && \"undefined\" != typeof window && (window[St] || (window[St] = 0), 1 === window[St] && console.warn(\"It looks like there are several instances of 'styled-components' initialized in this application. This may cause dynamic styles to not render properly, errors during the rehydration process, a missing theme prop, and makes your application bigger without good reason.\\n\\nSee https://s-c.sh/2BAXzed for more info.\"), window[St] += 1);\nexport { vt as ServerStyleSheet, Be as StyleSheetConsumer, $e as StyleSheetContext, Ye as StyleSheetManager, tt as ThemeConsumer, et as ThemeContext, ot as ThemeProvider, gt as __PRIVATE__, ft as createGlobalStyle, lt as css, dt as default, se as isStyledComponent, mt as keyframes, dt as styled, nt as useTheme, v as version, yt as withTheme };", "map": {"version": 3, "names": ["f", "process", "env", "REACT_APP_SC_ATTR", "SC_ATTR", "m", "y", "v", "g", "S", "window", "document", "w", "Boolean", "SC_DISABLE_SPEEDY", "REACT_APP_SC_DISABLE_SPEEDY", "NODE_ENV", "b", "E", "N", "Set", "P", "checkDynamicCreation", "t", "n", "o", "concat", "s", "i", "console", "error", "a", "arguments", "length", "test", "delete", "apply", "e", "r", "has", "warn", "add", "message", "_", "Object", "freeze", "C", "I", "theme", "A", "O", "D", "R", "replace", "T", "k", "j", "getAlphabeticChar", "String", "fromCharCode", "x", "Math", "abs", "V", "F", "M", "phash", "charCodeAt", "z", "hash", "$", "B", "displayName", "name", "L", "char<PERSON>t", "toLowerCase", "G", "Symbol", "for", "Y", "W", "q", "childContextTypes", "contextType", "contextTypes", "defaultProps", "getDefaultProps", "getDerivedStateFromError", "getDerivedStateFromProps", "mixins", "propTypes", "type", "H", "prototype", "caller", "callee", "arity", "U", "$$typeof", "compare", "J", "render", "X", "Z", "defineProperty", "K", "getOwnPropertyNames", "Q", "getOwnPropertySymbols", "ee", "getOwnPropertyDescriptor", "te", "getPrototypeOf", "ne", "oe", "c", "l", "re", "se", "ie", "ae", "ce", "constructor", "le", "Array", "isArray", "ue", "value", "pe", "de", "push", "for<PERSON>ach", "he", "Error", "join", "trim", "fe", "groupSizes", "Uint32Array", "tag", "indexOfGroup", "insertRules", "set", "insertRule", "clearGroup", "deleteRule", "getGroup", "getRule", "me", "ye", "Map", "ve", "ge", "Se", "getGroupForId", "get", "we", "setGroupForId", "be", "Ee", "RegExp", "Ne", "rehydrateNamesFromContent", "split", "registerName", "Pe", "rehydrateSheetFromTag", "textContent", "match", "parseInt", "u", "getTag", "_e", "rehydrateSheet", "querySelectorAll", "getAttribute", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "Ce", "__webpack_nonce__", "Ie", "makeStyleTag", "head", "createElement", "from", "nextS<PERSON>ling", "setAttribute", "insertBefore", "Ae", "element", "append<PERSON><PERSON><PERSON>", "createTextNode", "sheet", "styleSheets", "ownerNode", "cssRules", "cssText", "Oe", "nodes", "childNodes", "De", "rules", "splice", "Re", "Te", "isServer", "useCSSOMInjection", "ke", "options", "gs", "names", "server", "size", "registerId", "rehydrate", "reconstructWithOptions", "allocateGSInstance", "target", "hasNameForId", "clearNames", "clear", "clearRules", "clearTag", "je", "xe", "Ve", "map", "replaceAll", "props", "children", "Fe", "plugins", "selfReferenceReplacer", "startsWith", "endsWith", "slice", "d", "RULESET", "includes", "prefix", "prefixer", "stringify", "p", "stringifyRules", "compile", "namespace", "serialize", "middleware", "rulesheet", "reduce", "toString", "Me", "ze", "$e", "createContext", "shouldForwardProp", "styleSheet", "stylis", "Be", "Consumer", "Le", "Ge", "Ye", "stylisPlugins", "disableCSSOMInjection", "enableVendorPrefixes", "Provider", "We", "inject", "id", "getName", "qe", "isUpper", "He", "Ue", "isFalsish", "Je", "objToCssArray", "hasOwnProperty", "isCss", "h", "Xe", "styledComponentId", "isReactComponent", "Ze", "<PERSON>", "Qe", "staticRulesId", "isStatic", "componentId", "baseHash", "baseStyle", "generateAndInjectStyles", "et", "tt", "nt", "ot", "useContext", "rt", "st", "it", "attrs", "parentComponentId", "filter", "componentStyle", "foldedComponentIds", "className", "as", "forwardedAs", "warnTooManyClasses", "ref", "forwardRef", "_foldedDefaultProps", "keys", "at", "ct", "addTag", "assign", "lt", "ut", "templateFunction", "withConfig", "pt", "baseStyled", "dt", "ht", "createStyles", "removeStyles", "renderStyles", "ft", "JSON", "GlobalStyleComponent", "useRef", "current", "Children", "count", "some", "indexOf", "useLayoutEffect", "memo", "mt", "navigator", "product", "yt", "vt", "_emitSheetCSS", "instance", "getStyleTags", "sealed", "getStyleElement", "dangerouslySetInnerHTML", "__html", "nonce", "key", "seal", "collectStyles", "interleaveWithNodeStream", "gt", "StyleSheet", "mainSheet", "St", "ServerStyleSheet", "StyleSheetConsumer", "StyleSheetContext", "StyleSheetManager", "ThemeConsumer", "ThemeContext", "ThemeProvider", "__PRIVATE__", "createGlobalStyle", "css", "default", "isStyledComponent", "keyframes", "styled", "useTheme", "version", "withTheme"], "sources": ["D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\constants.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\checkDynamicCreation.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\empties.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\determineTheme.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\domElements.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\escape.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\generateAlphabeticName.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\hash.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\generateComponentId.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\getComponentName.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\isTag.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\hoist.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\isFunction.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\isStyledComponent.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\joinStrings.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\isPlainObject.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\mixinDeep.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\setToString.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\errors.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\error.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\sheet\\GroupedTag.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\sheet\\GroupIDAllocator.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\sheet\\Rehydration.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\nonce.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\sheet\\dom.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\sheet\\Tag.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\sheet\\Sheet.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\stylis.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\models\\StyleSheetManager.tsx", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\models\\Keyframes.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\hyphenateStyleName.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\flatten.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\addUnitIfNeeded.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\isStatelessFunction.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\isStaticRules.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\models\\ComponentStyle.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\models\\ThemeProvider.tsx", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\models\\StyledComponent.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\generateDisplayName.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\createWarnTooManyClasses.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\utils\\interleave.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\constructors\\css.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\constructors\\constructWithOptions.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\constructors\\styled.tsx", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\models\\GlobalStyle.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\constructors\\createGlobalStyle.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\constructors\\keyframes.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\hoc\\withTheme.tsx", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\models\\ServerStyleSheet.tsx", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\secretInternals.ts", "D:\\ASL\\ASL-Training\\node_modules\\styled-components\\src\\base.ts"], "sourcesContent": ["declare let SC_DISABLE_SPEEDY: boolean | null | undefined;\ndeclare let __VERSION__: string;\n\nexport const SC_ATTR: string =\n  (typeof process !== 'undefined' &&\n    typeof process.env !== 'undefined' &&\n    (process.env.REACT_APP_SC_ATTR || process.env.SC_ATTR)) ||\n  'data-styled';\n\nexport const SC_ATTR_ACTIVE = 'active';\nexport const SC_ATTR_VERSION = 'data-styled-version';\nexport const SC_VERSION = __VERSION__;\nexport const SPLITTER = '/*!sc*/\\n';\n\nexport const IS_BROWSER = typeof window !== 'undefined' && typeof document !== 'undefined';\n\nexport const DISABLE_SPEEDY = Boolean(\n  typeof SC_DISABLE_SPEEDY === 'boolean'\n    ? SC_DISABLE_SPEEDY\n    : typeof process !== 'undefined' &&\n        typeof process.env !== 'undefined' &&\n        typeof process.env.REACT_APP_SC_DISABLE_SPEEDY !== 'undefined' &&\n        process.env.REACT_APP_SC_DISABLE_SPEEDY !== ''\n      ? process.env.REACT_APP_SC_DISABLE_SPEEDY === 'false'\n        ? false\n        : process.env.REACT_APP_SC_DISABLE_SPEEDY\n      : typeof process !== 'undefined' &&\n          typeof process.env !== 'undefined' &&\n          typeof process.env.SC_DISABLE_SPEEDY !== 'undefined' &&\n          process.env.SC_DISABLE_SPEEDY !== ''\n        ? process.env.SC_DISABLE_SPEEDY === 'false'\n          ? false\n          : process.env.SC_DISABLE_SPEEDY\n        : process.env.NODE_ENV !== 'production'\n);\n\n// Shared empty execution context when generating static styles\nexport const STATIC_EXECUTION_CONTEXT = {};\n", "import { useRef } from 'react';\n\nconst invalidHookCallRe = /invalid hook call/i;\nconst seen = new Set();\n\nexport const checkDynamicCreation = (displayName: string, componentId?: string | undefined) => {\n  if (process.env.NODE_ENV !== 'production') {\n    const parsedIdString = componentId ? ` with the id of \"${componentId}\"` : '';\n    const message =\n      `The component ${displayName}${parsedIdString} has been created dynamically.\\n` +\n      \"You may see this warning because you've called styled inside another component.\\n\" +\n      'To resolve this only create new StyledComponents outside of any render method and function component.\\n' +\n      'See https://styled-components.com/docs/basics#define-styled-components-outside-of-the-render-method for more info.\\n';\n\n    // If a hook is called outside of a component:\n    // React 17 and earlier throw an error\n    // React 18 and above use console.error\n\n    const originalConsoleError = console.error;\n    try {\n      let didNotCallInvalidHook = true;\n      console.error = (consoleErrorMessage, ...consoleErrorArgs) => {\n        // The error here is expected, since we're expecting anything that uses `checkDynamicCreation` to\n        // be called outside of a React component.\n        if (invalidHookCallRe.test(consoleErrorMessage)) {\n          didNotCallInvalidHook = false;\n          // This shouldn't happen, but resets `warningSeen` if we had this error happen intermittently\n          seen.delete(message);\n        } else {\n          originalConsoleError(consoleErrorMessage, ...consoleErrorArgs);\n        }\n      };\n      // We purposefully call `useRef` outside of a component and expect it to throw\n      // If it doesn't, then we're inside another component.\n      useRef();\n\n      if (didNotCallInvalidHook && !seen.has(message)) {\n        console.warn(message);\n        seen.add(message);\n      }\n    } catch (error) {\n      // The error here is expected, since we're expecting anything that uses `checkDynamicCreation` to\n      // be called outside of a React component.\n      if (invalidHookCallRe.test((error as Error).message)) {\n        // This shouldn't happen, but resets `warningSeen` if we had this error happen intermittently\n        seen.delete(message);\n      }\n    } finally {\n      console.error = originalConsoleError;\n    }\n  }\n};\n", "import { Dict } from '../types';\n\nexport const EMPTY_ARRAY = Object.freeze([]) as Readonly<any[]>;\nexport const EMPTY_OBJECT = Object.freeze({}) as Readonly<Dict<any>>;\n", "import { DefaultTheme, ExecutionProps } from '../types';\nimport { EMPTY_OBJECT } from './empties';\n\nexport default function determineTheme(\n  props: ExecutionProps,\n  providedTheme?: DefaultTheme | undefined,\n  defaultProps: { theme?: DefaultTheme | undefined } = EMPTY_OBJECT\n): DefaultTheme | undefined {\n  return (props.theme !== defaultProps.theme && props.theme) || providedTheme || defaultProps.theme;\n}\n", "// Thanks to ReactDOMFactories for this handy list!\n\nconst elements = [\n  'a',\n  'abbr',\n  'address',\n  'area',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'base',\n  'bdi',\n  'bdo',\n  'big',\n  'blockquote',\n  'body',\n  'br',\n  'button',\n  'canvas',\n  'caption',\n  'cite',\n  'code',\n  'col',\n  'colgroup',\n  'data',\n  'datalist',\n  'dd',\n  'del',\n  'details',\n  'dfn',\n  'dialog',\n  'div',\n  'dl',\n  'dt',\n  'em',\n  'embed',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'header',\n  'hgroup',\n  'hr',\n  'html',\n  'i',\n  'iframe',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'keygen',\n  'label',\n  'legend',\n  'li',\n  'link',\n  'main',\n  'map',\n  'mark',\n  'menu',\n  'menuitem',\n  'meta',\n  'meter',\n  'nav',\n  'noscript',\n  'object',\n  'ol',\n  'optgroup',\n  'option',\n  'output',\n  'p',\n  'param',\n  'picture',\n  'pre',\n  'progress',\n  'q',\n  'rp',\n  'rt',\n  'ruby',\n  's',\n  'samp',\n  'script',\n  'section',\n  'select',\n  'small',\n  'source',\n  'span',\n  'strong',\n  'style',\n  'sub',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'track',\n  'u',\n  'ul',\n  'use',\n  'var',\n  'video',\n  'wbr', // SVG\n  'circle',\n  'clipPath',\n  'defs',\n  'ellipse',\n  'foreignObject',\n  'g',\n  'image',\n  'line',\n  'linearGradient',\n  'marker',\n  'mask',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialGradient',\n  'rect',\n  'stop',\n  'svg',\n  'text',\n  'tspan',\n] as const;\n\nexport default new Set(elements);\nexport type SupportedHTMLElements = (typeof elements)[number];\n", "// Source: https://www.w3.org/TR/cssom-1/#serialize-an-identifier\n// Control characters and non-letter first symbols are not supported\nconst escapeRegex = /[!\"#$%&'()*+,./:;<=>?@[\\\\\\]^`{|}~-]+/g;\n\nconst dashesAtEnds = /(^-|-$)/g;\n\n/**\n * TODO: Explore using CSS.escape when it becomes more available\n * in evergreen browsers.\n */\nexport default function escape(str: string) {\n  return str // Replace all possible CSS selectors\n    .replace(escapeRegex, '-') // Remove extraneous hyphens at the start and end\n    .replace(dashesAtEnds, '');\n}\n", "const AD_REPLACER_R = /(a)(d)/gi;\n\n/* This is the \"capacity\" of our alphabet i.e. 2x26 for all letters plus their capitalised\n * counterparts */\nconst charsLength = 52;\n\n/* start at 75 for 'a' until 'z' (25) and then start at 65 for capitalised letters */\nconst getAlphabeticChar = (code: number) => String.fromCharCode(code + (code > 25 ? 39 : 97));\n\n/* input a number, usually a hash and convert it to base-52 */\nexport default function generateAlphabeticName(code: number) {\n  let name = '';\n  let x;\n\n  /* get a char and divide by alphabet-length */\n  for (x = Math.abs(code); x > charsLength; x = (x / charsLength) | 0) {\n    name = getAlphabeticChar(x % charsLength) + name;\n  }\n\n  return (getAlphabeticChar(x % charsLength) + name).replace(AD_REPLACER_R, '$1-$2');\n}\n", "export const SEED = 5381;\n\n// When we have separate strings it's useful to run a progressive\n// version of djb2 where we pretend that we're still looping over\n// the same string\nexport const phash = (h: number, x: string) => {\n  let i = x.length;\n\n  while (i) {\n    h = (h * 33) ^ x.charCodeAt(--i);\n  }\n\n  return h;\n};\n\n// This is a djb2 hashing function\nexport const hash = (x: string) => {\n  return phash(SEED, x);\n};\n", "import generateAlphabeticName from './generateAlphabeticName';\nimport { hash } from './hash';\n\nexport default function generateComponentId(str: string) {\n  return generateAlphabeticName(hash(str) >>> 0);\n}\n", "import { StyledTarget } from '../types';\n\nexport default function getComponentName(target: StyledTarget<any>) {\n  return (\n    (process.env.NODE_ENV !== 'production' ? typeof target === 'string' && target : false) ||\n    (target as Exclude<StyledTarget<any>, string>).displayName ||\n    (target as Function).name ||\n    'Component'\n  );\n}\n", "import { StyledTarget } from '../types';\n\nexport default function isTag(target: StyledTarget<'web'>): target is string {\n  return (\n    typeof target === 'string' &&\n    (process.env.NODE_ENV !== 'production'\n      ? target.charAt(0) === target.charAt(0).toLowerCase()\n      : true)\n  );\n}\n", "import React from 'react';\nimport { AnyComponent } from '../types';\n\nconst hasSymbol = typeof Symbol === 'function' && Symbol.for;\n\n// copied from react-is\nconst REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;\nconst REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;\n\n/**\n * Adapted from hoist-non-react-statics to avoid the react-is dependency.\n */\nconst REACT_STATICS = {\n  childContextTypes: true,\n  contextType: true,\n  contextTypes: true,\n  defaultProps: true,\n  displayName: true,\n  getDefaultProps: true,\n  getDerivedStateFromError: true,\n  getDerivedStateFromProps: true,\n  mixins: true,\n  propTypes: true,\n  type: true,\n};\n\nconst KNOWN_STATICS = {\n  name: true,\n  length: true,\n  prototype: true,\n  caller: true,\n  callee: true,\n  arguments: true,\n  arity: true,\n};\n\nconst FORWARD_REF_STATICS = {\n  $$typeof: true,\n  render: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true,\n};\n\nconst MEMO_STATICS = {\n  $$typeof: true,\n  compare: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true,\n  type: true,\n};\n\nconst TYPE_STATICS = {\n  [REACT_FORWARD_REF_TYPE]: FORWARD_REF_STATICS,\n  [REACT_MEMO_TYPE]: MEMO_STATICS,\n};\n\ntype OmniComponent = AnyComponent;\n\n// adapted from react-is\nfunction isMemo(\n  object: OmniComponent | React.MemoExoticComponent<any>\n): object is React.MemoExoticComponent<any> {\n  const $$typeofType = 'type' in object && object.type.$$typeof;\n\n  return $$typeofType === REACT_MEMO_TYPE;\n}\n\nfunction getStatics(component: OmniComponent) {\n  // React v16.11 and below\n  if (isMemo(component)) {\n    return MEMO_STATICS;\n  }\n\n  // React v16.12 and above\n  return '$$typeof' in component\n    ? TYPE_STATICS[component['$$typeof'] as unknown as string]\n    : REACT_STATICS;\n}\n\nconst defineProperty = Object.defineProperty;\nconst getOwnPropertyNames = Object.getOwnPropertyNames;\nconst getOwnPropertySymbols = Object.getOwnPropertySymbols;\nconst getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nconst getPrototypeOf = Object.getPrototypeOf;\nconst objectPrototype = Object.prototype;\n\ntype ExcludeList = {\n  [key: string]: true;\n};\n\nexport type NonReactStatics<S extends OmniComponent, C extends ExcludeList = {}> = {\n  [key in Exclude<\n    keyof S,\n    S extends React.MemoExoticComponent<any>\n      ? keyof typeof MEMO_STATICS | keyof C\n      : S extends React.ForwardRefExoticComponent<any>\n        ? keyof typeof FORWARD_REF_STATICS | keyof C\n        : keyof typeof REACT_STATICS | keyof typeof KNOWN_STATICS | keyof C\n  >]: S[key];\n};\n\nexport default function hoistNonReactStatics<\n  T extends OmniComponent,\n  S extends OmniComponent,\n  C extends ExcludeList = {},\n>(targetComponent: T, sourceComponent: S, excludelist?: C | undefined) {\n  if (typeof sourceComponent !== 'string') {\n    // don't hoist over string (html) components\n\n    if (objectPrototype) {\n      const inheritedComponent = getPrototypeOf(sourceComponent);\n      if (inheritedComponent && inheritedComponent !== objectPrototype) {\n        hoistNonReactStatics(targetComponent, inheritedComponent, excludelist);\n      }\n    }\n\n    let keys: (String | Symbol)[] = getOwnPropertyNames(sourceComponent);\n\n    if (getOwnPropertySymbols) {\n      keys = keys.concat(getOwnPropertySymbols(sourceComponent));\n    }\n\n    const targetStatics = getStatics(targetComponent);\n    const sourceStatics = getStatics(sourceComponent);\n\n    for (let i = 0; i < keys.length; ++i) {\n      const key = keys[i] as unknown as string;\n      if (\n        !(key in KNOWN_STATICS) &&\n        !(excludelist && excludelist[key]) &&\n        !(sourceStatics && key in sourceStatics) &&\n        !(targetStatics && key in targetStatics)\n      ) {\n        const descriptor = getOwnPropertyDescriptor(sourceComponent, key);\n\n        try {\n          // Avoid failures from read-only properties\n          defineProperty(targetComponent, key, descriptor!);\n        } catch (e) {\n          /* ignore */\n        }\n      }\n    }\n  }\n\n  return targetComponent as T & NonReactStatics<S, C>;\n}\n", "export default function isFunction(test: any): test is Function {\n  return typeof test === 'function';\n}\n", "import { StyledComponentBrand } from '../types';\n\nexport default function isStyledComponent(target: any): target is StyledComponentBrand {\n  return typeof target === 'object' && 'styledComponentId' in target;\n}\n", "/**\n * Convenience function for joining strings to form className chains\n */\nexport function joinStrings(a?: string | undefined, b?: string | undefined): string {\n  return a && b ? `${a} ${b}` : a || b || '';\n}\n\nexport function joinStringArray(arr: string[], sep?: string | undefined): string {\n  if (arr.length === 0) {\n    return '';\n  }\n\n  let result = arr[0];\n  for (let i = 1; i < arr.length; i++) {\n    result += sep ? sep + arr[i] : arr[i];\n  }\n  return result;\n}\n", "export default function isPlainObject(x: any): x is Record<any, any> {\n  return (\n    x !== null &&\n    typeof x === 'object' &&\n    x.constructor.name === Object.name &&\n    /* check for reasonable markers that the object isn't an element for react & preact/compat */\n    !('props' in x && x.$$typeof)\n  );\n}\n", "import isPlainObject from './isPlainObject';\n\nfunction mixinRecursively(target: any, source: any, forceMerge = false) {\n  /* only merge into POJOs, Arrays, but for top level objects only\n   * allow to merge into anything by passing forceMerge = true */\n  if (!forceMerge && !isPlainObject(target) && !Array.isArray(target)) {\n    return source;\n  }\n\n  if (Array.isArray(source)) {\n    for (let key = 0; key < source.length; key++) {\n      target[key] = mixinRecursively(target[key], source[key]);\n    }\n  } else if (isPlainObject(source)) {\n    for (const key in source) {\n      target[key] = mixinRecursively(target[key], source[key]);\n    }\n  }\n\n  return target;\n}\n\n/**\n * Arrays & POJOs merged recursively, other objects and value types are overridden\n * If target is not a POJO or an Array, it will get source properties injected via shallow merge\n * Source objects applied left to right.  Mutates & returns target.  Similar to lodash merge.\n */\nexport default function mixinDeep(target: any, ...sources: any[]) {\n  for (const source of sources) {\n    mixinRecursively(target, source, true);\n  }\n\n  return target;\n}\n", "/**\n * If the Object prototype is frozen, the \"toString\" property is non-writable. This means that any objects which inherit this property\n * cannot have the property changed using a \"=\" assignment operator. If using strict mode, attempting that will cause an error. If not using\n * strict mode, attempting that will be silently ignored.\n *\n * If the Object prototype is frozen, inherited non-writable properties can still be shadowed using one of two mechanisms:\n *\n *  1. ES6 class methods: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Classes#methods\n *  2. Using the `Object.defineProperty()` static method:\n *     https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/defineProperty\n *\n * However, this project uses Babel to transpile ES6 classes, and transforms ES6 class methods to use the assignment operator instead:\n * https://babeljs.io/docs/babel-plugin-transform-class-properties#options\n *\n * Therefore, the most compatible way to shadow the prototype's \"toString\" property is to define a new \"toString\" property on this object.\n */\nexport function setToString(object: object, toStringFn: () => string) {\n  Object.defineProperty(object, 'toString', { value: toStringFn });\n}\n", "export default {\n  '1': 'Cannot create styled-component for component: %s.\\n\\n',\n  '2': \"Can't collect styles once you've consumed a `ServerStyleSheet`'s styles! `ServerStyleSheet` is a one off instance for each server-side render cycle.\\n\\n- Are you trying to reuse it across renders?\\n- Are you accidentally calling collectStyles twice?\\n\\n\",\n  '3': 'Streaming SSR is only supported in a Node.js environment; Please do not try to call this method in the browser.\\n\\n',\n  '4': 'The `StyleSheetManager` expects a valid target or sheet prop!\\n\\n- Does this error occur on the client and is your target falsy?\\n- Does this error occur on the server and is the sheet falsy?\\n\\n',\n  '5': 'The clone method cannot be used on the client!\\n\\n- Are you running in a client-like environment on the server?\\n- Are you trying to run SSR on the client?\\n\\n',\n  '6': \"Trying to insert a new style tag, but the given Node is unmounted!\\n\\n- Are you using a custom target that isn't mounted?\\n- Does your document not have a valid head element?\\n- Have you accidentally removed a style tag manually?\\n\\n\",\n  '7': 'ThemeProvider: Please return an object from your \"theme\" prop function, e.g.\\n\\n```js\\ntheme={() => ({})}\\n```\\n\\n',\n  '8': 'ThemeProvider: Please make your \"theme\" prop an object.\\n\\n',\n  '9': 'Missing document `<head>`\\n\\n',\n  '10': 'Cannot find a StyleSheet instance. Usually this happens if there are multiple copies of styled-components loaded at once. Check out this issue for how to troubleshoot and fix the common cases where this situation can happen: https://github.com/styled-components/styled-components/issues/1941#issuecomment-417862021\\n\\n',\n  '11': '_This error was replaced with a dev-time warning, it will be deleted for v4 final._ [createGlobalStyle] received children which will not be rendered. Please use the component without passing children elements.\\n\\n',\n  '12': 'It seems you are interpolating a keyframe declaration (%s) into an untagged string. This was supported in styled-components v3, but is not longer supported in v4 as keyframes are now injected on-demand. Please wrap your string in the css\\\\`\\\\` helper which ensures the styles are injected correctly. See https://www.styled-components.com/docs/api#css\\n\\n',\n  '13': '%s is not a styled component and cannot be referred to via component selector. See https://www.styled-components.com/docs/advanced#referring-to-other-components for more details.\\n\\n',\n  '14': 'ThemeProvider: \"theme\" prop is required.\\n\\n',\n  '15': \"A stylis plugin has been supplied that is not named. We need a name for each plugin to be able to prevent styling collisions between different stylis configurations within the same app. Before you pass your plugin to `<StyleSheetManager stylisPlugins={[]}>`, please make sure each plugin is uniquely-named, e.g.\\n\\n```js\\nObject.defineProperty(importedPlugin, 'name', { value: 'some-unique-name' });\\n```\\n\\n\",\n  '16': \"Reached the limit of how many styled components may be created at group %s.\\nYou may only create up to 1,073,741,824 components. If you're creating components dynamically,\\nas for instance in your render method then you may be running into this limitation.\\n\\n\",\n  '17': \"CSSStyleSheet could not be found on HTMLStyleElement.\\nHas styled-components' style tag been unmounted or altered by another script?\\n\",\n  '18': 'ThemeProvider: Please make sure your useTheme hook is within a `<ThemeProvider>`',\n};\n", "import { Dict } from '../types';\nimport errorMap from './errors';\n\nconst ERRORS: Dict<any> = process.env.NODE_ENV !== 'production' ? errorMap : {};\n\n/**\n * super basic version of sprintf\n */\nfunction format(...args: [string, ...any]) {\n  let a = args[0];\n  const b = [];\n\n  for (let c = 1, len = args.length; c < len; c += 1) {\n    b.push(args[c]);\n  }\n\n  b.forEach(d => {\n    a = a.replace(/%[a-z]/, d);\n  });\n\n  return a;\n}\n\n/**\n * Create an error file out of errors.md for development and a simple web link to the full errors\n * in production mode.\n */\nexport default function throwStyledComponentsError(\n  code: string | number,\n  ...interpolations: any[]\n) {\n  if (process.env.NODE_ENV === 'production') {\n    return new Error(\n      `An error occurred. See https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#${code} for more information.${\n        interpolations.length > 0 ? ` Args: ${interpolations.join(', ')}` : ''\n      }`\n    );\n  } else {\n    return new Error(format(ERRORS[code], ...interpolations).trim());\n  }\n}\n", "import { SPLITTER } from '../constants';\nimport styledError from '../utils/error';\nimport { GroupedTag, Tag } from './types';\n\n/** Create a GroupedTag with an underlying Tag implementation */\nexport const makeGroupedTag = (tag: Tag) => {\n  return new DefaultGroupedTag(tag);\n};\n\nconst BASE_SIZE = 1 << 9;\n\nconst DefaultGroupedTag = class DefaultGroupedTag implements GroupedTag {\n  groupSizes: Uint32Array;\n  length: number;\n  tag: Tag;\n\n  constructor(tag: Tag) {\n    this.groupSizes = new Uint32Array(BASE_SIZE);\n    this.length = BASE_SIZE;\n    this.tag = tag;\n  }\n\n  indexOfGroup(group: number) {\n    let index = 0;\n    for (let i = 0; i < group; i++) {\n      index += this.groupSizes[i];\n    }\n\n    return index;\n  }\n\n  insertRules(group: number, rules: string[]) {\n    if (group >= this.groupSizes.length) {\n      const oldBuffer = this.groupSizes;\n      const oldSize = oldBuffer.length;\n\n      let newSize = oldSize;\n      while (group >= newSize) {\n        newSize <<= 1;\n        if (newSize < 0) {\n          throw styledError(16, `${group}`);\n        }\n      }\n\n      this.groupSizes = new Uint32Array(newSize);\n      this.groupSizes.set(oldBuffer);\n      this.length = newSize;\n\n      for (let i = oldSize; i < newSize; i++) {\n        this.groupSizes[i] = 0;\n      }\n    }\n\n    let ruleIndex = this.indexOfGroup(group + 1);\n\n    for (let i = 0, l = rules.length; i < l; i++) {\n      if (this.tag.insertRule(ruleIndex, rules[i])) {\n        this.groupSizes[group]++;\n        ruleIndex++;\n      }\n    }\n  }\n\n  clearGroup(group: number) {\n    if (group < this.length) {\n      const length = this.groupSizes[group];\n      const startIndex = this.indexOfGroup(group);\n      const endIndex = startIndex + length;\n\n      this.groupSizes[group] = 0;\n\n      for (let i = startIndex; i < endIndex; i++) {\n        this.tag.deleteRule(startIndex);\n      }\n    }\n  }\n\n  getGroup(group: number) {\n    let css = '';\n    if (group >= this.length || this.groupSizes[group] === 0) {\n      return css;\n    }\n\n    const length = this.groupSizes[group];\n    const startIndex = this.indexOfGroup(group);\n    const endIndex = startIndex + length;\n\n    for (let i = startIndex; i < endIndex; i++) {\n      css += `${this.tag.getRule(i)}${SPLITTER}`;\n    }\n\n    return css;\n  }\n};\n", "import styledError from '../utils/error';\n\nconst MAX_SMI = 1 << (31 - 1);\n\nlet groupIDRegister: Map<string, number> = new Map();\nlet reverseRegister: Map<number, string> = new Map();\nlet nextFreeGroup = 1;\n\nexport const resetGroupIds = () => {\n  groupIDRegister = new Map();\n  reverseRegister = new Map();\n  nextFreeGroup = 1;\n};\n\nexport const getGroupForId = (id: string): number => {\n  if (groupIDRegister.has(id)) {\n    return groupIDRegister.get(id) as any;\n  }\n\n  while (reverseRegister.has(nextFreeGroup)) {\n    nextFreeGroup++;\n  }\n\n  const group = nextFreeGroup++;\n\n  if (process.env.NODE_ENV !== 'production' && ((group | 0) < 0 || group > MAX_SMI)) {\n    throw styledError(16, `${group}`);\n  }\n\n  groupIDRegister.set(id, group);\n  reverseRegister.set(group, id);\n  return group;\n};\n\nexport const getIdForGroup = (group: number): void | string => {\n  return reverseRegister.get(group);\n};\n\nexport const setGroupForId = (id: string, group: number) => {\n  // move pointer\n  nextFreeGroup = group + 1;\n\n  groupIDRegister.set(id, group);\n  reverseRegister.set(group, id);\n};\n", "import { SC_ATTR, SC_ATTR_ACTIVE, SC_ATTR_VERSION, SC_VERSION, SPLITTER } from '../constants';\nimport { getIdForGroup, setGroupForId } from './GroupIDAllocator';\nimport { Sheet } from './types';\n\nconst SELECTOR = `style[${SC_ATTR}][${SC_ATTR_VERSION}=\"${SC_VERSION}\"]`;\nconst MARKER_RE = new RegExp(`^${SC_ATTR}\\\\.g(\\\\d+)\\\\[id=\"([\\\\w\\\\d-]+)\"\\\\].*?\"([^\"]*)`);\n\nexport const outputSheet = (sheet: Sheet) => {\n  const tag = sheet.getTag();\n  const { length } = tag;\n\n  let css = '';\n  for (let group = 0; group < length; group++) {\n    const id = getIdForGroup(group);\n    if (id === undefined) continue;\n\n    const names = sheet.names.get(id);\n    const rules = tag.getGroup(group);\n    if (names === undefined || !names.size || rules.length === 0) continue;\n\n    const selector = `${SC_ATTR}.g${group}[id=\"${id}\"]`;\n\n    let content = '';\n    if (names !== undefined) {\n      names.forEach(name => {\n        if (name.length > 0) {\n          content += `${name},`;\n        }\n      });\n    }\n\n    // NOTE: It's easier to collect rules and have the marker\n    // after the actual rules to simplify the rehydration\n    css += `${rules}${selector}{content:\"${content}\"}${SPLITTER}`;\n  }\n\n  return css;\n};\n\nconst rehydrateNamesFromContent = (sheet: Sheet, id: string, content: string) => {\n  const names = content.split(',');\n  let name;\n\n  for (let i = 0, l = names.length; i < l; i++) {\n    if ((name = names[i])) {\n      sheet.registerName(id, name);\n    }\n  }\n};\n\nconst rehydrateSheetFromTag = (sheet: Sheet, style: HTMLStyleElement) => {\n  const parts = (style.textContent ?? '').split(SPLITTER);\n  const rules: string[] = [];\n\n  for (let i = 0, l = parts.length; i < l; i++) {\n    const part = parts[i].trim();\n    if (!part) continue;\n\n    const marker = part.match(MARKER_RE);\n\n    if (marker) {\n      const group = parseInt(marker[1], 10) | 0;\n      const id = marker[2];\n\n      if (group !== 0) {\n        // Rehydrate componentId to group index mapping\n        setGroupForId(id, group);\n        // Rehydrate names and rules\n        // looks like: data-styled.g11[id=\"idA\"]{content:\"nameA,\"}\n        rehydrateNamesFromContent(sheet, id, marker[3]);\n        sheet.getTag().insertRules(group, rules);\n      }\n\n      rules.length = 0;\n    } else {\n      rules.push(part);\n    }\n  }\n};\n\nexport const rehydrateSheet = (sheet: Sheet) => {\n  const nodes = document.querySelectorAll(SELECTOR);\n\n  for (let i = 0, l = nodes.length; i < l; i++) {\n    const node = nodes[i] as any as HTMLStyleElement;\n    if (node && node.getAttribute(SC_ATTR) !== SC_ATTR_ACTIVE) {\n      rehydrateSheetFromTag(sheet, node);\n\n      if (node.parentNode) {\n        node.parentNode.removeChild(node);\n      }\n    }\n  }\n};\n", "declare let __webpack_nonce__: string;\n\nexport default function getNonce() {\n  return typeof __webpack_nonce__ !== 'undefined' ? __webpack_nonce__ : null;\n}\n", "import { SC_ATTR, SC_ATTR_ACTIVE, SC_ATTR_VERSION, SC_VERSION } from '../constants';\nimport { InsertionTarget } from '../types';\nimport styledError from '../utils/error';\nimport getNonce from '../utils/nonce';\n\n/** Find last style element if any inside target */\nconst findLastStyleTag = (target: InsertionTarget): void | HTMLStyleElement => {\n  const arr = Array.from(target.querySelectorAll<HTMLStyleElement>(`style[${SC_ATTR}]`));\n\n  return arr[arr.length - 1];\n};\n\n/** Create a style element inside `target` or <head> after the last */\nexport const makeStyleTag = (target?: InsertionTarget | undefined): HTMLStyleElement => {\n  const head = document.head;\n  const parent = target || head;\n  const style = document.createElement('style');\n  const prevStyle = findLastStyleTag(parent);\n  const nextSibling = prevStyle !== undefined ? prevStyle.nextSibling : null;\n\n  style.setAttribute(SC_ATTR, SC_ATTR_ACTIVE);\n  style.setAttribute(SC_ATTR_VERSION, SC_VERSION);\n\n  const nonce = getNonce();\n\n  if (nonce) style.setAttribute('nonce', nonce);\n\n  parent.insertBefore(style, nextSibling);\n\n  return style;\n};\n\n/** Get the CSSStyleSheet instance for a given style element */\nexport const getSheet = (tag: HTMLStyleElement): CSSStyleSheet => {\n  if (tag.sheet) {\n    return tag.sheet as any as CSSStyleSheet;\n  }\n\n  // Avoid Firefox quirk where the style element might not have a sheet property\n  const { styleSheets } = document;\n  for (let i = 0, l = styleSheets.length; i < l; i++) {\n    const sheet = styleSheets[i];\n    if (sheet.ownerNode === tag) {\n      return sheet as any as CSSStyleSheet;\n    }\n  }\n\n  throw styledError(17);\n};\n", "import { InsertionTarget } from '../types';\nimport { getSheet, makeStyleTag } from './dom';\nimport { SheetOptions, Tag } from './types';\n\n/** Create a CSSStyleSheet-like tag depending on the environment */\nexport const makeTag = ({ isServer, useCSSOMInjection, target }: SheetOptions) => {\n  if (isServer) {\n    return new VirtualTag(target);\n  } else if (useCSSOMInjection) {\n    return new CSSOMTag(target);\n  } else {\n    return new TextTag(target);\n  }\n};\n\nexport const CSSOMTag = class CSSOMTag implements Tag {\n  element: HTMLStyleElement;\n\n  sheet: CSSStyleSheet;\n\n  length: number;\n\n  constructor(target?: InsertionTarget | undefined) {\n    this.element = makeStyleTag(target);\n\n    // Avoid Edge bug where empty style elements don't create sheets\n    this.element.appendChild(document.createTextNode(''));\n\n    this.sheet = getSheet(this.element);\n    this.length = 0;\n  }\n\n  insertRule(index: number, rule: string): boolean {\n    try {\n      this.sheet.insertRule(rule, index);\n      this.length++;\n      return true;\n    } catch (_error) {\n      return false;\n    }\n  }\n\n  deleteRule(index: number): void {\n    this.sheet.deleteRule(index);\n    this.length--;\n  }\n\n  getRule(index: number): string {\n    const rule = this.sheet.cssRules[index];\n\n    // Avoid IE11 quirk where cssText is inaccessible on some invalid rules\n    if (rule && rule.cssText) {\n      return rule.cssText;\n    } else {\n      return '';\n    }\n  }\n};\n\n/** A Tag that emulates the CSSStyleSheet API but uses text nodes */\nexport const TextTag = class TextTag implements Tag {\n  element: HTMLStyleElement;\n  nodes: NodeListOf<Node>;\n  length: number;\n\n  constructor(target?: InsertionTarget | undefined) {\n    this.element = makeStyleTag(target);\n    this.nodes = this.element.childNodes;\n    this.length = 0;\n  }\n\n  insertRule(index: number, rule: string) {\n    if (index <= this.length && index >= 0) {\n      const node = document.createTextNode(rule);\n      const refNode = this.nodes[index];\n      this.element.insertBefore(node, refNode || null);\n      this.length++;\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  deleteRule(index: number) {\n    this.element.removeChild(this.nodes[index]);\n    this.length--;\n  }\n\n  getRule(index: number) {\n    if (index < this.length) {\n      return this.nodes[index].textContent as string;\n    } else {\n      return '';\n    }\n  }\n};\n\n/** A completely virtual (server-side) Tag that doesn't manipulate the DOM */\nexport const VirtualTag = class VirtualTag implements Tag {\n  rules: string[];\n\n  length: number;\n\n  constructor(_target?: InsertionTarget | undefined) {\n    this.rules = [];\n    this.length = 0;\n  }\n\n  insertRule(index: number, rule: string) {\n    if (index <= this.length) {\n      this.rules.splice(index, 0, rule);\n      this.length++;\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  deleteRule(index: number) {\n    this.rules.splice(index, 1);\n    this.length--;\n  }\n\n  getRule(index: number) {\n    if (index < this.length) {\n      return this.rules[index];\n    } else {\n      return '';\n    }\n  }\n};\n", "import { DISABLE_SPEEDY, IS_BROWSER } from '../constants';\nimport { InsertionTarget } from '../types';\nimport { EMPTY_OBJECT } from '../utils/empties';\nimport { setToString } from '../utils/setToString';\nimport { makeGroupedTag } from './GroupedTag';\nimport { getGroupForId } from './GroupIDAllocator';\nimport { outputSheet, rehydrateSheet } from './Rehydration';\nimport { makeTag } from './Tag';\nimport { GroupedTag, Sheet, SheetOptions } from './types';\n\nlet SHOULD_REHYDRATE = IS_BROWSER;\n\ntype SheetConstructorArgs = {\n  isServer?: boolean;\n  useCSSOMInjection?: boolean;\n  target?: InsertionTarget | undefined;\n};\n\ntype GlobalStylesAllocationMap = {\n  [key: string]: number;\n};\ntype NamesAllocationMap = Map<string, Set<string>>;\n\nconst defaultOptions: SheetOptions = {\n  isServer: !IS_BROWSER,\n  useCSSOMInjection: !DISABLE_SPEEDY,\n};\n\n/** Contains the main stylesheet logic for stringification and caching */\nexport default class StyleSheet implements Sheet {\n  gs: GlobalStylesAllocationMap;\n  names: NamesAllocationMap;\n  options: SheetOptions;\n  server: boolean;\n  tag?: GroupedTag | undefined;\n\n  /** Register a group ID to give it an index */\n  static registerId(id: string): number {\n    return getGroupForId(id);\n  }\n\n  constructor(\n    options: SheetConstructorArgs = EMPTY_OBJECT as Object,\n    globalStyles: GlobalStylesAllocationMap = {},\n    names?: NamesAllocationMap | undefined\n  ) {\n    this.options = {\n      ...defaultOptions,\n      ...options,\n    };\n\n    this.gs = globalStyles;\n    this.names = new Map(names as NamesAllocationMap);\n    this.server = !!options.isServer;\n\n    // We rehydrate only once and use the sheet that is created first\n    if (!this.server && IS_BROWSER && SHOULD_REHYDRATE) {\n      SHOULD_REHYDRATE = false;\n      rehydrateSheet(this);\n    }\n\n    setToString(this, () => outputSheet(this));\n  }\n\n  rehydrate(): void {\n    if (!this.server && IS_BROWSER) {\n      rehydrateSheet(this);\n    }\n  }\n\n  reconstructWithOptions(options: SheetConstructorArgs, withNames = true) {\n    return new StyleSheet(\n      { ...this.options, ...options },\n      this.gs,\n      (withNames && this.names) || undefined\n    );\n  }\n\n  allocateGSInstance(id: string) {\n    return (this.gs[id] = (this.gs[id] || 0) + 1);\n  }\n\n  /** Lazily initialises a GroupedTag for when it's actually needed */\n  getTag() {\n    return this.tag || (this.tag = makeGroupedTag(makeTag(this.options)));\n  }\n\n  /** Check whether a name is known for caching */\n  hasNameForId(id: string, name: string): boolean {\n    return this.names.has(id) && (this.names.get(id) as any).has(name);\n  }\n\n  /** Mark a group's name as known for caching */\n  registerName(id: string, name: string) {\n    getGroupForId(id);\n\n    if (!this.names.has(id)) {\n      const groupNames = new Set<string>();\n      groupNames.add(name);\n      this.names.set(id, groupNames);\n    } else {\n      (this.names.get(id) as any).add(name);\n    }\n  }\n\n  /** Insert new rules which also marks the name as known */\n  insertRules(id: string, name: string, rules: string | string[]) {\n    this.registerName(id, name);\n    this.getTag().insertRules(getGroupForId(id), rules);\n  }\n\n  /** Clears all cached names for a given group ID */\n  clearNames(id: string) {\n    if (this.names.has(id)) {\n      (this.names.get(id) as any).clear();\n    }\n  }\n\n  /** Clears all rules for a given group ID */\n  clearRules(id: string) {\n    this.getTag().clearGroup(getGroupForId(id));\n    this.clearNames(id);\n  }\n\n  /** Clears the entire tag which deletes all rules but not its names */\n  clearTag() {\n    // NOTE: This does not clear the names, since it's only used during SSR\n    // so that we can continuously output only new rules\n    this.tag = undefined;\n  }\n}\n", "import * as stylis from 'stylis';\nimport { Stringifier } from '../types';\nimport { EMPTY_ARRAY, EMPTY_OBJECT } from './empties';\nimport throwStyledError from './error';\nimport { SEED, phash } from './hash';\n\nconst AMP_REGEX = /&/g;\nconst COMMENT_REGEX = /^\\s*\\/\\/.*$/gm;\n\nexport type ICreateStylisInstance = {\n  options?: { namespace?: string | undefined; prefix?: boolean | undefined } | undefined;\n  plugins?: stylis.Middleware[] | undefined;\n};\n\n/**\n * Takes an element and recurses through it's rules added the namespace to the start of each selector.\n * Takes into account media queries by recursing through child rules if they are present.\n */\nfunction recursivelySetNamepace(compiled: stylis.Element[], namespace: String): stylis.Element[] {\n  return compiled.map(rule => {\n    if (rule.type === 'rule') {\n      // add the namespace to the start\n      rule.value = `${namespace} ${rule.value}`;\n      // add the namespace after each comma for subsequent selectors.\n      rule.value = rule.value.replaceAll(',', `,${namespace} `);\n      rule.props = (rule.props as string[]).map(prop => {\n        return `${namespace} ${prop}`;\n      });\n    }\n\n    if (Array.isArray(rule.children) && rule.type !== '@keyframes') {\n      rule.children = recursivelySetNamepace(rule.children, namespace);\n    }\n    return rule;\n  });\n}\n\nexport default function createStylisInstance(\n  {\n    options = EMPTY_OBJECT as object,\n    plugins = EMPTY_ARRAY as unknown as stylis.Middleware[],\n  }: ICreateStylisInstance = EMPTY_OBJECT as object\n) {\n  let _componentId: string;\n  let _selector: string;\n  let _selectorRegexp: RegExp;\n\n  const selfReferenceReplacer = (match: string, offset: number, string: string) => {\n    if (\n      /**\n       * We only want to refer to the static class directly if the selector is part of a\n       * self-reference selector `& + & { color: red; }`\n       */\n      string.startsWith(_selector) &&\n      string.endsWith(_selector) &&\n      string.replaceAll(_selector, '').length > 0\n    ) {\n      return `.${_componentId}`;\n    }\n\n    return match;\n  };\n\n  /**\n   * When writing a style like\n   *\n   * & + & {\n   *   color: red;\n   * }\n   *\n   * The second ampersand should be a reference to the static component class. stylis\n   * has no knowledge of static class so we have to intelligently replace the base selector.\n   *\n   * https://github.com/thysultan/stylis.js/tree/v4.0.2#abstract-syntax-structure\n   */\n  const selfReferenceReplacementPlugin: stylis.Middleware = element => {\n    if (element.type === stylis.RULESET && element.value.includes('&')) {\n      (element.props as string[])[0] = element.props[0]\n        // catch any hanging references that stylis missed\n        .replace(AMP_REGEX, _selector)\n        .replace(_selectorRegexp, selfReferenceReplacer);\n    }\n  };\n\n  const middlewares = plugins.slice();\n\n  middlewares.push(selfReferenceReplacementPlugin);\n\n  /**\n   * Enables automatic vendor-prefixing for styles.\n   */\n  if (options.prefix) {\n    middlewares.push(stylis.prefixer);\n  }\n\n  middlewares.push(stylis.stringify);\n\n  const stringifyRules: Stringifier = (\n    css: string,\n    selector = '',\n    /**\n     * This \"prefix\" referes to a _selector_ prefix.\n     */\n    prefix = '',\n    componentId = '&'\n  ) => {\n    // stylis has no concept of state to be passed to plugins\n    // but since JS is single-threaded, we can rely on that to ensure\n    // these properties stay in sync with the current stylis run\n    _componentId = componentId;\n    _selector = selector;\n    _selectorRegexp = new RegExp(`\\\\${_selector}\\\\b`, 'g');\n\n    const flatCSS = css.replace(COMMENT_REGEX, '');\n    let compiled = stylis.compile(\n      prefix || selector ? `${prefix} ${selector} { ${flatCSS} }` : flatCSS\n    );\n\n    if (options.namespace) {\n      compiled = recursivelySetNamepace(compiled, options.namespace);\n    }\n\n    const stack: string[] = [];\n\n    stylis.serialize(\n      compiled,\n      stylis.middleware(middlewares.concat(stylis.rulesheet(value => stack.push(value))))\n    );\n\n    return stack;\n  };\n\n  stringifyRules.hash = plugins.length\n    ? plugins\n        .reduce((acc, plugin) => {\n          if (!plugin.name) {\n            throwStyledError(15);\n          }\n\n          return phash(acc, plugin.name);\n        }, SEED)\n        .toString()\n    : '';\n\n  return stringifyRules;\n}\n", "import React, { useContext, useEffect, useMemo, useState } from 'react';\nimport shallowequal from 'shallowequal';\nimport type stylis from 'stylis';\nimport StyleSheet from '../sheet';\nimport { InsertionTarget, ShouldForwardProp, Stringifier } from '../types';\nimport createStylisInstance from '../utils/stylis';\n\nexport const mainSheet: StyleSheet = new StyleSheet();\nexport const mainStylis: Stringifier = createStylisInstance();\n\nexport type IStyleSheetContext = {\n  shouldForwardProp?: ShouldForwardProp<'web'> | undefined;\n  styleSheet: StyleSheet;\n  stylis: Stringifier;\n};\n\nexport const StyleSheetContext = React.createContext<IStyleSheetContext>({\n  shouldForwardProp: undefined,\n  styleSheet: mainSheet,\n  stylis: mainStylis,\n});\n\nexport const StyleSheetConsumer = StyleSheetContext.Consumer;\n\nexport type IStylisContext = Stringifier | void;\nexport const StylisContext = React.createContext<IStylisContext>(undefined);\nexport const StylisConsumer = StylisContext.Consumer;\n\nexport function useStyleSheetContext() {\n  return useContext(StyleSheetContext);\n}\n\nexport type IStyleSheetManager = React.PropsWithChildren<{\n  /**\n   * If desired, you can pass this prop to disable \"speedy\" insertion mode, which\n   * uses the browser [CSSOM APIs](https://developer.mozilla.org/en-US/docs/Web/API/CSSStyleSheet).\n   * When disabled, rules are inserted as simple text into style blocks.\n   */\n  disableCSSOMInjection?: undefined | boolean;\n  /**\n   * If you are working exclusively with modern browsers, vendor prefixes can often be omitted\n   * to reduce the weight of CSS on the page.\n   */\n  enableVendorPrefixes?: undefined | boolean;\n  /**\n   * Provide an optional selector to be prepended to all generated style rules.\n   */\n  namespace?: undefined | string;\n  /**\n   * Create and provide your own `StyleSheet` if necessary for advanced SSR scenarios.\n   */\n  sheet?: undefined | StyleSheet;\n  /**\n   * Starting in v6, styled-components no longer does its own prop validation\n   * and recommends use of transient props \"$prop\" to pass style-only props to\n   * components. If for some reason you are not able to use transient props, a\n   * prop validation function can be provided via `StyleSheetManager`, such as\n   * `@emotion/is-prop-valid`.\n   *\n   * When the return value is `true`, props will be forwarded to the DOM/underlying\n   * component. If return value is `false`, the prop will be discarded after styles\n   * are calculated.\n   *\n   * Manually composing `styled.{element}.withConfig({shouldForwardProp})` will\n   * override this default.\n   */\n  shouldForwardProp?: undefined | IStyleSheetContext['shouldForwardProp'];\n  /**\n   * An array of plugins to be run by stylis (style processor) during compilation.\n   * Check out [what's available on npm*](https://www.npmjs.com/search?q=keywords%3Astylis).\n   *\n   * \\* The plugin(s) must be compatible with stylis v4 or above.\n   */\n  stylisPlugins?: undefined | stylis.Middleware[];\n  /**\n   * Provide an alternate DOM node to host generated styles; useful for iframes.\n   */\n  target?: undefined | InsertionTarget;\n}>;\n\nexport function StyleSheetManager(props: IStyleSheetManager): React.JSX.Element {\n  const [plugins, setPlugins] = useState(props.stylisPlugins);\n  const { styleSheet } = useStyleSheetContext();\n\n  const resolvedStyleSheet = useMemo(() => {\n    let sheet = styleSheet;\n\n    if (props.sheet) {\n      sheet = props.sheet;\n    } else if (props.target) {\n      sheet = sheet.reconstructWithOptions({ target: props.target }, false);\n    }\n\n    if (props.disableCSSOMInjection) {\n      sheet = sheet.reconstructWithOptions({ useCSSOMInjection: false });\n    }\n\n    return sheet;\n  }, [props.disableCSSOMInjection, props.sheet, props.target, styleSheet]);\n\n  const stylis = useMemo(\n    () =>\n      createStylisInstance({\n        options: { namespace: props.namespace, prefix: props.enableVendorPrefixes },\n        plugins,\n      }),\n    [props.enableVendorPrefixes, props.namespace, plugins]\n  );\n\n  useEffect(() => {\n    if (!shallowequal(plugins, props.stylisPlugins)) setPlugins(props.stylisPlugins);\n  }, [props.stylisPlugins]);\n\n  const styleSheetContextValue = useMemo(\n    () => ({\n      shouldForwardProp: props.shouldForwardProp,\n      styleSheet: resolvedStyleSheet,\n      stylis,\n    }),\n    [props.shouldForwardProp, resolvedStyleSheet, stylis]\n  );\n\n  return (\n    <StyleSheetContext.Provider value={styleSheetContextValue}>\n      <StylisContext.Provider value={stylis}>{props.children}</StylisContext.Provider>\n    </StyleSheetContext.Provider>\n  );\n}\n", "import StyleSheet from '../sheet';\nimport { Keyframes as KeyframesType, Stringifier } from '../types';\nimport styledError from '../utils/error';\nimport { setToString } from '../utils/setToString';\nimport { mainStylis } from './StyleSheetManager';\n\nexport default class Keyframes implements KeyframesType {\n  id: string;\n  name: string;\n  rules: string;\n\n  constructor(name: string, rules: string) {\n    this.name = name;\n    this.id = `sc-keyframes-${name}`;\n    this.rules = rules;\n\n    setToString(this, () => {\n      throw styledError(12, String(this.name));\n    });\n  }\n\n  inject = (styleSheet: StyleSheet, stylisInstance: Stringifier = mainStylis): void => {\n    const resolvedName = this.name + stylisInstance.hash;\n\n    if (!styleSheet.hasNameForId(this.id, resolvedName)) {\n      styleSheet.insertRules(\n        this.id,\n        resolvedName,\n        stylisInstance(this.rules, resolvedName, '@keyframes')\n      );\n    }\n  };\n\n  getName(stylisInstance: Stringifier = mainStylis): string {\n    return this.name + stylisInstance.hash;\n  }\n}\n", "const isUpper = (c: string) => c >= 'A' && c <= 'Z';\n\n/**\n * Hyphenates a camelcased CSS property name, for example:\n *\n *   > hyphenateStyleName('backgroundColor')\n *   < \"background-color\"\n *   > hyphenateStyleName('MozTransition')\n *   < \"-moz-transition\"\n *   > hyphenateStyleName('msTransition')\n *   < \"-ms-transition\"\n *\n * As Modernizr suggests (http://modernizr.com/docs/#prefixed), an `ms` prefix\n * is converted to `-ms-`.\n */\nexport default function hyphenateStyleName(string: string): string {\n  let output = '';\n\n  for (let i = 0; i < string.length; i++) {\n    const c = string[i];\n    // Check for CSS variable prefix\n    if (i === 1 && c === '-' && string[0] === '-') {\n      return string;\n    }\n\n    if (isUpper(c)) {\n      output += '-' + c.toLowerCase();\n    } else {\n      output += c;\n    }\n  }\n\n  return output.startsWith('ms-') ? '-' + output : output;\n}\n", "import Keyframes from '../models/Keyframes';\nimport StyleSheet from '../sheet';\nimport {\n  AnyComponent,\n  Dict,\n  ExecutionContext,\n  Interpolation,\n  IStyledComponent,\n  RuleSet,\n  Stringifier,\n  StyledObject,\n} from '../types';\nimport addUnitIfNeeded from './addUnitIfNeeded';\nimport { EMPTY_ARRAY } from './empties';\nimport getComponentName from './getComponentName';\nimport hyphenate from './hyphenateStyleName';\nimport isFunction from './isFunction';\nimport isPlainObject from './isPlainObject';\nimport isStatelessFunction from './isStatelessFunction';\nimport isStyledComponent from './isStyledComponent';\n\n/**\n * It's falsish not falsy because 0 is allowed.\n */\nconst isFalsish = (chunk: any): chunk is undefined | null | false | '' =>\n  chunk === undefined || chunk === null || chunk === false || chunk === '';\n\nexport const objToCssArray = (obj: Dict<any>): string[] => {\n  const rules = [];\n\n  for (const key in obj) {\n    const val = obj[key];\n    if (!obj.hasOwnProperty(key) || isFalsish(val)) continue;\n\n    // @ts-expect-error Property 'isCss' does not exist on type 'any[]'\n    if ((Array.isArray(val) && val.isCss) || isFunction(val)) {\n      rules.push(`${hyphenate(key)}:`, val, ';');\n    } else if (isPlainObject(val)) {\n      rules.push(`${key} {`, ...objToCssArray(val), '}');\n    } else {\n      rules.push(`${hyphenate(key)}: ${addUnitIfNeeded(key, val)};`);\n    }\n  }\n\n  return rules;\n};\n\nexport default function flatten<Props extends object>(\n  chunk: Interpolation<object>,\n  executionContext?: (ExecutionContext & Props) | undefined,\n  styleSheet?: StyleSheet | undefined,\n  stylisInstance?: Stringifier | undefined\n): RuleSet<Props> {\n  if (isFalsish(chunk)) {\n    return [];\n  }\n\n  /* Handle other components */\n  if (isStyledComponent(chunk)) {\n    return [`.${(chunk as unknown as IStyledComponent<'web', any>).styledComponentId}`];\n  }\n\n  /* Either execute or defer the function */\n  if (isFunction(chunk)) {\n    if (isStatelessFunction(chunk) && executionContext) {\n      const result = chunk(executionContext);\n\n      if (\n        process.env.NODE_ENV !== 'production' &&\n        typeof result === 'object' &&\n        !Array.isArray(result) &&\n        !(result instanceof Keyframes) &&\n        !isPlainObject(result) &&\n        result !== null\n      ) {\n        console.error(\n          `${getComponentName(\n            chunk as AnyComponent\n          )} is not a styled component and cannot be referred to via component selector. See https://www.styled-components.com/docs/advanced#referring-to-other-components for more details.`\n        );\n      }\n\n      return flatten<Props>(result, executionContext, styleSheet, stylisInstance);\n    } else {\n      return [chunk as unknown as IStyledComponent<'web'>];\n    }\n  }\n\n  if (chunk instanceof Keyframes) {\n    if (styleSheet) {\n      chunk.inject(styleSheet, stylisInstance);\n      return [chunk.getName(stylisInstance)];\n    } else {\n      return [chunk];\n    }\n  }\n\n  /* Handle objects */\n  if (isPlainObject(chunk)) {\n    return objToCssArray(chunk as StyledObject<Props>);\n  }\n\n  if (!Array.isArray(chunk)) {\n    return [chunk.toString()];\n  }\n\n  return flatMap(chunk, chunklet =>\n    flatten<Props>(chunklet, executionContext, styleSheet, stylisInstance)\n  );\n}\n\nfunction flatMap<T, U>(array: T[], transform: (value: T, index: number, array: T[]) => U[]): U[] {\n  return Array.prototype.concat.apply(EMPTY_ARRAY, array.map(transform));\n}\n", "import unitless from '@emotion/unitless';\n\n// Taken from https://github.com/facebook/react/blob/b87aabdfe1b7461e7331abb3601d9e6bb27544bc/packages/react-dom/src/shared/dangerousStyleValue.js\nexport default function addUnitIfNeeded(name: string, value: any) {\n  // https://github.com/amilajack/eslint-plugin-flowtype-errors/issues/133\n  if (value == null || typeof value === 'boolean' || value === '') {\n    return '';\n  }\n\n  if (typeof value === 'number' && value !== 0 && !(name in unitless) && !name.startsWith('--')) {\n    return `${value}px`; // Presumes implicit 'px' suffix for unitless numbers except for CSS variables\n  }\n\n  return String(value).trim();\n}\n", "import isFunction from './isFunction';\n\nexport default function isStatelessFunction(test: any): test is Function {\n  return isFunction(test) && !(test.prototype && test.prototype.isReactComponent);\n}\n", "import { RuleSet } from '../types';\nimport isFunction from './isFunction';\nimport isStyledComponent from './isStyledComponent';\n\nexport default function isStaticRules<Props extends object>(rules: RuleSet<Props>) {\n  for (let i = 0; i < rules.length; i += 1) {\n    const rule = rules[i];\n\n    if (isFunction(rule) && !isStyledComponent(rule)) {\n      // functions are allowed to be static if they're just being\n      // used to get the classname of a nested styled component\n      return false;\n    }\n  }\n\n  return true;\n}\n", "import { SC_VERSION } from '../constants';\nimport StyleSheet from '../sheet';\nimport { ExecutionContext, RuleSet, Stringifier } from '../types';\nimport flatten from '../utils/flatten';\nimport generateName from '../utils/generateAlphabeticName';\nimport { hash, phash } from '../utils/hash';\nimport isStaticRules from '../utils/isStaticRules';\nimport { joinStringArray, joinStrings } from '../utils/joinStrings';\n\nconst SEED = hash(SC_VERSION);\n\n/**\n * ComponentStyle is all the CSS-specific stuff, not the React-specific stuff.\n */\nexport default class ComponentStyle {\n  baseHash: number;\n  baseStyle: ComponentStyle | null | undefined;\n  componentId: string;\n  isStatic: boolean;\n  rules: RuleSet<any>;\n  staticRulesId: string;\n\n  constructor(rules: RuleSet<any>, componentId: string, baseStyle?: ComponentStyle | undefined) {\n    this.rules = rules;\n    this.staticRulesId = '';\n    this.isStatic =\n      process.env.NODE_ENV === 'production' &&\n      (baseStyle === undefined || baseStyle.isStatic) &&\n      isStaticRules(rules);\n    this.componentId = componentId;\n    this.baseHash = phash(SEED, componentId);\n    this.baseStyle = baseStyle;\n\n    // NOTE: This registers the componentId, which ensures a consistent order\n    // for this component's styles compared to others\n    StyleSheet.registerId(componentId);\n  }\n\n  generateAndInjectStyles(\n    executionContext: ExecutionContext,\n    styleSheet: StyleSheet,\n    stylis: Stringifier\n  ): string {\n    let names = this.baseStyle\n      ? this.baseStyle.generateAndInjectStyles(executionContext, styleSheet, stylis)\n      : '';\n\n    // force dynamic classnames if user-supplied stylis plugins are in use\n    if (this.isStatic && !stylis.hash) {\n      if (this.staticRulesId && styleSheet.hasNameForId(this.componentId, this.staticRulesId)) {\n        names = joinStrings(names, this.staticRulesId);\n      } else {\n        const cssStatic = joinStringArray(\n          flatten(this.rules, executionContext, styleSheet, stylis) as string[]\n        );\n        const name = generateName(phash(this.baseHash, cssStatic) >>> 0);\n\n        if (!styleSheet.hasNameForId(this.componentId, name)) {\n          const cssStaticFormatted = stylis(cssStatic, `.${name}`, undefined, this.componentId);\n          styleSheet.insertRules(this.componentId, name, cssStaticFormatted);\n        }\n\n        names = joinStrings(names, name);\n        this.staticRulesId = name;\n      }\n    } else {\n      let dynamicHash = phash(this.baseHash, stylis.hash);\n      let css = '';\n\n      for (let i = 0; i < this.rules.length; i++) {\n        const partRule = this.rules[i];\n\n        if (typeof partRule === 'string') {\n          css += partRule;\n\n          if (process.env.NODE_ENV !== 'production') dynamicHash = phash(dynamicHash, partRule);\n        } else if (partRule) {\n          const partString = joinStringArray(\n            flatten(partRule, executionContext, styleSheet, stylis) as string[]\n          );\n          // The same value can switch positions in the array, so we include \"i\" in the hash.\n          dynamicHash = phash(dynamicHash, partString + i);\n          css += partString;\n        }\n      }\n\n      if (css) {\n        const name = generateName(dynamicHash >>> 0);\n\n        if (!styleSheet.hasNameForId(this.componentId, name)) {\n          styleSheet.insertRules(\n            this.componentId,\n            name,\n            stylis(css, `.${name}`, undefined, this.componentId)\n          );\n        }\n\n        names = joinStrings(names, name);\n      }\n    }\n\n    return names;\n  }\n}\n", "import React, { useContext, useMemo } from 'react';\nimport styledError from '../utils/error';\nimport isFunction from '../utils/isFunction';\n\n// Helper type for the `DefaultTheme` interface that enforces an object type & exclusively allows\n// for typed keys.\ntype DefaultThemeAsObject<T = object> = Record<keyof T, any>;\n\n/**\n * Override DefaultTheme to get accurate typings for your project.\n *\n * ```\n * // create styled-components.d.ts in your project source\n * // if it isn't being picked up, check tsconfig compilerOptions.types\n * import type { CSSProp } from \"styled-components\";\n * import Theme from './theme';\n *\n * type ThemeType = typeof Theme;\n *\n * declare module \"styled-components\" {\n *  export interface DefaultTheme extends ThemeType {}\n * }\n *\n * declare module \"react\" {\n *  interface DOMAttributes<T> {\n *    css?: CSSProp;\n *  }\n * }\n * ```\n */\nexport interface DefaultTheme extends DefaultThemeAsObject {}\n\ntype ThemeFn = (outerTheme?: DefaultTheme | undefined) => DefaultTheme;\ntype ThemeArgument = DefaultTheme | ThemeFn;\n\ntype Props = {\n  children?: React.ReactNode;\n  theme: ThemeArgument;\n};\n\nexport const ThemeContext = React.createContext<DefaultTheme | undefined>(undefined);\n\nexport const ThemeConsumer = ThemeContext.Consumer;\n\nfunction mergeTheme(theme: ThemeArgument, outerTheme?: DefaultTheme | undefined): DefaultTheme {\n  if (!theme) {\n    throw styledError(14);\n  }\n\n  if (isFunction(theme)) {\n    const themeFn = theme as ThemeFn;\n    const mergedTheme = themeFn(outerTheme);\n\n    if (\n      process.env.NODE_ENV !== 'production' &&\n      (mergedTheme === null || Array.isArray(mergedTheme) || typeof mergedTheme !== 'object')\n    ) {\n      throw styledError(7);\n    }\n\n    return mergedTheme;\n  }\n\n  if (Array.isArray(theme) || typeof theme !== 'object') {\n    throw styledError(8);\n  }\n\n  return outerTheme ? { ...outerTheme, ...theme } : theme;\n}\n\n/**\n * Returns the current theme (as provided by the closest ancestor `ThemeProvider`.)\n *\n * If no `ThemeProvider` is found, the function will error. If you need access to the theme in an\n * uncertain composition scenario, `React.useContext(ThemeContext)` will not emit an error if there\n * is no `ThemeProvider` ancestor.\n */\nexport function useTheme(): DefaultTheme {\n  const theme = useContext(ThemeContext);\n\n  if (!theme) {\n    throw styledError(18);\n  }\n\n  return theme;\n}\n\n/**\n * Provide a theme to an entire react component tree via context\n */\nexport default function ThemeProvider(props: Props): React.JSX.Element | null {\n  const outerTheme = React.useContext(ThemeContext);\n  const themeContext = useMemo(\n    () => mergeTheme(props.theme, outerTheme),\n    [props.theme, outerTheme]\n  );\n\n  if (!props.children) {\n    return null;\n  }\n\n  return <ThemeContext.Provider value={themeContext}>{props.children}</ThemeContext.Provider>;\n}\n", "import isPropValid from '@emotion/is-prop-valid';\nimport React, { createElement, Ref, useDebugValue } from 'react';\nimport { SC_VERSION } from '../constants';\nimport type {\n  AnyComponent,\n  Attrs,\n  BaseObject,\n  Dict,\n  ExecutionContext,\n  ExecutionProps,\n  IStyledComponent,\n  IStyledComponentFactory,\n  IStyledStatics,\n  OmitNever,\n  RuleSet,\n  StyledOptions,\n  WebTarget,\n} from '../types';\nimport { checkDynamicCreation } from '../utils/checkDynamicCreation';\nimport createWarnTooManyClasses from '../utils/createWarnTooManyClasses';\nimport determineTheme from '../utils/determineTheme';\nimport domElements from '../utils/domElements';\nimport { EMPTY_ARRAY, EMPTY_OBJECT } from '../utils/empties';\nimport escape from '../utils/escape';\nimport generateComponentId from '../utils/generateComponentId';\nimport generateDisplayName from '../utils/generateDisplayName';\nimport hoist from '../utils/hoist';\nimport isFunction from '../utils/isFunction';\nimport isStyledComponent from '../utils/isStyledComponent';\nimport isTag from '../utils/isTag';\nimport { joinStrings } from '../utils/joinStrings';\nimport merge from '../utils/mixinDeep';\nimport { setToString } from '../utils/setToString';\nimport ComponentStyle from './ComponentStyle';\nimport { useStyleSheetContext } from './StyleSheetManager';\nimport { DefaultTheme, ThemeContext } from './ThemeProvider';\n\nconst identifiers: { [key: string]: number } = {};\n\n/* We depend on components having unique IDs */\nfunction generateId(\n  displayName?: string | undefined,\n  parentComponentId?: string | undefined\n): string {\n  const name = typeof displayName !== 'string' ? 'sc' : escape(displayName);\n  // Ensure that no displayName can lead to duplicate componentIds\n  identifiers[name] = (identifiers[name] || 0) + 1;\n\n  const componentId = `${name}-${generateComponentId(\n    // SC_VERSION gives us isolation between multiple runtimes on the page at once\n    // this is improved further with use of the babel plugin \"namespace\" feature\n    SC_VERSION + name + identifiers[name]\n  )}`;\n\n  return parentComponentId ? `${parentComponentId}-${componentId}` : componentId;\n}\n\nfunction useInjectedStyle<T extends ExecutionContext>(\n  componentStyle: ComponentStyle,\n  resolvedAttrs: T\n) {\n  const ssc = useStyleSheetContext();\n\n  const className = componentStyle.generateAndInjectStyles(\n    resolvedAttrs,\n    ssc.styleSheet,\n    ssc.stylis\n  );\n\n  if (process.env.NODE_ENV !== 'production') useDebugValue(className);\n\n  return className;\n}\n\nfunction resolveContext<Props extends object>(\n  attrs: Attrs<React.HTMLAttributes<Element> & Props>[],\n  props: React.HTMLAttributes<Element> & ExecutionProps & Props,\n  theme: DefaultTheme\n) {\n  const context: React.HTMLAttributes<Element> &\n    ExecutionContext &\n    Props & { [key: string]: any; class?: string; ref?: React.Ref<any> } = {\n    ...props,\n    // unset, add `props.className` back at the end so props always \"wins\"\n    className: undefined,\n    theme,\n  };\n  let attrDef;\n\n  for (let i = 0; i < attrs.length; i += 1) {\n    attrDef = attrs[i];\n    const resolvedAttrDef = isFunction(attrDef) ? attrDef(context) : attrDef;\n\n    for (const key in resolvedAttrDef) {\n      context[key as keyof typeof context] =\n        key === 'className'\n          ? joinStrings(context[key] as string | undefined, resolvedAttrDef[key] as string)\n          : key === 'style'\n            ? { ...context[key], ...resolvedAttrDef[key] }\n            : resolvedAttrDef[key as keyof typeof resolvedAttrDef];\n    }\n  }\n\n  if (props.className) {\n    context.className = joinStrings(context.className, props.className);\n  }\n\n  return context;\n}\n\nlet seenUnknownProps = new Set();\n\nfunction useStyledComponentImpl<Props extends object>(\n  forwardedComponent: IStyledComponent<'web', Props>,\n  props: ExecutionProps & Props,\n  forwardedRef: Ref<Element>\n) {\n  const {\n    attrs: componentAttrs,\n    componentStyle,\n    defaultProps,\n    foldedComponentIds,\n    styledComponentId,\n    target,\n  } = forwardedComponent;\n\n  const contextTheme = React.useContext(ThemeContext);\n  const ssc = useStyleSheetContext();\n  const shouldForwardProp = forwardedComponent.shouldForwardProp || ssc.shouldForwardProp;\n\n  if (process.env.NODE_ENV !== 'production') useDebugValue(styledComponentId);\n\n  // NOTE: the non-hooks version only subscribes to this when !componentStyle.isStatic,\n  // but that'd be against the rules-of-hooks. We could be naughty and do it anyway as it\n  // should be an immutable value, but behave for now.\n  const theme = determineTheme(props, contextTheme, defaultProps) || EMPTY_OBJECT;\n\n  const context = resolveContext<Props>(componentAttrs, props, theme);\n  const elementToBeCreated: WebTarget = context.as || target;\n  const propsForElement: Dict<any> = {};\n\n  for (const key in context) {\n    if (context[key] === undefined) {\n      // Omit undefined values from props passed to wrapped element.\n      // This enables using .attrs() to remove props, for example.\n    } else if (key[0] === '$' || key === 'as' || (key === 'theme' && context.theme === theme)) {\n      // Omit transient props and execution props.\n    } else if (key === 'forwardedAs') {\n      propsForElement.as = context.forwardedAs;\n    } else if (!shouldForwardProp || shouldForwardProp(key, elementToBeCreated)) {\n      propsForElement[key] = context[key];\n\n      if (\n        !shouldForwardProp &&\n        process.env.NODE_ENV === 'development' &&\n        !isPropValid(key) &&\n        !seenUnknownProps.has(key) &&\n        // Only warn on DOM Element.\n        domElements.has(elementToBeCreated as any)\n      ) {\n        seenUnknownProps.add(key);\n        console.warn(\n          `styled-components: it looks like an unknown prop \"${key}\" is being sent through to the DOM, which will likely trigger a React console error. If you would like automatic filtering of unknown props, you can opt-into that behavior via \\`<StyleSheetManager shouldForwardProp={...}>\\` (connect an API like \\`@emotion/is-prop-valid\\`) or consider using transient props (\\`$\\` prefix for automatic filtering.)`\n        );\n      }\n    }\n  }\n\n  const generatedClassName = useInjectedStyle(componentStyle, context);\n\n  if (process.env.NODE_ENV !== 'production' && forwardedComponent.warnTooManyClasses) {\n    forwardedComponent.warnTooManyClasses(generatedClassName);\n  }\n\n  let classString = joinStrings(foldedComponentIds, styledComponentId);\n  if (generatedClassName) {\n    classString += ' ' + generatedClassName;\n  }\n  if (context.className) {\n    classString += ' ' + context.className;\n  }\n\n  propsForElement[\n    // handle custom elements which React doesn't properly alias\n    isTag(elementToBeCreated) &&\n    !domElements.has(elementToBeCreated as Extract<typeof domElements, string>)\n      ? 'class'\n      : 'className'\n  ] = classString;\n\n  // forwardedRef is coming from React.forwardRef.\n  // But it might not exist. Since React 19 handles `ref` like a prop, it only define it if there is a value.\n  // We don't want to inject an empty ref.\n  if (forwardedRef) {\n    propsForElement.ref = forwardedRef;\n  }\n\n  return createElement(elementToBeCreated, propsForElement);\n}\n\nfunction createStyledComponent<\n  Target extends WebTarget,\n  OuterProps extends object,\n  Statics extends object = BaseObject,\n>(\n  target: Target,\n  options: StyledOptions<'web', OuterProps>,\n  rules: RuleSet<OuterProps>\n): ReturnType<IStyledComponentFactory<'web', Target, OuterProps, Statics>> {\n  const isTargetStyledComp = isStyledComponent(target);\n  const styledComponentTarget = target as IStyledComponent<'web', OuterProps>;\n  const isCompositeComponent = !isTag(target);\n\n  const {\n    attrs = EMPTY_ARRAY,\n    componentId = generateId(options.displayName, options.parentComponentId),\n    displayName = generateDisplayName(target),\n  } = options;\n\n  const styledComponentId =\n    options.displayName && options.componentId\n      ? `${escape(options.displayName)}-${options.componentId}`\n      : options.componentId || componentId;\n\n  // fold the underlying StyledComponent attrs up (implicit extend)\n  const finalAttrs =\n    isTargetStyledComp && styledComponentTarget.attrs\n      ? styledComponentTarget.attrs.concat(attrs as unknown as Attrs<OuterProps>[]).filter(Boolean)\n      : (attrs as Attrs<OuterProps>[]);\n\n  let { shouldForwardProp } = options;\n\n  if (isTargetStyledComp && styledComponentTarget.shouldForwardProp) {\n    const shouldForwardPropFn = styledComponentTarget.shouldForwardProp;\n\n    if (options.shouldForwardProp) {\n      const passedShouldForwardPropFn = options.shouldForwardProp;\n\n      // compose nested shouldForwardProp calls\n      shouldForwardProp = (prop, elementToBeCreated) =>\n        shouldForwardPropFn(prop, elementToBeCreated) &&\n        passedShouldForwardPropFn(prop, elementToBeCreated);\n    } else {\n      shouldForwardProp = shouldForwardPropFn;\n    }\n  }\n\n  const componentStyle = new ComponentStyle(\n    rules,\n    styledComponentId,\n    isTargetStyledComp ? (styledComponentTarget.componentStyle as ComponentStyle) : undefined\n  );\n\n  function forwardRefRender(props: ExecutionProps & OuterProps, ref: Ref<Element>) {\n    return useStyledComponentImpl<OuterProps>(WrappedStyledComponent, props, ref);\n  }\n\n  forwardRefRender.displayName = displayName;\n\n  /**\n   * forwardRef creates a new interim component, which we'll take advantage of\n   * instead of extending ParentComponent to create _another_ interim class\n   */\n  let WrappedStyledComponent = React.forwardRef(forwardRefRender) as unknown as IStyledComponent<\n    'web',\n    any\n  > &\n    Statics;\n  WrappedStyledComponent.attrs = finalAttrs;\n  WrappedStyledComponent.componentStyle = componentStyle;\n  WrappedStyledComponent.displayName = displayName;\n  WrappedStyledComponent.shouldForwardProp = shouldForwardProp;\n\n  // this static is used to preserve the cascade of static classes for component selector\n  // purposes; this is especially important with usage of the css prop\n  WrappedStyledComponent.foldedComponentIds = isTargetStyledComp\n    ? joinStrings(styledComponentTarget.foldedComponentIds, styledComponentTarget.styledComponentId)\n    : '';\n\n  WrappedStyledComponent.styledComponentId = styledComponentId;\n\n  // fold the underlying StyledComponent target up since we folded the styles\n  WrappedStyledComponent.target = isTargetStyledComp ? styledComponentTarget.target : target;\n\n  Object.defineProperty(WrappedStyledComponent, 'defaultProps', {\n    get() {\n      return this._foldedDefaultProps;\n    },\n\n    set(obj) {\n      this._foldedDefaultProps = isTargetStyledComp\n        ? merge({}, styledComponentTarget.defaultProps, obj)\n        : obj;\n    },\n  });\n\n  if (process.env.NODE_ENV !== 'production') {\n    checkDynamicCreation(displayName, styledComponentId);\n\n    WrappedStyledComponent.warnTooManyClasses = createWarnTooManyClasses(\n      displayName,\n      styledComponentId\n    );\n  }\n\n  setToString(WrappedStyledComponent, () => `.${WrappedStyledComponent.styledComponentId}`);\n\n  if (isCompositeComponent) {\n    const compositeComponentTarget = target as AnyComponent;\n\n    hoist<typeof WrappedStyledComponent, typeof compositeComponentTarget>(\n      WrappedStyledComponent,\n      compositeComponentTarget,\n      {\n        // all SC-specific things should not be hoisted\n        attrs: true,\n        componentStyle: true,\n        displayName: true,\n        foldedComponentIds: true,\n        shouldForwardProp: true,\n        styledComponentId: true,\n        target: true,\n      } as { [key in keyof OmitNever<IStyledStatics<'web', OuterProps>>]: true }\n    );\n  }\n\n  return WrappedStyledComponent;\n}\n\nexport default createStyledComponent;\n", "import { StyledTarget } from '../types';\nimport getComponentName from './getComponentName';\nimport isTag from './isTag';\n\nexport default function generateDisplayName(target: StyledTarget<any>) {\n  return isTag(target) ? `styled.${target}` : `Styled(${getComponentName(target)})`;\n}\n", "import { Dict } from '../types';\n\nexport const LIMIT = 200;\n\nexport default (displayName: string, componentId: string) => {\n  let generatedClasses: Dict<any> = {};\n  let warningSeen = false;\n\n  return (className: string) => {\n    if (!warningSeen) {\n      generatedClasses[className] = true;\n      if (Object.keys(generatedClasses).length >= LIMIT) {\n        // Unable to find latestRule in test environment.\n\n        const parsedIdString = componentId ? ` with the id of \"${componentId}\"` : '';\n\n        console.warn(\n          `Over ${LIMIT} classes were generated for component ${displayName}${parsedIdString}.\\n` +\n            'Consider using the attrs method, together with a style object for frequently changed styles.\\n' +\n            'Example:\\n' +\n            '  const Component = styled.div.attrs(props => ({\\n' +\n            '    style: {\\n' +\n            '      background: props.background,\\n' +\n            '    },\\n' +\n            '  }))`width: 100%;`\\n\\n' +\n            '  <Component />'\n        );\n        warningSeen = true;\n        generatedClasses = {};\n      }\n    }\n  };\n};\n", "import { Interpolation } from '../types';\n\nexport default function interleave<Props extends object>(\n  strings: readonly string[],\n  interpolations: Interpolation<Props>[]\n): Interpolation<Props>[] {\n  const result: Interpolation<Props>[] = [strings[0]];\n\n  for (let i = 0, len = interpolations.length; i < len; i += 1) {\n    result.push(interpolations[i], strings[i + 1]);\n  }\n\n  return result;\n}\n", "import {\n  BaseObject,\n  Interpolation,\n  NoInfer,\n  RuleSet,\n  StyledObject,\n  StyleFunction,\n  Styles,\n} from '../types';\nimport { EMPTY_ARRAY } from '../utils/empties';\nimport flatten from '../utils/flatten';\nimport interleave from '../utils/interleave';\nimport isFunction from '../utils/isFunction';\nimport isPlainObject from '../utils/isPlainObject';\n\n/**\n * Used when flattening object styles to determine if we should\n * expand an array of styles.\n */\nconst addTag = <T extends RuleSet<any>>(arg: T): T & { isCss: true } =>\n  Object.assign(arg, { isCss: true } as const);\n\nfunction css(styles: Styles<object>, ...interpolations: Interpolation<object>[]): RuleSet<object>;\nfunction css<Props extends object>(\n  styles: Styles<NoInfer<Props>>,\n  ...interpolations: Interpolation<NoInfer<Props>>[]\n): RuleSet<NoInfer<Props>>;\nfunction css<Props extends object = BaseObject>(\n  styles: Styles<NoInfer<Props>>,\n  ...interpolations: Interpolation<NoInfer<Props>>[]\n): RuleSet<NoInfer<Props>> {\n  if (isFunction(styles) || isPlainObject(styles)) {\n    const styleFunctionOrObject = styles as StyleFunction<Props> | StyledObject<Props>;\n\n    return addTag(\n      flatten<Props>(\n        interleave<Props>(EMPTY_ARRAY, [\n          styleFunctionOrObject,\n          ...interpolations,\n        ]) as Interpolation<object>\n      )\n    );\n  }\n\n  const styleStringArray = styles as TemplateStringsArray;\n\n  if (\n    interpolations.length === 0 &&\n    styleStringArray.length === 1 &&\n    typeof styleStringArray[0] === 'string'\n  ) {\n    return flatten<Props>(styleStringArray);\n  }\n\n  return addTag(\n    flatten<Props>(interleave<Props>(styleStringArray, interpolations) as Interpolation<object>)\n  );\n}\n\nexport default css;\n", "import {\n  Attrs,\n  BaseObject,\n  ExecutionProps,\n  Interpolation,\n  IStyledComponent,\n  IStyledComponentFactory,\n  KnownTarget,\n  NoInfer,\n  Runtime,\n  StyledOptions,\n  StyledTarget,\n  Styles,\n  Substitute,\n} from '../types';\nimport { EMPTY_OBJECT } from '../utils/empties';\nimport styledError from '../utils/error';\nimport css from './css';\n\ntype AttrsResult<T extends Attrs<any>> = T extends (...args: any) => infer P\n  ? P extends object\n    ? P\n    : never\n  : T extends object\n    ? T\n    : never;\n\n/**\n * Based on Attrs being a simple object or function that returns\n * a prop object, inspect the attrs result and attempt to extract\n * any \"as\" prop usage to modify the runtime target.\n */\ntype AttrsTarget<\n  R extends Runtime,\n  T extends Attrs<any>,\n  FallbackTarget extends StyledTarget<R>,\n  Result extends ExecutionProps = AttrsResult<T>,\n> = Result extends { as: infer RuntimeTarget }\n  ? RuntimeTarget extends KnownTarget\n    ? RuntimeTarget\n    : FallbackTarget\n  : FallbackTarget;\n\nexport interface Styled<\n  R extends Runtime,\n  Target extends StyledTarget<R>,\n  OuterProps extends object,\n  OuterStatics extends object = BaseObject,\n> {\n  <Props extends object = BaseObject, Statics extends object = BaseObject>(\n    initialStyles: Styles<Substitute<OuterProps, NoInfer<Props>>>,\n    ...interpolations: Interpolation<Substitute<OuterProps, NoInfer<Props>>>[]\n  ): IStyledComponent<R, Substitute<OuterProps, Props>> &\n    OuterStatics &\n    Statics &\n    (R extends 'web'\n      ? Target extends string\n        ? {}\n        : Omit<Target, keyof React.Component<any>>\n      : {});\n\n  attrs: <\n    Props extends object = BaseObject,\n    PrivateMergedProps extends object = Substitute<OuterProps, Props>,\n    PrivateAttrsArg extends Attrs<PrivateMergedProps> = Attrs<PrivateMergedProps>,\n    PrivateResolvedTarget extends StyledTarget<R> = AttrsTarget<R, PrivateAttrsArg, Target>,\n  >(\n    attrs: PrivateAttrsArg\n  ) => Styled<\n    R,\n    PrivateResolvedTarget,\n    PrivateResolvedTarget extends KnownTarget\n      ? Substitute<\n          Substitute<OuterProps, React.ComponentPropsWithRef<PrivateResolvedTarget>>,\n          Props\n        >\n      : PrivateMergedProps,\n    OuterStatics\n  >;\n\n  withConfig: (config: StyledOptions<R, OuterProps>) => Styled<R, Target, OuterProps, OuterStatics>;\n}\n\nexport default function constructWithOptions<\n  R extends Runtime,\n  Target extends StyledTarget<R>,\n  OuterProps extends object = Target extends KnownTarget\n    ? React.ComponentPropsWithRef<Target>\n    : BaseObject,\n  OuterStatics extends object = BaseObject,\n>(\n  componentConstructor: IStyledComponentFactory<R, StyledTarget<R>, object, any>,\n  tag: StyledTarget<R>,\n  options: StyledOptions<R, OuterProps> = EMPTY_OBJECT\n): Styled<R, Target, OuterProps, OuterStatics> {\n  /**\n   * We trust that the tag is a valid component as long as it isn't\n   * falsish. Typically the tag here is a string or function (i.e.\n   * class or pure function component), however a component may also be\n   * an object if it uses another utility, e.g. React.memo. React will\n   * output an appropriate warning however if the `tag` isn't valid.\n   */\n  if (!tag) {\n    throw styledError(1, tag);\n  }\n\n  /* This is callable directly as a template function */\n  const templateFunction = <Props extends object = BaseObject, Statics extends object = BaseObject>(\n    initialStyles: Styles<Substitute<OuterProps, Props>>,\n    ...interpolations: Interpolation<Substitute<OuterProps, Props>>[]\n  ) =>\n    componentConstructor<Substitute<OuterProps, Props>, Statics>(\n      tag,\n      options as StyledOptions<R, Substitute<OuterProps, Props>>,\n      css<Substitute<OuterProps, Props>>(initialStyles, ...interpolations)\n    );\n\n  /**\n   * Attrs allows for accomplishing two goals:\n   *\n   * 1. Backfilling props at runtime more expressively than defaultProps\n   * 2. Amending the prop interface of a wrapped styled component\n   */\n  templateFunction.attrs = <\n    Props extends object = BaseObject,\n    PrivateMergedProps extends object = Substitute<OuterProps, Props>,\n    PrivateAttrsArg extends Attrs<PrivateMergedProps> = Attrs<PrivateMergedProps>,\n    PrivateResolvedTarget extends StyledTarget<R> = AttrsTarget<R, PrivateAttrsArg, Target>,\n  >(\n    attrs: PrivateAttrsArg\n  ) =>\n    constructWithOptions<\n      R,\n      PrivateResolvedTarget,\n      PrivateResolvedTarget extends KnownTarget\n        ? Substitute<\n            Substitute<OuterProps, React.ComponentPropsWithRef<PrivateResolvedTarget>>,\n            Props\n          >\n        : PrivateMergedProps,\n      OuterStatics\n    >(componentConstructor, tag, {\n      ...options,\n      attrs: Array.prototype.concat(options.attrs, attrs).filter(Boolean),\n    });\n\n  /**\n   * If config methods are called, wrap up a new template function\n   * and merge options.\n   */\n  templateFunction.withConfig = (config: StyledOptions<R, OuterProps>) =>\n    constructWithOptions<R, Target, OuterProps, OuterStatics>(componentConstructor, tag, {\n      ...options,\n      ...config,\n    });\n\n  return templateFunction;\n}\n", "import * as React from 'react';\nimport createStyledComponent from '../models/StyledComponent';\nimport { BaseObject, KnownTarget, WebTarget } from '../types';\nimport domElements, { SupportedHTMLElements } from '../utils/domElements';\nimport constructWithOptions, { Styled as StyledInstance } from './constructWithOptions';\n\nconst baseStyled = <Target extends WebTarget, InjectedProps extends object = BaseObject>(\n  tag: Target\n) =>\n  constructWithOptions<\n    'web',\n    Target,\n    Target extends KnownTarget ? React.ComponentPropsWithRef<Target> & InjectedProps : InjectedProps\n  >(createStyledComponent, tag);\n\nconst styled = baseStyled as typeof baseStyled & {\n  [E in SupportedHTMLElements]: StyledInstance<'web', E, React.JSX.IntrinsicElements[E]>;\n};\n\n// Shorthands for all valid HTML Elements\ndomElements.forEach(domElement => {\n  // @ts-expect-error some react typing bs\n  styled[domElement] = baseStyled<typeof domElement>(domElement);\n});\n\nexport default styled;\nexport { StyledInstance };\n\n/**\n * This is the type of the `styled` HOC.\n */\nexport type Styled = typeof styled;\n\n/**\n * Use this higher-order type for scenarios where you are wrapping `styled`\n * and providing extra props as a third-party library.\n */\nexport type LibraryStyled<LibraryProps extends object = BaseObject> = <Target extends WebTarget>(\n  tag: Target\n) => typeof baseStyled<Target, LibraryProps>;\n", "import StyleSheet from '../sheet';\nimport { ExecutionContext, RuleSet, Stringifier } from '../types';\nimport flatten from '../utils/flatten';\nimport isStaticRules from '../utils/isStaticRules';\nimport { joinStringArray } from '../utils/joinStrings';\n\nexport default class GlobalStyle<Props extends object> {\n  componentId: string;\n  isStatic: boolean;\n  rules: RuleSet<Props>;\n\n  constructor(rules: RuleSet<Props>, componentId: string) {\n    this.rules = rules;\n    this.componentId = componentId;\n    this.isStatic = isStaticRules(rules);\n\n    // pre-register the first instance to ensure global styles\n    // load before component ones\n    StyleSheet.registerId(this.componentId + 1);\n  }\n\n  createStyles(\n    instance: number,\n    executionContext: ExecutionContext & Props,\n    styleSheet: StyleSheet,\n    stylis: Stringifier\n  ): void {\n    const flatCSS = joinStringArray(\n      flatten(this.rules as RuleSet<object>, executionContext, styleSheet, stylis) as string[]\n    );\n    const css = stylis(flatCSS, '');\n    const id = this.componentId + instance;\n\n    // NOTE: We use the id as a name as well, since these rules never change\n    styleSheet.insertRules(id, id, css);\n  }\n\n  removeStyles(instance: number, styleSheet: StyleSheet): void {\n    styleSheet.clearRules(this.componentId + instance);\n  }\n\n  renderStyles(\n    instance: number,\n    executionContext: ExecutionContext & Props,\n    styleSheet: StyleSheet,\n    stylis: Stringifier\n  ): void {\n    if (instance > 2) StyleSheet.registerId(this.componentId + instance);\n\n    // NOTE: Remove old styles, then inject the new ones\n    this.removeStyles(instance, styleSheet);\n    this.createStyles(instance, executionContext, styleSheet, stylis);\n  }\n}\n", "import React from 'react';\nimport { STATIC_EXECUTION_CONTEXT } from '../constants';\nimport GlobalStyle from '../models/GlobalStyle';\nimport { useStyleSheetContext } from '../models/StyleSheetManager';\nimport { DefaultTheme, ThemeContext } from '../models/ThemeProvider';\nimport StyleSheet from '../sheet';\nimport { ExecutionContext, ExecutionProps, Interpolation, Stringifier, Styles } from '../types';\nimport { checkDynamicCreation } from '../utils/checkDynamicCreation';\nimport determineTheme from '../utils/determineTheme';\nimport generateComponentId from '../utils/generateComponentId';\nimport css from './css';\n\nexport default function createGlobalStyle<Props extends object>(\n  strings: Styles<Props>,\n  ...interpolations: Array<Interpolation<Props>>\n) {\n  const rules = css<Props>(strings, ...interpolations);\n  const styledComponentId = `sc-global-${generateComponentId(JSON.stringify(rules))}`;\n  const globalStyle = new GlobalStyle<Props>(rules, styledComponentId);\n\n  if (process.env.NODE_ENV !== 'production') {\n    checkDynamicCreation(styledComponentId);\n  }\n\n  const GlobalStyleComponent: React.ComponentType<ExecutionProps & Props> = props => {\n    const ssc = useStyleSheetContext();\n    const theme = React.useContext(ThemeContext);\n    const instanceRef = React.useRef(ssc.styleSheet.allocateGSInstance(styledComponentId));\n\n    const instance = instanceRef.current;\n\n    if (process.env.NODE_ENV !== 'production' && React.Children.count(props.children)) {\n      console.warn(\n        `The global style component ${styledComponentId} was given child JSX. createGlobalStyle does not render children.`\n      );\n    }\n\n    if (\n      process.env.NODE_ENV !== 'production' &&\n      rules.some(rule => typeof rule === 'string' && rule.indexOf('@import') !== -1)\n    ) {\n      console.warn(\n        `Please do not use @import CSS syntax in createGlobalStyle at this time, as the CSSOM APIs we use in production do not handle it well. Instead, we recommend using a library such as react-helmet to inject a typical <link> meta tag to the stylesheet, or simply embedding it manually in your index.html <head> section for a simpler app.`\n      );\n    }\n\n    if (ssc.styleSheet.server) {\n      renderStyles(instance, props, ssc.styleSheet, theme, ssc.stylis);\n    }\n\n    if (!__SERVER__) {\n      React.useLayoutEffect(() => {\n        if (!ssc.styleSheet.server) {\n          renderStyles(instance, props, ssc.styleSheet, theme, ssc.stylis);\n          return () => globalStyle.removeStyles(instance, ssc.styleSheet);\n        }\n      }, [instance, props, ssc.styleSheet, theme, ssc.stylis]);\n    }\n\n    return null;\n  };\n\n  function renderStyles(\n    instance: number,\n    props: ExecutionProps,\n    styleSheet: StyleSheet,\n    theme: DefaultTheme | undefined,\n    stylis: Stringifier\n  ) {\n    if (globalStyle.isStatic) {\n      globalStyle.renderStyles(\n        instance,\n        STATIC_EXECUTION_CONTEXT as unknown as ExecutionContext & Props,\n        styleSheet,\n        stylis\n      );\n    } else {\n      const context = {\n        ...props,\n        theme: determineTheme(props, theme, GlobalStyleComponent.defaultProps),\n      } as ExecutionContext & Props;\n\n      globalStyle.renderStyles(instance, context, styleSheet, stylis);\n    }\n  }\n\n  return React.memo(GlobalStyleComponent);\n}\n", "import Keyframes from '../models/Keyframes';\nimport { Interpolation, Styles } from '../types';\nimport generateComponentId from '../utils/generateComponentId';\nimport { joinStringArray } from '../utils/joinStrings';\nimport css from './css';\n\nexport default function keyframes<Props extends object = {}>(\n  strings: Styles<Props>,\n  ...interpolations: Array<Interpolation<Props>>\n): Keyframes {\n  /* Warning if you've used keyframes on React Native */\n  if (\n    process.env.NODE_ENV !== 'production' &&\n    typeof navigator !== 'undefined' &&\n    navigator.product === 'ReactNative'\n  ) {\n    console.warn(\n      '`keyframes` cannot be used on ReactNative, only on the web. To do animation in ReactNative please use Animated.'\n    );\n  }\n\n  const rules = joinStringArray(css<Props>(strings, ...interpolations) as string[]);\n  const name = generateComponentId(rules);\n  return new Keyframes(name, rules);\n}\n", "import React from 'react';\nimport { ThemeContext } from '../models/ThemeProvider';\nimport { AnyComponent, ExecutionProps } from '../types';\nimport determineTheme from '../utils/determineTheme';\nimport getComponentName from '../utils/getComponentName';\nimport hoist, { NonReactStatics } from '../utils/hoist';\n\nexport default function withTheme<T extends AnyComponent>(\n  Component: T\n): React.ForwardRefExoticComponent<\n  React.PropsWithoutRef<React.JSX.LibraryManagedAttributes<T, ExecutionProps>> &\n    React.RefAttributes<T>\n> &\n  NonReactStatics<T> {\n  const WithTheme = React.forwardRef<T, React.JSX.LibraryManagedAttributes<T, ExecutionProps>>(\n    (props, ref) => {\n      const theme = React.useContext(ThemeContext);\n      const themeProp = determineTheme(props, theme, Component.defaultProps);\n\n      if (process.env.NODE_ENV !== 'production' && themeProp === undefined) {\n        console.warn(\n          `[withTheme] You are not using a ThemeProvider nor passing a theme prop or a theme in defaultProps in component class \"${getComponentName(\n            Component\n          )}\"`\n        );\n      }\n\n      return <Component {...props} theme={themeProp} ref={ref} />;\n    }\n  );\n\n  WithTheme.displayName = `WithTheme(${getComponentName(Component)})`;\n\n  return hoist(WithTheme, Component);\n}\n", "import React from 'react';\nimport type * as streamInternal from 'stream';\nimport { Readable } from 'stream';\nimport { IS_BROWSER, SC_ATTR, SC_ATTR_VERSION, SC_VERSION } from '../constants';\nimport StyleSheet from '../sheet';\nimport styledError from '../utils/error';\nimport { joinStringArray } from '../utils/joinStrings';\nimport getNonce from '../utils/nonce';\nimport { StyleSheetManager } from './StyleSheetManager';\n\ndeclare const __SERVER__: boolean;\n\nconst CLOSING_TAG_R = /^\\s*<\\/[a-z]/i;\n\nexport default class ServerStyleSheet {\n  instance: StyleSheet;\n  sealed: boolean;\n\n  constructor() {\n    this.instance = new StyleSheet({ isServer: true });\n    this.sealed = false;\n  }\n\n  _emitSheetCSS = (): string => {\n    const css = this.instance.toString();\n    if (!css) return '';\n    const nonce = getNonce();\n    const attrs = [\n      nonce && `nonce=\"${nonce}\"`,\n      `${SC_ATTR}=\"true\"`,\n      `${SC_ATTR_VERSION}=\"${SC_VERSION}\"`,\n    ];\n    const htmlAttr = joinStringArray(attrs.filter(Boolean) as string[], ' ');\n\n    return `<style ${htmlAttr}>${css}</style>`;\n  };\n\n  collectStyles(children: any): React.JSX.Element {\n    if (this.sealed) {\n      throw styledError(2);\n    }\n\n    return <StyleSheetManager sheet={this.instance}>{children}</StyleSheetManager>;\n  }\n\n  getStyleTags = (): string => {\n    if (this.sealed) {\n      throw styledError(2);\n    }\n\n    return this._emitSheetCSS();\n  };\n\n  getStyleElement = () => {\n    if (this.sealed) {\n      throw styledError(2);\n    }\n\n    const css = this.instance.toString();\n    if (!css) return [];\n\n    const props = {\n      [SC_ATTR]: '',\n      [SC_ATTR_VERSION]: SC_VERSION,\n      dangerouslySetInnerHTML: {\n        __html: css,\n      },\n    };\n\n    const nonce = getNonce();\n    if (nonce) {\n      (props as any).nonce = nonce;\n    }\n\n    // v4 returned an array for this fn, so we'll do the same for v5 for backward compat\n    return [<style {...props} key=\"sc-0-0\" />];\n  };\n\n  // @ts-expect-error alternate return types are not possible due to code transformation\n  interleaveWithNodeStream(input: Readable): streamInternal.Transform {\n    if (!__SERVER__ || IS_BROWSER) {\n      throw styledError(3);\n    } else if (this.sealed) {\n      throw styledError(2);\n    }\n\n    if (__SERVER__) {\n      this.seal();\n\n      const { Transform } = require('stream');\n\n      const readableStream: Readable = input;\n      const { instance: sheet, _emitSheetCSS } = this;\n\n      const transformer: streamInternal.Transform = new Transform({\n        transform: function appendStyleChunks(\n          chunk: string,\n          /* encoding */\n          _: string,\n          callback: Function\n        ) {\n          // Get the chunk and retrieve the sheet's CSS as an HTML chunk,\n          // then reset its rules so we get only new ones for the next chunk\n          const renderedHtml = chunk.toString();\n          const html = _emitSheetCSS();\n\n          sheet.clearTag();\n\n          // prepend style html to chunk, unless the start of the chunk is a\n          // closing tag in which case append right after that\n          if (CLOSING_TAG_R.test(renderedHtml)) {\n            const endOfClosingTag = renderedHtml.indexOf('>') + 1;\n            const before = renderedHtml.slice(0, endOfClosingTag);\n            const after = renderedHtml.slice(endOfClosingTag);\n\n            this.push(before + html + after);\n          } else {\n            this.push(html + renderedHtml);\n          }\n\n          callback();\n        },\n      });\n\n      readableStream.on('error', err => {\n        // forward the error to the transform stream\n        transformer.emit('error', err);\n      });\n\n      return readableStream.pipe(transformer);\n    }\n  }\n\n  seal = (): void => {\n    this.sealed = true;\n  };\n}\n", "import { mainSheet } from './models/StyleSheetManager';\nimport StyleSheet from './sheet';\n\nexport const __PRIVATE__ = {\n  StyleSheet,\n  mainSheet,\n};\n", "/* Import singletons */\nimport { SC_ATTR, SC_VERSION } from './constants';\nimport createGlobalStyle from './constructors/createGlobalStyle';\nimport css from './constructors/css';\nimport keyframes from './constructors/keyframes';\n/* Import Higher Order Components */\nimport withTheme from './hoc/withTheme';\n/* Import hooks */\nimport ServerStyleSheet from './models/ServerStyleSheet';\nimport {\n  IStyleSheetContext,\n  IStyleSheetManager,\n  IStylisContext,\n  StyleSheetConsumer,\n  StyleSheetContext,\n  StyleSheetManager,\n} from './models/StyleSheetManager';\n/* Import components */\nimport ThemeProvider, { ThemeConsumer, ThemeContext, useTheme } from './models/ThemeProvider';\nimport isStyledComponent from './utils/isStyledComponent';\n\n/* Warning if you've imported this file on React Native */\nif (\n  process.env.NODE_ENV !== 'production' &&\n  typeof navigator !== 'undefined' &&\n  navigator.product === 'ReactNative'\n) {\n  console.warn(\n    `It looks like you've imported 'styled-components' on React Native.\\nPerhaps you're looking to import 'styled-components/native'?\\nRead more about this at https://www.styled-components.com/docs/basics#react-native`\n  );\n}\n\nconst windowGlobalKey = `__sc-${SC_ATTR}__`;\n\n/* Warning if there are several instances of styled-components */\nif (\n  process.env.NODE_ENV !== 'production' &&\n  process.env.NODE_ENV !== 'test' &&\n  typeof window !== 'undefined'\n) {\n  // @ts-expect-error dynamic key not in window object\n  window[windowGlobalKey] ||= 0;\n\n  // @ts-expect-error dynamic key not in window object\n  if (window[windowGlobalKey] === 1) {\n    console.warn(\n      `It looks like there are several instances of 'styled-components' initialized in this application. This may cause dynamic styles to not render properly, errors during the rehydration process, a missing theme prop, and makes your application bigger without good reason.\\n\\nSee https://s-c.sh/2BAXzed for more info.`\n    );\n  }\n\n  // @ts-expect-error dynamic key not in window object\n  window[windowGlobalKey] += 1;\n}\n\n/* Export everything */\nexport * from './secretInternals';\nexport { Attrs, DefaultTheme, ShouldForwardProp } from './types';\nexport {\n  IStyleSheetContext,\n  IStyleSheetManager,\n  IStylisContext,\n  ServerStyleSheet,\n  StyleSheetConsumer,\n  StyleSheetContext,\n  StyleSheetManager,\n  ThemeConsumer,\n  ThemeContext,\n  ThemeProvider,\n  createGlobalStyle,\n  css,\n  isStyledComponent,\n  keyframes,\n  useTheme,\n  SC_VERSION as version,\n  withTheme,\n};\n"], "mappings": ";;;;;;AAGO,IAAMA,CAAA,GACS,sBAAZC,OAAA,SACiB,MAAhBA,OAAA,CAAQC,GAAA,KACdD,OAAA,CAAQC,GAAA,CAAIC,iBAAA,IAAqBF,OAAA,CAAQC,GAAA,CAAIE,OAAA,KAChD;EAEWC,CAAA,GAAiB;EACjBC,CAAA,GAAkB;EAClBC,CAAA,GAAa;EACbC,CAAA,GAAW;EAEXC,CAAA,GAA+B,sBAAXC,MAAA,IAA8C,sBAAbC,QAAA;EAErDC,CAAA,GAAiBC,OAAA,CACC,oBAAtBC,iBAAA,GACHA,iBAAA,GACmB,sBAAZb,OAAA,SACkB,MAAhBA,OAAA,CAAQC,GAAA,SACoC,MAA5CD,OAAA,CAAQC,GAAA,CAAIa,2BAAA,IACyB,OAA5Cd,OAAA,CAAQC,GAAA,CAAIa,2BAAA,GACgC,YAA5Cd,OAAA,CAAQC,GAAA,CAAIa,2BAAA,IAEVd,OAAA,CAAQC,GAAA,CAAIa,2BAAA,GACK,sBAAZd,OAAA,SACkB,MAAhBA,OAAA,CAAQC,GAAA,SAC0B,MAAlCD,OAAA,CAAQC,GAAA,CAAIY,iBAAA,IACe,OAAlCb,OAAA,CAAQC,GAAA,CAAIY,iBAAA,GACsB,YAAlCb,OAAA,CAAQC,GAAA,CAAIY,iBAAA,IAEVb,OAAA,CAAQC,GAAA,CAAIY,iBAAA,GACW,iBAAzBb,OAAA,CAAQC,GAAA,CAAIc,QAAA;EAITC,CAAA,GAA2B,CAAE;ECnCpCC,CAAA,GAAoB;EACpBC,CAAA,GAAO,IAAIC,GAAA;EAEJC,CAAA,GAAuB,SAAAC,CAACC,CAAA,EAAqBC,CAAA;IACxD,IAA6B,iBAAzBvB,OAAA,CAAQC,GAAA,CAAIc,QAAA,EAA2B;MACzC,IAAMS,CAAA,GAAiBD,CAAA,GAAc,oBAAoBE,MAAA,CAAAF,CAAA,EAAc,OAAG;QACpEG,CAAA,GACJ,iBAAAD,MAAA,CAAiBH,CAAA,EAAWG,MAAA,CAAGD,CAAA,EAAgD,sCAA/E;QASIG,CAAA,GAAuBC,OAAA,CAAQC,KAAA;MACrC;QACE,IAAIC,CAAA,IAAwB;QAC5BF,OAAA,CAAQC,KAAA,GAAQ,UAACP,CAAA;UAAA,KAAqB,IAAmBC,CAAA,OAAAC,CAAA,MAAnBA,CAAA,GAAmBO,SAAA,CAAAC,MAAA,EAAnBR,CAAA,IAAAD,CAAA,CAAmBC,CAAA,QAAAO,SAAA,CAAAP,CAAA;UAGnDP,CAAA,CAAkBgB,IAAA,CAAKX,CAAA,KACzBQ,CAAA,IAAwB,GAExBZ,CAAA,CAAKgB,MAAA,CAAOR,CAAA,KAEZC,CAAA,CAAqBQ,KAAA,SAAAC,CAAA,EAAAd,CAAA,GAAwBC,CAAA,GAAkB,GAEnE;QAAA,GAGAc,CAAA,IAEIP,CAAA,KAA0BZ,CAAA,CAAKoB,GAAA,CAAIZ,CAAA,MACrCE,OAAA,CAAQW,IAAA,CAAKb,CAAA,GACbR,CAAA,CAAKsB,GAAA,CAAId,CAAA,EAEZ;MAAA,CAAC,QAAOU,CAAA;QAGHnB,CAAA,CAAkBgB,IAAA,CAAMG,CAAA,CAAgBK,OAAA,KAE1CvB,CAAA,CAAKgB,MAAA,CAAOR,CAAA,CAEf;MAAA,CAAS;QACRE,OAAA,CAAQC,KAAA,GAAQF,CACjB;MAAA;IACF;EACH;ECjDae,CAAA,GAAcC,MAAA,CAAOC,MAAA,CAAO;EAC5BC,CAAA,GAAeF,MAAA,CAAOC,MAAA,CAAO;ACAlB,SAAAE,EACtBV,CAAA,EACAd,CAAA,EACAC,CAAA;EAEA,YAFA,MAAAA,CAAA,KAAAA,CAAA,GAAiEsB,CAAA,GAEzDT,CAAA,CAAMW,KAAA,KAAUxB,CAAA,CAAawB,KAAA,IAASX,CAAA,CAAMW,KAAA,IAAUzB,CAAA,IAAiBC,CAAA,CAAawB,KAC9F;AAAA;ACPA,IAwIAC,CAAA,GAAe,IAAI7B,GAAA,CAxIF,CACf,KACA,QACA,WACA,QACA,WACA,SACA,SACA,KACA,QACA,OACA,OACA,OACA,cACA,QACA,MACA,UACA,UACA,WACA,QACA,QACA,OACA,YACA,QACA,YACA,MACA,OACA,WACA,OACA,UACA,OACA,MACA,MACA,MACA,SACA,YACA,cACA,UACA,UACA,QACA,MACA,MACA,MACA,MACA,MACA,MACA,UACA,UACA,MACA,QACA,KACA,UACA,OACA,SACA,OACA,OACA,UACA,SACA,UACA,MACA,QACA,QACA,OACA,QACA,QACA,YACA,QACA,SACA,OACA,YACA,UACA,MACA,YACA,UACA,UACA,KACA,SACA,WACA,OACA,YACA,KACA,MACA,MACA,QACA,KACA,QACA,UACA,WACA,UACA,SACA,UACA,QACA,UACA,SACA,OACA,WACA,OACA,SACA,SACA,MACA,YACA,SACA,MACA,SACA,QACA,MACA,SACA,KACA,MACA,OACA,OACA,SACA,OACA,UACA,YACA,QACA,WACA,iBACA,KACA,SACA,QACA,kBACA,UACA,QACA,QACA,WACA,WACA,YACA,kBACA,QACA,QACA,OACA,QACA;ECrII8B,CAAA,GAAc;EAEdC,CAAA,GAAe;AAMG,SAAAC,EAAOf,CAAA;EAC7B,OAAOA,CAAA,CACJgB,OAAA,CAAQH,CAAA,EAAa,KACrBG,OAAA,CAAQF,CAAA,EAAc,GAC3B;AAAA;ACdA,IAAMG,CAAA,GAAgB;EAIhBC,CAAA,GAAc;EAGdC,CAAA,GAAoB,SAAAC,CAACpB,CAAA;IAAiB,OAAAqB,MAAA,CAAOC,YAAA,CAAatB,CAAA,IAAQA,CAAA,GAAO,KAAK,KAAK,IAA7C;EAAA;AAGpB,SAAAuB,EAAuBvB,CAAA;EAC7C,IACId,CAAA;IADAC,CAAA,GAAO;EAIX,KAAKD,CAAA,GAAIsC,IAAA,CAAKC,GAAA,CAAIzB,CAAA,GAAOd,CAAA,GAAIgC,CAAA,EAAahC,CAAA,GAAKA,CAAA,GAAIgC,CAAA,GAAe,GAChE/B,CAAA,GAAOgC,CAAA,CAAkBjC,CAAA,GAAIgC,CAAA,IAAe/B,CAAA;EAG9C,QAAQgC,CAAA,CAAkBjC,CAAA,GAAIgC,CAAA,IAAe/B,CAAA,EAAM6B,OAAA,CAAQC,CAAA,EAAe,QAC5E;AAAA;ACpBO,IAAAS,CAAA;EAAMC,CAAA,GAAO;EAKPC,CAAA,GAAQ,SAAAC,CAAC7B,CAAA,EAAWd,CAAA;IAG/B,KAFA,IAAIC,CAAA,GAAID,CAAA,CAAEU,MAAA,EAEHT,CAAA,GACLa,CAAA,GAAS,KAAJA,CAAA,GAAUd,CAAA,CAAE4C,UAAA,GAAa3C,CAAA;IAGhC,OAAOa,CACT;EAAA;EAGa+B,CAAA,GAAO,SAAAC,CAAChC,CAAA;IACnB,OAAO4B,CAAA,CAAMD,CAAA,EAAM3B,CAAA,CACrB;EAAA;ACfwB,SAAAiC,EAAoBjC,CAAA;EAC1C,OAAOuB,CAAA,CAAuBQ,CAAA,CAAK/B,CAAA,MAAS,EAC9C;AAAA;ACHwB,SAAAkC,EAAiBlC,CAAA;EACvC,OAC4B,iBAAzBpC,OAAA,CAAQC,GAAA,CAAIc,QAAA,IAA8C,mBAAXqB,CAAA,IAAuBA,CAAA,IACtEA,CAAA,CAA8CmC,WAAA,IAC9CnC,CAAA,CAAoBoC,IAAA,IACrB,WAEJ;AAAA;ACPwB,SAAAC,EAAMrC,CAAA;EAC5B,OACoB,mBAAXA,CAAA,KACmB,iBAAzBpC,OAAA,CAAQC,GAAA,CAAIc,QAAA,IACTqB,CAAA,CAAOsC,MAAA,CAAO,OAAOtC,CAAA,CAAOsC,MAAA,CAAO,GAAGC,WAAA,GAG9C;AAAA;ACNA,IAAMC,CAAA,GAA8B,qBAAXC,MAAA,IAAyBA,MAAA,CAAOC,GAAA;EAGnDC,CAAA,GAAkBH,CAAA,GAAYC,MAAA,CAAOC,GAAA,CAAI,gBAAgB;EACzDE,CAAA,GAAyBJ,CAAA,GAAYC,MAAA,CAAOC,GAAA,CAAI,uBAAuB;EAKvEG,CAAA,GAAgB;IACpBC,iBAAA,GAAmB;IACnBC,WAAA,GAAa;IACbC,YAAA,GAAc;IACdC,YAAA,GAAc;IACdd,WAAA,GAAa;IACbe,eAAA,GAAiB;IACjBC,wBAAA,GAA0B;IAC1BC,wBAAA,GAA0B;IAC1BC,MAAA,GAAQ;IACRC,SAAA,GAAW;IACXC,IAAA,GAAM;EAAA;EAGFC,CAAA,GAAgB;IACpBpB,IAAA,GAAM;IACNxC,MAAA,GAAQ;IACR6D,SAAA,GAAW;IACXC,MAAA,GAAQ;IACRC,MAAA,GAAQ;IACRhE,SAAA,GAAW;IACXiE,KAAA,GAAO;EAAA;EAWHC,CAAA,GAAe;IACnBC,QAAA,GAAU;IACVC,OAAA,GAAS;IACTd,YAAA,GAAc;IACdd,WAAA,GAAa;IACbmB,SAAA,GAAW;IACXC,IAAA,GAAM;EAAA;EAGFS,CAAA,KAAYtC,CAAA,OACfkB,CAAA,IAlByB;IAC1BkB,QAAA,GAAU;IACVG,MAAA,GAAQ;IACRhB,YAAA,GAAc;IACdd,WAAA,GAAa;IACbmB,SAAA,GAAW;EAAA,GAcX5B,CAAA,CAACiB,CAAA,IAAkBkB,CAAA,EAAAnC,CAAA;AAcrB,SAASwC,EAAWlE,CAAA;EAElB,QAPqB,WAFrBd,CAAA,GASWc,CAAA,KAP8Bd,CAAA,CAAOqE,IAAA,CAAKO,QAAA,MAE7BnB,CAAA,GAMfkB,CAAA,GAIF,cAAc7D,CAAA,GACjBgE,CAAA,CAAahE,CAAA,CAAoB8D,QAAA,IACjCjB,CAAA;EAjBN,IACE3D,CAiBF;AAAA;AAEA,IAAMiF,CAAA,GAAiB5D,MAAA,CAAO6D,cAAA;EACxBC,CAAA,GAAsB9D,MAAA,CAAO+D,mBAAA;EAC7BC,CAAA,GAAwBhE,MAAA,CAAOiE,qBAAA;EAC/BC,EAAA,GAA2BlE,MAAA,CAAOmE,wBAAA;EAClCC,EAAA,GAAiBpE,MAAA,CAAOqE,cAAA;EACxBC,EAAA,GAAkBtE,MAAA,CAAOkD,SAAA;AAiBP,SAAAqB,GAItB9E,CAAA,EAAoBd,CAAA,EAAoBC,CAAA;EACxC,IAA+B,mBAApBD,CAAA,EAA8B;IAGvC,IAAI2F,EAAA,EAAiB;MACnB,IAAMzF,CAAA,GAAqBuF,EAAA,CAAezF,CAAA;MACtCE,CAAA,IAAsBA,CAAA,KAAuByF,EAAA,IAC/CC,EAAA,CAAqB9E,CAAA,EAAiBZ,CAAA,EAAoBD,CAAA,CAE7D;IAAA;IAED,IAAIc,CAAA,GAA4BoE,CAAA,CAAoBnF,CAAA;IAEhDqF,CAAA,KACFtE,CAAA,GAAOA,CAAA,CAAKZ,MAAA,CAAOkF,CAAA,CAAsBrF,CAAA;IAM3C,KAHA,IAAMI,CAAA,GAAgB4E,CAAA,CAAWlE,CAAA,GAC3BT,CAAA,GAAgB2E,CAAA,CAAWhF,CAAA,GAExBQ,CAAA,GAAI,GAAGA,CAAA,GAAIO,CAAA,CAAKL,MAAA,IAAUF,CAAA,EAAG;MACpC,IAAMqF,CAAA,GAAM9E,CAAA,CAAKP,CAAA;MACjB,MACIqF,CAAA,IAAOvB,CAAA,IACPrE,CAAA,IAAeA,CAAA,CAAY4F,CAAA,KAC3BxF,CAAA,IAAiBwF,CAAA,IAAOxF,CAAA,IACxBD,CAAA,IAAiByF,CAAA,IAAOzF,CAAA,GAC1B;QACA,IAAM0F,CAAA,GAAaP,EAAA,CAAyBvF,CAAA,EAAiB6F,CAAA;QAE7D;UAEEZ,CAAA,CAAenE,CAAA,EAAiB+E,CAAA,EAAKC,CAAA,CACtC;QAAA,CAAC,QAAOhF,CAAA,GAER;MACF;IACF;EACF;EAED,OAAOA,CACT;AAAA;ACpJwB,SAAAiF,GAAWjF,CAAA;EACjC,OAAuB,qBAATA,CAChB;AAAA;ACAwB,SAAAkF,GAAkBlF,CAAA;EACxC,OAAyB,mBAAXA,CAAA,IAAuB,uBAAuBA,CAC9D;AAAA;ACDgB,SAAAmF,GAAYnF,CAAA,EAAwBd,CAAA;EAClD,OAAOc,CAAA,IAAKd,CAAA,GAAI,GAAAG,MAAA,CAAGW,CAAA,EAAC,KAAAX,MAAA,CAAIH,CAAA,IAAMc,CAAA,IAAKd,CAAA,IAAK,EAC1C;AAAA;AAEgB,SAAAkG,GAAgBpF,CAAA,EAAed,CAAA;EAC7C,IAAmB,MAAfc,CAAA,CAAIJ,MAAA,EACN,OAAO;EAIT,KADA,IAAIT,CAAA,GAASa,CAAA,CAAI,IACRZ,CAAA,GAAI,GAAGA,CAAA,GAAIY,CAAA,CAAIJ,MAAA,EAAQR,CAAA,IAC9BD,CAAA,IAAUD,CAAA,GAAMA,CAAA,GAAMc,CAAA,CAAIZ,CAAA,IAAKY,CAAA,CAAIZ,CAAA;EAErC,OAAOD,CACT;AAAA;ACjBwB,SAAAkG,GAAcrF,CAAA;EACpC,OACQ,SAANA,CAAA,IACa,mBAANA,CAAA,IACPA,CAAA,CAAEsF,WAAA,CAAYlD,IAAA,KAAS7B,MAAA,CAAO6B,IAAA,MAE5B,WAAWpC,CAAA,IAAKA,CAAA,CAAE8D,QAAA,CAExB;AAAA;ACNA,SAASyB,GAAiBvF,CAAA,EAAad,CAAA,EAAaC,CAAA;EAGlD,SAHkD,MAAAA,CAAA,KAAAA,CAAA,IAAkB,KAG/DA,CAAA,KAAekG,EAAA,CAAcrF,CAAA,MAAYwF,KAAA,CAAMC,OAAA,CAAQzF,CAAA,GAC1D,OAAOd,CAAA;EAGT,IAAIsG,KAAA,CAAMC,OAAA,CAAQvG,CAAA,GAChB,KAAK,IAAIE,CAAA,GAAM,GAAGA,CAAA,GAAMF,CAAA,CAAOU,MAAA,EAAQR,CAAA,IACrCY,CAAA,CAAOZ,CAAA,IAAOmG,EAAA,CAAiBvF,CAAA,CAAOZ,CAAA,GAAMF,CAAA,CAAOE,CAAA,QAEhD,IAAIiG,EAAA,CAAcnG,CAAA,GACvB,KAAK,IAAME,CAAA,IAAOF,CAAA,EAChBc,CAAA,CAAOZ,CAAA,IAAOmG,EAAA,CAAiBvF,CAAA,CAAOZ,CAAA,GAAMF,CAAA,CAAOE,CAAA;EAIvD,OAAOY,CACT;AAAA;ACJgB,SAAA0F,GAAY1F,CAAA,EAAgBd,CAAA;EAC1CqB,MAAA,CAAO6D,cAAA,CAAepE,CAAA,EAAQ,YAAY;IAAE2F,KAAA,EAAOzG;EAAA,EACrD;AAAA;AClBA,ICGM0G,EAAA,GAA6C,iBAAzBhI,OAAA,CAAQC,GAAA,CAAIc,QAAA,GDHvB;EACb,GAAK;EACL,GAAK;EACL,GAAK;EACL,GAAK;EACL,GAAK;EACL,GAAK;EACL,GAAK;EACL,GAAK;EACL,GAAK;EACL,IAAM;EACN,IAAM;EACN,IAAM;EACN,IAAM;EACN,IAAM;EACN,IAAM;EACN,IAAM;EACN,IAAM;EACN,IAAM;AAAA,ICfqE;AAK7E,SAASkH,GAAA;EAAA,KAAO,IAAyB7F,CAAA,OAAAd,CAAA,MAAzBA,CAAA,GAAyBS,SAAA,CAAAC,MAAA,EAAzBV,CAAA,IAAAc,CAAA,CAAyBd,CAAA,IAAAS,SAAA,CAAAT,CAAA;EAIvC,KAHA,IAAIC,CAAA,GAAIa,CAAA,CAAK,IACPZ,CAAA,GAAI,IAEDa,CAAA,GAAI,GAAGX,CAAA,GAAMU,CAAA,CAAKJ,MAAA,EAAQK,CAAA,GAAIX,CAAA,EAAKW,CAAA,IAAK,GAC/Cb,CAAA,CAAE0G,IAAA,CAAK9F,CAAA,CAAKC,CAAA;EAOd,OAJAb,CAAA,CAAE2G,OAAA,CAAQ,UAAA/F,CAAA;IACRb,CAAA,GAAIA,CAAA,CAAE6B,OAAA,CAAQ,UAAUhB,CAAA,CAC1B;EAAA,IAEOb,CACT;AAAA;AAMwB,SAAA6G,GACtB9G,CAAA;EAAA,KACA,IAAwBC,CAAA,OAAAC,CAAA,MAAxBA,CAAA,GAAwBO,SAAA,CAAAC,MAAA,EAAxBR,CAAA,IAAAD,CAAA,CAAwBC,CAAA,QAAAO,SAAA,CAAAP,CAAA;EAExB,OAA6B,iBAAzBxB,OAAA,CAAQC,GAAA,CAAIc,QAAA,GACP,IAAIsH,KAAA,CACT,0IAAA5G,MAAA,CAA0IH,CAAA,EAAI,0BAAAG,MAAA,CAC5IF,CAAA,CAAeS,MAAA,GAAS,IAAI,UAAUP,MAAA,CAAAF,CAAA,CAAe+G,IAAA,CAAK,SAAU,OAIjE,IAAID,KAAA,CAAMJ,EAAA,CAAA9F,KAAA,SAAAC,CAAA,EAAO4F,EAAA,CAAO1G,CAAA,IAAUC,CAAA,GAAc,IAAEgH,IAAA,GAE7D;AAAA;ACnCO,IAMDC,EAAA,GAAiB;IAKrB,SAAApG,EAAYA,CAAA;MACV,KAAKqG,UAAA,GAAa,IAAIC,WAAA,CARR,MASd,KAAK1G,MAAA,GATS,KAUd,KAAK2G,GAAA,GAAMvG,CACZ;IAAA;IAyEH,OAvEEA,CAAA,CAAYyD,SAAA,CAAA+C,YAAA,GAAZ,UAAaxG,CAAA;MAEX,KADA,IAAId,CAAA,GAAQ,GACHC,CAAA,GAAI,GAAGA,CAAA,GAAIa,CAAA,EAAOb,CAAA,IACzBD,CAAA,IAAS,KAAKmH,UAAA,CAAWlH,CAAA;MAG3B,OAAOD,CAAA;IAAA,GAGTc,CAAA,CAAAyD,SAAA,CAAAgD,WAAA,aAAYzG,CAAA,EAAed,CAAA;MACzB,IAAIc,CAAA,IAAS,KAAKqG,UAAA,CAAWzG,MAAA,EAAQ;QAKnC,KAJA,IAAMT,CAAA,GAAY,KAAKkH,UAAA,EACjBjH,CAAA,GAAUD,CAAA,CAAUS,MAAA,EAEtBK,CAAA,GAAUb,CAAA,EACPY,CAAA,IAASC,CAAA,GAEd,KADAA,CAAA,KAAY,KACE,GACZ,MAAM+F,EAAA,CAAY,IAAI,GAAA3G,MAAA,CAAGW,CAAA;QAI7B,KAAKqG,UAAA,GAAa,IAAIC,WAAA,CAAYrG,CAAA,GAClC,KAAKoG,UAAA,CAAWK,GAAA,CAAIvH,CAAA,GACpB,KAAKS,MAAA,GAASK,CAAA;QAEd,KAAK,IAAIX,CAAA,GAAIF,CAAA,EAASE,CAAA,GAAIW,CAAA,EAASX,CAAA,IACjC,KAAK+G,UAAA,CAAW/G,CAAA,IAAK,CAExB;MAAA;MAID,KAFA,IAAIC,CAAA,GAAY,KAAKiH,YAAA,CAAaxG,CAAA,GAAQ,IAE1BN,CAAA,IAAPJ,CAAA,GAAI,GAAOJ,CAAA,CAAMU,MAAA,GAAQN,CAAA,GAAII,CAAA,EAAGJ,CAAA,IACnC,KAAKiH,GAAA,CAAII,UAAA,CAAWpH,CAAA,EAAWL,CAAA,CAAMI,CAAA,OACvC,KAAK+G,UAAA,CAAWrG,CAAA,KAChBT,CAAA;IAAA,GAKNS,CAAA,CAAUyD,SAAA,CAAAmD,UAAA,GAAV,UAAW5G,CAAA;MACT,IAAIA,CAAA,GAAQ,KAAKJ,MAAA,EAAQ;QACvB,IAAMV,CAAA,GAAS,KAAKmH,UAAA,CAAWrG,CAAA;UACzBb,CAAA,GAAa,KAAKqH,YAAA,CAAaxG,CAAA;UAC/BZ,CAAA,GAAWD,CAAA,GAAaD,CAAA;QAE9B,KAAKmH,UAAA,CAAWrG,CAAA,IAAS;QAEzB,KAAK,IAAIC,CAAA,GAAId,CAAA,EAAYc,CAAA,GAAIb,CAAA,EAAUa,CAAA,IACrC,KAAKsG,GAAA,CAAIM,UAAA,CAAW1H,CAAA,CAEvB;MAAA;IAAA,GAGHa,CAAA,CAAQyD,SAAA,CAAAqD,QAAA,GAAR,UAAS9G,CAAA;MACP,IAAId,CAAA,GAAM;MACV,IAAIc,CAAA,IAAS,KAAKJ,MAAA,IAAqC,MAA3B,KAAKyG,UAAA,CAAWrG,CAAA,GAC1C,OAAOd,CAAA;MAOT,KAJA,IAAMC,CAAA,GAAS,KAAKkH,UAAA,CAAWrG,CAAA,GACzBZ,CAAA,GAAa,KAAKoH,YAAA,CAAaxG,CAAA,GAC/BC,CAAA,GAAWb,CAAA,GAAaD,CAAA,EAErBG,CAAA,GAAIF,CAAA,EAAYE,CAAA,GAAIW,CAAA,EAAUX,CAAA,IACrCJ,CAAA,IAAO,GAAAG,MAAA,CAAG,KAAKkH,GAAA,CAAIQ,OAAA,CAAQzH,CAAA,GAAKD,MAAA,CAAAlB,CAAA;MAGlC,OAAOe,CAAA;IAAA,GAEVc,CAAD;EAAA;EC3FMgH,EAAA,GAAU,KAAC;EAEbC,EAAA,GAAuC,IAAIC,GAAA;EAC3CC,EAAA,GAAuC,IAAID,GAAA;EAC3CE,EAAA,GAAgB;EAQPC,EAAA,GAAgB,SAAAC,CAACtH,CAAA;IAC5B,IAAIiH,EAAA,CAAgB/G,GAAA,CAAIF,CAAA,GACtB,OAAOiH,EAAA,CAAgBM,GAAA,CAAIvH,CAAA;IAG7B,OAAOmH,EAAA,CAAgBjH,GAAA,CAAIkH,EAAA,IACzBA,EAAA;IAGF,IAAMlI,CAAA,GAAQkI,EAAA;IAEd,IAA6B,iBAAzBxJ,OAAA,CAAQC,GAAA,CAAIc,QAAA,MAAuC,IAARO,CAAA,IAAa,KAAKA,CAAA,GAAQ8H,EAAA,GACvE,MAAMhB,EAAA,CAAY,IAAI,GAAA3G,MAAA,CAAGH,CAAA;IAK3B,OAFA+H,EAAA,CAAgBP,GAAA,CAAI1G,CAAA,EAAId,CAAA,GACxBiI,EAAA,CAAgBT,GAAA,CAAIxH,CAAA,EAAOc,CAAA,GACpBd,CACT;EAAA;EAMasI,EAAA,GAAgB,SAAAC,CAACzH,CAAA,EAAYd,CAAA;IAExCkI,EAAA,GAAgBlI,CAAA,GAAQ,GAExB+H,EAAA,CAAgBP,GAAA,CAAI1G,CAAA,EAAId,CAAA,GACxBiI,EAAA,CAAgBT,GAAA,CAAIxH,CAAA,EAAOc,CAAA,CAC7B;EAAA;ECxCM0H,EAAA,GAAW,SAASrI,MAAA,CAAA1B,CAAA,QAAA0B,MAAA,CAAYpB,CAAA,EAAe,MAAAoB,MAAA,CAAKnB,CAAA,EAAU;EAC9DyJ,EAAA,GAAY,IAAIC,MAAA,CAAO,IAAIvI,MAAA,CAAA1B,CAAA,EAAqD;EAkChFkK,EAAA,GAA4B,SAAAC,CAAC9H,CAAA,EAAcd,CAAA,EAAYC,CAAA;IAI3D,KAHA,IACIC,CAAA,EADEa,CAAA,GAAQd,CAAA,CAAQ4I,KAAA,CAAM,MAGnBzI,CAAA,GAAI,GAAGC,CAAA,GAAIU,CAAA,CAAML,MAAA,EAAQN,CAAA,GAAIC,CAAA,EAAGD,CAAA,KAClCF,CAAA,GAAOa,CAAA,CAAMX,CAAA,MAChBU,CAAA,CAAMgI,YAAA,CAAa9I,CAAA,EAAIE,CAAA,CAG7B;EAAA;EAEM6I,EAAA,GAAwB,SAAAC,CAAClI,CAAA,EAAcd,CAAA;IAI3C,SAAAC,CAAA,EAHMC,CAAA,IAA8B,UAArBD,CAAA,GAAAD,CAAA,CAAMiJ,WAAA,UAAe,MAAAhJ,CAAA,GAAAA,CAAA,OAAI4I,KAAA,CAAM5J,CAAA,GACxC8B,CAAA,GAAkB,IAEfX,CAAA,GAAI,GAAGC,CAAA,GAAIH,CAAA,CAAMQ,MAAA,EAAQN,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;MAC5C,IAAMI,CAAA,GAAON,CAAA,CAAME,CAAA,EAAG6G,IAAA;MACtB,IAAKzG,CAAA,EAAL;QAEA,IAAMqF,CAAA,GAASrF,CAAA,CAAK0I,KAAA,CAAMT,EAAA;QAE1B,IAAI5C,CAAA,EAAQ;UACV,IAAMC,CAAA,GAAkC,IAA1BqD,QAAA,CAAStD,CAAA,CAAO,IAAI;YAC5BuD,CAAA,GAAKvD,CAAA,CAAO;UAEJ,MAAVC,CAAA,KAEFwC,EAAA,CAAcc,CAAA,EAAItD,CAAA,GAGlB6C,EAAA,CAA0B7H,CAAA,EAAOsI,CAAA,EAAIvD,CAAA,CAAO,KAC5C/E,CAAA,CAAMuI,MAAA,GAAS9B,WAAA,CAAYzB,CAAA,EAAO/E,CAAA,IAGpCA,CAAA,CAAML,MAAA,GAAS,CAChB;QAAA,OACCK,CAAA,CAAM6F,IAAA,CAAKpG,CAAA,CAnBO;MAAA;IAqBrB;EACH;EAEa8I,EAAA,GAAiB,SAAAC,CAACzI,CAAA;IAG7B,KAFA,IAAMd,CAAA,GAAQZ,QAAA,CAASoK,gBAAA,CAAiBhB,EAAA,GAE/BvI,CAAA,GAAI,GAAGC,CAAA,GAAIF,CAAA,CAAMU,MAAA,EAAQT,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;MAC5C,IAAMc,CAAA,GAAOf,CAAA,CAAMC,CAAA;MACfc,CAAA,IAAQA,CAAA,CAAK0I,YAAA,CAAahL,CAAA,MAAaK,CAAA,KACzCiK,EAAA,CAAsBjI,CAAA,EAAOC,CAAA,GAEzBA,CAAA,CAAK2I,UAAA,IACP3I,CAAA,CAAK2I,UAAA,CAAWC,WAAA,CAAY5I,CAAA,EAGjC;IAAA;EACH;AC3Fc,SAAU6I,GAAA;EACtB,OAAoC,sBAAtBC,iBAAA,GAAoCA,iBAAA,GAAoB,IACxE;AAAA;ACEA,IAOaC,EAAA,GAAe,SAAAC,CAACjJ,CAAA;IAC3B,IAAMd,CAAA,GAAOZ,QAAA,CAAS4K,IAAA;MAChB/J,CAAA,GAASa,CAAA,IAAUd,CAAA;MACnBE,CAAA,GAAQd,QAAA,CAAS6K,aAAA,CAAc;MAC/BlJ,CAAA,GAXiB,UAACD,CAAA;QACxB,IAAMd,CAAA,GAAMsG,KAAA,CAAM4D,IAAA,CAAKpJ,CAAA,CAAO0I,gBAAA,CAAmC,SAASrJ,MAAA,CAAA1B,CAAA,EAAU;QAEpF,OAAOuB,CAAA,CAAIA,CAAA,CAAIU,MAAA,GAAS,EAC1B;MAAA,CAOoB,CAAiBT,CAAA;MAC7BG,CAAA,QAA4B,MAAdW,CAAA,GAA0BA,CAAA,CAAUoJ,WAAA,GAAc;IAEtEjK,CAAA,CAAMkK,YAAA,CAAa3L,CAAA,EAASK,CAAA,GAC5BoB,CAAA,CAAMkK,YAAA,CAAarL,CAAA,EAAiBC,CAAA;IAEpC,IAAMqB,CAAA,GAAQuJ,EAAA;IAMd,OAJIvJ,CAAA,IAAOH,CAAA,CAAMkK,YAAA,CAAa,SAAS/J,CAAA,GAEvCJ,CAAA,CAAOoK,YAAA,CAAanK,CAAA,EAAOE,CAAA,GAEpBF,CACT;EAAA;ECfaoK,EAAA,GAAQ;IAOnB,SAAAxJ,EAAYA,CAAA;MACV,KAAKyJ,OAAA,GAAUT,EAAA,CAAahJ,CAAA,GAG5B,KAAKyJ,OAAA,CAAQC,WAAA,CAAYpL,QAAA,CAASqL,cAAA,CAAe,MAEjD,KAAKC,KAAA,GDKe,UAAC5J,CAAA;QACvB,IAAIA,CAAA,CAAI4J,KAAA,EACN,OAAO5J,CAAA,CAAI4J,KAAA;QAKb,KADQ,IAAA1K,CAAA,GAAgBZ,QAAA,CAAQuL,WAAA,EACvB1K,CAAA,GAAI,GAAGC,CAAA,GAAIF,CAAA,CAAYU,MAAA,EAAQT,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;UAClD,IAAMc,CAAA,GAAQf,CAAA,CAAYC,CAAA;UAC1B,IAAIc,CAAA,CAAM6J,SAAA,KAAc9J,CAAA,EACtB,OAAOC,CAEV;QAAA;QAED,MAAM+F,EAAA,CAAY,GACpB;MAAA,CCpBiB,CAAS,KAAKyD,OAAA,GAC3B,KAAK7J,MAAA,GAAS,CACf;IAAA;IA2BH,OAzBEI,CAAA,CAAAyD,SAAA,CAAAkD,UAAA,aAAW3G,CAAA,EAAed,CAAA;MACxB;QAGE,OAFA,KAAK0K,KAAA,CAAMjD,UAAA,CAAWzH,CAAA,EAAMc,CAAA,GAC5B,KAAKJ,MAAA,KACE,CACR;MAAA,CAAC,QAAOI,CAAA;QACP,QAAO,CACR;MAAA;IAAA,GAGHA,CAAA,CAAUyD,SAAA,CAAAoD,UAAA,GAAV,UAAW7G,CAAA;MACT,KAAK4J,KAAA,CAAM/C,UAAA,CAAW7G,CAAA,GACtB,KAAKJ,MAAA;IAAA,GAGPI,CAAA,CAAOyD,SAAA,CAAAsD,OAAA,GAAP,UAAQ/G,CAAA;MACN,IAAMd,CAAA,GAAO,KAAK0K,KAAA,CAAMG,QAAA,CAAS/J,CAAA;MAGjC,OAAId,CAAA,IAAQA,CAAA,CAAK8K,OAAA,GACR9K,CAAA,CAAK8K,OAAA,GAEL;IAAA,GAGZhK,CAAD;EAAA;EAGaiK,EAAA,GAAO;IAKlB,SAAAjK,EAAYA,CAAA;MACV,KAAKyJ,OAAA,GAAUT,EAAA,CAAahJ,CAAA,GAC5B,KAAKkK,KAAA,GAAQ,KAAKT,OAAA,CAAQU,UAAA,EAC1B,KAAKvK,MAAA,GAAS,CACf;IAAA;IA0BH,OAxBEI,CAAA,CAAAyD,SAAA,CAAAkD,UAAA,aAAW3G,CAAA,EAAed,CAAA;MACxB,IAAIc,CAAA,IAAS,KAAKJ,MAAA,IAAUI,CAAA,IAAS,GAAG;QACtC,IAAMb,CAAA,GAAOb,QAAA,CAASqL,cAAA,CAAezK,CAAA;QAIrC,OAFA,KAAKuK,OAAA,CAAQF,YAAA,CAAapK,CAAA,EADV,KAAK+K,KAAA,CAAMlK,CAAA,KACgB,OAC3C,KAAKJ,MAAA,KACE,CACR;MAAA;MACC,QAAO;IAAA,GAIXI,CAAA,CAAUyD,SAAA,CAAAoD,UAAA,GAAV,UAAW7G,CAAA;MACT,KAAKyJ,OAAA,CAAQZ,WAAA,CAAY,KAAKqB,KAAA,CAAMlK,CAAA,IACpC,KAAKJ,MAAA;IAAA,GAGPI,CAAA,CAAOyD,SAAA,CAAAsD,OAAA,GAAP,UAAQ/G,CAAA;MACN,OAAIA,CAAA,GAAQ,KAAKJ,MAAA,GACR,KAAKsK,KAAA,CAAMlK,CAAA,EAAOmI,WAAA,GAElB;IAAA,GAGZnI,CAAD;EAAA;EAGaoK,EAAA,GAAU;IAKrB,SAAApK,EAAYA,CAAA;MACV,KAAKqK,KAAA,GAAQ,IACb,KAAKzK,MAAA,GAAS,CACf;IAAA;IAwBH,OAtBEI,CAAA,CAAAyD,SAAA,CAAAkD,UAAA,aAAW3G,CAAA,EAAed,CAAA;MACxB,OAAIc,CAAA,IAAS,KAAKJ,MAAA,KAChB,KAAKyK,KAAA,CAAMC,MAAA,CAAOtK,CAAA,EAAO,GAAGd,CAAA,GAC5B,KAAKU,MAAA,KACE;IAAA,GAMXI,CAAA,CAAUyD,SAAA,CAAAoD,UAAA,GAAV,UAAW7G,CAAA;MACT,KAAKqK,KAAA,CAAMC,MAAA,CAAOtK,CAAA,EAAO,IACzB,KAAKJ,MAAA;IAAA,GAGPI,CAAA,CAAOyD,SAAA,CAAAsD,OAAA,GAAP,UAAQ/G,CAAA;MACN,OAAIA,CAAA,GAAQ,KAAKJ,MAAA,GACR,KAAKyK,KAAA,CAAMrK,CAAA,IAEX;IAAA,GAGZA,CAAD;EAAA;ECxHIuK,EAAA,GAAmBnM,CAAA;EAajBoM,EAAA,GAA+B;IACnCC,QAAA,GAAWrM,CAAA;IACXsM,iBAAA,GAAoBnM;EAAA;EAItBoM,EAAA;IAYE,SAAA3K,EACEA,CAAA,EACAb,CAAA,EACAC,CAAA;MAAA,KAFA,MAAAY,CAAA,KAAAA,CAAA,GAAgCS,CAAA,QAChC,MAAAtB,CAAA,KAAAA,CAAA,GAA4C;MAF9C,IAqBCc,CAAA;MAhBC,KAAK2K,OAAA,GAAO1L,CAAA,CAAAA,CAAA,KACPsL,EAAA,GACAxK,CAAA,GAGL,KAAK6K,EAAA,GAAK1L,CAAA,EACV,KAAK2L,KAAA,GAAQ,IAAI5D,GAAA,CAAI9H,CAAA,GACrB,KAAK2L,MAAA,KAAW/K,CAAA,CAAQyK,QAAA,GAGnB,KAAKM,MAAA,IAAU3M,CAAA,IAAcmM,EAAA,KAChCA,EAAA,IAAmB,GACnB/B,EAAA,CAAe,QAGjB9C,EAAA,CAAY,MAAM;QAAM,OJtDD,UAAC1F,CAAA;UAK1B,KAJA,IAAMd,CAAA,GAAMc,CAAA,CAAMuI,MAAA,IACVpJ,CAAA,GAAWD,CAAA,CAAGU,MAAA,EAElBR,CAAA,GAAM,IAAAa,CAAA,YAAAA,CACDd,CAAA;cACP,IAAMc,CAAA,GDqBmB,UAACD,CAAA;gBAC5B,OAAOmH,EAAA,CAAgBI,GAAA,CAAIvH,CAAA,CAC7B;cAAA,CCvBe,CAAcb,CAAA;cACzB,SAAW,MAAPc,CAAA,EAA2B;cAE/B,IAAMX,CAAA,GAAQU,CAAA,CAAM8K,KAAA,CAAMvD,GAAA,CAAItH,CAAA;gBACxBV,CAAA,GAAQL,CAAA,CAAI4H,QAAA,CAAS3H,CAAA;cAC3B,SAAc,MAAVG,CAAA,KAAwBA,CAAA,CAAM0L,IAAA,IAAyB,MAAjBzL,CAAA,CAAMK,MAAA,EAAuB;cAEvE,IAAMF,CAAA,GAAW,GAAGL,MAAA,CAAA1B,CAAA,QAAA0B,MAAA,CAAYF,CAAA,EAAK,SAAAE,MAAA,CAAQY,CAAA,EAAE;gBAE3C8E,CAAA,GAAU;cAAA,KACA,MAAVzF,CAAA,IACFA,CAAA,CAAMyG,OAAA,CAAQ,UAAA/F,CAAA;gBACRA,CAAA,CAAKJ,MAAA,GAAS,MAChBmF,CAAA,IAAW,GAAA1F,MAAA,CAAGW,CAAA,EAAI,KAEtB;cAAA,IAKFZ,CAAA,IAAO,GAAGC,MAAA,CAAAE,CAAA,EAAQF,MAAA,CAAAK,CAAA,gBAAAL,MAAA,CAAqB0F,CAAA,EAAO,MAAA1F,MAAA,CAAKlB,CAAA;YAAA,GArB5CmB,CAAA,GAAQ,GAAGA,CAAA,GAAQH,CAAA,EAAQG,CAAA,IAAAW,CAAA,CAA3BX,CAAA;UAwBT,OAAOF,CACT;QAAA,CIwB4B,CAAYa,CAAA,CAAK;MAAA,EAC1C;IAAA;IAoEH,OA7FSD,CAAA,CAAUiL,UAAA,GAAjB,UAAkBjL,CAAA;MAChB,OAAOqH,EAAA,CAAcrH,CAAA;IAAA,GA0BvBA,CAAA,CAAAyD,SAAA,CAAAyH,SAAA;MAAA,CACO,KAAKH,MAAA,IAAU3M,CAAA,IAClBoK,EAAA,CAAe;IAAA,GAInBxI,CAAA,CAAAyD,SAAA,CAAA0H,sBAAA,aAAuBhM,CAAA,EAA+BC,CAAA;MACpD,YADoD,MAAAA,CAAA,KAAAA,CAAA,IAAgB,IAC7D,IAAIY,CAAA,CACJd,CAAA,CAAAA,CAAA,UAAK0L,OAAA,GAAYzL,CAAA,GACtB,KAAK0L,EAAA,EACJzL,CAAA,IAAa,KAAK0L,KAAA,SAAU;IAAA,GAIjC9K,CAAA,CAAkByD,SAAA,CAAA2H,kBAAA,GAAlB,UAAmBpL,CAAA;MACjB,OAAQ,KAAK6K,EAAA,CAAG7K,CAAA,KAAO,KAAK6K,EAAA,CAAG7K,CAAA,KAAO,KAAK;IAAA,GAI7CA,CAAA,CAAAyD,SAAA,CAAA8E,MAAA;MACE,OAAO,KAAKhC,GAAA,KAAQ,KAAKA,GAAA,IN/EEvG,CAAA,GKAR,UAACA,CAAA;QAAE,IAAUd,CAAA,GAAiBc,CAAA,CAAA0K,iBAAA;UAAEvL,CAAA,GAAMa,CAAA,CAAAqL,MAAA;QAC3D,OAAArL,CAAA,CAAAyK,QAAA,GACS,IAAIL,EAAA,CAAWjL,CAAA,IACbD,CAAA,GACF,IAAIsK,EAAA,CAASrK,CAAA,IAEb,IAAI8K,EAAA,CAAQ9K,CAAA,CAEvB;MAAA,CCuEkD,CAAQ,KAAKyL,OAAA,GN9EtD,IAAIxE,EAAA,CAAkBpG,CAAA;MADD,IAACA,CAAA;IAAA,GMmF7BA,CAAA,CAAAyD,SAAA,CAAA6H,YAAA,aAAatL,CAAA,EAAYd,CAAA;MACvB,OAAO,KAAK4L,KAAA,CAAM5K,GAAA,CAAIF,CAAA,KAAQ,KAAK8K,KAAA,CAAMvD,GAAA,CAAIvH,CAAA,EAAYE,GAAA,CAAIhB,CAAA;IAAA,GAI/Dc,CAAA,CAAAyD,SAAA,CAAAuE,YAAA,aAAahI,CAAA,EAAYd,CAAA;MAGvB,IAFAmI,EAAA,CAAcrH,CAAA,GAET,KAAK8K,KAAA,CAAM5K,GAAA,CAAIF,CAAA,GAKjB,KAAK8K,KAAA,CAAMvD,GAAA,CAAIvH,CAAA,EAAYI,GAAA,CAAIlB,CAAA,OALT;QACvB,IAAMC,CAAA,GAAa,IAAIJ,GAAA;QACvBI,CAAA,CAAWiB,GAAA,CAAIlB,CAAA,GACf,KAAK4L,KAAA,CAAMpE,GAAA,CAAI1G,CAAA,EAAIb,CAAA,CACpB;MAAA;IAAA,GAMHa,CAAA,CAAAyD,SAAA,CAAAgD,WAAA,aAAYzG,CAAA,EAAYd,CAAA,EAAcC,CAAA;MACpC,KAAK6I,YAAA,CAAahI,CAAA,EAAId,CAAA,GACtB,KAAKqJ,MAAA,GAAS9B,WAAA,CAAYY,EAAA,CAAcrH,CAAA,GAAKb,CAAA;IAAA,GAI/Ca,CAAA,CAAUyD,SAAA,CAAA8H,UAAA,GAAV,UAAWvL,CAAA;MACL,KAAK8K,KAAA,CAAM5K,GAAA,CAAIF,CAAA,KAChB,KAAK8K,KAAA,CAAMvD,GAAA,CAAIvH,CAAA,EAAYwL,KAAA;IAAA,GAKhCxL,CAAA,CAAUyD,SAAA,CAAAgI,UAAA,GAAV,UAAWzL,CAAA;MACT,KAAKuI,MAAA,GAAS3B,UAAA,CAAWS,EAAA,CAAcrH,CAAA,IACvC,KAAKuL,UAAA,CAAWvL,CAAA;IAAA,GAIlBA,CAAA,CAAAyD,SAAA,CAAAiI,QAAA;MAGE,KAAKnF,GAAA,QAAM;IAAA,GAEdvG,CAAD;EAAA;EC5HM2L,EAAA,GAAY;EACZC,EAAA,GAAgB;AAWtB,SAASC,GAAuB7L,CAAA,EAA4Bd,CAAA;EAC1D,OAAOc,CAAA,CAAS8L,GAAA,CAAI,UAAA9L,CAAA;IAclB,OAbkB,WAAdA,CAAA,CAAKuD,IAAA,KAEPvD,CAAA,CAAK2F,KAAA,GAAQ,GAAGtG,MAAA,CAAAH,CAAA,OAAAG,MAAA,CAAaW,CAAA,CAAK2F,KAAA,GAElC3F,CAAA,CAAK2F,KAAA,GAAQ3F,CAAA,CAAK2F,KAAA,CAAMoG,UAAA,CAAW,KAAK,IAAA1M,MAAA,CAAIH,CAAA,EAAS,OACrDc,CAAA,CAAKgM,KAAA,GAAShM,CAAA,CAAKgM,KAAA,CAAmBF,GAAA,CAAI,UAAA9L,CAAA;MACxC,OAAO,GAAGX,MAAA,CAAAH,CAAA,EAAa,KAAAG,MAAA,CAAAW,CAAA,CACzB;IAAA,KAGEwF,KAAA,CAAMC,OAAA,CAAQzF,CAAA,CAAKiM,QAAA,KAA2B,iBAAdjM,CAAA,CAAKuD,IAAA,KACvCvD,CAAA,CAAKiM,QAAA,GAAWJ,EAAA,CAAuB7L,CAAA,CAAKiM,QAAA,EAAU/M,CAAA,IAEjDc,CACT;EAAA,EACF;AAAA;AAEwB,SAAAkM,GACtBlM,CAAA;EAAA,IAKId,CAAA;IACAC,CAAA;IACAC,CAAA;IAPJa,CAAA,cAAAD,CAAA,GAG2BS,CAAA,GAAsBT,CAAA;IAF/CV,CAAA,GAAAW,CAAA,CAAA2K,OAAA;IAAArL,CAAA,QAAO,MAAAD,CAAA,GAAGmB,CAAA,GAAsBnB,CAAA;IAChCI,CAAA,GAAuDO,CAAA,CAAAkM,OAAA;IAAvDpH,CAAA,QAAO,MAAArF,CAAA,GAAGY,CAAA,GAA6CZ,CAAA;IAOnDsF,CAAA,GAAwB,SAAAoH,CAACpM,CAAA,EAAeZ,CAAA,EAAgBa,CAAA;MAC5D,OAKEA,CAAA,CAAOoM,UAAA,CAAWlN,CAAA,KAClBc,CAAA,CAAOqM,QAAA,CAASnN,CAAA,KAChBc,CAAA,CAAO8L,UAAA,CAAW5M,CAAA,EAAW,IAAIS,MAAA,GAAS,IAEnC,IAAAP,MAAA,CAAIH,CAAA,IAGNc,CACT;IAAA;IAuBMsI,CAAA,GAAcvD,CAAA,CAAQwH,KAAA;EAE5BjE,CAAA,CAAYxC,IAAA,CAX8C,UAAA9F,CAAA;IACpDA,CAAA,CAAQuD,IAAA,KAASiJ,CAAA,CAAOC,OAAA,IAAWzM,CAAA,CAAQ2F,KAAA,CAAM+G,QAAA,CAAS,SAC3D1M,CAAA,CAAQgM,KAAA,CAAmB,KAAKhM,CAAA,CAAQgM,KAAA,CAAM,GAE5ChL,OAAA,CAAQ2K,EAAA,EAAWxM,CAAA,EACnB6B,OAAA,CAAQ5B,CAAA,EAAiB4F,CAAA,EAEhC;EAAA,IASIzF,CAAA,CAAQoN,MAAA,IACVrE,CAAA,CAAYxC,IAAA,CAAK0G,CAAA,CAAOI,QAAA,GAG1BtE,CAAA,CAAYxC,IAAA,CAAK0G,CAAA,CAAOK,SAAA;EAExB,IAAMC,CAAA,GAA8B,SAAAC,CAClC/M,CAAA,EACAC,CAAA,EAIAX,CAAA,EACAI,CAAA;IAAA,KALA,MAAAO,CAAA,KAAAA,CAAA,GAAa,UAIb,MAAAX,CAAA,KAAAA,CAAA,GAAW,UACX,MAAAI,CAAA,KAAAA,CAAA,GAAiB,MAKjBR,CAAA,GAAeQ,CAAA,EACfP,CAAA,GAAYc,CAAA,EACZb,CAAA,GAAkB,IAAIwI,MAAA,CAAO,KAAAvI,MAAA,CAAKF,CAAA,EAAc,QAAE;IAElD,IAAM4F,CAAA,GAAU/E,CAAA,CAAIgB,OAAA,CAAQ4K,EAAA,EAAe;MACvC5G,CAAA,GAAWwH,CAAA,CAAOQ,OAAA,CACpB1N,CAAA,IAAUW,CAAA,GAAW,GAAAZ,MAAA,CAAGC,CAAA,EAAM,KAAAD,MAAA,CAAIY,CAAA,EAAQ,OAAAZ,MAAA,CAAM0F,CAAA,EAAO,QAAOA,CAAA;IAG5DxF,CAAA,CAAQ0N,SAAA,KACVjI,CAAA,GAAW6G,EAAA,CAAuB7G,CAAA,EAAUzF,CAAA,CAAQ0N,SAAA;IAGtD,IAAMH,CAAA,GAAkB;IAOxB,OALAN,CAAA,CAAOU,SAAA,CACLlI,CAAA,EACAwH,CAAA,CAAOW,UAAA,CAAW7E,CAAA,CAAYjJ,MAAA,CAAOmN,CAAA,CAAOY,SAAA,CAAU,UAAApN,CAAA;MAAS,OAAA8M,CAAA,CAAMhH,IAAA,CAAK9F,CAAA,CAAM;IAAA,OAG3E8M,CACT;EAAA;EAcA,OAZAA,CAAA,CAAe9K,IAAA,GAAO+C,CAAA,CAAQnF,MAAA,GAC1BmF,CAAA,CACGsI,MAAA,CAAO,UAACrN,CAAA,EAAKd,CAAA;IAKZ,OAJKA,CAAA,CAAOkD,IAAA,IACV4D,EAAA,CAAiB,KAGZpE,CAAA,CAAM5B,CAAA,EAAKd,CAAA,CAAOkD,IAAA,CAC1B;EAAA,GAAET,CAAA,EACF2L,QAAA,KACH,IAEGR,CACT;AAAA;AC1IO,IAAMS,EAAA,GAAwB,IAAI5C,EAAA;EAC5B6C,EAAA,GAA0BtB,EAAA;EAQ1BuB,EAAA,GAAoBrO,CAAA,CAAMsO,aAAA,CAAkC;IACvEC,iBAAA,OAAmB;IACnBC,UAAA,EAAYL,EAAA;IACZM,MAAA,EAAQL;EAAA;EAGGM,EAAA,GAAqBL,EAAA,CAAkBM,QAAA;EAGvCC,EAAA,GAAgB5O,CAAA,CAAMsO,aAAA,MAA8B;AAAA,SAGjDO,GAAA;EACd,OAAOlJ,CAAA,CAAW0I,EAAA,CACpB;AAAA;AAkDM,SAAUS,GAAkBlO,CAAA;EAC1B,IAAAd,CAAA,GAAwBI,CAAA,CAASU,CAAA,CAAMmO,aAAA;IAAtChP,CAAA,GAAOD,CAAA;IAAEe,CAAA,GAAAf,CAAA;IACR6F,CAAA,GAAekJ,EAAA,GAAAL,UAAA;IAEjB5I,CAAA,GAAqBzF,CAAA,CAAQ;MACjC,IAAIL,CAAA,GAAQ6F,CAAA;MAYZ,OAVI/E,CAAA,CAAM4J,KAAA,GACR1K,CAAA,GAAQc,CAAA,CAAM4J,KAAA,GACL5J,CAAA,CAAMqL,MAAA,KACfnM,CAAA,GAAQA,CAAA,CAAMiM,sBAAA,CAAuB;QAAEE,MAAA,EAAQrL,CAAA,CAAMqL;MAAA,IAAU,KAG7DrL,CAAA,CAAMoO,qBAAA,KACRlP,CAAA,GAAQA,CAAA,CAAMiM,sBAAA,CAAuB;QAAET,iBAAA,GAAmB;MAAA,KAGrDxL,CACT;IAAA,GAAG,CAACc,CAAA,CAAMoO,qBAAA,EAAuBpO,CAAA,CAAM4J,KAAA,EAAO5J,CAAA,CAAMqL,MAAA,EAAQtG,CAAA;IAEtDuD,CAAA,GAAS/I,CAAA,CACb;MACE,OAAA2M,EAAA,CAAqB;QACnBtB,OAAA,EAAS;UAAEqC,SAAA,EAAWjN,CAAA,CAAMiN,SAAA;UAAWN,MAAA,EAAQ3M,CAAA,CAAMqO;QAAA;QACrDlC,OAAA,EAAOhN;MAAA,EAFT;IAAA,GAIF,CAACa,CAAA,CAAMqO,oBAAA,EAAsBrO,CAAA,CAAMiN,SAAA,EAAW9N,CAAA;EAGhDO,CAAA,CAAU;IACHoN,CAAA,CAAa3N,CAAA,EAASa,CAAA,CAAMmO,aAAA,KAAgBlO,CAAA,CAAWD,CAAA,CAAMmO,aAAA,CACpE;EAAA,GAAG,CAACnO,CAAA,CAAMmO,aAAA;EAEV,IAAM3B,CAAA,GAAyBjN,CAAA,CAC7B;IAAM,OAAC;MACLoO,iBAAA,EAAmB3N,CAAA,CAAM2N,iBAAA;MACzBC,UAAA,EAAY5I,CAAA;MACZ6I,MAAA,EAAMvF;IAAA,CAHF;EAAA,GAKN,CAACtI,CAAA,CAAM2N,iBAAA,EAAmB3I,CAAA,EAAoBsD,CAAA;EAGhD,OACElJ,CAAA,CAAA+J,aAAA,CAACsE,EAAA,CAAkBa,QAAA,EAAS;IAAA3I,KAAA,EAAO6G;EAAA,GACjCpN,CAAA,CAAA+J,aAAA,CAAC6E,EAAA,CAAcM,QAAA,EAAQ;IAAC3I,KAAA,EAAO2C;EAAA,GAAStI,CAAA,CAAMiM,QAAA,EAGpD;AAAA;ACzHA,IAAAsC,EAAA;IAKE,SAAYvO,GAAA,EAAcd,CAAA;MAA1B,IAQCC,CAAA;MAED,KAAAqP,MAAA,GAAS,UAACxO,CAAA,EAAwBd,CAAA;QAAA,WAAAA,CAAA,KAAAA,CAAA,GAAwCsO,EAAA;QACxE,IAAMpO,CAAA,GAAeD,CAAA,CAAKiD,IAAA,GAAOlD,CAAA,CAAe8C,IAAA;QAE3ChC,CAAA,CAAWsL,YAAA,CAAanM,CAAA,CAAKsP,EAAA,EAAIrP,CAAA,KACpCY,CAAA,CAAWyG,WAAA,CACTtH,CAAA,CAAKsP,EAAA,EACLrP,CAAA,EACAF,CAAA,CAAeC,CAAA,CAAKkL,KAAA,EAAOjL,CAAA,EAAc,cAG/C;MAAA,GAnBE,KAAKgD,IAAA,GAAOpC,CAAA,EACZ,KAAKyO,EAAA,GAAK,gBAAgBpP,MAAA,CAAAW,CAAA,GAC1B,KAAKqK,KAAA,GAAQnL,CAAA,EAEbwG,EAAA,CAAY,MAAM;QAChB,MAAMM,EAAA,CAAY,IAAI3E,MAAA,CAAOlC,CAAA,CAAKiD,IAAA,EACpC;MAAA,EACD;IAAA;IAiBH,OAHEpC,CAAA,CAAOyD,SAAA,CAAAiL,OAAA,GAAP,UAAQ1O,CAAA;MACN,YADM,MAAAA,CAAA,KAAAA,CAAA,GAAwCwN,EAAA,GACvC,KAAKpL,IAAA,GAAOpC,CAAA,CAAegC,IAAA;IAAA,GAErChC,CAAD;EAAA;ECpCM2O,EAAA,GAAU,SAAAC,CAAC5O,CAAA;IAAc,OAAAA,CAAA,IAAK,OAAOA,CAAA,IAAK;EAAA;AAexB,SAAA6O,GAAmB7O,CAAA;EAGzC,KAFA,IAAId,CAAA,GAAS,IAEJC,CAAA,GAAI,GAAGA,CAAA,GAAIa,CAAA,CAAOJ,MAAA,EAAQT,CAAA,IAAK;IACtC,IAAMC,CAAA,GAAIY,CAAA,CAAOb,CAAA;IAEjB,IAAU,MAANA,CAAA,IAAiB,QAANC,CAAA,IAA2B,QAAdY,CAAA,CAAO,IACjC,OAAOA,CAAA;IAGL2O,EAAA,CAAQvP,CAAA,IACVF,CAAA,IAAU,MAAME,CAAA,CAAEmD,WAAA,KAElBrD,CAAA,IAAUE,CAEb;EAAA;EAED,OAAOF,CAAA,CAAOmN,UAAA,CAAW,SAAS,MAAMnN,CAAA,GAASA,CACnD;AAAA;ACTA,IAAM4P,EAAA,GAAY,SAAAC,CAAC/O,CAAA;IACjB,eAAAA,CAAA,KAAmD,MAAVA,CAAA,IAA6B,OAAVA,CAA5D;EAAA;EAEWgP,EAAA,GAAgB,SAAAC,CAAC/P,CAAA;IAC5B,ICzBsCC,CAAA;MAAcC,CAAA;MDyB9Ca,CAAA,GAAQ;IAEd,KAAK,IAAMX,CAAA,IAAOJ,CAAA,EAAK;MACrB,IAAMK,CAAA,GAAML,CAAA,CAAII,CAAA;MACXJ,CAAA,CAAIgQ,cAAA,CAAe5P,CAAA,MAAQwP,EAAA,CAAUvP,CAAA,MAGrCiG,KAAA,CAAMC,OAAA,CAAQlG,CAAA,KAAQA,CAAA,CAAI4P,KAAA,IAAUlK,EAAA,CAAW1F,CAAA,IAClDU,CAAA,CAAM6F,IAAA,CAAK,GAAAzG,MAAA,CAAGwP,EAAA,CAAUvP,CAAA,GAAI,MAAKC,CAAA,EAAK,OAC7B8F,EAAA,CAAc9F,CAAA,IACvBU,CAAA,CAAM6F,IAAA,CAAN/F,KAAA,CAAAE,CAAA,EAAAD,CAAA,CAAAA,CAAA,EAAW,GAAGX,MAAA,CAAAC,CAAA,EAAO,QAAK0P,EAAA,CAAczP,CAAA,IAAI,KAAE,OAAK,MAEnDU,CAAA,CAAM6F,IAAA,CAAK,GAAGzG,MAAA,CAAAwP,EAAA,CAAUvP,CAAA,GAAS,MAAAD,MAAA,ECrCCF,CAAA,GDqCeG,CAAA,ECnCxC,SAFuCF,CAAA,GDqCMG,CAAA,KCnCpB,oBAAVH,CAAA,IAAiC,OAAVA,CAAA,GAC1C,KAGY,mBAAVA,CAAA,IAAgC,MAAVA,CAAA,IAAiBD,CAAA,IAAQiQ,CAAA,IAAcjQ,CAAA,CAAKkN,UAAA,CAAW,QAIjFhL,MAAA,CAAOjC,CAAA,EAAO+G,IAAA,KAHZ,GAAG9G,MAAA,CAAAD,CAAA,EAAS,QD8ByC,MAE7D;IAAA;IAED,OAAOa,CACT;EAAA;AAEc,SAAUoP,GACtBrP,CAAA,EACAd,CAAA,EACAC,CAAA,EACAC,CAAA;EAEA,IAAI0P,EAAA,CAAU9O,CAAA,GACZ,OAAO;EAIT,IAAIkF,EAAA,CAAkBlF,CAAA,GACpB,OAAO,CAAC,IAAKX,MAAA,CAAAW,CAAA,CAAkDsP,iBAAA;EAIjE,IAAIrK,EAAA,CAAWjF,CAAA,GAAQ;IACrB,KE7DKiF,EAAA,CADmC3F,CAAA,GF8DhBU,CAAA,KE7DGV,CAAA,CAAKmE,SAAA,IAAanE,CAAA,CAAKmE,SAAA,CAAU8L,gBAAA,KF6D1BrQ,CAAA,EAoBhC,OAAO,CAACc,CAAA;IAnBR,IAAMC,CAAA,GAASD,CAAA,CAAMd,CAAA;IAiBrB,OAd2B,iBAAzBtB,OAAA,CAAQC,GAAA,CAAIc,QAAA,IACM,mBAAXsB,CAAA,IACNuF,KAAA,CAAMC,OAAA,CAAQxF,CAAA,KACbA,CAAA,YAAkBsO,EAAA,IACnBlJ,EAAA,CAAcpF,CAAA,KACJ,SAAXA,CAAA,IAEAT,OAAA,CAAQC,KAAA,CACN,GAAGJ,MAAA,CAAA6C,CAAA,CACDlC,CAAA,GACiL,sLAIhLqP,EAAA,CAAepP,CAAA,EAAQf,CAAA,EAAkBC,CAAA,EAAYC,CAAA,CAI/D;EAAA;EEpFqB,IAAoBE,CAAA;EFsF1C,OAAIU,CAAA,YAAiBuO,EAAA,GACfpP,CAAA,IACFa,CAAA,CAAMwO,MAAA,CAAOrP,CAAA,EAAYC,CAAA,GAClB,CAACY,CAAA,CAAM0O,OAAA,CAAQtP,CAAA,MAEf,CAACY,CAAA,IAKRqF,EAAA,CAAcrF,CAAA,IACTgP,EAAA,CAAchP,CAAA,IAGlBwF,KAAA,CAAMC,OAAA,CAAQzF,CAAA,IAUZwF,KAAA,CAAM/B,SAAA,CAAUpE,MAAA,CAAOU,KAAA,CAAMO,CAAA,EANrBN,CAAA,CAMwC8L,GAAA,CANjC,UAAA9L,CAAA;IACpB,OAAAqP,EAAA,CAAerP,CAAA,EAAUd,CAAA,EAAkBC,CAAA,EAAYC,CAAA,CAAvD;EAAA,MAJO,CAACY,CAAA,CAAMsN,QAAA,GAMlB;AAAA;AGzGwB,SAAAkC,GAAoCxP,CAAA;EAC1D,KAAK,IAAId,CAAA,GAAI,GAAGA,CAAA,GAAIc,CAAA,CAAMJ,MAAA,EAAQV,CAAA,IAAK,GAAG;IACxC,IAAMC,CAAA,GAAOa,CAAA,CAAMd,CAAA;IAEnB,IAAI+F,EAAA,CAAW9F,CAAA,MAAU+F,EAAA,CAAkB/F,CAAA,GAGzC,QAAO,CAEV;EAAA;EAED,QAAO,CACT;AAAA;ACPA,IAAMsQ,EAAA,GAAO1N,CAAA,CAAK7D,CAAA;EAKlBwR,EAAA;IAQE,SAAA1P,EAAYA,CAAA,EAAqBd,CAAA,EAAqBC,CAAA;MACpD,KAAKkL,KAAA,GAAQrK,CAAA,EACb,KAAK2P,aAAA,GAAgB,IACrB,KAAKC,QAAA,GACsB,iBAAzBhS,OAAA,CAAQC,GAAA,CAAIc,QAAA,UACG,MAAdQ,CAAA,IAA2BA,CAAA,CAAUyQ,QAAA,KACtCJ,EAAA,CAAcxP,CAAA,GAChB,KAAK6P,WAAA,GAAc3Q,CAAA,EACnB,KAAK4Q,QAAA,GAAWlO,CAAA,CAAM6N,EAAA,EAAMvQ,CAAA,GAC5B,KAAK6Q,SAAA,GAAY5Q,CAAA,EAIjBwL,EAAA,CAAWM,UAAA,CAAW/L,CAAA,CACvB;IAAA;IAmEH,OAjEEc,CAAA,CAAAyD,SAAA,CAAAuM,uBAAA,aACEhQ,CAAA,EACAd,CAAA,EACAC,CAAA;MAEA,IAAIC,CAAA,GAAQ,KAAK2Q,SAAA,GACb,KAAKA,SAAA,CAAUC,uBAAA,CAAwBhQ,CAAA,EAAkBd,CAAA,EAAYC,CAAA,IACrE;MAGJ,IAAI,KAAKyQ,QAAA,KAAazQ,CAAA,CAAO6C,IAAA;QAC3B,IAAI,KAAK2N,aAAA,IAAiBzQ,CAAA,CAAWoM,YAAA,CAAa,KAAKuE,WAAA,EAAa,KAAKF,aAAA,GACvEvQ,CAAA,GAAQ+F,EAAA,CAAY/F,CAAA,EAAO,KAAKuQ,aAAA,OAC3B;UACL,IAAM1P,CAAA,GAAYmF,EAAA,CAChBiK,EAAA,CAAQ,KAAKhF,KAAA,EAAOrK,CAAA,EAAkBd,CAAA,EAAYC,CAAA;YAE9CG,CAAA,GAAOiC,CAAA,CAAaK,CAAA,CAAM,KAAKkO,QAAA,EAAU7P,CAAA,MAAe;UAE9D,KAAKf,CAAA,CAAWoM,YAAA,CAAa,KAAKuE,WAAA,EAAavQ,CAAA,GAAO;YACpD,IAAMC,CAAA,GAAqBJ,CAAA,CAAOc,CAAA,EAAW,IAAIZ,MAAA,CAAAC,CAAA,QAAQ,GAAW,KAAKuQ,WAAA;YACzE3Q,CAAA,CAAWuH,WAAA,CAAY,KAAKoJ,WAAA,EAAavQ,CAAA,EAAMC,CAAA,CAChD;UAAA;UAEDH,CAAA,GAAQ+F,EAAA,CAAY/F,CAAA,EAAOE,CAAA,GAC3B,KAAKqQ,aAAA,GAAgBrQ,CACtB;QAAA;MAAA,OACI;QAIL,KAHA,IAAII,CAAA,GAAckC,CAAA,CAAM,KAAKkO,QAAA,EAAU3Q,CAAA,CAAO6C,IAAA,GAC1C+C,CAAA,GAAM,IAEDC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKqF,KAAA,CAAMzK,MAAA,EAAQoF,CAAA,IAAK;UAC1C,IAAMsD,CAAA,GAAW,KAAK+B,KAAA,CAAMrF,CAAA;UAE5B,IAAwB,mBAAbsD,CAAA,EACTvD,CAAA,IAAOuD,CAAA,EAEsB,iBAAzB1K,OAAA,CAAQC,GAAA,CAAIc,QAAA,KAA2Be,CAAA,GAAckC,CAAA,CAAMlC,CAAA,EAAa4I,CAAA,QACvE,IAAIA,CAAA,EAAU;YACnB,IAAMwE,CAAA,GAAa1H,EAAA,CACjBiK,EAAA,CAAQ/G,CAAA,EAAUtI,CAAA,EAAkBd,CAAA,EAAYC,CAAA;YAGlDO,CAAA,GAAckC,CAAA,CAAMlC,CAAA,EAAaoN,CAAA,GAAa9H,CAAA,GAC9CD,CAAA,IAAO+H,CACR;UAAA;QACF;QAED,IAAI/H,CAAA,EAAK;UACP,IAAMyH,CAAA,GAAOjL,CAAA,CAAa7B,CAAA,KAAgB;UAErCR,CAAA,CAAWoM,YAAA,CAAa,KAAKuE,WAAA,EAAarD,CAAA,KAC7CtN,CAAA,CAAWuH,WAAA,CACT,KAAKoJ,WAAA,EACLrD,CAAA,EACArN,CAAA,CAAO4F,CAAA,EAAK,IAAI1F,MAAA,CAAAmN,CAAA,QAAQ,GAAW,KAAKqD,WAAA,IAI5CzQ,CAAA,GAAQ+F,EAAA,CAAY/F,CAAA,EAAOoN,CAAA,CAC5B;QAAA;MACF;MAED,OAAOpN,CAAA;IAAA,GAEVY,CAAD;EAAA;EC/DaiQ,EAAA,GAAe7Q,CAAA,CAAMsO,aAAA,MAAwC;EAE7DwC,EAAA,GAAgBD,EAAA,CAAalC,QAAA;AAAA,SAmC1BoC,GAAA;EACd,IAAMnQ,CAAA,GAAQ+E,CAAA,CAAWkL,EAAA;EAEzB,KAAKjQ,CAAA,EACH,MAAMgG,EAAA,CAAY;EAGpB,OAAOhG,CACT;AAAA;AAKwB,SAAAoQ,GAAcpQ,CAAA;EACpC,IAAMb,CAAA,GAAaC,CAAA,CAAMiR,UAAA,CAAWJ,EAAA;IAC9BhQ,CAAA,GAAeV,CAAA,CACnB;MAAM,OAjDV,UAAoBS,CAAA,EAAsBb,CAAA;QACxC,KAAKa,CAAA,EACH,MAAMgG,EAAA,CAAY;QAGpB,IAAIf,EAAA,CAAWjF,CAAA,GAAQ;UACrB,IACMZ,CAAA,GADUY,CAAA,CACYb,CAAA;UAE5B,IAC2B,iBAAzBvB,OAAA,CAAQC,GAAA,CAAIc,QAAA,KACK,SAAhBS,CAAA,IAAwBoG,KAAA,CAAMC,OAAA,CAAQrG,CAAA,KAAuC,mBAAhBA,CAAA,GAE9D,MAAM4G,EAAA,CAAY;UAGpB,OAAO5G,CACR;QAAA;QAED,IAAIoG,KAAA,CAAMC,OAAA,CAAQzF,CAAA,KAA2B,mBAAVA,CAAA,EACjC,MAAMgG,EAAA,CAAY;QAGpB,OAAO7G,CAAA,GAAkBD,CAAA,CAAAA,CAAA,KAAAC,CAAA,GAAea,CAAA,IAAUA,CACpD;MAAA,CAyBU,CAAWA,CAAA,CAAMW,KAAA,EAAOxB,CAAA,CAAW;IAAA,GACzC,CAACa,CAAA,CAAMW,KAAA,EAAOxB,CAAA;EAGhB,OAAKa,CAAA,CAAMiM,QAAA,GAIJ7M,CAAA,CAAC+J,aAAA,CAAA8G,EAAA,CAAa3B,QAAA,EAAS;IAAA3I,KAAA,EAAO1F;EAAA,GAAeD,CAAA,CAAMiM,QAAA,IAHjD,IAIX;AAAA;ACjEA,IAAMqE,EAAA,GAAyC;EAyE3CC,EAAA,GAAmB,IAAIxR,GAAA;AA0F3B,SAASyR,GAKPxQ,CAAA,EACAC,CAAA,EACAX,CAAA;EAEA,IAAMC,CAAA,GAAqB2F,EAAA,CAAkBlF,CAAA;IACvCN,CAAA,GAAwBM,CAAA;IACxB+E,CAAA,IAAwB1C,CAAA,CAAMrC,CAAA;IAGlC8M,CAAA,GAGE7M,CAAA,CAAOwQ,KAAA;IAHTjE,CAAA,cAAAM,CAAA,GAAQxM,CAAA,GAAWwM,CAAA;IACnBsC,CAAA,GAEEnP,CAAA,CAFsE4P,WAAA;IAAxElS,CAAA,QAAc,MAAAyR,CAAA,GA/KlB,UACEpP,CAAA,EACAd,CAAA;MAEA,IAAMC,CAAA,GAA8B,mBAAhBa,CAAA,GAA2B,OAAOe,CAAA,CAAOf,CAAA;MAE7DsQ,EAAA,CAAYnR,CAAA,KAASmR,EAAA,CAAYnR,CAAA,KAAS,KAAK;MAE/C,IAAMC,CAAA,GAAc,GAAGC,MAAA,CAAAF,CAAA,OAAAE,MAAA,CAAQ4C,CAAA,CAG7B/D,CAAA,GAAaiB,CAAA,GAAOmR,EAAA,CAAYnR,CAAA;MAGlC,OAAOD,CAAA,GAAoB,GAAGG,MAAA,CAAAH,CAAA,EAAqB,KAAAG,MAAA,CAAAD,CAAA,IAAgBA,CACrE;IAAA,CAgKkB,CAAWa,CAAA,CAAQkC,WAAA,EAAalC,CAAA,CAAQyQ,iBAAA,IAAkBtB,CAAA;IACxEpR,CAAA,GACEiC,CAAA,CADuCkC,WAAA;IAAzClE,CAAA,QAAc,MAAAD,CAAA,GCpNM,UAAoBgC,CAAA;MAC1C,OAAOqC,CAAA,CAAMrC,CAAA,IAAU,UAAUX,MAAA,CAAAW,CAAA,IAAW,UAAUX,MAAA,CAAA6C,CAAA,CAAiBlC,CAAA,OACzE;IAAA,CDkNkB,CAAoBA,CAAA,IAAAhC,CAAA;IAG9BG,CAAA,GACJ8B,CAAA,CAAQkC,WAAA,IAAelC,CAAA,CAAQ4P,WAAA,GAC3B,GAAAxQ,MAAA,CAAG0B,CAAA,CAAOd,CAAA,CAAQkC,WAAA,GAAgB,KAAA9C,MAAA,CAAAY,CAAA,CAAQ4P,WAAA,IAC1C5P,CAAA,CAAQ4P,WAAA,IAAelS,CAAA;IAGvBS,CAAA,GACJmB,CAAA,IAAsBG,CAAA,CAAsB+Q,KAAA,GACxC/Q,CAAA,CAAsB+Q,KAAA,CAAMpR,MAAA,CAAOmN,CAAA,EAAyCmE,MAAA,CAAOnS,OAAA,IAClFgO,CAAA;IAEDjO,CAAA,GAAsB0B,CAAA,CAAO0N,iBAAA;EAEnC,IAAIpO,CAAA,IAAsBG,CAAA,CAAsBiO,iBAAA,EAAmB;IACjE,IAAM/O,CAAA,GAAsBc,CAAA,CAAsBiO,iBAAA;IAElD,IAAI1N,CAAA,CAAQ0N,iBAAA,EAAmB;MAC7B,IAAM9O,CAAA,GAA4BoB,CAAA,CAAQ0N,iBAAA;MAG1CpP,CAAA,GAAoB,SAAAoP,CAAC3N,CAAA,EAAMd,CAAA;QACzB,OAAAN,CAAA,CAAoBoB,CAAA,EAAMd,CAAA,KAC1BL,CAAA,CAA0BmB,CAAA,EAAMd,CAAA,CADhC;MAAA,CAEH;IAAA,OACCX,CAAA,GAAoBK,CAEvB;EAAA;EAED,IAAME,CAAA,GAAiB,IAAI4Q,EAAA,CACzBpQ,CAAA,EACAnB,CAAA,EACAoB,CAAA,GAAsBG,CAAA,CAAsBkR,cAAA,QAAoC;EAGlF,SAAS/P,EAAiBb,CAAA,EAAoCC,CAAA;IAC5D,OA9IJ,UACED,CAAA,EACAC,CAAA,EACAX,CAAA;MAGE,IAAOC,CAAA,GAMLS,CAAA,CAAkByQ,KAAA;QALpB/Q,CAAA,GAKEM,CAAA,CALY4Q,cAAA;QACd7L,CAAA,GAIE/E,CAAA,CAAkBiD,YAAA;QAHpB6J,CAAA,GAGE9M,CAAA,CAHgB6Q,kBAAA;QAClBrE,CAAA,GAEExM,CAAA,CAAkBsP,iBAAA;QADpBF,CAAA,GACEpP,CAAA,CAAAqL,MAAA;QAEE1N,CAAA,GAAeyB,CAAA,CAAMiR,UAAA,CAAWJ,EAAA;QAChCjS,CAAA,GAAMiQ,EAAA;QACNhQ,CAAA,GAAoB+B,CAAA,CAAmB2N,iBAAA,IAAqB3P,CAAA,CAAI2P,iBAAA;MAEzC,iBAAzB/P,OAAA,CAAQC,GAAA,CAAIc,QAAA,IAA2BqG,CAAA,CAAcwH,CAAA;MAKzD,IAAMtO,CAAA,GAAQwC,CAAA,CAAeT,CAAA,EAAOtC,CAAA,EAAcoH,CAAA,KAAiBtE,CAAA;QAE7DtC,CAAA,GA/DR,UACE6B,CAAA,EACAb,CAAA,EACAC,CAAA;UAYA,KAVA,IAQIa,CAAA,EAREX,CAAA,GAAAJ,CAAA,CAAAA,CAAA,KAGDC,CAAA,GAAK;cAER2R,SAAA,OAAW;cACXnQ,KAAA,EAAKvB;YAAA,IAIEG,CAAA,GAAI,GAAGA,CAAA,GAAIS,CAAA,CAAMJ,MAAA,EAAQL,CAAA,IAAK,GAAG;YAExC,IAAMG,CAAA,GAAkBuF,EAAA,CADxBhF,CAAA,GAAUD,CAAA,CAAMT,CAAA,KAC8BU,CAAA,CAAQX,CAAA,IAAWW,CAAA;YAEjE,KAAK,IAAM8E,CAAA,IAAOrF,CAAA,EAChBJ,CAAA,CAAQyF,CAAA,IACE,gBAARA,CAAA,GACII,EAAA,CAAY7F,CAAA,CAAQyF,CAAA,GAA4BrF,CAAA,CAAgBqF,CAAA,KACxD,YAARA,CAAA,GAAA7F,CAAA,CAAAA,CAAA,KACOI,CAAA,CAAQyF,CAAA,IAASrF,CAAA,CAAgBqF,CAAA,KACtCrF,CAAA,CAAgBqF,CAAA,CAE3B;UAAA;UAMD,OAJI5F,CAAA,CAAM2R,SAAA,KACRxR,CAAA,CAAQwR,SAAA,GAAY3L,EAAA,CAAY7F,CAAA,CAAQwR,SAAA,EAAW3R,CAAA,CAAM2R,SAAA,IAGpDxR,CACT;QAAA,CA6BkB,CAAsBC,CAAA,EAAgBU,CAAA,EAAO/B,CAAA;QACvDE,CAAA,GAAgCD,CAAA,CAAQ4S,EAAA,IAAM3B,CAAA;QAC9C7Q,CAAA,GAA6B;MAEnC,KAAK,IAAMK,CAAA,IAAOT,CAAA,OACK,MAAjBA,CAAA,CAAQS,CAAA,KAGU,QAAXA,CAAA,CAAI,MAAsB,SAARA,CAAA,IAAyB,YAARA,CAAA,IAAmBT,CAAA,CAAQwC,KAAA,KAAUzC,CAAA,KAEhE,kBAARU,CAAA,GACTL,CAAA,CAAgBwS,EAAA,GAAK5S,CAAA,CAAQ6S,WAAA,GACnB/S,CAAA,KAAqBA,CAAA,CAAkBW,CAAA,EAAKR,CAAA,MACtDG,CAAA,CAAgBK,CAAA,IAAOT,CAAA,CAAQS,CAAA,GAG5BX,CAAA,IACwB,kBAAzBL,OAAA,CAAQC,GAAA,CAAIc,QAAA,IACXQ,CAAA,CAAYP,CAAA,KACZ2R,EAAA,CAAiBrQ,GAAA,CAAItB,CAAA,MAEtBgC,CAAA,CAAYV,GAAA,CAAI9B,CAAA,MAEhBmS,EAAA,CAAiBnQ,GAAA,CAAIxB,CAAA,GACrBY,OAAA,CAAQW,IAAA,CACN,qDAAAd,MAAA,CAAqDT,CAAA,EAAG;MAMhE,IAAMC,CAAA,GA/GR,UACEmB,CAAA,EACAd,CAAA;QAEA,IAAMC,CAAA,GAAM8O,EAAA;UAEN7O,CAAA,GAAYY,CAAA,CAAegQ,uBAAA,CAC/B9Q,CAAA,EACAC,CAAA,CAAIyO,UAAA,EACJzO,CAAA,CAAI0O,MAAA;QAKN,OAF6B,iBAAzBjQ,OAAA,CAAQC,GAAA,CAAIc,QAAA,IAA2BqG,CAAA,CAAc5F,CAAA,GAElDA,CACT;MAAA,CAgG6B,CAAiBM,CAAA,EAAgBvB,CAAA;MAE/B,iBAAzBP,OAAA,CAAQC,GAAA,CAAIc,QAAA,IAA6BqB,CAAA,CAAmBiR,kBAAA,IAC9DjR,CAAA,CAAmBiR,kBAAA,CAAmBpS,CAAA;MAGxC,IAAIC,CAAA,GAAcqG,EAAA,CAAY2H,CAAA,EAAoBN,CAAA;MAuBlD,OAtBI3N,CAAA,KACFC,CAAA,IAAe,MAAMD,CAAA,GAEnBV,CAAA,CAAQ2S,SAAA,KACVhS,CAAA,IAAe,MAAMX,CAAA,CAAQ2S,SAAA,GAG/BvS,CAAA,CAEE8D,CAAA,CAAMjE,CAAA,MACLwC,CAAA,CAAYV,GAAA,CAAI9B,CAAA,IACb,UACA,eACFU,CAAA,EAKAQ,CAAA,KACFf,CAAA,CAAgB2S,GAAA,GAAM5R,CAAA,GAGjBgJ,CAAA,CAAclK,CAAA,EAAoBG,CAAA,CAC3C;IAAA,CAwDW,CAAmCuC,CAAA,EAAwBd,CAAA,EAAOC,CAAA,CAC1E;EAAA;EAEDY,CAAA,CAAiBsB,WAAA,GAAclE,CAAA;EAM/B,IAAI6C,CAAA,GAAyB1B,CAAA,CAAM+R,UAAA,CAAWtQ,CAAA;EA+D9C,OA1DAC,CAAA,CAAuB2P,KAAA,GAAQrS,CAAA,EAC/B0C,CAAA,CAAuB8P,cAAA,GAAiB9R,CAAA,EACxCgC,CAAA,CAAuBqB,WAAA,GAAclE,CAAA,EACrC6C,CAAA,CAAuB6M,iBAAA,GAAoBpP,CAAA,EAI3CuC,CAAA,CAAuB+P,kBAAA,GAAqBtR,CAAA,GACxC4F,EAAA,CAAYzF,CAAA,CAAsBmR,kBAAA,EAAoBnR,CAAA,CAAsB4P,iBAAA,IAC5E,IAEJxO,CAAA,CAAuBwO,iBAAA,GAAoBnR,CAAA,EAG3C2C,CAAA,CAAuBuK,MAAA,GAAS9L,CAAA,GAAqBG,CAAA,CAAsB2L,MAAA,GAASrL,CAAA,EAEpFO,MAAA,CAAO6D,cAAA,CAAetD,CAAA,EAAwB,gBAAgB;IAC5DyG,GAAA,EAAG,SAAAA,CAAA;MACD,OAAO,KAAK6J,mBACb;IAAA;IAED1K,GAAA,WAAAA,CAAI1G,CAAA;MACF,KAAKoR,mBAAA,GAAsB7R,CAAA,GrBvQT,UAAUS,CAAA;QAAA,KAAa,IAAiBd,CAAA,OAAAC,CAAA,MAAjBA,CAAA,GAAiBQ,SAAA,CAAAC,MAAA,EAAjBT,CAAA,IAAAD,CAAA,CAAiBC,CAAA,QAAAQ,SAAA,CAAAR,CAAA;QAC9D,KAAqB,IAAAC,CAAA,MAAAa,CAAA,GAAOf,CAAA,EAAPE,CAAA,GAAAa,CAAA,CAAAL,MAAA,EAAAR,CAAA,IACnBmG,EAAA,CAAiBvF,CAAA,EADFC,CAAA,CAAAb,CAAA,IACkB;QAGnC,OAAOY,CACT;MAAA,CqBkQU,CAAM,CAAE,GAAEN,CAAA,CAAsBuD,YAAA,EAAcjD,CAAA,IAC9CA,CACL;IAAA;EAAA,IAG0B,iBAAzBpC,OAAA,CAAQC,GAAA,CAAIc,QAAA,KACdK,CAAA,CAAqBf,CAAA,EAAaE,CAAA,GAElC2C,CAAA,CAAuBmQ,kBAAA,GEvSZ,UAACjR,CAAA,EAAqBd,CAAA;IACnC,IAAIC,CAAA,GAA8B;MAC9BC,CAAA,IAAc;IAElB,OAAO,UAACa,CAAA;MACN,KAAKb,CAAA,KACHD,CAAA,CAAiBc,CAAA,KAAa,GAC1BM,MAAA,CAAO8Q,IAAA,CAAKlS,CAAA,EAAkBS,MAAA,IATnB,MASoC;QAGjD,IAAMN,CAAA,GAAiBJ,CAAA,GAAc,oBAAoBG,MAAA,CAAAH,CAAA,EAAc,OAAG;QAE1EM,OAAA,CAAQW,IAAA,CACN,QAAAd,MAAA,CAfW,KAe2C,0CAAAA,MAAA,CAAAW,CAAA,EAAcX,MAAA,CAAAC,CAAA,EAAmB,SAAvF,gQAUFF,CAAA,IAAc,GACdD,CAAA,GAAmB,EACpB;MAAA;IAEL,CACD;EAAA,CF2Q+C,CAC1ClB,CAAA,EACAE,CAAA,IAIJuH,EAAA,CAAY5E,CAAA,EAAwB;IAAM,WAAAzB,MAAA,CAAIyB,CAAA,CAAuBwO,iBAAA,CAA3B;EAAA,IAEtCvK,CAAA,IAGFD,EAAA,CACEhE,CAAA,EAH+Bd,CAAA,EAK/B;IAEEyQ,KAAA,GAAO;IACPG,cAAA,GAAgB;IAChBzO,WAAA,GAAa;IACb0O,kBAAA,GAAoB;IACpBlD,iBAAA,GAAmB;IACnB2B,iBAAA,GAAmB;IACnBjE,MAAA,GAAQ;EAAA,IAKPvK,CACT;AAAA;AGrUc,SAAUwQ,GACtBtR,CAAA,EACAd,CAAA;EAIA,KAFA,IAAMC,CAAA,GAAiC,CAACa,CAAA,CAAQ,KAEvCZ,CAAA,GAAI,GAAGa,CAAA,GAAMf,CAAA,CAAeU,MAAA,EAAQR,CAAA,GAAIa,CAAA,EAAKb,CAAA,IAAK,GACzDD,CAAA,CAAO2G,IAAA,CAAK5G,CAAA,CAAeE,CAAA,GAAIY,CAAA,CAAQZ,CAAA,GAAI;EAG7C,OAAOD,CACT;AAAA;ACMA,IAAMoS,EAAA,GAAS,SAAAC,CAAyBxR,CAAA;EACtC,OAAAO,MAAA,CAAOkR,MAAA,CAAOzR,CAAA,EAAK;IAAEmP,KAAA,GAAO;EAAA,EAA5B;AAAA;AAOF,SAASuC,GACPxS,CAAA;EAAA,KACA,IAAkDC,CAAA,OAAAC,CAAA,MAAlDA,CAAA,GAAkDO,SAAA,CAAAC,MAAA,EAAlDR,CAAA,IAAAD,CAAA,CAAkDC,CAAA,QAAAO,SAAA,CAAAP,CAAA;EAElD,IAAI6F,EAAA,CAAW/F,CAAA,KAAWmG,EAAA,CAAcnG,CAAA,GAGtC,OAAOqS,EAAA,CACLlC,EAAA,CACEiC,EAAA,CAAkBhR,CAAA,EAAWN,CAAA,EAJHd,CAAA,GAMrBC,CAAA,GAAc;EAMzB,IAAMc,CAAA,GAAmBf,CAAA;EAEzB,OAC4B,MAA1BC,CAAA,CAAeS,MAAA,IACa,MAA5BK,CAAA,CAAiBL,MAAA,IACc,mBAAxBK,CAAA,CAAiB,KAEjBoP,EAAA,CAAepP,CAAA,IAGjBsR,EAAA,CACLlC,EAAA,CAAeiC,EAAA,CAAkBrR,CAAA,EAAkBd,CAAA,GAEvD;AAAA;AC0BwB,SAAAwS,GAQtBxS,CAAA,EACAC,CAAA,EACAa,CAAA;EASA,SATA,MAAAA,CAAA,KAAAA,CAAA,GAAoDQ,CAAA,IAS/CrB,CAAA,EACH,MAAM4G,EAAA,CAAY,GAAG5G,CAAA;EAIvB,IAAME,CAAA,GAAmB,SAAAsS,CACvB1S,CAAA;IAAA,KACA,IAAiEI,CAAA,OAAAC,CAAA,MAAjEA,CAAA,GAAiEI,SAAA,CAAAC,MAAA,EAAjEL,CAAA,IAAAD,CAAA,CAAiEC,CAAA,QAAAI,SAAA,CAAAJ,CAAA;IAEjE,OAAAJ,CAAA,CACEC,CAAA,EACAa,CAAA,EACAyR,EAAA,CAAmC3R,KAAA,SAAAC,CAAA,EAAAd,CAAA,GAAkBI,CAAA,GACtD,IAJD;EAAA;EA6CF,OAjCAA,CAAA,CAAiBmR,KAAA,GAAQ,UAMvBzQ,CAAA;IAEA,OAAA2R,EAAA,CAUExS,CAAA,EAAsBC,CAAA,EACnBF,CAAA,CAAAA,CAAA,KAAAe,CAAA,GACH;MAAAwQ,KAAA,EAAOjL,KAAA,CAAM/B,SAAA,CAAUpE,MAAA,CAAOY,CAAA,CAAQwQ,KAAA,EAAOzQ,CAAA,EAAO2Q,MAAA,CAAOnS,OAAA;IAAA,GAZ7D;EAAA,GAmBFc,CAAA,CAAiBuS,UAAA,GAAa,UAAC7R,CAAA;IAC7B,OAAA2R,EAAA,CAA0DxS,CAAA,EAAsBC,CAAA,EAC3EF,CAAA,CAAAA,CAAA,KAAAe,CAAA,GACAD,CAAA,EAFL;EAAA,GAKKV,CACT;AAAA;ACvJA,IAAMwS,EAAA,GAAa,SAAAC,CACjB/R,CAAA;IAEA,OAAA2R,EAAA,CAIEnB,EAAA,EAAuBxQ,CAAA,CAJzB;EAAA;EAMIgS,EAAA,GAASF,EAAA;AAKflR,CAAA,CAAYmF,OAAA,CAAQ,UAAA/F,CAAA;EAElBgS,EAAA,CAAOhS,CAAA,IAAc8R,EAAA,CAA8B9R,CAAA,CACrD;AAAA;ACjBA,IAAAiS,EAAA;EAKE,SAAYjS,GAAA,EAAuBd,CAAA;IACjC,KAAKmL,KAAA,GAAQrK,CAAA,EACb,KAAK6P,WAAA,GAAc3Q,CAAA,EACnB,KAAK0Q,QAAA,GAAWJ,EAAA,CAAcxP,CAAA,GAI9B2K,EAAA,CAAWM,UAAA,CAAW,KAAK4E,WAAA,GAAc,EAC1C;EAAA;EAkCH,OAhCE7P,CAAA,CAAYyD,SAAA,CAAAyO,YAAA,GAAZ,UACElS,CAAA,EACAd,CAAA,EACAC,CAAA,EACAC,CAAA;IAEA,IAGMa,CAAA,GAAMb,CAAA,CAHIgG,EAAA,CACdiK,EAAA,CAAQ,KAAKhF,KAAA,EAA0BnL,CAAA,EAAkBC,CAAA,EAAYC,CAAA,IAE3C;MACtBE,CAAA,GAAK,KAAKuQ,WAAA,GAAc7P,CAAA;IAG9Bb,CAAA,CAAWsH,WAAA,CAAYnH,CAAA,EAAIA,CAAA,EAAIW,CAAA;EAAA,GAGjCD,CAAA,CAAAyD,SAAA,CAAA0O,YAAA,aAAanS,CAAA,EAAkBd,CAAA;IAC7BA,CAAA,CAAWuM,UAAA,CAAW,KAAKoE,WAAA,GAAc7P,CAAA;EAAA,GAG3CA,CAAA,CAAYyD,SAAA,CAAA2O,YAAA,GAAZ,UACEpS,CAAA,EACAd,CAAA,EACAC,CAAA,EACAC,CAAA;IAEIY,CAAA,GAAW,KAAG2K,EAAA,CAAWM,UAAA,CAAW,KAAK4E,WAAA,GAAc7P,CAAA,GAG3D,KAAKmS,YAAA,CAAanS,CAAA,EAAUb,CAAA,GAC5B,KAAK+S,YAAA,CAAalS,CAAA,EAAUd,CAAA,EAAkBC,CAAA,EAAYC,CAAA;EAAA,GAE7DY,CAAD;AAAA;ACzCwB,SAAAqS,GACtBlT,CAAA;EAAA,KACA,IAA8Cc,CAAA,OAAAX,CAAA,MAA9CA,CAAA,GAA8CK,SAAA,CAAAC,MAAA,EAA9CN,CAAA,IAAAW,CAAA,CAA8CX,CAAA,QAAAK,SAAA,CAAAL,CAAA;EAE9C,IAAMC,CAAA,GAAQmS,EAAA,CAAG3R,KAAA,SAAAC,CAAA,EAAQb,CAAA,GAAYc,CAAA;IAC/BP,CAAA,GAAoB,aAAaL,MAAA,CAAA4C,CAAA,CAAoBqQ,IAAA,CAAKzF,SAAA,CAAUtN,CAAA;IACpEwF,CAAA,GAAc,IAAIkN,EAAA,CAAmB1S,CAAA,EAAOG,CAAA;EAErB,iBAAzB9B,OAAA,CAAQC,GAAA,CAAIc,QAAA,IACdK,CAAA,CAAqBU,CAAA;EAGvB,IAAMsF,CAAA,GAAoE,SAAAuN,CAAAvS,CAAA;IACxE,IAAMd,CAAA,GAAM+O,EAAA;MACN9O,CAAA,GAAQC,CAAA,CAAMiR,UAAA,CAAWJ,EAAA;MAGzBhQ,CAAA,GAFcb,CAAA,CAAMoT,MAAA,CAAOtT,CAAA,CAAI0O,UAAA,CAAWxC,kBAAA,CAAmB1L,CAAA,GAEtC+S,OAAA;IA8B7B,OA5B6B,iBAAzB7U,OAAA,CAAQC,GAAA,CAAIc,QAAA,IAA6BS,CAAA,CAAMsT,QAAA,CAASC,KAAA,CAAM3S,CAAA,CAAMiM,QAAA,KACtEzM,OAAA,CAAQW,IAAA,CACN,8BAAAd,MAAA,CAA8BK,CAAA,EAAiB,uEAKxB,iBAAzB9B,OAAA,CAAQC,GAAA,CAAIc,QAAA,IACZY,CAAA,CAAMqT,IAAA,CAAK,UAAA5S,CAAA;MAAQ,OAAgB,mBAATA,CAAA,KAAkD,MAA7BA,CAAA,CAAK6S,OAAA,CAAQ,UAAiB;IAAA,MAE7ErT,OAAA,CAAQW,IAAA,CACN,iVAIAjB,CAAA,CAAI0O,UAAA,CAAW7C,MAAA,IACjBzC,CAAA,CAAarI,CAAA,EAAUD,CAAA,EAAOd,CAAA,CAAI0O,UAAA,EAAYzO,CAAA,EAAOD,CAAA,CAAI2O,MAAA,GAIzDzO,CAAA,CAAM0T,eAAA,CAAgB;MACpB,KAAK5T,CAAA,CAAI0O,UAAA,CAAW7C,MAAA,EAElB,OADAzC,CAAA,CAAarI,CAAA,EAAUD,CAAA,EAAOd,CAAA,CAAI0O,UAAA,EAAYzO,CAAA,EAAOD,CAAA,CAAI2O,MAAA,GAClD;QAAM,OAAA9I,CAAA,CAAYoN,YAAA,CAAalS,CAAA,EAAUf,CAAA,CAAI0O,UAAA;MAAA,CAExD;IAAA,GAAG,CAAC3N,CAAA,EAAUD,CAAA,EAAOd,CAAA,CAAI0O,UAAA,EAAYzO,CAAA,EAAOD,CAAA,CAAI2O,MAAA,IAG3C,IACT;EAAA;EAEA,SAASvF,EACPtI,CAAA,EACAb,CAAA,EACAC,CAAA,EACAa,CAAA,EACAX,CAAA;IAEA,IAAIyF,CAAA,CAAY6K,QAAA,EACd7K,CAAA,CAAYqN,YAAA,CACVpS,CAAA,EACApB,CAAA,EACAQ,CAAA,EACAE,CAAA,OAEG;MACL,IAAMC,CAAA,GAAUL,CAAA,CAAAA,CAAA,KACXC,CAAA,GACH;QAAAwB,KAAA,EAAOD,CAAA,CAAevB,CAAA,EAAOc,CAAA,EAAO+E,CAAA,CAAqB/B,YAAA;MAAA;MAG3D8B,CAAA,CAAYqN,YAAA,CAAapS,CAAA,EAAUT,CAAA,EAASH,CAAA,EAAYE,CAAA,CACzD;IAAA;EACF;EAED,OAAOF,CAAA,CAAM2T,IAAA,CAAK/N,CAAA,CACpB;AAAA;ACjFwB,SAAAgO,GACtB9T,CAAA;EAAA,KACA,IAA8CC,CAAA,OAAAC,CAAA,MAA9CA,CAAA,GAA8CO,SAAA,CAAAC,MAAA,EAA9CR,CAAA,IAAAD,CAAA,CAA8CC,CAAA,QAAAO,SAAA,CAAAP,CAAA;EAInB,iBAAzBxB,OAAA,CAAQC,GAAA,CAAIc,QAAA,IACS,sBAAdsU,SAAA,IACe,kBAAtBA,SAAA,CAAUC,OAAA,IAEV1T,OAAA,CAAQW,IAAA,CACN;EAIJ,IAAMF,CAAA,GAAQmF,EAAA,CAAgBsM,EAAA,CAAW3R,KAAA,SAAAC,CAAA,EAAAd,CAAA,GAAYC,CAAA,GAA2B;IAC1EG,CAAA,GAAO2C,CAAA,CAAoBhC,CAAA;EACjC,OAAO,IAAIsO,EAAA,CAAUjP,CAAA,EAAMW,CAAA,CAC7B;AAAA;ACjBwB,SAAAkT,GACtBnT,CAAA;EAMA,IAAMb,CAAA,GAAYC,CAAA,CAAM+R,UAAA,CACtB,UAAChS,CAAA,EAAOc,CAAA;IACN,IACMX,CAAA,GAAYoB,CAAA,CAAevB,CAAA,EADnBC,CAAA,CAAMiR,UAAA,CAAWJ,EAAA,GACgBjQ,CAAA,CAAUiD,YAAA;IAUzD,OAR6B,iBAAzBrF,OAAA,CAAQC,GAAA,CAAIc,QAAA,SAA2C,MAAdW,CAAA,IAC3CE,OAAA,CAAQW,IAAA,CACN,yHAAyHd,MAAA,CAAA6C,CAAA,CACvHlC,CAAA,GACE,OAIDZ,CAAA,CAAC+J,aAAA,CAAAnJ,CAAA,EAAcd,CAAA,KAAAC,CAAA,EAAO;MAAAwB,KAAA,EAAOrB,CAAA;MAAW4R,GAAA,EAAKjR;IAAA,GACtD;EAAA;EAKF,OAFAd,CAAA,CAAUgD,WAAA,GAAc,aAAA9C,MAAA,CAAa6C,CAAA,CAAiBlC,CAAA,GAAU,MAEzD8E,EAAA,CAAM3F,CAAA,EAAWa,CAAA,CAC1B;AAAA;ACpBA,IAAAoT,EAAA;IAIE,SAAApT,EAAA;MAAA,IAGCA,CAAA;MAED,KAAAqT,aAAA,GAAgB;QACd,IAAMnU,CAAA,GAAMc,CAAA,CAAKsT,QAAA,CAAShG,QAAA;QAC1B,KAAKpO,CAAA,EAAK,OAAO;QACjB,IAAMC,CAAA,GAAQ2J,EAAA;UAMR1J,CAAA,GAAWgG,EAAA,CALH,CACZjG,CAAA,IAAS,UAAUE,MAAA,CAAAF,CAAA,EAAQ,MAC3B,GAAAE,MAAA,CAAG1B,CAAA,EAAgB,YACnB,GAAG0B,MAAA,CAAApB,CAAA,EAAoB,MAAAoB,MAAA,CAAAnB,CAAA,EAAa,MAECyS,MAAA,CAAOnS,OAAA,GAAsB;QAEpE,OAAO,UAAUa,MAAA,CAAAD,CAAA,EAAY,KAAAC,MAAA,CAAAH,CAAA,aAC/B;MAAA,GAUA,KAAAqU,YAAA,GAAe;QACb,IAAIvT,CAAA,CAAKwT,MAAA,EACP,MAAMxN,EAAA,CAAY;QAGpB,OAAOhG,CAAA,CAAKqT,aAAA,EACd;MAAA,GAEA,KAAAI,eAAA,GAAkB;QAAA,IAAAtU,CAAA;QAChB,IAAIa,CAAA,CAAKwT,MAAA,EACP,MAAMxN,EAAA,CAAY;QAGpB,IAAM/F,CAAA,GAAMD,CAAA,CAAKsT,QAAA,CAAShG,QAAA;QAC1B,KAAKrN,CAAA,EAAK,OAAO;QAEjB,IAAMX,CAAA,KAAKH,CAAA,OACRxB,CAAA,IAAU,IACXwB,CAAA,CAAClB,CAAA,IAAkBC,CAAA,EACnBiB,CAAA,CAAAuU,uBAAA,GAAyB;YACvBC,MAAA,EAAQ1T;UAAA,GAAAd,CAAA;UAINI,CAAA,GAAQuJ,EAAA;QAMd,OALIvJ,CAAA,KACDD,CAAA,CAAcsU,KAAA,GAAQrU,CAAA,GAIlB,CAACH,CAAA,CAAA+J,aAAA,UAAAjK,CAAA,KAAWI,CAAA,EAAK;UAAEuU,GAAA,EAAI;QAAA,IAChC;MAAA,GAyDA,KAAAC,IAAA,GAAO;QACL9T,CAAA,CAAKwT,MAAA,IAAS,CAChB;MAAA,GApHE,KAAKF,QAAA,GAAW,IAAI3I,EAAA,CAAW;QAAEF,QAAA,GAAU;MAAA,IAC3C,KAAK+I,MAAA,IAAS,CACf;IAAA;IAmHH,OAnGExT,CAAA,CAAayD,SAAA,CAAAsQ,aAAA,GAAb,UAAc/T,CAAA;MACZ,IAAI,KAAKwT,MAAA,EACP,MAAMxN,EAAA,CAAY;MAGpB,OAAO5G,CAAA,CAAA+J,aAAA,CAAC+E,EAAA,EAAiB;QAACtE,KAAA,EAAO,KAAK0J;MAAA,GAAWtT,CAAA;IAAA,GAqCnDA,CAAA,CAAwByD,SAAA,CAAAuQ,wBAAA,GAAxB,UAAyBhU,CAAA;MAErB,MAAMgG,EAAA,CAAY;IAAA,GAuDvBhG,CAAD;EAAA;ECrIaiU,EAAA,GAAc;IACzBC,UAAA,EAAUvJ,EAAA;IACVwJ,SAAA,EAAS5G;EAAA;ACkBgB,iBAAzB3P,OAAA,CAAQC,GAAA,CAAIc,QAAA,IACS,sBAAdsU,SAAA,IACe,kBAAtBA,SAAA,CAAUC,OAAA,IAEV1T,OAAA,CAAQW,IAAA,CACN;AAIJ,IAAMiU,EAAA,GAAkB,QAAQ/U,MAAA,CAAA1B,CAAA;AAIL,iBAAzBC,OAAA,CAAQC,GAAA,CAAIc,QAAA,IACa,WAAzBf,OAAA,CAAQC,GAAA,CAAIc,QAAA,IACM,sBAAXN,MAAA,KAGPA,MAAA,CAAO+V,EAAA,MAAP/V,MAAA,CAAO+V,EAAA,IAAqB,IAGI,MAA5B/V,MAAA,CAAO+V,EAAA,KACT5U,OAAA,CAAQW,IAAA,CACN,6TAKJ9B,MAAA,CAAO+V,EAAA,KAAoB;AAAA,SAAAhB,EAAA,IAAAiB,gBAAA,EAAAvG,EAAA,IAAAwG,kBAAA,EAAA7G,EAAA,IAAA8G,iBAAA,EAAArG,EAAA,IAAAsG,iBAAA,EAAAtE,EAAA,IAAAuE,aAAA,EAAAxE,EAAA,IAAAyE,YAAA,EAAAtE,EAAA,IAAAuE,aAAA,EAAAV,EAAA,IAAAW,WAAA,EAAAvC,EAAA,IAAAwC,iBAAA,EAAAnD,EAAA,IAAAoD,GAAA,EAAA9C,EAAA,IAAA+C,OAAA,EAAA7P,EAAA,IAAA8P,iBAAA,EAAAhC,EAAA,IAAAiC,SAAA,EAAAjD,EAAA,IAAAkD,MAAA,EAAA/E,EAAA,IAAAgF,QAAA,EAAAjX,CAAA,IAAAkX,OAAA,EAAAjC,EAAA,IAAAkC,SAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}