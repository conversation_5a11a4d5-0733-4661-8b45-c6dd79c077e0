{"ast": null, "code": "var _jsxFileName = \"D:\\\\ASL\\\\ASL-Training\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport HomePage from './components/HomePage';\nimport TrainingPage from './components/TrainingPage';\nimport LevelsPage from './components/LevelsPage';\nimport FlashCardsPage from './components/FlashCardsPage';\nimport AboutPage from './components/AboutPage';\nimport ContactPage from './components/ContactPage';\nimport './App.css';\n\n// Import signLanguageData from TrainingPage\nimport TrainingPageComponent from './components/TrainingPage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  font-family: var(--font-secondary);\n  overflow-x: hidden;\n  position: relative;\n\n  /* Subtle background pattern for visual interest */\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 25% 25%, var(--primary-50) 0%, transparent 50%),\n      radial-gradient(circle at 75% 75%, var(--secondary-50) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n`;\n_c = AppContainer;\nconst PageWrapper = styled.div`\n  position: relative;\n  z-index: 1;\n  min-height: 100vh;\n`;\n_c2 = PageWrapper;\nfunction App() {\n  _s();\n  const [currentPage, setCurrentPage] = useState('home');\n  const [currentLevel, setCurrentLevel] = useState(null);\n  const [currentLevelData, setCurrentLevelData] = useState(null);\n  const navigateToTraining = () => {\n    setCurrentPage('training');\n  };\n  const navigateToLevels = () => {\n    setCurrentPage('levels');\n  };\n  const navigateToAbout = () => {\n    setCurrentPage('about');\n  };\n  const navigateToContact = () => {\n    setCurrentPage('contact');\n  };\n  const navigateToHome = () => {\n    setCurrentPage('home');\n  };\n  const startLevel = (levelId, levelData) => {\n    setCurrentLevel(levelId);\n    setCurrentLevelData(levelData);\n    setCurrentPage('flashcards');\n  };\n  const backToLevels = () => {\n    setCurrentPage('levels');\n    setCurrentLevel(null);\n    setCurrentLevelData(null);\n  };\n  const renderCurrentPage = () => {\n    switch (currentPage) {\n      case 'training':\n        return /*#__PURE__*/_jsxDEV(TrainingPage, {\n          onBackToHome: navigateToHome\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 16\n        }, this);\n      case 'levels':\n        return /*#__PURE__*/_jsxDEV(LevelsPage, {\n          onBackToHome: navigateToHome,\n          onStartLevel: startLevel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 16\n        }, this);\n      case 'flashcards':\n        return /*#__PURE__*/_jsxDEV(FlashCardsPage, {\n          onBackToHome: backToLevels,\n          levelId: currentLevel,\n          levelData: currentLevelData,\n          signLanguageData: signLanguageData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this);\n      case 'about':\n        return /*#__PURE__*/_jsxDEV(AboutPage, {\n          onBackToHome: navigateToHome\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 16\n        }, this);\n      case 'contact':\n        return /*#__PURE__*/_jsxDEV(ContactPage, {\n          onBackToHome: navigateToHome\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(HomePage, {\n          onStartTraining: navigateToTraining,\n          onStartLevels: navigateToLevels,\n          onNavigateToAbout: navigateToAbout,\n          onNavigateToContact: navigateToContact\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(AppContainer, {\n    children: /*#__PURE__*/_jsxDEV(PageWrapper, {\n      children: renderCurrentPage()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 112,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"e9cCHXm7rpO3x/ssX2xZuyPn7pE=\");\n_c3 = App;\nexport default App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"AppContainer\");\n$RefreshReg$(_c2, \"PageWrapper\");\n$RefreshReg$(_c3, \"App\");", "map": {"version": 3, "names": ["React", "useState", "styled", "HomePage", "TrainingPage", "LevelsPage", "FlashCardsPage", "AboutPage", "ContactPage", "TrainingPageComponent", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "PageWrapper", "_c2", "App", "_s", "currentPage", "setCurrentPage", "currentLevel", "setCurrentLevel", "currentLevelData", "setCurrentLevelData", "navigateToTraining", "navigateToLevels", "navigateToAbout", "navigateToContact", "navigateToHome", "startLevel", "levelId", "levelData", "backToLevels", "renderCurrentPage", "onBackToHome", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onStartLevel", "signLanguageData", "onStartTraining", "onStartLevels", "onNavigateToAbout", "onNavigateToContact", "children", "_c3", "$RefreshReg$"], "sources": ["D:/ASL/ASL-Training/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport HomePage from './components/HomePage';\nimport TrainingPage from './components/TrainingPage';\nimport LevelsPage from './components/LevelsPage';\nimport FlashCardsPage from './components/FlashCardsPage';\nimport AboutPage from './components/AboutPage';\nimport ContactPage from './components/ContactPage';\nimport './App.css';\n\n// Import signLanguageData from TrainingPage\nimport TrainingPageComponent from './components/TrainingPage';\n\nconst AppContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  font-family: var(--font-secondary);\n  overflow-x: hidden;\n  position: relative;\n\n  /* Subtle background pattern for visual interest */\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 25% 25%, var(--primary-50) 0%, transparent 50%),\n      radial-gradient(circle at 75% 75%, var(--secondary-50) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n`;\n\nconst PageWrapper = styled.div`\n  position: relative;\n  z-index: 1;\n  min-height: 100vh;\n`;\n\nfunction App() {\n  const [currentPage, setCurrentPage] = useState('home');\n  const [currentLevel, setCurrentLevel] = useState(null);\n  const [currentLevelData, setCurrentLevelData] = useState(null);\n\n  const navigateToTraining = () => {\n    setCurrentPage('training');\n  };\n\n  const navigateToLevels = () => {\n    setCurrentPage('levels');\n  };\n\n  const navigateToAbout = () => {\n    setCurrentPage('about');\n  };\n\n  const navigateToContact = () => {\n    setCurrentPage('contact');\n  };\n\n  const navigateToHome = () => {\n    setCurrentPage('home');\n  };\n\n  const startLevel = (levelId, levelData) => {\n    setCurrentLevel(levelId);\n    setCurrentLevelData(levelData);\n    setCurrentPage('flashcards');\n  };\n\n  const backToLevels = () => {\n    setCurrentPage('levels');\n    setCurrentLevel(null);\n    setCurrentLevelData(null);\n  };\n\n  const renderCurrentPage = () => {\n    switch (currentPage) {\n      case 'training':\n        return <TrainingPage onBackToHome={navigateToHome} />;\n      case 'levels':\n        return <LevelsPage onBackToHome={navigateToHome} onStartLevel={startLevel} />;\n      case 'flashcards':\n        return (\n          <FlashCardsPage\n            onBackToHome={backToLevels}\n            levelId={currentLevel}\n            levelData={currentLevelData}\n            signLanguageData={signLanguageData}\n          />\n        );\n      case 'about':\n        return <AboutPage onBackToHome={navigateToHome} />;\n      case 'contact':\n        return <ContactPage onBackToHome={navigateToHome} />;\n      default:\n        return (\n          <HomePage\n            onStartTraining={navigateToTraining}\n            onStartLevels={navigateToLevels}\n            onNavigateToAbout={navigateToAbout}\n            onNavigateToContact={navigateToContact}\n          />\n        );\n    }\n  };\n\n  return (\n    <AppContainer>\n      <PageWrapper>\n        {renderCurrentPage()}\n      </PageWrapper>\n    </AppContainer>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAO,WAAW;;AAElB;AACA,OAAOC,qBAAqB,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,YAAY,GAAGV,MAAM,CAACW,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GArBIF,YAAY;AAuBlB,MAAMG,WAAW,GAAGb,MAAM,CAACW,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAJID,WAAW;AAMjB,SAASE,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,MAAM,CAAC;EACtD,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACsB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAE9D,MAAMwB,kBAAkB,GAAGA,CAAA,KAAM;IAC/BL,cAAc,CAAC,UAAU,CAAC;EAC5B,CAAC;EAED,MAAMM,gBAAgB,GAAGA,CAAA,KAAM;IAC7BN,cAAc,CAAC,QAAQ,CAAC;EAC1B,CAAC;EAED,MAAMO,eAAe,GAAGA,CAAA,KAAM;IAC5BP,cAAc,CAAC,OAAO,CAAC;EACzB,CAAC;EAED,MAAMQ,iBAAiB,GAAGA,CAAA,KAAM;IAC9BR,cAAc,CAAC,SAAS,CAAC;EAC3B,CAAC;EAED,MAAMS,cAAc,GAAGA,CAAA,KAAM;IAC3BT,cAAc,CAAC,MAAM,CAAC;EACxB,CAAC;EAED,MAAMU,UAAU,GAAGA,CAACC,OAAO,EAAEC,SAAS,KAAK;IACzCV,eAAe,CAACS,OAAO,CAAC;IACxBP,mBAAmB,CAACQ,SAAS,CAAC;IAC9BZ,cAAc,CAAC,YAAY,CAAC;EAC9B,CAAC;EAED,MAAMa,YAAY,GAAGA,CAAA,KAAM;IACzBb,cAAc,CAAC,QAAQ,CAAC;IACxBE,eAAe,CAAC,IAAI,CAAC;IACrBE,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMU,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQf,WAAW;MACjB,KAAK,UAAU;QACb,oBAAOR,OAAA,CAACP,YAAY;UAAC+B,YAAY,EAAEN;QAAe;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvD,KAAK,QAAQ;QACX,oBAAO5B,OAAA,CAACN,UAAU;UAAC8B,YAAY,EAAEN,cAAe;UAACW,YAAY,EAAEV;QAAW;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/E,KAAK,YAAY;QACf,oBACE5B,OAAA,CAACL,cAAc;UACb6B,YAAY,EAAEF,YAAa;UAC3BF,OAAO,EAAEV,YAAa;UACtBW,SAAS,EAAET,gBAAiB;UAC5BkB,gBAAgB,EAAEA;QAAiB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAEN,KAAK,OAAO;QACV,oBAAO5B,OAAA,CAACJ,SAAS;UAAC4B,YAAY,EAAEN;QAAe;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpD,KAAK,SAAS;QACZ,oBAAO5B,OAAA,CAACH,WAAW;UAAC2B,YAAY,EAAEN;QAAe;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD;QACE,oBACE5B,OAAA,CAACR,QAAQ;UACPuC,eAAe,EAAEjB,kBAAmB;UACpCkB,aAAa,EAAEjB,gBAAiB;UAChCkB,iBAAiB,EAAEjB,eAAgB;UACnCkB,mBAAmB,EAAEjB;QAAkB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;IAER;EACF,CAAC;EAED,oBACE5B,OAAA,CAACC,YAAY;IAAAkC,QAAA,eACXnC,OAAA,CAACI,WAAW;MAAA+B,QAAA,EACTZ,iBAAiB,CAAC;IAAC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEnB;AAACrB,EAAA,CA3EQD,GAAG;AAAA8B,GAAA,GAAH9B,GAAG;AA6EZ,eAAeA,GAAG;AAAC,IAAAH,EAAA,EAAAE,GAAA,EAAA+B,GAAA;AAAAC,YAAA,CAAAlC,EAAA;AAAAkC,YAAA,CAAAhC,GAAA;AAAAgC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}