{"ast": null, "code": "import _taggedTemplateLiteral from\"D:/ASL/ASL-Training/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6,_templateObject7,_templateObject8,_templateObject9,_templateObject0,_templateObject1,_templateObject10,_templateObject11,_templateObject12,_templateObject13,_templateObject14,_templateObject15,_templateObject16,_templateObject17,_templateObject18,_templateObject19,_templateObject20;import React,{useState,useEffect}from'react';import styled,{keyframes,css}from'styled-components';import{CheckCircle,RotateCcw,ArrowRight,ArrowLeft,Target,Zap,Sparkles}from'lucide-react';import{theme}from'../styles/theme';import{Card,Badge,Text,Heading}from'./ui/ModernComponents';// Modern Animations\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const flipIn=keyframes(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  from {\\n    transform: perspective(600px) rotateY(-90deg);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: perspective(600px) rotateY(0deg);\\n    opacity: 1;\\n  }\\n\"])));const slideInRight=keyframes(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  from {\\n    transform: translateX(100%) scale(0.9);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateX(0) scale(1);\\n    opacity: 1;\\n  }\\n\"])));const slideInLeft=keyframes(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  from {\\n    transform: translateX(-100%) scale(0.9);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateX(0) scale(1);\\n    opacity: 1;\\n  }\\n\"])));const successPulse=keyframes(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  0% {\\n    transform: scale(1);\\n    box-shadow: \",\";\\n  }\\n  50% {\\n    transform: scale(1.02);\\n    box-shadow: \",\";\\n  }\\n  100% {\\n    transform: scale(1);\\n    box-shadow: \",\";\\n  }\\n\"])),theme.shadows.lg,theme.shadows.glowSuccess,theme.shadows.lg);const shake=keyframes(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  0%, 100% { transform: translateX(0); }\\n  25% { transform: translateX(-8px); }\\n  75% { transform: translateX(8px); }\\n\"])));const sparkle=keyframes(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  0%, 100% {\\n    transform: scale(0) rotate(0deg);\\n    opacity: 0;\\n  }\\n  50% {\\n    transform: scale(1) rotate(180deg);\\n    opacity: 1;\\n  }\\n\"])));// Modern Styled Components\nconst CardContainer=styled.div(_templateObject7||(_templateObject7=_taggedTemplateLiteral([\"\\n  position: relative;\\n  width: 100%;\\n  max-width: 480px;\\n  margin: 0 auto;\\n  perspective: 1200px;\\n\\n  @media (max-width: \",\") {\\n    max-width: 420px;\\n  }\\n\\n  @media (max-width: \",\") {\\n    max-width: 380px;\\n  }\\n\\n  @media (max-width: \",\") {\\n    max-width: 100%;\\n    padding: 0 \",\";\\n    margin: 0;\\n  }\\n\"])),theme.breakpoints.lg,theme.breakpoints.md,theme.breakpoints.sm,theme.spacing[2]);const ModernCard=styled(Card)(_templateObject8||(_templateObject8=_taggedTemplateLiteral([\"\\n  background: \",\";\\n  border-radius: \",\";\\n  box-shadow: \",\";\\n  padding: \",\";\\n  text-align: center;\\n  position: relative;\\n  overflow: hidden;\\n  transition: all \",\";\\n  border: 1px solid \",\";\\n  \\n  @media (max-width: \",\") {\\n    padding: \",\";\\n    border-radius: \",\";\\n  }\\n\\n  animation: \",\";\\n\\n  &::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: 0;\\n    right: 0;\\n    height: 6px;\\n    background: \",\";\\n    border-radius: \",\" \",\" 0 0;\\n  }\\n\\n  &::after {\\n    content: '';\\n    position: absolute;\\n    top: -50%;\\n    left: -50%;\\n    width: 200%;\\n    height: 200%;\\n    background: \",\";\\n    opacity: \",\";\\n    transition: opacity \",\";\\n    pointer-events: none;\\n    z-index: 0;\\n  }\\n\\n  > * {\\n    position: relative;\\n    z-index: 1;\\n  }\\n\\n  @media (max-width: \",\") {\\n    padding: \",\";\\n    border-radius: \",\";\\n  }\\n\\n  @media (max-width: \",\") {\\n    padding: \",\";\\n    border-radius: \",\";\\n  }\\n\\n  @media (max-width: \",\") {\\n    padding: \",\";\\n    border-radius: \",\";\\n    box-shadow: \",\";\\n  }\\n\"])),theme.colors.background,theme.borderRadius['3xl'],theme.shadows.xl,theme.spacing[8],theme.transitions.normal,theme.colors.neutral[100],theme.breakpoints.sm,theme.spacing[6],theme.borderRadius['2xl'],props=>{if(props.isCorrect)return css(_templateObject9||(_templateObject9=_taggedTemplateLiteral([\"\",\" 0.8s ease\"])),successPulse);if(props.isIncorrect)return css(_templateObject0||(_templateObject0=_taggedTemplateLiteral([\"\",\" 0.6s ease\"])),shake);if(props.slideDirection==='right')return css(_templateObject1||(_templateObject1=_taggedTemplateLiteral([\"\",\" 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)\"])),slideInRight);if(props.slideDirection==='left')return css(_templateObject10||(_templateObject10=_taggedTemplateLiteral([\"\",\" 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)\"])),slideInLeft);return css(_templateObject11||(_templateObject11=_taggedTemplateLiteral([\"\",\" 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)\"])),flipIn);},props=>{if(props.isCorrect)return theme.colors.gradients.success;if(props.isIncorrect)return theme.colors.gradients.error;return theme.colors.gradients.primary;},theme.borderRadius['3xl'],theme.borderRadius['3xl'],props=>{if(props.isCorrect)return\"radial-gradient(circle, \".concat(theme.colors.success[100],\" 0%, transparent 70%)\");if(props.isIncorrect)return\"radial-gradient(circle, \".concat(theme.colors.error[100],\" 0%, transparent 70%)\");return'transparent';},props=>props.isCorrect||props.isIncorrect?0.3:0,theme.transitions.normal,theme.breakpoints.lg,theme.spacing[7],theme.borderRadius['2xl'],theme.breakpoints.md,theme.spacing[6],theme.borderRadius['2xl'],theme.breakpoints.sm,theme.spacing[5],theme.borderRadius.xl,theme.shadows.lg);const SignGif=styled.div(_templateObject12||(_templateObject12=_taggedTemplateLiteral([\"\\n  width: 240px;\\n  height: 240px;\\n  margin: 0 auto \",\";\\n  border-radius: \",\";\\n  overflow: hidden;\\n  background: \",\";\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  box-shadow: \",\";\\n  border: 2px solid \",\";\\n  transition: all \",\";\\n\\n  &:hover {\\n    transform: scale(1.02);\\n    box-shadow: \",\";\\n  }\\n\\n  img {\\n    width: 100%;\\n    height: 100%;\\n    object-fit: cover;\\n    border-radius: \",\";\\n    transition: transform \",\";\\n  }\\n\\n  .fallback {\\n    font-size: 5rem;\\n    color: \",\";\\n    filter: grayscale(0.3);\\n  }\\n\\n  @media (max-width: \",\") {\\n    width: 220px;\\n    height: 220px;\\n    margin-bottom: \",\";\\n  }\\n\\n  @media (max-width: \",\") {\\n    width: 200px;\\n    height: 200px;\\n    margin-bottom: \",\";\\n  }\\n\\n  @media (max-width: \",\") {\\n    width: 160px;\\n    height: 160px;\\n    margin-bottom: \",\";\\n    border-radius: \",\";\\n  }\\n\"])),theme.spacing[6],theme.borderRadius['2xl'],theme.colors.gradients.surface,theme.shadows.md,theme.colors.neutral[100],theme.transitions.normal,theme.shadows.lg,theme.borderRadius.xl,theme.transitions.normal,theme.colors.text.tertiary,theme.breakpoints.lg,theme.spacing[6],theme.breakpoints.md,theme.spacing[5],theme.breakpoints.sm,theme.spacing[4],theme.borderRadius.lg);const SignName=styled(Heading)(_templateObject13||(_templateObject13=_taggedTemplateLiteral([\"\\n  margin-bottom: \",\";\\n  background: \",\";\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n\"])),theme.spacing[2],theme.colors.gradients.primary);const SignDescription=styled(Text)(_templateObject14||(_templateObject14=_taggedTemplateLiteral([\"\\n  margin-bottom: \",\";\\n  max-width: 300px;\\n  margin-left: auto;\\n  margin-right: auto;\\n\\n  @media (max-width: \",\") {\\n    margin-bottom: \",\";\\n  }\\n\"])),theme.spacing[6],theme.breakpoints.sm,theme.spacing[4]);const StatusIndicator=styled.div(_templateObject15||(_templateObject15=_taggedTemplateLiteral([\"\\n  position: absolute;\\n  top: \",\";\\n  right: \",\";\\n  width: 56px;\\n  height: 56px;\\n  border-radius: \",\";\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: \",\";\\n  color: \",\";\\n  transition: all \",\";\\n  box-shadow: \",\";\\n  z-index: 10;\\n\\n  \",\"\\n\\n  \",\"\\n\\n  @media (max-width: \",\") {\\n    width: 48px;\\n    height: 48px;\\n    top: \",\";\\n    right: \",\";\\n  }\\n\"])),theme.spacing[4],theme.spacing[4],theme.borderRadius.full,props=>{if(props.isCorrect)return theme.colors.gradients.success;if(props.isIncorrect)return theme.colors.gradients.error;if(props.isDetecting)return theme.colors.gradients.warning;return'transparent';},theme.colors.text.inverse,theme.transitions.normal,props=>{if(props.isCorrect)return theme.shadows.glowSuccess;if(props.isIncorrect)return theme.shadows.glowError;return theme.shadows.md;},props=>props.isDetecting&&css(_templateObject16||(_templateObject16=_taggedTemplateLiteral([\"\\n    animation: \",\" 1.5s ease infinite;\\n  \"])),successPulse),props=>props.isCorrect&&css(_templateObject17||(_templateObject17=_taggedTemplateLiteral([\"\\n    &::before {\\n      content: '';\\n      position: absolute;\\n      top: -10px;\\n      right: -10px;\\n      width: 20px;\\n      height: 20px;\\n      background: \",\";\\n      border-radius: \",\";\\n      animation: \",\" 1s ease;\\n    }\\n  \"])),theme.colors.warning[400],theme.borderRadius.full,sparkle),theme.breakpoints.sm,theme.spacing[3],theme.spacing[3]);const ProgressBar=styled.div(_templateObject18||(_templateObject18=_taggedTemplateLiteral([\"\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  height: 6px;\\n  background: \",\";\\n  transition: width \",\";\\n  border-radius: 0 0 \",\" \",\";\\n  box-shadow: 0 -2px 10px rgba(34, 197, 94, 0.3);\\n\\n  &::after {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: 0;\\n    right: 0;\\n    bottom: 0;\\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\\n    animation: shimmer 2s infinite;\\n  }\\n\"])),theme.colors.gradients.success,theme.transitions.slow,theme.borderRadius['3xl'],theme.borderRadius['3xl']);const CardNumber=styled(Badge)(_templateObject19||(_templateObject19=_taggedTemplateLiteral([\"\\n  position: absolute;\\n  top: \",\";\\n  left: \",\";\\n  background: rgba(255, 255, 255, 0.9);\\n  backdrop-filter: blur(10px);\\n  color: \",\";\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  font-weight: \",\";\\n  font-size: \",\";\\n  padding: \",\" \",\";\\n  box-shadow: \",\";\\n\\n  @media (max-width: \",\") {\\n    top: \",\";\\n    left: \",\";\\n    font-size: \",\";\\n    padding: \",\" \",\";\\n  }\\n\"])),theme.spacing[4],theme.spacing[4],theme.colors.primary[700],theme.typography.fontWeight.semibold,theme.typography.fontSize.sm,theme.spacing[2],theme.spacing[3],theme.shadows.sm,theme.breakpoints.sm,theme.spacing[3],theme.spacing[3],theme.typography.fontSize.xs,theme.spacing[1],theme.spacing[2]);const shimmer=keyframes(_templateObject20||(_templateObject20=_taggedTemplateLiteral([\"\\n  0% { transform: translateX(-100%); }\\n  100% { transform: translateX(100%); }\\n\"])));const FlashCard=_ref=>{let{sign,cardNumber,totalCards,isCorrect,isIncorrect,isDetecting,slideDirection,progress=0}=_ref;const[imgError,setImgError]=useState(false);const[animationKey,setAnimationKey]=useState(0);useEffect(()=>{setAnimationKey(prev=>prev+1);setImgError(false);},[sign.key]);const getStatusIcon=()=>{if(isCorrect)return/*#__PURE__*/_jsx(CheckCircle,{size:20});if(isIncorrect)return/*#__PURE__*/_jsx(RotateCcw,{size:20});if(isDetecting)return/*#__PURE__*/_jsx(Zap,{size:20});return null;};return/*#__PURE__*/_jsx(CardContainer,{children:/*#__PURE__*/_jsxs(ModernCard,{isCorrect:isCorrect,isIncorrect:isIncorrect,slideDirection:slideDirection,children:[/*#__PURE__*/_jsxs(CardNumber,{variant:\"primary\",children:[cardNumber,\" / \",totalCards]}),/*#__PURE__*/_jsx(StatusIndicator,{isCorrect:isCorrect,isIncorrect:isIncorrect,isDetecting:isDetecting,children:getStatusIcon()}),/*#__PURE__*/_jsx(SignGif,{children:!imgError?/*#__PURE__*/_jsx(\"img\",{src:sign.gif,alt:sign.name,onError:()=>setImgError(true)}):/*#__PURE__*/_jsx(\"div\",{className:\"fallback\",children:/*#__PURE__*/_jsx(Sparkles,{size:60})})}),/*#__PURE__*/_jsx(SignName,{level:2,gradient:\"primary\",children:sign.name}),/*#__PURE__*/_jsx(SignDescription,{size:\"lg\",muted:true,children:sign.description}),/*#__PURE__*/_jsx(ProgressBar,{style:{width:\"\".concat(progress,\"%\")}})]},animationKey)});};export default FlashCard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "keyframes", "css", "CheckCircle", "RotateCcw", "ArrowRight", "ArrowLeft", "Target", "Zap", "<PERSON><PERSON><PERSON>", "theme", "Card", "Badge", "Text", "Heading", "jsx", "_jsx", "jsxs", "_jsxs", "flipIn", "_templateObject", "_taggedTemplateLiteral", "slideInRight", "_templateObject2", "slideInLeft", "_templateObject3", "successPulse", "_templateObject4", "shadows", "lg", "glowSuccess", "shake", "_templateObject5", "sparkle", "_templateObject6", "CardContainer", "div", "_templateObject7", "breakpoints", "md", "sm", "spacing", "ModernCard", "_templateObject8", "colors", "background", "borderRadius", "xl", "transitions", "normal", "neutral", "props", "isCorrect", "_templateObject9", "isIncorrect", "_templateObject0", "slideDirection", "_templateObject1", "_templateObject10", "_templateObject11", "gradients", "success", "error", "primary", "concat", "SignGif", "_templateObject12", "surface", "text", "tertiary", "SignName", "_templateObject13", "SignDescription", "_templateObject14", "StatusIndicator", "_templateObject15", "full", "isDetecting", "warning", "inverse", "glowError", "_templateObject16", "_templateObject17", "ProgressBar", "_templateObject18", "slow", "CardNumber", "_templateObject19", "typography", "fontWeight", "semibold", "fontSize", "xs", "shimmer", "_templateObject20", "FlashCard", "_ref", "sign", "cardNumber", "totalCards", "progress", "imgError", "setImgError", "animationKey", "setAnimationKey", "prev", "key", "getStatusIcon", "size", "children", "variant", "src", "gif", "alt", "name", "onError", "className", "level", "gradient", "muted", "description", "style", "width"], "sources": ["D:/ASL/ASL-Training/src/components/FlashCard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled, { keyframes, css } from 'styled-components';\nimport { CheckCircle, RotateCcw, ArrowRight, ArrowLeft, Target, Zap, Sparkles } from 'lucide-react';\nimport { theme } from '../styles/theme';\nimport { Card, Badge, Text, Heading } from './ui/ModernComponents';\n\n// Modern Animations\nconst flipIn = keyframes`\n  from {\n    transform: perspective(600px) rotateY(-90deg);\n    opacity: 0;\n  }\n  to {\n    transform: perspective(600px) rotateY(0deg);\n    opacity: 1;\n  }\n`;\n\nconst slideInRight = keyframes`\n  from {\n    transform: translateX(100%) scale(0.9);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0) scale(1);\n    opacity: 1;\n  }\n`;\n\nconst slideInLeft = keyframes`\n  from {\n    transform: translateX(-100%) scale(0.9);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0) scale(1);\n    opacity: 1;\n  }\n`;\n\nconst successPulse = keyframes`\n  0% {\n    transform: scale(1);\n    box-shadow: ${theme.shadows.lg};\n  }\n  50% {\n    transform: scale(1.02);\n    box-shadow: ${theme.shadows.glowSuccess};\n  }\n  100% {\n    transform: scale(1);\n    box-shadow: ${theme.shadows.lg};\n  }\n`;\n\nconst shake = keyframes`\n  0%, 100% { transform: translateX(0); }\n  25% { transform: translateX(-8px); }\n  75% { transform: translateX(8px); }\n`;\n\nconst sparkle = keyframes`\n  0%, 100% {\n    transform: scale(0) rotate(0deg);\n    opacity: 0;\n  }\n  50% {\n    transform: scale(1) rotate(180deg);\n    opacity: 1;\n  }\n`;\n\n// Modern Styled Components\nconst CardContainer = styled.div`\n  position: relative;\n  width: 100%;\n  max-width: 480px;\n  margin: 0 auto;\n  perspective: 1200px;\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    max-width: 420px;\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    max-width: 380px;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    max-width: 100%;\n    padding: 0 ${theme.spacing[2]};\n    margin: 0;\n  }\n`;\n\nconst ModernCard = styled(Card)`\n  background: ${theme.colors.background};\n  border-radius: ${theme.borderRadius['3xl']};\n  box-shadow: ${theme.shadows.xl};\n  padding: ${theme.spacing[8]};\n  text-align: center;\n  position: relative;\n  overflow: hidden;\n  transition: all ${theme.transitions.normal};\n  border: 1px solid ${theme.colors.neutral[100]};\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: ${theme.spacing[6]};\n    border-radius: ${theme.borderRadius['2xl']};\n  }\n\n  animation: ${props => {\n    if (props.isCorrect) return css`${successPulse} 0.8s ease`;\n    if (props.isIncorrect) return css`${shake} 0.6s ease`;\n    if (props.slideDirection === 'right') return css`${slideInRight} 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)`;\n    if (props.slideDirection === 'left') return css`${slideInLeft} 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)`;\n    return css`${flipIn} 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)`;\n  }};\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 6px;\n    background: ${props => {\n      if (props.isCorrect) return theme.colors.gradients.success;\n      if (props.isIncorrect) return theme.colors.gradients.error;\n      return theme.colors.gradients.primary;\n    }};\n    border-radius: ${theme.borderRadius['3xl']} ${theme.borderRadius['3xl']} 0 0;\n  }\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: -50%;\n    left: -50%;\n    width: 200%;\n    height: 200%;\n    background: ${props => {\n      if (props.isCorrect) return `radial-gradient(circle, ${theme.colors.success[100]} 0%, transparent 70%)`;\n      if (props.isIncorrect) return `radial-gradient(circle, ${theme.colors.error[100]} 0%, transparent 70%)`;\n      return 'transparent';\n    }};\n    opacity: ${props => (props.isCorrect || props.isIncorrect) ? 0.3 : 0};\n    transition: opacity ${theme.transitions.normal};\n    pointer-events: none;\n    z-index: 0;\n  }\n\n  > * {\n    position: relative;\n    z-index: 1;\n  }\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    padding: ${theme.spacing[7]};\n    border-radius: ${theme.borderRadius['2xl']};\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    padding: ${theme.spacing[6]};\n    border-radius: ${theme.borderRadius['2xl']};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: ${theme.spacing[5]};\n    border-radius: ${theme.borderRadius.xl};\n    box-shadow: ${theme.shadows.lg};\n  }\n`;\n\nconst SignGif = styled.div`\n  width: 240px;\n  height: 240px;\n  margin: 0 auto ${theme.spacing[6]};\n  border-radius: ${theme.borderRadius['2xl']};\n  overflow: hidden;\n  background: ${theme.colors.gradients.surface};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  box-shadow: ${theme.shadows.md};\n  border: 2px solid ${theme.colors.neutral[100]};\n  transition: all ${theme.transitions.normal};\n\n  &:hover {\n    transform: scale(1.02);\n    box-shadow: ${theme.shadows.lg};\n  }\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: ${theme.borderRadius.xl};\n    transition: transform ${theme.transitions.normal};\n  }\n\n  .fallback {\n    font-size: 5rem;\n    color: ${theme.colors.text.tertiary};\n    filter: grayscale(0.3);\n  }\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    width: 220px;\n    height: 220px;\n    margin-bottom: ${theme.spacing[6]};\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    width: 200px;\n    height: 200px;\n    margin-bottom: ${theme.spacing[5]};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    width: 160px;\n    height: 160px;\n    margin-bottom: ${theme.spacing[4]};\n    border-radius: ${theme.borderRadius.lg};\n  }\n`;\n\nconst SignName = styled(Heading)`\n  margin-bottom: ${theme.spacing[2]};\n  background: ${theme.colors.gradients.primary};\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n`;\n\nconst SignDescription = styled(Text)`\n  margin-bottom: ${theme.spacing[6]};\n  max-width: 300px;\n  margin-left: auto;\n  margin-right: auto;\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    margin-bottom: ${theme.spacing[4]};\n  }\n`;\n\nconst StatusIndicator = styled.div`\n  position: absolute;\n  top: ${theme.spacing[4]};\n  right: ${theme.spacing[4]};\n  width: 56px;\n  height: 56px;\n  border-radius: ${theme.borderRadius.full};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: ${props => {\n    if (props.isCorrect) return theme.colors.gradients.success;\n    if (props.isIncorrect) return theme.colors.gradients.error;\n    if (props.isDetecting) return theme.colors.gradients.warning;\n    return 'transparent';\n  }};\n  color: ${theme.colors.text.inverse};\n  transition: all ${theme.transitions.normal};\n  box-shadow: ${props => {\n    if (props.isCorrect) return theme.shadows.glowSuccess;\n    if (props.isIncorrect) return theme.shadows.glowError;\n    return theme.shadows.md;\n  }};\n  z-index: 10;\n\n  ${props => props.isDetecting && css`\n    animation: ${successPulse} 1.5s ease infinite;\n  `}\n\n  ${props => props.isCorrect && css`\n    &::before {\n      content: '';\n      position: absolute;\n      top: -10px;\n      right: -10px;\n      width: 20px;\n      height: 20px;\n      background: ${theme.colors.warning[400]};\n      border-radius: ${theme.borderRadius.full};\n      animation: ${sparkle} 1s ease;\n    }\n  `}\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    width: 48px;\n    height: 48px;\n    top: ${theme.spacing[3]};\n    right: ${theme.spacing[3]};\n  }\n`;\n\nconst ProgressBar = styled.div`\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  height: 6px;\n  background: ${theme.colors.gradients.success};\n  transition: width ${theme.transitions.slow};\n  border-radius: 0 0 ${theme.borderRadius['3xl']} ${theme.borderRadius['3xl']};\n  box-shadow: 0 -2px 10px rgba(34, 197, 94, 0.3);\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\n    animation: shimmer 2s infinite;\n  }\n`;\n\nconst CardNumber = styled(Badge)`\n  position: absolute;\n  top: ${theme.spacing[4]};\n  left: ${theme.spacing[4]};\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(10px);\n  color: ${theme.colors.primary[700]};\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  font-weight: ${theme.typography.fontWeight.semibold};\n  font-size: ${theme.typography.fontSize.sm};\n  padding: ${theme.spacing[2]} ${theme.spacing[3]};\n  box-shadow: ${theme.shadows.sm};\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    top: ${theme.spacing[3]};\n    left: ${theme.spacing[3]};\n    font-size: ${theme.typography.fontSize.xs};\n    padding: ${theme.spacing[1]} ${theme.spacing[2]};\n  }\n`;\n\nconst shimmer = keyframes`\n  0% { transform: translateX(-100%); }\n  100% { transform: translateX(100%); }\n`;\n\nconst FlashCard = ({ \n  sign, \n  cardNumber, \n  totalCards, \n  isCorrect, \n  isIncorrect, \n  isDetecting,\n  slideDirection,\n  progress = 0 \n}) => {\n  const [imgError, setImgError] = useState(false);\n  const [animationKey, setAnimationKey] = useState(0);\n\n  useEffect(() => {\n    setAnimationKey(prev => prev + 1);\n    setImgError(false);\n  }, [sign.key]);\n\n  const getStatusIcon = () => {\n    if (isCorrect) return <CheckCircle size={20} />;\n    if (isIncorrect) return <RotateCcw size={20} />;\n    if (isDetecting) return <Zap size={20} />;\n    return null;\n  };\n\n  return (\n    <CardContainer>\n      <ModernCard\n        key={animationKey}\n        isCorrect={isCorrect}\n        isIncorrect={isIncorrect}\n        slideDirection={slideDirection}\n      >\n        <CardNumber variant=\"primary\">\n          {cardNumber} / {totalCards}\n        </CardNumber>\n\n        <StatusIndicator\n          isCorrect={isCorrect}\n          isIncorrect={isIncorrect}\n          isDetecting={isDetecting}\n        >\n          {getStatusIcon()}\n        </StatusIndicator>\n\n        <SignGif>\n          {!imgError ? (\n            <img\n              src={sign.gif}\n              alt={sign.name}\n              onError={() => setImgError(true)}\n            />\n          ) : (\n            <div className=\"fallback\">\n              <Sparkles size={60} />\n            </div>\n          )}\n        </SignGif>\n\n        <SignName level={2} gradient=\"primary\">\n          {sign.name}\n        </SignName>\n\n        <SignDescription size=\"lg\" muted>\n          {sign.description}\n        </SignDescription>\n\n        <ProgressBar style={{ width: `${progress}%` }} />\n      </ModernCard>\n    </CardContainer>\n  );\n};\n\nexport default FlashCard;\n"], "mappings": "6fAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,MAAM,EAAIC,SAAS,CAAEC,GAAG,KAAQ,mBAAmB,CAC1D,OAASC,WAAW,CAAEC,SAAS,CAAEC,UAAU,CAAEC,SAAS,CAAEC,MAAM,CAAEC,GAAG,CAAEC,QAAQ,KAAQ,cAAc,CACnG,OAASC,KAAK,KAAQ,iBAAiB,CACvC,OAASC,IAAI,CAAEC,KAAK,CAAEC,IAAI,CAAEC,OAAO,KAAQ,uBAAuB,CAElE;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,MAAM,CAAGlB,SAAS,CAAAmB,eAAA,GAAAA,eAAA,CAAAC,sBAAA,8KASvB,CAED,KAAM,CAAAC,YAAY,CAAGrB,SAAS,CAAAsB,gBAAA,GAAAA,gBAAA,CAAAF,sBAAA,6JAS7B,CAED,KAAM,CAAAG,WAAW,CAAGvB,SAAS,CAAAwB,gBAAA,GAAAA,gBAAA,CAAAJ,sBAAA,8JAS5B,CAED,KAAM,CAAAK,YAAY,CAAGzB,SAAS,CAAA0B,gBAAA,GAAAA,gBAAA,CAAAN,sBAAA,uMAGZX,KAAK,CAACkB,OAAO,CAACC,EAAE,CAIhBnB,KAAK,CAACkB,OAAO,CAACE,WAAW,CAIzBpB,KAAK,CAACkB,OAAO,CAACC,EAAE,CAEjC,CAED,KAAM,CAAAE,KAAK,CAAG9B,SAAS,CAAA+B,gBAAA,GAAAA,gBAAA,CAAAX,sBAAA,mIAItB,CAED,KAAM,CAAAY,OAAO,CAAGhC,SAAS,CAAAiC,gBAAA,GAAAA,gBAAA,CAAAb,sBAAA,6JASxB,CAED;AACA,KAAM,CAAAc,aAAa,CAAGnC,MAAM,CAACoC,GAAG,CAAAC,gBAAA,GAAAA,gBAAA,CAAAhB,sBAAA,sUAOTX,KAAK,CAAC4B,WAAW,CAACT,EAAE,CAIpBnB,KAAK,CAAC4B,WAAW,CAACC,EAAE,CAIpB7B,KAAK,CAAC4B,WAAW,CAACE,EAAE,CAE1B9B,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CAGhC,CAED,KAAM,CAAAC,UAAU,CAAG1C,MAAM,CAACW,IAAI,CAAC,CAAAgC,gBAAA,GAAAA,gBAAA,CAAAtB,sBAAA,miCACfX,KAAK,CAACkC,MAAM,CAACC,UAAU,CACpBnC,KAAK,CAACoC,YAAY,CAAC,KAAK,CAAC,CAC5BpC,KAAK,CAACkB,OAAO,CAACmB,EAAE,CACnBrC,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CAIT/B,KAAK,CAACsC,WAAW,CAACC,MAAM,CACtBvC,KAAK,CAACkC,MAAM,CAACM,OAAO,CAAC,GAAG,CAAC,CAExBxC,KAAK,CAAC4B,WAAW,CAACE,EAAE,CAC5B9B,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CACV/B,KAAK,CAACoC,YAAY,CAAC,KAAK,CAAC,CAG/BK,KAAK,EAAI,CACpB,GAAIA,KAAK,CAACC,SAAS,CAAE,MAAO,CAAAlD,GAAG,CAAAmD,gBAAA,GAAAA,gBAAA,CAAAhC,sBAAA,qBAAGK,YAAY,EAC9C,GAAIyB,KAAK,CAACG,WAAW,CAAE,MAAO,CAAApD,GAAG,CAAAqD,gBAAA,GAAAA,gBAAA,CAAAlC,sBAAA,qBAAGU,KAAK,EACzC,GAAIoB,KAAK,CAACK,cAAc,GAAK,OAAO,CAAE,MAAO,CAAAtD,GAAG,CAAAuD,gBAAA,GAAAA,gBAAA,CAAApC,sBAAA,kDAAGC,YAAY,EAC/D,GAAI6B,KAAK,CAACK,cAAc,GAAK,MAAM,CAAE,MAAO,CAAAtD,GAAG,CAAAwD,iBAAA,GAAAA,iBAAA,CAAArC,sBAAA,kDAAGG,WAAW,EAC7D,MAAO,CAAAtB,GAAG,CAAAyD,iBAAA,GAAAA,iBAAA,CAAAtC,sBAAA,kDAAGF,MAAM,EACrB,CAAC,CASegC,KAAK,EAAI,CACrB,GAAIA,KAAK,CAACC,SAAS,CAAE,MAAO,CAAA1C,KAAK,CAACkC,MAAM,CAACgB,SAAS,CAACC,OAAO,CAC1D,GAAIV,KAAK,CAACG,WAAW,CAAE,MAAO,CAAA5C,KAAK,CAACkC,MAAM,CAACgB,SAAS,CAACE,KAAK,CAC1D,MAAO,CAAApD,KAAK,CAACkC,MAAM,CAACgB,SAAS,CAACG,OAAO,CACvC,CAAC,CACgBrD,KAAK,CAACoC,YAAY,CAAC,KAAK,CAAC,CAAIpC,KAAK,CAACoC,YAAY,CAAC,KAAK,CAAC,CAUzDK,KAAK,EAAI,CACrB,GAAIA,KAAK,CAACC,SAAS,CAAE,iCAAAY,MAAA,CAAkCtD,KAAK,CAACkC,MAAM,CAACiB,OAAO,CAAC,GAAG,CAAC,0BAChF,GAAIV,KAAK,CAACG,WAAW,CAAE,iCAAAU,MAAA,CAAkCtD,KAAK,CAACkC,MAAM,CAACkB,KAAK,CAAC,GAAG,CAAC,0BAChF,MAAO,aAAa,CACtB,CAAC,CACUX,KAAK,EAAKA,KAAK,CAACC,SAAS,EAAID,KAAK,CAACG,WAAW,CAAI,GAAG,CAAG,CAAC,CAC9C5C,KAAK,CAACsC,WAAW,CAACC,MAAM,CAU3BvC,KAAK,CAAC4B,WAAW,CAACT,EAAE,CAC5BnB,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CACV/B,KAAK,CAACoC,YAAY,CAAC,KAAK,CAAC,CAGvBpC,KAAK,CAAC4B,WAAW,CAACC,EAAE,CAC5B7B,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CACV/B,KAAK,CAACoC,YAAY,CAAC,KAAK,CAAC,CAGvBpC,KAAK,CAAC4B,WAAW,CAACE,EAAE,CAC5B9B,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CACV/B,KAAK,CAACoC,YAAY,CAACC,EAAE,CACxBrC,KAAK,CAACkB,OAAO,CAACC,EAAE,CAEjC,CAED,KAAM,CAAAoC,OAAO,CAAGjE,MAAM,CAACoC,GAAG,CAAA8B,iBAAA,GAAAA,iBAAA,CAAA7C,sBAAA,m5BAGPX,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CAChB/B,KAAK,CAACoC,YAAY,CAAC,KAAK,CAAC,CAE5BpC,KAAK,CAACkC,MAAM,CAACgB,SAAS,CAACO,OAAO,CAK9BzD,KAAK,CAACkB,OAAO,CAACW,EAAE,CACV7B,KAAK,CAACkC,MAAM,CAACM,OAAO,CAAC,GAAG,CAAC,CAC3BxC,KAAK,CAACsC,WAAW,CAACC,MAAM,CAI1BvC,KAAK,CAACkB,OAAO,CAACC,EAAE,CAObnB,KAAK,CAACoC,YAAY,CAACC,EAAE,CACdrC,KAAK,CAACsC,WAAW,CAACC,MAAM,CAKvCvC,KAAK,CAACkC,MAAM,CAACwB,IAAI,CAACC,QAAQ,CAIhB3D,KAAK,CAAC4B,WAAW,CAACT,EAAE,CAGtBnB,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CAGd/B,KAAK,CAAC4B,WAAW,CAACC,EAAE,CAGtB7B,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CAGd/B,KAAK,CAAC4B,WAAW,CAACE,EAAE,CAGtB9B,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CAChB/B,KAAK,CAACoC,YAAY,CAACjB,EAAE,CAEzC,CAED,KAAM,CAAAyC,QAAQ,CAAGtE,MAAM,CAACc,OAAO,CAAC,CAAAyD,iBAAA,GAAAA,iBAAA,CAAAlD,sBAAA,0JACbX,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CACnB/B,KAAK,CAACkC,MAAM,CAACgB,SAAS,CAACG,OAAO,CAI7C,CAED,KAAM,CAAAS,eAAe,CAAGxE,MAAM,CAACa,IAAI,CAAC,CAAA4D,iBAAA,GAAAA,iBAAA,CAAApD,sBAAA,gKACjBX,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CAKZ/B,KAAK,CAAC4B,WAAW,CAACE,EAAE,CACtB9B,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CAEpC,CAED,KAAM,CAAAiC,eAAe,CAAG1E,MAAM,CAACoC,GAAG,CAAAuC,iBAAA,GAAAA,iBAAA,CAAAtD,sBAAA,qZAEzBX,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CACd/B,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CAGR/B,KAAK,CAACoC,YAAY,CAAC8B,IAAI,CAI1BzB,KAAK,EAAI,CACrB,GAAIA,KAAK,CAACC,SAAS,CAAE,MAAO,CAAA1C,KAAK,CAACkC,MAAM,CAACgB,SAAS,CAACC,OAAO,CAC1D,GAAIV,KAAK,CAACG,WAAW,CAAE,MAAO,CAAA5C,KAAK,CAACkC,MAAM,CAACgB,SAAS,CAACE,KAAK,CAC1D,GAAIX,KAAK,CAAC0B,WAAW,CAAE,MAAO,CAAAnE,KAAK,CAACkC,MAAM,CAACgB,SAAS,CAACkB,OAAO,CAC5D,MAAO,aAAa,CACtB,CAAC,CACQpE,KAAK,CAACkC,MAAM,CAACwB,IAAI,CAACW,OAAO,CAChBrE,KAAK,CAACsC,WAAW,CAACC,MAAM,CAC5BE,KAAK,EAAI,CACrB,GAAIA,KAAK,CAACC,SAAS,CAAE,MAAO,CAAA1C,KAAK,CAACkB,OAAO,CAACE,WAAW,CACrD,GAAIqB,KAAK,CAACG,WAAW,CAAE,MAAO,CAAA5C,KAAK,CAACkB,OAAO,CAACoD,SAAS,CACrD,MAAO,CAAAtE,KAAK,CAACkB,OAAO,CAACW,EAAE,CACzB,CAAC,CAGCY,KAAK,EAAIA,KAAK,CAAC0B,WAAW,EAAI3E,GAAG,CAAA+E,iBAAA,GAAAA,iBAAA,CAAA5D,sBAAA,oDACpBK,YAAY,CAC1B,CAECyB,KAAK,EAAIA,KAAK,CAACC,SAAS,EAAIlD,GAAG,CAAAgF,iBAAA,GAAAA,iBAAA,CAAA7D,sBAAA,sPAQfX,KAAK,CAACkC,MAAM,CAACkC,OAAO,CAAC,GAAG,CAAC,CACtBpE,KAAK,CAACoC,YAAY,CAAC8B,IAAI,CAC3B3C,OAAO,CAEvB,CAEoBvB,KAAK,CAAC4B,WAAW,CAACE,EAAE,CAGhC9B,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CACd/B,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CAE5B,CAED,KAAM,CAAA0C,WAAW,CAAGnF,MAAM,CAACoC,GAAG,CAAAgD,iBAAA,GAAAA,iBAAA,CAAA/D,sBAAA,ucAKdX,KAAK,CAACkC,MAAM,CAACgB,SAAS,CAACC,OAAO,CACxBnD,KAAK,CAACsC,WAAW,CAACqC,IAAI,CACrB3E,KAAK,CAACoC,YAAY,CAAC,KAAK,CAAC,CAAIpC,KAAK,CAACoC,YAAY,CAAC,KAAK,CAAC,CAa5E,CAED,KAAM,CAAAwC,UAAU,CAAGtF,MAAM,CAACY,KAAK,CAAC,CAAA2E,iBAAA,GAAAA,iBAAA,CAAAlE,sBAAA,mYAEvBX,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CACf/B,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CAGf/B,KAAK,CAACkC,MAAM,CAACmB,OAAO,CAAC,GAAG,CAAC,CAEnBrD,KAAK,CAAC8E,UAAU,CAACC,UAAU,CAACC,QAAQ,CACtChF,KAAK,CAAC8E,UAAU,CAACG,QAAQ,CAACnD,EAAE,CAC9B9B,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CAAI/B,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CACjC/B,KAAK,CAACkB,OAAO,CAACY,EAAE,CAET9B,KAAK,CAAC4B,WAAW,CAACE,EAAE,CAChC9B,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CACf/B,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CACX/B,KAAK,CAAC8E,UAAU,CAACG,QAAQ,CAACC,EAAE,CAC9BlF,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CAAI/B,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CAElD,CAED,KAAM,CAAAoD,OAAO,CAAG5F,SAAS,CAAA6F,iBAAA,GAAAA,iBAAA,CAAAzE,sBAAA,2FAGxB,CAED,KAAM,CAAA0E,SAAS,CAAGC,IAAA,EASZ,IATa,CACjBC,IAAI,CACJC,UAAU,CACVC,UAAU,CACV/C,SAAS,CACTE,WAAW,CACXuB,WAAW,CACXrB,cAAc,CACd4C,QAAQ,CAAG,CACb,CAAC,CAAAJ,IAAA,CACC,KAAM,CAACK,QAAQ,CAAEC,WAAW,CAAC,CAAGxG,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAACyG,YAAY,CAAEC,eAAe,CAAC,CAAG1G,QAAQ,CAAC,CAAC,CAAC,CAEnDC,SAAS,CAAC,IAAM,CACdyG,eAAe,CAACC,IAAI,EAAIA,IAAI,CAAG,CAAC,CAAC,CACjCH,WAAW,CAAC,KAAK,CAAC,CACpB,CAAC,CAAE,CAACL,IAAI,CAACS,GAAG,CAAC,CAAC,CAEd,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,CAC1B,GAAIvD,SAAS,CAAE,mBAAOpC,IAAA,CAACb,WAAW,EAACyG,IAAI,CAAE,EAAG,CAAE,CAAC,CAC/C,GAAItD,WAAW,CAAE,mBAAOtC,IAAA,CAACZ,SAAS,EAACwG,IAAI,CAAE,EAAG,CAAE,CAAC,CAC/C,GAAI/B,WAAW,CAAE,mBAAO7D,IAAA,CAACR,GAAG,EAACoG,IAAI,CAAE,EAAG,CAAE,CAAC,CACzC,MAAO,KAAI,CACb,CAAC,CAED,mBACE5F,IAAA,CAACmB,aAAa,EAAA0E,QAAA,cACZ3F,KAAA,CAACwB,UAAU,EAETU,SAAS,CAAEA,SAAU,CACrBE,WAAW,CAAEA,WAAY,CACzBE,cAAc,CAAEA,cAAe,CAAAqD,QAAA,eAE/B3F,KAAA,CAACoE,UAAU,EAACwB,OAAO,CAAC,SAAS,CAAAD,QAAA,EAC1BX,UAAU,CAAC,KAAG,CAACC,UAAU,EAChB,CAAC,cAEbnF,IAAA,CAAC0D,eAAe,EACdtB,SAAS,CAAEA,SAAU,CACrBE,WAAW,CAAEA,WAAY,CACzBuB,WAAW,CAAEA,WAAY,CAAAgC,QAAA,CAExBF,aAAa,CAAC,CAAC,CACD,CAAC,cAElB3F,IAAA,CAACiD,OAAO,EAAA4C,QAAA,CACL,CAACR,QAAQ,cACRrF,IAAA,QACE+F,GAAG,CAAEd,IAAI,CAACe,GAAI,CACdC,GAAG,CAAEhB,IAAI,CAACiB,IAAK,CACfC,OAAO,CAAEA,CAAA,GAAMb,WAAW,CAAC,IAAI,CAAE,CAClC,CAAC,cAEFtF,IAAA,QAAKoG,SAAS,CAAC,UAAU,CAAAP,QAAA,cACvB7F,IAAA,CAACP,QAAQ,EAACmG,IAAI,CAAE,EAAG,CAAE,CAAC,CACnB,CACN,CACM,CAAC,cAEV5F,IAAA,CAACsD,QAAQ,EAAC+C,KAAK,CAAE,CAAE,CAACC,QAAQ,CAAC,SAAS,CAAAT,QAAA,CACnCZ,IAAI,CAACiB,IAAI,CACF,CAAC,cAEXlG,IAAA,CAACwD,eAAe,EAACoC,IAAI,CAAC,IAAI,CAACW,KAAK,MAAAV,QAAA,CAC7BZ,IAAI,CAACuB,WAAW,CACF,CAAC,cAElBxG,IAAA,CAACmE,WAAW,EAACsC,KAAK,CAAE,CAAEC,KAAK,IAAA1D,MAAA,CAAKoC,QAAQ,KAAI,CAAE,CAAE,CAAC,GAvC5CG,YAwCK,CAAC,CACA,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAR,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}