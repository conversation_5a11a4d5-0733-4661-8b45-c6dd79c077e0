{"ast": null, "code": "var _jsxFileName = \"D:\\\\ASL\\\\ASL-Training\\\\src\\\\components\\\\FlashCardTraining.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useCallback, useEffect } from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport Webcam from 'react-webcam';\nimport { ArrowLeft, ArrowRight, RotateCcw, Home, Camera, Wifi, WifiOff, RefreshCw, CheckCircle, Trophy, Target, Zap, Sparkles, Award } from 'lucide-react';\nimport FlashCard from './FlashCard';\nimport { useSignDetection } from '../hooks/useSignDetection';\nimport { getSignsForLevel, getLevelInfo } from '../data/signLevels';\nimport { theme } from '../styles/theme';\nimport { Container, Card, Button, Heading, Text, Badge } from './ui/ModernComponents';\n\n// Animations\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst fadeIn = keyframes`\n  from { opacity: 0; transform: translateY(20px); }\n  to { opacity: 1; transform: translateY(0); }\n`;\nconst celebration = keyframes`\n  0%, 100% { transform: scale(1) rotate(0deg); }\n  25% { transform: scale(1.1) rotate(-5deg); }\n  75% { transform: scale(1.1) rotate(5deg); }\n`;\n\n// Modern Styled Components\nconst ModernContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  padding: ${theme.spacing[4]};\n  position: relative;\n  overflow-x: hidden;\n\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n\n  > * {\n    position: relative;\n    z-index: 1;\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    padding: ${theme.spacing[3]};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: ${theme.spacing[2]};\n    min-height: 100dvh; /* Use dynamic viewport height for mobile */\n  }\n`;\n_c = ModernContainer;\nconst Header = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${theme.spacing[8]};\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    flex-direction: column;\n    gap: ${theme.spacing[4]};\n  }\n`;\nconst ModernHeader = styled(Card)`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${theme.spacing[8]};\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: ${theme.shadows.xl};\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    flex-direction: column;\n    gap: ${theme.spacing[4]};\n    text-align: center;\n    margin-bottom: ${theme.spacing[6]};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    margin-bottom: ${theme.spacing[4]};\n    padding: ${theme.spacing[3]};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    margin-bottom: ${theme.spacing[4]};\n  }\n`;\n_c2 = ModernHeader;\nconst ModernBackButton = styled(Button)`\n  background: rgba(59, 130, 246, 0.15);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(59, 130, 246, 0.3);\n  color: #3b82f6;\n  font-weight: 600;\n  transition: all 0.3s ease;\n\n  &:hover:not(:disabled) {\n    background: rgba(59, 130, 246, 0.25);\n    border-color: rgba(59, 130, 246, 0.5);\n    color: #2563eb;\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);\n  }\n\n  &:active:not(:disabled) {\n    transform: translateY(0);\n    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    width: 100%;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: 0.75rem 1rem;\n    font-size: 0.875rem;\n  }\n`;\n_c3 = ModernBackButton;\nconst ModernLevelInfo = styled.div`\n  text-align: center;\n  color: ${theme.colors.text.primary};\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    order: -1;\n  }\n`;\n_c4 = ModernLevelInfo;\nconst ModernLevelTitle = styled(Heading)`\n  margin-bottom: ${theme.spacing[1]};\n  background: ${theme.colors.gradients.primary};\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n`;\n_c5 = ModernLevelTitle;\nconst ModernLevelTheme = styled(Text)`\n  opacity: 0.8;\n  font-weight: ${theme.typography.fontWeight.medium};\n`;\n_c6 = ModernLevelTheme;\nconst ModernConnectionStatus = styled(Badge)`\n  background: ${props => props.isConnected ? 'rgba(34, 197, 94, 0.15)' : 'rgba(239, 68, 68, 0.15)'};\n  color: ${props => props.isConnected ? theme.colors.success[700] : theme.colors.error[700]};\n  border: 1px solid ${props => props.isConnected ? theme.colors.success[200] : theme.colors.error[200]};\n  backdrop-filter: blur(10px);\n  cursor: ${props => props.isConnected ? 'default' : 'pointer'};\n\n  &:hover {\n    background: ${props => props.isConnected ? 'rgba(34, 197, 94, 0.2)' : 'rgba(239, 68, 68, 0.2)'};\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    width: 100%;\n    justify-content: center;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: 0.5rem 0.75rem;\n    font-size: 0.75rem;\n    \n    .connection-text {\n      display: none; /* Hide text on mobile, show only icon */\n    }\n  }\n`;\n_c7 = ModernConnectionStatus;\nconst MainContent = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 500px;\n  gap: ${theme.spacing[6]};\n  max-width: 1400px;\n  margin: 0 auto;\n  align-items: start;\n\n  @media (max-width: ${theme.breakpoints.xl}) {\n    grid-template-columns: 1fr 450px;\n    gap: ${theme.spacing[5]};\n    max-width: 1200px;\n  }\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    grid-template-columns: 1fr;\n    gap: ${theme.spacing[6]};\n    max-width: 800px;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    /* On mobile, show flash card first, then camera */\n    display: flex;\n    flex-direction: column;\n    gap: ${theme.spacing[4]};\n    max-width: 100%;\n  }\n`;\n_c8 = MainContent;\nconst FlashCardSection = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  animation: ${fadeIn} 0.6s ease;\n  position: sticky;\n  top: ${theme.spacing[8]};\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    position: static;\n  }\n`;\n_c9 = FlashCardSection;\nconst ProgressSection = styled.div`\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20px;\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n  text-align: center;\n  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    padding: 1.25rem;\n    margin-bottom: 1.5rem;\n    border-radius: 18px;\n  }\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: 1rem;\n    margin-bottom: 1.5rem;\n    border-radius: 16px;\n  }\n`;\n_c0 = ProgressSection;\nconst ProgressTitle = styled.h3`\n  font-size: 1.25rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin-bottom: 1rem;\n`;\n_c1 = ProgressTitle;\nconst ProgressBar = styled.div`\n  width: 100%;\n  height: 16px;\n  background: #e2e8f0;\n  border-radius: 8px;\n  overflow: hidden;\n  margin-bottom: 1.5rem;\n  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    height: 14px;\n    margin-bottom: 1rem;\n  }\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    height: 12px;\n    margin-bottom: 1rem;\n  }\n`;\n_c10 = ProgressBar;\nconst ProgressFill = styled.div`\n  height: 100%;\n  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #10b981);\n  border-radius: 8px;\n  transition: width 0.5s ease;\n  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    border-radius: 7px;\n  }\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    border-radius: 6px;\n  }\n`;\n_c11 = ProgressFill;\nconst ProgressText = styled.div`\n  font-size: 1rem;\n  color: #64748b;\n  font-weight: 600;\n`;\n_c12 = ProgressText;\nconst Controls = styled.div`\n  display: flex;\n  gap: 1rem;\n  margin-top: 2rem;\n  justify-content: center;\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    gap: 0.75rem;\n    margin-top: 1.5rem;\n  }\n  \n  @media (max-width: 768px) {\n    gap: 0.75rem;\n    margin-top: 1.5rem;\n    flex-wrap: wrap;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    gap: 0.5rem;\n    margin-top: 1rem;\n    padding: 0 ${theme.spacing[2]};\n  }\n`;\n_c13 = Controls;\nconst ControlButton = styled.button`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 1rem 2rem;\n  border: none;\n  border-radius: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  min-height: 48px; /* Minimum touch target size */\n  touch-action: manipulation; /* Optimize for touch */\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    padding: 1rem 2rem;\n    font-size: 1rem;\n    min-height: 48px;\n  }\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: 0.75rem 1.5rem;\n    font-size: 0.875rem;\n    min-height: 44px;\n  }\n  \n  ${props => {\n  if (props.variant === 'primary') {\n    return `\n        background: linear-gradient(135deg, #3b82f6, #8b5cf6);\n        color: white;\n        &:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);\n        }\n      `;\n  }\n  if (props.variant === 'success') {\n    return `\n        background: linear-gradient(135deg, #10b981, #34d399);\n        color: white;\n        &:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);\n        }\n      `;\n  }\n  return `\n      background: rgba(255, 255, 255, 0.9);\n      color: #64748b;\n      &:hover {\n        background: white;\n        transform: translateY(-2px);\n      }\n    `;\n}}\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none !important;\n  }\n  \n  @media (max-width: 768px) {\n    padding: 0.75rem 1.5rem;\n    font-size: 0.875rem;\n  }\n`;\n_c14 = ControlButton;\nconst CameraSection = styled(Card)`\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\n  border-radius: 24px;\n  padding: 2rem;\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    order: 1; /* Show after flash card on mobile */\n    padding: 1.5rem;\n    border-radius: 20px;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    order: 2; /* Ensure camera comes after flash card on mobile */\n    margin: 0 -${theme.spacing[2]}; /* Full width on mobile */\n    border-radius: 16px;\n    padding: 1rem;\n  }\n`;\n_c15 = CameraSection;\nconst CameraTitle = styled.h3`\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin-bottom: 1.5rem;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    font-size: 1.25rem;\n    margin-bottom: 1rem;\n    gap: 0.5rem;\n  }\n`;\n_c16 = CameraTitle;\nconst WebcamContainer = styled.div`\n  position: relative;\n  border-radius: 20px;\n  overflow: hidden;\n  background: #000;\n  margin-bottom: 1.5rem;\n  aspect-ratio: 4/3; /* Maintain aspect ratio */\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n  border: 2px solid rgba(255, 255, 255, 0.1);\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    border-radius: 16px;\n    margin-bottom: 1rem;\n  }\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    border-radius: 12px;\n    margin: 0 -${theme.spacing[2]} 1rem -${theme.spacing[2]}; /* Full width on mobile */\n  }\n`;\n_c17 = WebcamContainer;\nconst StatusOverlay = styled.div`\n  position: absolute;\n  top: 1rem;\n  left: 1rem;\n  right: 1rem;\n  background: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: 0.75rem;\n  border-radius: 8px;\n  font-weight: 600;\n  text-align: center;\n  font-size: 0.875rem;\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    top: 0.5rem;\n    left: 0.5rem;\n    right: 0.5rem;\n    padding: 0.5rem;\n    font-size: 0.75rem;\n  }\n`;\n_c18 = StatusOverlay;\nconst PredictionDisplay = styled.div`\n  background: linear-gradient(135deg, #f8fafc, #f1f5f9);\n  border-radius: 16px;\n  padding: 1.5rem;\n  text-align: center;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    padding: 1rem;\n    border-radius: 12px;\n  }\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: 0.75rem;\n    margin: 0 ${theme.spacing[2]};\n    border-radius: 12px;\n  }\n`;\n_c19 = PredictionDisplay;\nconst PredictionText = styled.div`\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin-bottom: 0.75rem;\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    font-size: 1.125rem;\n    margin-bottom: 0.5rem;\n  }\n`;\n_c20 = PredictionText;\nconst ConfidenceText = styled.div`\n  font-size: 0.875rem;\n  color: #64748b;\n`;\n_c21 = ConfidenceText;\nconst CompletionModal = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  animation: ${fadeIn} 0.3s ease;\n`;\n_c22 = CompletionModal;\nconst ModalContent = styled.div`\n  background: white;\n  border-radius: 24px;\n  padding: 3rem;\n  text-align: center;\n  max-width: 500px;\n  margin: 1rem;\n  animation: ${celebration} 0.6s ease;\n`;\n_c23 = ModalContent;\nconst ModalTitle = styled.h2`\n  font-size: 2.5rem;\n  font-weight: 800;\n  color: #1e293b;\n  margin-bottom: 1rem;\n`;\n_c24 = ModalTitle;\nconst ModalText = styled.p`\n  font-size: 1.125rem;\n  color: #64748b;\n  margin-bottom: 2rem;\n  line-height: 1.6;\n`;\n_c25 = ModalText;\nconst ModalButtons = styled.div`\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n`;\n_c26 = ModalButtons;\nconst FlashCardTraining = ({\n  level,\n  onBack,\n  userProgress = {},\n  onProgressUpdate\n}) => {\n  _s();\n  const webcamRef = useRef(null);\n  const [currentCardIndex, setCurrentCardIndex] = useState(0);\n  const [completedCards, setCompletedCards] = useState(new Set());\n  const [cardStates, setCardStates] = useState({});\n  const [slideDirection, setSlideDirection] = useState(null);\n  const [showCompletion, setShowCompletion] = useState(false);\n  const [isCapturing, setIsCapturing] = useState(false);\n  const levelInfo = getLevelInfo(level);\n  const signs = getSignsForLevel(level);\n  const currentSign = signs[currentCardIndex];\n\n  // Use sign detection hook\n  const {\n    isConnected,\n    prediction,\n    isAIRecording,\n    recordingStatus,\n    signMatched,\n    targetSign,\n    currentKeypoints,\n    startRecording: startAIRecording,\n    stopRecording: stopAIRecording,\n    startFrameCapture,\n    retryConnection,\n    setLevel\n  } = useSignDetection();\n  const progress = completedCards.size / signs.length * 100;\n\n  // Start detection when connected\n  const startDetection = useCallback(() => {\n    if (!webcamRef.current) return;\n    setIsCapturing(true);\n    startFrameCapture(webcamRef, 100);\n  }, [startFrameCapture]);\n\n  // Save training data when sign is detected correctly\n  const saveTrainingData = useCallback(async (signName, keypoints, confidence) => {\n    try {\n      console.log(`💾 Saving training data for ${signName} with confidence ${confidence}`);\n      const response = await fetch('http://localhost:8000/save-training-data', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          sign_name: signName,\n          keypoints: keypoints,\n          confidence: confidence,\n          timestamp: new Date().toISOString()\n        })\n      });\n      if (response.ok) {\n        const result = await response.json();\n        console.log(`✅ Training data saved: ${result.message}`);\n        return true;\n      } else {\n        console.error('❌ Failed to save training data');\n        return false;\n      }\n    } catch (error) {\n      console.error('❌ Error saving training data:', error);\n      return false;\n    }\n  }, []);\n  const nextCard = useCallback(() => {\n    if (currentCardIndex < signs.length - 1) {\n      setSlideDirection('right');\n      setCurrentCardIndex(prev => prev + 1);\n      setCardStates(prev => ({\n        ...prev,\n        [currentCardIndex]: null\n      }));\n      setTimeout(() => setSlideDirection(null), 500);\n    }\n  }, [currentCardIndex, signs.length]);\n\n  // Handle sign detection success with automatic recording and training data saving\n  useEffect(() => {\n    var _prediction$sign;\n    console.log(`🔍 Debug: signMatched=${signMatched}, currentSign=${currentSign === null || currentSign === void 0 ? void 0 : currentSign.name}, prediction=${prediction === null || prediction === void 0 ? void 0 : prediction.sign}, confidence=${prediction === null || prediction === void 0 ? void 0 : prediction.confidence}`);\n    if (signMatched && currentSign && (prediction === null || prediction === void 0 ? void 0 : (_prediction$sign = prediction.sign) === null || _prediction$sign === void 0 ? void 0 : _prediction$sign.toLowerCase()) === currentSign.name.toLowerCase()) {\n      console.log(`✅ Sign match confirmed: ${currentSign.name} with confidence ${prediction.confidence}`);\n\n      // Only proceed if this card hasn't been completed yet\n      if (!completedCards.has(currentCardIndex)) {\n        console.log(`🎯 Correct sign detected: ${currentSign.name} with confidence ${prediction.confidence}`);\n\n        // Save training data immediately when sign is detected correctly\n        const saveTrainingDataAsync = async () => {\n          if (currentKeypoints && (prediction === null || prediction === void 0 ? void 0 : prediction.confidence) >= 0.5) {\n            const saved = await saveTrainingData(currentSign.name, currentKeypoints, prediction.confidence);\n            if (saved) {\n              console.log(`✅ Training data saved successfully for ${currentSign.name}`);\n            }\n          }\n        };\n\n        // Save training data\n        saveTrainingDataAsync();\n\n        // Start automatic recording for additional training data\n        if (!isAIRecording && isConnected) {\n          console.log(`🎬 Starting automatic recording for ${currentSign.name}...`);\n          startAIRecording(currentSign.name, true); // Start immediate recording session\n\n          // Stop recording after 3 seconds\n          setTimeout(() => {\n            stopAIRecording();\n            console.log(`✅ Automatic recording completed for: ${currentSign.name}`);\n          }, 3000);\n        }\n\n        // Mark card as completed\n        setCardStates(prev => ({\n          ...prev,\n          [currentCardIndex]: 'correct'\n        }));\n        setCompletedCards(prev => new Set([...prev, currentCardIndex]));\n\n        // Update progress\n        if (onProgressUpdate) {\n          const newCompletedCount = completedCards.size + 1;\n          onProgressUpdate(level, newCompletedCount, signs.length);\n        }\n\n        // Auto-advance after 2 seconds (allowing time for user to see success)\n        setTimeout(() => {\n          if (currentCardIndex < signs.length - 1) {\n            nextCard();\n          } else {\n            // Level completed\n            setShowCompletion(true);\n            if (onProgressUpdate) {\n              onProgressUpdate(level, signs.length, signs.length);\n            }\n          }\n        }, 2000);\n      } else {\n        console.log(`⚠️ Card ${currentCardIndex} already completed`);\n      }\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [signMatched, currentSign, prediction, currentCardIndex, signs.length, level, onProgressUpdate, isAIRecording, isConnected, startAIRecording, stopAIRecording, completedCards, saveTrainingData, currentKeypoints]);\n\n  // Set level when connected\n  useEffect(() => {\n    if (isConnected && level) {\n      setLevel(level);\n    }\n  }, [isConnected, level, setLevel]);\n\n  // Auto-start detection when connected\n  useEffect(() => {\n    if (isConnected && webcamRef.current && !isCapturing) {\n      startDetection();\n    }\n  }, [isConnected, startDetection, isCapturing]);\n\n  // Set target sign when current sign changes\n  useEffect(() => {\n    if (isConnected && currentSign) {\n      console.log(`🎯 Setting target sign to: ${currentSign.name}`);\n      startAIRecording(currentSign.name, false); // Set target sign without starting session\n    }\n  }, [isConnected, currentSign, startAIRecording]);\n  const prevCard = useCallback(() => {\n    if (currentCardIndex > 0) {\n      setSlideDirection('left');\n      setCurrentCardIndex(prev => prev - 1);\n      setCardStates(prev => ({\n        ...prev,\n        [currentCardIndex]: null\n      }));\n      setTimeout(() => setSlideDirection(null), 500);\n    }\n  }, [currentCardIndex]);\n  const retryCard = useCallback(() => {\n    setCardStates(prev => ({\n      ...prev,\n      [currentCardIndex]: null\n    }));\n    setCompletedCards(prev => {\n      const newSet = new Set(prev);\n      newSet.delete(currentCardIndex);\n      return newSet;\n    });\n  }, [currentCardIndex]);\n  const handleLevelComplete = () => {\n    setShowCompletion(false);\n    onBack();\n  };\n  const handleNextLevel = () => {\n    setShowCompletion(false);\n    // This would typically navigate to the next level\n    onBack();\n  };\n  if (!levelInfo || !currentSign) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: 'white',\n          textAlign: 'center',\n          padding: '2rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Level not found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 776,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onBack,\n          children: \"Go Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 777,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 775,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 774,\n      columnNumber: 7\n    }, this);\n  }\n  const isCurrentCardCompleted = completedCards.has(currentCardIndex);\n  const currentCardState = cardStates[currentCardIndex];\n  return /*#__PURE__*/_jsxDEV(ModernContainer, {\n    children: [/*#__PURE__*/_jsxDEV(ModernHeader, {\n      size: \"md\",\n      children: [/*#__PURE__*/_jsxDEV(ModernBackButton, {\n        variant: \"ghost\",\n        size: \"md\",\n        onClick: onBack,\n        children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 790,\n          columnNumber: 11\n        }, this), \"Back to Levels\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 789,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ModernLevelInfo, {\n        children: [/*#__PURE__*/_jsxDEV(ModernLevelTitle, {\n          level: 3,\n          children: [\"Level \", level, \": \", levelInfo.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 795,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ModernLevelTheme, {\n          size: \"lg\",\n          children: levelInfo.theme\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 798,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 794,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ModernConnectionStatus, {\n        isConnected: isConnected,\n        onClick: !isConnected ? retryConnection : undefined,\n        children: [isConnected ? /*#__PURE__*/_jsxDEV(Wifi, {\n          size: 18\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 807,\n          columnNumber: 26\n        }, this) : /*#__PURE__*/_jsxDEV(WifiOff, {\n          size: 18\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 807,\n          columnNumber: 47\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"connection-text\",\n          children: isConnected ? 'Connected' : 'Disconnected'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 808,\n          columnNumber: 11\n        }, this), !isConnected && /*#__PURE__*/_jsxDEV(RefreshCw, {\n          size: 14\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 811,\n          columnNumber: 28\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 803,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 788,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n      children: [/*#__PURE__*/_jsxDEV(FlashCardSection, {\n        children: [/*#__PURE__*/_jsxDEV(ProgressSection, {\n          children: [/*#__PURE__*/_jsxDEV(ProgressTitle, {\n            children: \"Level Progress\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 818,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n            children: /*#__PURE__*/_jsxDEV(ProgressFill, {\n              style: {\n                width: `${progress}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 820,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 819,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressText, {\n            children: [completedCards.size, \" of \", signs.length, \" signs completed (\", Math.round(progress), \"%)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 822,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 817,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FlashCard, {\n          sign: currentSign,\n          cardNumber: currentCardIndex + 1,\n          totalCards: signs.length,\n          isCorrect: currentCardState === 'correct',\n          isIncorrect: currentCardState === 'incorrect',\n          isDetecting: isConnected && !isCurrentCardCompleted,\n          slideDirection: slideDirection,\n          progress: currentCardIndex / signs.length * 100\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 827,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Controls, {\n          children: [/*#__PURE__*/_jsxDEV(ControlButton, {\n            onClick: prevCard,\n            disabled: currentCardIndex === 0,\n            children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 843,\n              columnNumber: 15\n            }, this), \"Previous\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 839,\n            columnNumber: 13\n          }, this), isCurrentCardCompleted ? /*#__PURE__*/_jsxDEV(ControlButton, {\n            variant: \"success\",\n            onClick: nextCard,\n            disabled: currentCardIndex === signs.length - 1,\n            children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 853,\n              columnNumber: 17\n            }, this), \"Next Card\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 848,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(ControlButton, {\n            onClick: retryCard,\n            disabled: !isConnected,\n            children: [/*#__PURE__*/_jsxDEV(RotateCcw, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 861,\n              columnNumber: 17\n            }, this), \"Retry\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 857,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ControlButton, {\n            onClick: nextCard,\n            disabled: currentCardIndex === signs.length - 1,\n            children: [\"Next\", /*#__PURE__*/_jsxDEV(ArrowRight, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 871,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 866,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 838,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 816,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CameraSection, {\n        children: [/*#__PURE__*/_jsxDEV(CameraTitle, {\n          children: [/*#__PURE__*/_jsxDEV(Camera, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 878,\n            columnNumber: 13\n          }, this), \"Camera Feed\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 877,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(WebcamContainer, {\n          children: [/*#__PURE__*/_jsxDEV(Webcam, {\n            ref: webcamRef,\n            audio: false,\n            width: \"100%\",\n            height: \"auto\",\n            screenshotFormat: \"image/jpeg\",\n            videoConstraints: {\n              width: 640,\n              height: 480,\n              facingMode: \"user\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 883,\n            columnNumber: 13\n          }, this), recordingStatus && /*#__PURE__*/_jsxDEV(StatusOverlay, {\n            children: recordingStatus\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 897,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 882,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PredictionDisplay, {\n          children: [/*#__PURE__*/_jsxDEV(PredictionText, {\n            children: prediction !== null && prediction !== void 0 && prediction.sign ? `Detected: ${prediction.sign}` : 'Show the sign to get started'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 904,\n            columnNumber: 13\n          }, this), (prediction === null || prediction === void 0 ? void 0 : prediction.confidence) && /*#__PURE__*/_jsxDEV(ConfidenceText, {\n            children: [\"Confidence: \", Math.round(prediction.confidence * 100), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 908,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 903,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 876,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 815,\n      columnNumber: 7\n    }, this), showCompletion && /*#__PURE__*/_jsxDEV(CompletionModal, {\n      children: /*#__PURE__*/_jsxDEV(ModalContent, {\n        children: [/*#__PURE__*/_jsxDEV(Trophy, {\n          size: 80,\n          style: {\n            color: '#f59e0b',\n            marginBottom: '1rem'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 919,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ModalTitle, {\n          children: \"\\uD83C\\uDF89 Level Complete!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 920,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ModalText, {\n          children: [\"Congratulations! You've successfully completed Level \", level, \": \", levelInfo.name, \". You've mastered all \", signs.length, \" signs in this level!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 921,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ModalButtons, {\n          children: [/*#__PURE__*/_jsxDEV(ControlButton, {\n            onClick: handleLevelComplete,\n            children: [/*#__PURE__*/_jsxDEV(Home, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 927,\n              columnNumber: 17\n            }, this), \"Back to Levels\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 926,\n            columnNumber: 15\n          }, this), level < 5 && /*#__PURE__*/_jsxDEV(ControlButton, {\n            variant: \"primary\",\n            onClick: handleNextLevel,\n            children: [/*#__PURE__*/_jsxDEV(Target, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 932,\n              columnNumber: 19\n            }, this), \"Next Level\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 931,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 925,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 918,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 917,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 787,\n    columnNumber: 5\n  }, this);\n};\n_s(FlashCardTraining, \"8cy6qKBapZ1Z4JTGzu0t/hbSm1Q=\", false, function () {\n  return [useSignDetection];\n});\n_c27 = FlashCardTraining;\nexport default FlashCardTraining;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27;\n$RefreshReg$(_c, \"ModernContainer\");\n$RefreshReg$(_c2, \"ModernHeader\");\n$RefreshReg$(_c3, \"ModernBackButton\");\n$RefreshReg$(_c4, \"ModernLevelInfo\");\n$RefreshReg$(_c5, \"ModernLevelTitle\");\n$RefreshReg$(_c6, \"ModernLevelTheme\");\n$RefreshReg$(_c7, \"ModernConnectionStatus\");\n$RefreshReg$(_c8, \"MainContent\");\n$RefreshReg$(_c9, \"FlashCardSection\");\n$RefreshReg$(_c0, \"ProgressSection\");\n$RefreshReg$(_c1, \"ProgressTitle\");\n$RefreshReg$(_c10, \"ProgressBar\");\n$RefreshReg$(_c11, \"ProgressFill\");\n$RefreshReg$(_c12, \"ProgressText\");\n$RefreshReg$(_c13, \"Controls\");\n$RefreshReg$(_c14, \"ControlButton\");\n$RefreshReg$(_c15, \"CameraSection\");\n$RefreshReg$(_c16, \"CameraTitle\");\n$RefreshReg$(_c17, \"WebcamContainer\");\n$RefreshReg$(_c18, \"StatusOverlay\");\n$RefreshReg$(_c19, \"PredictionDisplay\");\n$RefreshReg$(_c20, \"PredictionText\");\n$RefreshReg$(_c21, \"ConfidenceText\");\n$RefreshReg$(_c22, \"CompletionModal\");\n$RefreshReg$(_c23, \"ModalContent\");\n$RefreshReg$(_c24, \"ModalTitle\");\n$RefreshReg$(_c25, \"ModalText\");\n$RefreshReg$(_c26, \"ModalButtons\");\n$RefreshReg$(_c27, \"FlashCardTraining\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "useEffect", "styled", "keyframes", "Webcam", "ArrowLeft", "ArrowRight", "RotateCcw", "Home", "Camera", "Wifi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RefreshCw", "CheckCircle", "Trophy", "Target", "Zap", "<PERSON><PERSON><PERSON>", "Award", "FlashCard", "useSignDetection", "getSignsForLevel", "getLevelInfo", "theme", "Container", "Card", "<PERSON><PERSON>", "Heading", "Text", "Badge", "jsxDEV", "_jsxDEV", "fadeIn", "celebration", "ModernContainer", "div", "spacing", "breakpoints", "md", "sm", "_c", "Header", "ModernHeader", "shadows", "xl", "_c2", "ModernBackButton", "_c3", "ModernLevelInfo", "colors", "text", "primary", "_c4", "ModernLevelTitle", "gradients", "_c5", "ModernLevelTheme", "typography", "fontWeight", "medium", "_c6", "ModernConnectionStatus", "props", "isConnected", "success", "error", "_c7", "MainContent", "lg", "_c8", "FlashCardSection", "_c9", "ProgressSection", "_c0", "ProgressTitle", "h3", "_c1", "ProgressBar", "_c10", "ProgressFill", "_c11", "ProgressText", "_c12", "Controls", "_c13", "ControlButton", "button", "variant", "_c14", "CameraSection", "_c15", "CameraTitle", "_c16", "WebcamContainer", "_c17", "StatusOverlay", "_c18", "PredictionDisplay", "_c19", "PredictionText", "_c20", "ConfidenceText", "_c21", "CompletionModal", "_c22", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c23", "ModalTitle", "h2", "_c24", "ModalText", "p", "_c25", "ModalButtons", "_c26", "FlashCardTraining", "level", "onBack", "userProgress", "onProgressUpdate", "_s", "webcamRef", "currentCardIndex", "setCurrentCardIndex", "completedCards", "setCompletedCards", "Set", "cardStates", "setCardStates", "slideDirection", "setSlideDirection", "showCompletion", "setShowCompletion", "isCapturing", "setIsCapturing", "levelInfo", "signs", "currentSign", "prediction", "isAIRecording", "recordingStatus", "signMatched", "targetSign", "currentKeypoints", "startRecording", "startAIRecording", "stopRecording", "stopAIRecording", "startFrameCapture", "retryConnection", "setLevel", "progress", "size", "length", "startDetection", "current", "saveTrainingData", "signName", "keypoints", "confidence", "console", "log", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "sign_name", "timestamp", "Date", "toISOString", "ok", "result", "json", "message", "nextCard", "prev", "setTimeout", "_prediction$sign", "name", "sign", "toLowerCase", "has", "saveTrainingDataAsync", "saved", "newCompletedCount", "prevCard", "retryCard", "newSet", "delete", "handleLevelComplete", "handleNextLevel", "children", "style", "color", "textAlign", "padding", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "isCurrentCardCompleted", "currentCardState", "undefined", "className", "width", "Math", "round", "cardNumber", "totalCards", "isCorrect", "isIncorrect", "isDetecting", "disabled", "ref", "audio", "height", "screenshotFormat", "videoConstraints", "facingMode", "marginBottom", "_c27", "$RefreshReg$"], "sources": ["D:/ASL/ASL-Training/src/components/FlashCardTraining.js"], "sourcesContent": ["import React, { useState, useRef, useCallback, useEffect } from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport Webcam from 'react-webcam';\nimport {\n  ArrowLeft,\n  ArrowRight,\n  RotateCcw,\n  Home,\n  Camera,\n  Wifi,\n  WifiOff,\n  RefreshCw,\n  CheckCircle,\n  Trophy,\n  Target,\n  Zap,\n  Sparkles,\n  Award\n} from 'lucide-react';\nimport Flash<PERSON>ard from './FlashCard';\nimport { useSignDetection } from '../hooks/useSignDetection';\nimport { getSignsForLevel, getLevelInfo } from '../data/signLevels';\nimport { theme } from '../styles/theme';\nimport { Container, Card, Button, Heading, Text, Badge } from './ui/ModernComponents';\n\n// Animations\nconst fadeIn = keyframes`\n  from { opacity: 0; transform: translateY(20px); }\n  to { opacity: 1; transform: translateY(0); }\n`;\n\nconst celebration = keyframes`\n  0%, 100% { transform: scale(1) rotate(0deg); }\n  25% { transform: scale(1.1) rotate(-5deg); }\n  75% { transform: scale(1.1) rotate(5deg); }\n`;\n\n// Modern Styled Components\nconst ModernContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  padding: ${theme.spacing[4]};\n  position: relative;\n  overflow-x: hidden;\n\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n\n  > * {\n    position: relative;\n    z-index: 1;\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    padding: ${theme.spacing[3]};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: ${theme.spacing[2]};\n    min-height: 100dvh; /* Use dynamic viewport height for mobile */\n  }\n`;\nconst Header = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${theme.spacing[8]};\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    flex-direction: column;\n    gap: ${theme.spacing[4]};\n  }\n`;\n\nconst ModernHeader = styled(Card)`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${theme.spacing[8]};\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: ${theme.shadows.xl};\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    flex-direction: column;\n    gap: ${theme.spacing[4]};\n    text-align: center;\n    margin-bottom: ${theme.spacing[6]};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    margin-bottom: ${theme.spacing[4]};\n    padding: ${theme.spacing[3]};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    margin-bottom: ${theme.spacing[4]};\n  }\n`;\n\nconst ModernBackButton = styled(Button)`\n  background: rgba(59, 130, 246, 0.15);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(59, 130, 246, 0.3);\n  color: #3b82f6;\n  font-weight: 600;\n  transition: all 0.3s ease;\n\n  &:hover:not(:disabled) {\n    background: rgba(59, 130, 246, 0.25);\n    border-color: rgba(59, 130, 246, 0.5);\n    color: #2563eb;\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);\n  }\n\n  &:active:not(:disabled) {\n    transform: translateY(0);\n    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    width: 100%;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: 0.75rem 1rem;\n    font-size: 0.875rem;\n  }\n`;\n\nconst ModernLevelInfo = styled.div`\n  text-align: center;\n  color: ${theme.colors.text.primary};\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    order: -1;\n  }\n`;\n\nconst ModernLevelTitle = styled(Heading)`\n  margin-bottom: ${theme.spacing[1]};\n  background: ${theme.colors.gradients.primary};\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n`;\n\nconst ModernLevelTheme = styled(Text)`\n  opacity: 0.8;\n  font-weight: ${theme.typography.fontWeight.medium};\n`;\n\nconst ModernConnectionStatus = styled(Badge)`\n  background: ${props => props.isConnected\n    ? 'rgba(34, 197, 94, 0.15)'\n    : 'rgba(239, 68, 68, 0.15)'\n  };\n  color: ${props => props.isConnected\n    ? theme.colors.success[700]\n    : theme.colors.error[700]\n  };\n  border: 1px solid ${props => props.isConnected\n    ? theme.colors.success[200]\n    : theme.colors.error[200]\n  };\n  backdrop-filter: blur(10px);\n  cursor: ${props => props.isConnected ? 'default' : 'pointer'};\n\n  &:hover {\n    background: ${props => props.isConnected\n      ? 'rgba(34, 197, 94, 0.2)'\n      : 'rgba(239, 68, 68, 0.2)'\n    };\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    width: 100%;\n    justify-content: center;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: 0.5rem 0.75rem;\n    font-size: 0.75rem;\n    \n    .connection-text {\n      display: none; /* Hide text on mobile, show only icon */\n    }\n  }\n`;\n\nconst MainContent = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 500px;\n  gap: ${theme.spacing[6]};\n  max-width: 1400px;\n  margin: 0 auto;\n  align-items: start;\n\n  @media (max-width: ${theme.breakpoints.xl}) {\n    grid-template-columns: 1fr 450px;\n    gap: ${theme.spacing[5]};\n    max-width: 1200px;\n  }\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    grid-template-columns: 1fr;\n    gap: ${theme.spacing[6]};\n    max-width: 800px;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    /* On mobile, show flash card first, then camera */\n    display: flex;\n    flex-direction: column;\n    gap: ${theme.spacing[4]};\n    max-width: 100%;\n  }\n`;\n\nconst FlashCardSection = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  animation: ${fadeIn} 0.6s ease;\n  position: sticky;\n  top: ${theme.spacing[8]};\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    position: static;\n  }\n`;\n\nconst ProgressSection = styled.div`\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20px;\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n  text-align: center;\n  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    padding: 1.25rem;\n    margin-bottom: 1.5rem;\n    border-radius: 18px;\n  }\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: 1rem;\n    margin-bottom: 1.5rem;\n    border-radius: 16px;\n  }\n`;\n\nconst ProgressTitle = styled.h3`\n  font-size: 1.25rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin-bottom: 1rem;\n`;\n\nconst ProgressBar = styled.div`\n  width: 100%;\n  height: 16px;\n  background: #e2e8f0;\n  border-radius: 8px;\n  overflow: hidden;\n  margin-bottom: 1.5rem;\n  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    height: 14px;\n    margin-bottom: 1rem;\n  }\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    height: 12px;\n    margin-bottom: 1rem;\n  }\n`;\n\nconst ProgressFill = styled.div`\n  height: 100%;\n  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #10b981);\n  border-radius: 8px;\n  transition: width 0.5s ease;\n  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    border-radius: 7px;\n  }\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    border-radius: 6px;\n  }\n`;\n\nconst ProgressText = styled.div`\n  font-size: 1rem;\n  color: #64748b;\n  font-weight: 600;\n`;\n\nconst Controls = styled.div`\n  display: flex;\n  gap: 1rem;\n  margin-top: 2rem;\n  justify-content: center;\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    gap: 0.75rem;\n    margin-top: 1.5rem;\n  }\n  \n  @media (max-width: 768px) {\n    gap: 0.75rem;\n    margin-top: 1.5rem;\n    flex-wrap: wrap;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    gap: 0.5rem;\n    margin-top: 1rem;\n    padding: 0 ${theme.spacing[2]};\n  }\n`;\n\nconst ControlButton = styled.button`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 1rem 2rem;\n  border: none;\n  border-radius: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  min-height: 48px; /* Minimum touch target size */\n  touch-action: manipulation; /* Optimize for touch */\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    padding: 1rem 2rem;\n    font-size: 1rem;\n    min-height: 48px;\n  }\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: 0.75rem 1.5rem;\n    font-size: 0.875rem;\n    min-height: 44px;\n  }\n  \n  ${props => {\n    if (props.variant === 'primary') {\n      return `\n        background: linear-gradient(135deg, #3b82f6, #8b5cf6);\n        color: white;\n        &:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);\n        }\n      `;\n    }\n    if (props.variant === 'success') {\n      return `\n        background: linear-gradient(135deg, #10b981, #34d399);\n        color: white;\n        &:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);\n        }\n      `;\n    }\n    return `\n      background: rgba(255, 255, 255, 0.9);\n      color: #64748b;\n      &:hover {\n        background: white;\n        transform: translateY(-2px);\n      }\n    `;\n  }}\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none !important;\n  }\n  \n  @media (max-width: 768px) {\n    padding: 0.75rem 1.5rem;\n    font-size: 0.875rem;\n  }\n`;\n\nconst CameraSection = styled(Card)`\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\n  border-radius: 24px;\n  padding: 2rem;\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    order: 1; /* Show after flash card on mobile */\n    padding: 1.5rem;\n    border-radius: 20px;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    order: 2; /* Ensure camera comes after flash card on mobile */\n    margin: 0 -${theme.spacing[2]}; /* Full width on mobile */\n    border-radius: 16px;\n    padding: 1rem;\n  }\n`;\n\nconst CameraTitle = styled.h3`\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin-bottom: 1.5rem;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    font-size: 1.25rem;\n    margin-bottom: 1rem;\n    gap: 0.5rem;\n  }\n`;\n\nconst WebcamContainer = styled.div`\n  position: relative;\n  border-radius: 20px;\n  overflow: hidden;\n  background: #000;\n  margin-bottom: 1.5rem;\n  aspect-ratio: 4/3; /* Maintain aspect ratio */\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n  border: 2px solid rgba(255, 255, 255, 0.1);\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    border-radius: 16px;\n    margin-bottom: 1rem;\n  }\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    border-radius: 12px;\n    margin: 0 -${theme.spacing[2]} 1rem -${theme.spacing[2]}; /* Full width on mobile */\n  }\n`;\n\nconst StatusOverlay = styled.div`\n  position: absolute;\n  top: 1rem;\n  left: 1rem;\n  right: 1rem;\n  background: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: 0.75rem;\n  border-radius: 8px;\n  font-weight: 600;\n  text-align: center;\n  font-size: 0.875rem;\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    top: 0.5rem;\n    left: 0.5rem;\n    right: 0.5rem;\n    padding: 0.5rem;\n    font-size: 0.75rem;\n  }\n`;\n\nconst PredictionDisplay = styled.div`\n  background: linear-gradient(135deg, #f8fafc, #f1f5f9);\n  border-radius: 16px;\n  padding: 1.5rem;\n  text-align: center;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    padding: 1rem;\n    border-radius: 12px;\n  }\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: 0.75rem;\n    margin: 0 ${theme.spacing[2]};\n    border-radius: 12px;\n  }\n`;\n\nconst PredictionText = styled.div`\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin-bottom: 0.75rem;\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    font-size: 1.125rem;\n    margin-bottom: 0.5rem;\n  }\n`;\n\nconst ConfidenceText = styled.div`\n  font-size: 0.875rem;\n  color: #64748b;\n`;\n\nconst CompletionModal = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  animation: ${fadeIn} 0.3s ease;\n`;\n\nconst ModalContent = styled.div`\n  background: white;\n  border-radius: 24px;\n  padding: 3rem;\n  text-align: center;\n  max-width: 500px;\n  margin: 1rem;\n  animation: ${celebration} 0.6s ease;\n`;\n\nconst ModalTitle = styled.h2`\n  font-size: 2.5rem;\n  font-weight: 800;\n  color: #1e293b;\n  margin-bottom: 1rem;\n`;\n\nconst ModalText = styled.p`\n  font-size: 1.125rem;\n  color: #64748b;\n  margin-bottom: 2rem;\n  line-height: 1.6;\n`;\n\nconst ModalButtons = styled.div`\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n`;\n\nconst FlashCardTraining = ({ \n  level, \n  onBack, \n  userProgress = {}, \n  onProgressUpdate \n}) => {\n  const webcamRef = useRef(null);\n  const [currentCardIndex, setCurrentCardIndex] = useState(0);\n  const [completedCards, setCompletedCards] = useState(new Set());\n  const [cardStates, setCardStates] = useState({});\n  const [slideDirection, setSlideDirection] = useState(null);\n  const [showCompletion, setShowCompletion] = useState(false);\n  const [isCapturing, setIsCapturing] = useState(false);\n\n  const levelInfo = getLevelInfo(level);\n  const signs = getSignsForLevel(level);\n  const currentSign = signs[currentCardIndex];\n\n  // Use sign detection hook\n  const {\n    isConnected,\n    prediction,\n    isAIRecording,\n    recordingStatus,\n    signMatched,\n    targetSign,\n    currentKeypoints,\n    startRecording: startAIRecording,\n    stopRecording: stopAIRecording,\n    startFrameCapture,\n    retryConnection,\n    setLevel\n  } = useSignDetection();\n\n  const progress = (completedCards.size / signs.length) * 100;\n\n  // Start detection when connected\n  const startDetection = useCallback(() => {\n    if (!webcamRef.current) return;\n    setIsCapturing(true);\n    startFrameCapture(webcamRef, 100);\n  }, [startFrameCapture]);\n\n  // Save training data when sign is detected correctly\n  const saveTrainingData = useCallback(async (signName, keypoints, confidence) => {\n    try {\n      console.log(`💾 Saving training data for ${signName} with confidence ${confidence}`);\n      \n      const response = await fetch('http://localhost:8000/save-training-data', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          sign_name: signName,\n          keypoints: keypoints,\n          confidence: confidence,\n          timestamp: new Date().toISOString()\n        })\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        console.log(`✅ Training data saved: ${result.message}`);\n        return true;\n      } else {\n        console.error('❌ Failed to save training data');\n        return false;\n      }\n    } catch (error) {\n      console.error('❌ Error saving training data:', error);\n      return false;\n    }\n  }, []);\n\n  const nextCard = useCallback(() => {\n    if (currentCardIndex < signs.length - 1) {\n      setSlideDirection('right');\n      setCurrentCardIndex(prev => prev + 1);\n      setCardStates(prev => ({ ...prev, [currentCardIndex]: null }));\n      setTimeout(() => setSlideDirection(null), 500);\n    }\n  }, [currentCardIndex, signs.length]);\n\n  // Handle sign detection success with automatic recording and training data saving\n  useEffect(() => {\n    console.log(`🔍 Debug: signMatched=${signMatched}, currentSign=${currentSign?.name}, prediction=${prediction?.sign}, confidence=${prediction?.confidence}`);\n    \n    if (signMatched && currentSign && prediction?.sign?.toLowerCase() === currentSign.name.toLowerCase()) {\n      console.log(`✅ Sign match confirmed: ${currentSign.name} with confidence ${prediction.confidence}`);\n      \n      // Only proceed if this card hasn't been completed yet\n      if (!completedCards.has(currentCardIndex)) {\n        console.log(`🎯 Correct sign detected: ${currentSign.name} with confidence ${prediction.confidence}`);\n        \n        // Save training data immediately when sign is detected correctly\n        const saveTrainingDataAsync = async () => {\n          if (currentKeypoints && prediction?.confidence >= 0.5) {\n            const saved = await saveTrainingData(currentSign.name, currentKeypoints, prediction.confidence);\n            if (saved) {\n              console.log(`✅ Training data saved successfully for ${currentSign.name}`);\n            }\n          }\n        };\n        \n        // Save training data\n        saveTrainingDataAsync();\n\n        // Start automatic recording for additional training data\n        if (!isAIRecording && isConnected) {\n          console.log(`🎬 Starting automatic recording for ${currentSign.name}...`);\n          startAIRecording(currentSign.name, true); // Start immediate recording session\n\n          // Stop recording after 3 seconds\n          setTimeout(() => {\n            stopAIRecording();\n            console.log(`✅ Automatic recording completed for: ${currentSign.name}`);\n          }, 3000);\n        }\n\n        // Mark card as completed\n        setCardStates(prev => ({ ...prev, [currentCardIndex]: 'correct' }));\n        setCompletedCards(prev => new Set([...prev, currentCardIndex]));\n\n        // Update progress\n        if (onProgressUpdate) {\n          const newCompletedCount = completedCards.size + 1;\n          onProgressUpdate(level, newCompletedCount, signs.length);\n        }\n\n        // Auto-advance after 2 seconds (allowing time for user to see success)\n        setTimeout(() => {\n          if (currentCardIndex < signs.length - 1) {\n            nextCard();\n          } else {\n            // Level completed\n            setShowCompletion(true);\n            if (onProgressUpdate) {\n              onProgressUpdate(level, signs.length, signs.length);\n            }\n          }\n        }, 2000);\n      } else {\n        console.log(`⚠️ Card ${currentCardIndex} already completed`);\n      }\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [signMatched, currentSign, prediction, currentCardIndex, signs.length, level, onProgressUpdate, isAIRecording, isConnected, startAIRecording, stopAIRecording, completedCards, saveTrainingData, currentKeypoints]);\n\n  // Set level when connected\n  useEffect(() => {\n    if (isConnected && level) {\n      setLevel(level);\n    }\n  }, [isConnected, level, setLevel]);\n\n  // Auto-start detection when connected\n  useEffect(() => {\n    if (isConnected && webcamRef.current && !isCapturing) {\n      startDetection();\n    }\n  }, [isConnected, startDetection, isCapturing]);\n\n  // Set target sign when current sign changes\n  useEffect(() => {\n    if (isConnected && currentSign) {\n      console.log(`🎯 Setting target sign to: ${currentSign.name}`);\n      startAIRecording(currentSign.name, false); // Set target sign without starting session\n    }\n  }, [isConnected, currentSign, startAIRecording]);\n\n  const prevCard = useCallback(() => {\n    if (currentCardIndex > 0) {\n      setSlideDirection('left');\n      setCurrentCardIndex(prev => prev - 1);\n      setCardStates(prev => ({ ...prev, [currentCardIndex]: null }));\n      setTimeout(() => setSlideDirection(null), 500);\n    }\n  }, [currentCardIndex]);\n\n  const retryCard = useCallback(() => {\n    setCardStates(prev => ({ ...prev, [currentCardIndex]: null }));\n    setCompletedCards(prev => {\n      const newSet = new Set(prev);\n      newSet.delete(currentCardIndex);\n      return newSet;\n    });\n  }, [currentCardIndex]);\n\n  const handleLevelComplete = () => {\n    setShowCompletion(false);\n    onBack();\n  };\n\n  const handleNextLevel = () => {\n    setShowCompletion(false);\n    // This would typically navigate to the next level\n    onBack();\n  };\n\n  if (!levelInfo || !currentSign) {\n    return (\n      <Container>\n        <div style={{ color: 'white', textAlign: 'center', padding: '2rem' }}>\n          <h2>Level not found</h2>\n          <button onClick={onBack}>Go Back</button>\n        </div>\n      </Container>\n    );\n  }\n\n  const isCurrentCardCompleted = completedCards.has(currentCardIndex);\n  const currentCardState = cardStates[currentCardIndex];\n\n  return (\n    <ModernContainer>\n      <ModernHeader size=\"md\">\n        <ModernBackButton variant=\"ghost\" size=\"md\" onClick={onBack}>\n          <ArrowLeft size={20} />\n          Back to Levels\n        </ModernBackButton>\n\n        <ModernLevelInfo>\n          <ModernLevelTitle level={3}>\n            Level {level}: {levelInfo.name}\n          </ModernLevelTitle>\n          <ModernLevelTheme size=\"lg\">\n            {levelInfo.theme}\n          </ModernLevelTheme>\n        </ModernLevelInfo>\n\n        <ModernConnectionStatus\n          isConnected={isConnected}\n          onClick={!isConnected ? retryConnection : undefined}\n        >\n          {isConnected ? <Wifi size={18} /> : <WifiOff size={18} />}\n          <span className=\"connection-text\">\n            {isConnected ? 'Connected' : 'Disconnected'}\n          </span>\n          {!isConnected && <RefreshCw size={14} />}\n        </ModernConnectionStatus>\n      </ModernHeader>\n\n      <MainContent>\n        <FlashCardSection>\n          <ProgressSection>\n            <ProgressTitle>Level Progress</ProgressTitle>\n            <ProgressBar>\n              <ProgressFill style={{ width: `${progress}%` }} />\n            </ProgressBar>\n            <ProgressText>\n              {completedCards.size} of {signs.length} signs completed ({Math.round(progress)}%)\n            </ProgressText>\n          </ProgressSection>\n\n          <FlashCard\n            sign={currentSign}\n            cardNumber={currentCardIndex + 1}\n            totalCards={signs.length}\n            isCorrect={currentCardState === 'correct'}\n            isIncorrect={currentCardState === 'incorrect'}\n            isDetecting={isConnected && !isCurrentCardCompleted}\n            slideDirection={slideDirection}\n            progress={(currentCardIndex / signs.length) * 100}\n          />\n\n          <Controls>\n            <ControlButton\n              onClick={prevCard}\n              disabled={currentCardIndex === 0}\n            >\n              <ArrowLeft size={20} />\n              Previous\n            </ControlButton>\n\n            {isCurrentCardCompleted ? (\n              <ControlButton\n                variant=\"success\"\n                onClick={nextCard}\n                disabled={currentCardIndex === signs.length - 1}\n              >\n                <CheckCircle size={20} />\n                Next Card\n              </ControlButton>\n            ) : (\n              <ControlButton\n                onClick={retryCard}\n                disabled={!isConnected}\n              >\n                <RotateCcw size={20} />\n                Retry\n              </ControlButton>\n            )}\n\n            <ControlButton\n              onClick={nextCard}\n              disabled={currentCardIndex === signs.length - 1}\n            >\n              Next\n              <ArrowRight size={20} />\n            </ControlButton>\n          </Controls>\n        </FlashCardSection>\n\n        <CameraSection>\n          <CameraTitle>\n            <Camera size={24} />\n            Camera Feed\n          </CameraTitle>\n\n          <WebcamContainer>\n            <Webcam\n              ref={webcamRef}\n              audio={false}\n              width=\"100%\"\n              height=\"auto\"\n              screenshotFormat=\"image/jpeg\"\n              videoConstraints={{\n                width: 640,\n                height: 480,\n                facingMode: \"user\"\n              }}\n            />\n\n            {recordingStatus && (\n              <StatusOverlay>\n                {recordingStatus}\n              </StatusOverlay>\n            )}\n          </WebcamContainer>\n\n          <PredictionDisplay>\n            <PredictionText>\n              {prediction?.sign ? `Detected: ${prediction.sign}` : 'Show the sign to get started'}\n            </PredictionText>\n            {prediction?.confidence && (\n              <ConfidenceText>\n                Confidence: {Math.round(prediction.confidence * 100)}%\n              </ConfidenceText>\n            )}\n          </PredictionDisplay>\n        </CameraSection>\n      </MainContent>\n\n      {showCompletion && (\n        <CompletionModal>\n          <ModalContent>\n            <Trophy size={80} style={{ color: '#f59e0b', marginBottom: '1rem' }} />\n            <ModalTitle>🎉 Level Complete!</ModalTitle>\n            <ModalText>\n              Congratulations! You've successfully completed Level {level}: {levelInfo.name}.\n              You've mastered all {signs.length} signs in this level!\n            </ModalText>\n            <ModalButtons>\n              <ControlButton onClick={handleLevelComplete}>\n                <Home size={20} />\n                Back to Levels\n              </ControlButton>\n              {level < 5 && (\n                <ControlButton variant=\"primary\" onClick={handleNextLevel}>\n                  <Target size={20} />\n                  Next Level\n                </ControlButton>\n              )}\n            </ModalButtons>\n          </ModalContent>\n        </CompletionModal>\n      )}\n    </ModernContainer>\n  );\n};\n\nexport default FlashCardTraining;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AACvE,OAAOC,MAAM,IAAIC,SAAS,QAAQ,mBAAmB;AACrD,OAAOC,MAAM,MAAM,cAAc;AACjC,SACEC,SAAS,EACTC,UAAU,EACVC,SAAS,EACTC,IAAI,EACJC,MAAM,EACNC,IAAI,EACJC,OAAO,EACPC,SAAS,EACTC,WAAW,EACXC,MAAM,EACNC,MAAM,EACNC,GAAG,EACHC,QAAQ,EACRC,KAAK,QACA,cAAc;AACrB,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,oBAAoB;AACnE,SAASC,KAAK,QAAQ,iBAAiB;AACvC,SAASC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAEC,KAAK,QAAQ,uBAAuB;;AAErF;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,MAAM,GAAG7B,SAAS;AACxB;AACA;AACA,CAAC;AAED,MAAM8B,WAAW,GAAG9B,SAAS;AAC7B;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAM+B,eAAe,GAAGhC,MAAM,CAACiC,GAAG;AAClC;AACA;AACA,aAAaZ,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBb,KAAK,CAACc,WAAW,CAACC,EAAE;AAC3C,eAAef,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC;AAC/B;AACA;AACA,uBAAuBb,KAAK,CAACc,WAAW,CAACE,EAAE;AAC3C,eAAehB,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC;AAC/B;AACA;AACA,CAAC;AAACI,EAAA,GAlCIN,eAAe;AAmCrB,MAAMO,MAAM,GAAGvC,MAAM,CAACiC,GAAG;AACzB;AACA;AACA;AACA,mBAAmBZ,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC;AACnC;AACA,uBAAuBb,KAAK,CAACc,WAAW,CAACC,EAAE;AAC3C;AACA,WAAWf,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC;AAC3B;AACA,CAAC;AAED,MAAMM,YAAY,GAAGxC,MAAM,CAACuB,IAAI,CAAC;AACjC;AACA;AACA;AACA,mBAAmBF,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC;AACnC;AACA;AACA;AACA,gBAAgBb,KAAK,CAACoB,OAAO,CAACC,EAAE;AAChC;AACA,uBAAuBrB,KAAK,CAACc,WAAW,CAACC,EAAE;AAC3C;AACA,WAAWf,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC;AAC3B;AACA,qBAAqBb,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC;AACrC;AACA;AACA,uBAAuBb,KAAK,CAACc,WAAW,CAACE,EAAE;AAC3C,qBAAqBhB,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC;AACrC,eAAeb,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC;AAC/B;AACA;AACA,uBAAuBb,KAAK,CAACc,WAAW,CAACE,EAAE;AAC3C,qBAAqBhB,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC;AACrC;AACA,CAAC;AAACS,GAAA,GAzBIH,YAAY;AA2BlB,MAAMI,gBAAgB,GAAG5C,MAAM,CAACwB,MAAM,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBH,KAAK,CAACc,WAAW,CAACC,EAAE;AAC3C;AACA;AACA;AACA,uBAAuBf,KAAK,CAACc,WAAW,CAACE,EAAE;AAC3C;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GA7BID,gBAAgB;AA+BtB,MAAME,eAAe,GAAG9C,MAAM,CAACiC,GAAG;AAClC;AACA,WAAWZ,KAAK,CAAC0B,MAAM,CAACC,IAAI,CAACC,OAAO;AACpC;AACA,uBAAuB5B,KAAK,CAACc,WAAW,CAACC,EAAE;AAC3C;AACA;AACA,CAAC;AAACc,GAAA,GAPIJ,eAAe;AASrB,MAAMK,gBAAgB,GAAGnD,MAAM,CAACyB,OAAO,CAAC;AACxC,mBAAmBJ,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC;AACnC,gBAAgBb,KAAK,CAAC0B,MAAM,CAACK,SAAS,CAACH,OAAO;AAC9C;AACA;AACA;AACA,CAAC;AAACI,GAAA,GANIF,gBAAgB;AAQtB,MAAMG,gBAAgB,GAAGtD,MAAM,CAAC0B,IAAI,CAAC;AACrC;AACA,iBAAiBL,KAAK,CAACkC,UAAU,CAACC,UAAU,CAACC,MAAM;AACnD,CAAC;AAACC,GAAA,GAHIJ,gBAAgB;AAKtB,MAAMK,sBAAsB,GAAG3D,MAAM,CAAC2B,KAAK,CAAC;AAC5C,gBAAgBiC,KAAK,IAAIA,KAAK,CAACC,WAAW,GACpC,yBAAyB,GACzB,yBAAyB;AAC/B,WACWD,KAAK,IAAIA,KAAK,CAACC,WAAW,GAC/BxC,KAAK,CAAC0B,MAAM,CAACe,OAAO,CAAC,GAAG,CAAC,GACzBzC,KAAK,CAAC0B,MAAM,CAACgB,KAAK,CAAC,GAAG,CAAC;AAC7B,sBACsBH,KAAK,IAAIA,KAAK,CAACC,WAAW,GAC1CxC,KAAK,CAAC0B,MAAM,CAACe,OAAO,CAAC,GAAG,CAAC,GACzBzC,KAAK,CAAC0B,MAAM,CAACgB,KAAK,CAAC,GAAG,CAAC;AAC7B;AACA,YACYH,KAAK,IAAIA,KAAK,CAACC,WAAW,GAAG,SAAS,GAAG,SAAS;AAC9D;AACA;AACA,kBAAkBD,KAAK,IAAIA,KAAK,CAACC,WAAW,GACpC,wBAAwB,GACxB,wBAAwB;AAChC;AACA;AACA,uBACuBxC,KAAK,CAACc,WAAW,CAACC,EAAE;AAC3C;AACA;AACA;AACA;AACA,uBAAuBf,KAAK,CAACc,WAAW,CAACE,EAAE;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC2B,GAAA,GApCIL,sBAAsB;AAsC5B,MAAMM,WAAW,GAAGjE,MAAM,CAACiC,GAAG;AAC9B;AACA;AACA,SAASZ,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC;AACzB;AACA;AACA;AACA;AACA,uBAAuBb,KAAK,CAACc,WAAW,CAACO,EAAE;AAC3C;AACA,WAAWrB,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC;AAC3B;AACA;AACA;AACA,uBAAuBb,KAAK,CAACc,WAAW,CAAC+B,EAAE;AAC3C;AACA,WAAW7C,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC;AAC3B;AACA;AACA;AACA,uBAAuBb,KAAK,CAACc,WAAW,CAACE,EAAE;AAC3C;AACA;AACA;AACA,WAAWhB,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC;AAC3B;AACA;AACA,CAAC;AAACiC,GAAA,GA3BIF,WAAW;AA6BjB,MAAMG,gBAAgB,GAAGpE,MAAM,CAACiC,GAAG;AACnC;AACA;AACA;AACA,eAAeH,MAAM;AACrB;AACA,SAAST,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC;AACzB;AACA,uBAAuBb,KAAK,CAACc,WAAW,CAAC+B,EAAE;AAC3C;AACA;AACA,CAAC;AAACG,GAAA,GAXID,gBAAgB;AAatB,MAAME,eAAe,GAAGtE,MAAM,CAACiC,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBZ,KAAK,CAACc,WAAW,CAAC+B,EAAE;AAC3C;AACA;AACA;AACA;AACA;AACA,uBAAuB7C,KAAK,CAACc,WAAW,CAACE,EAAE;AAC3C;AACA;AACA;AACA;AACA,CAAC;AAACkC,GAAA,GArBID,eAAe;AAuBrB,MAAME,aAAa,GAAGxE,MAAM,CAACyE,EAAE;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,aAAa;AAOnB,MAAMG,WAAW,GAAG3E,MAAM,CAACiC,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBZ,KAAK,CAACc,WAAW,CAAC+B,EAAE;AAC3C;AACA;AACA;AACA;AACA,uBAAuB7C,KAAK,CAACc,WAAW,CAACE,EAAE;AAC3C;AACA;AACA;AACA,CAAC;AAACuC,IAAA,GAlBID,WAAW;AAoBjB,MAAME,YAAY,GAAG7E,MAAM,CAACiC,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBZ,KAAK,CAACc,WAAW,CAAC+B,EAAE;AAC3C;AACA;AACA;AACA,uBAAuB7C,KAAK,CAACc,WAAW,CAACE,EAAE;AAC3C;AACA;AACA,CAAC;AAACyC,IAAA,GAdID,YAAY;AAgBlB,MAAME,YAAY,GAAG/E,MAAM,CAACiC,GAAG;AAC/B;AACA;AACA;AACA,CAAC;AAAC+C,IAAA,GAJID,YAAY;AAMlB,MAAME,QAAQ,GAAGjF,MAAM,CAACiC,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA,uBAAuBZ,KAAK,CAACc,WAAW,CAAC+B,EAAE;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB7C,KAAK,CAACc,WAAW,CAACE,EAAE;AAC3C;AACA;AACA,iBAAiBhB,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC;AACjC;AACA,CAAC;AAACgD,IAAA,GAtBID,QAAQ;AAwBd,MAAME,aAAa,GAAGnF,MAAM,CAACoF,MAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB/D,KAAK,CAACc,WAAW,CAAC+B,EAAE;AAC3C;AACA;AACA;AACA;AACA;AACA,uBAAuB7C,KAAK,CAACc,WAAW,CAACE,EAAE;AAC3C;AACA;AACA;AACA;AACA;AACA,IAAIuB,KAAK,IAAI;EACT,IAAIA,KAAK,CAACyB,OAAO,KAAK,SAAS,EAAE;IAC/B,OAAO;AACb;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;EACH;EACA,IAAIzB,KAAK,CAACyB,OAAO,KAAK,SAAS,EAAE;IAC/B,OAAO;AACb;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;EACH;EACA,OAAO;AACX;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACH,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAlEIH,aAAa;AAoEnB,MAAMI,aAAa,GAAGvF,MAAM,CAACuB,IAAI,CAAC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBF,KAAK,CAACc,WAAW,CAAC+B,EAAE;AAC3C;AACA;AACA;AACA;AACA;AACA,uBAAuB7C,KAAK,CAACc,WAAW,CAACE,EAAE;AAC3C;AACA,iBAAiBhB,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC;AACjC;AACA;AACA;AACA,CAAC;AAACsD,IAAA,GApBID,aAAa;AAsBnB,MAAME,WAAW,GAAGzF,MAAM,CAACyE,EAAE;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBpD,KAAK,CAACc,WAAW,CAAC+B,EAAE;AAC3C;AACA;AACA;AACA;AACA,CAAC;AAACwB,IAAA,GAdID,WAAW;AAgBjB,MAAME,eAAe,GAAG3F,MAAM,CAACiC,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBZ,KAAK,CAACc,WAAW,CAAC+B,EAAE;AAC3C;AACA;AACA;AACA;AACA,uBAAuB7C,KAAK,CAACc,WAAW,CAACE,EAAE;AAC3C;AACA,iBAAiBhB,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,UAAUb,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC;AAC3D;AACA,CAAC;AAAC0D,IAAA,GAnBID,eAAe;AAqBrB,MAAME,aAAa,GAAG7F,MAAM,CAACiC,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBZ,KAAK,CAACc,WAAW,CAACE,EAAE;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyD,IAAA,GApBID,aAAa;AAsBnB,MAAME,iBAAiB,GAAG/F,MAAM,CAACiC,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBZ,KAAK,CAACc,WAAW,CAAC+B,EAAE;AAC3C;AACA;AACA;AACA;AACA,uBAAuB7C,KAAK,CAACc,WAAW,CAACE,EAAE;AAC3C;AACA,gBAAgBhB,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC;AAChC;AACA;AACA,CAAC;AAAC8D,IAAA,GAlBID,iBAAiB;AAoBvB,MAAME,cAAc,GAAGjG,MAAM,CAACiC,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA,uBAAuBZ,KAAK,CAACc,WAAW,CAAC+B,EAAE;AAC3C;AACA;AACA;AACA,CAAC;AAACgC,IAAA,GAVID,cAAc;AAYpB,MAAME,cAAc,GAAGnG,MAAM,CAACiC,GAAG;AACjC;AACA;AACA,CAAC;AAACmE,IAAA,GAHID,cAAc;AAKpB,MAAME,eAAe,GAAGrG,MAAM,CAACiC,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeH,MAAM;AACrB,CAAC;AAACwE,IAAA,GAZID,eAAe;AAcrB,MAAME,YAAY,GAAGvG,MAAM,CAACiC,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,eAAeF,WAAW;AAC1B,CAAC;AAACyE,IAAA,GARID,YAAY;AAUlB,MAAME,UAAU,GAAGzG,MAAM,CAAC0G,EAAE;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GALIF,UAAU;AAOhB,MAAMG,SAAS,GAAG5G,MAAM,CAAC6G,CAAC;AAC1B;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GALIF,SAAS;AAOf,MAAMG,YAAY,GAAG/G,MAAM,CAACiC,GAAG;AAC/B;AACA;AACA;AACA,CAAC;AAAC+E,IAAA,GAJID,YAAY;AAMlB,MAAME,iBAAiB,GAAGA,CAAC;EACzBC,KAAK;EACLC,MAAM;EACNC,YAAY,GAAG,CAAC,CAAC;EACjBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,SAAS,GAAG1H,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM,CAAC2H,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7H,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAM,CAAC8H,cAAc,EAAEC,iBAAiB,CAAC,GAAG/H,QAAQ,CAAC,IAAIgI,GAAG,CAAC,CAAC,CAAC;EAC/D,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlI,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACmI,cAAc,EAAEC,iBAAiB,CAAC,GAAGpI,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACqI,cAAc,EAAEC,iBAAiB,CAAC,GAAGtI,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACuI,WAAW,EAAEC,cAAc,CAAC,GAAGxI,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMyI,SAAS,GAAGjH,YAAY,CAAC8F,KAAK,CAAC;EACrC,MAAMoB,KAAK,GAAGnH,gBAAgB,CAAC+F,KAAK,CAAC;EACrC,MAAMqB,WAAW,GAAGD,KAAK,CAACd,gBAAgB,CAAC;;EAE3C;EACA,MAAM;IACJ3D,WAAW;IACX2E,UAAU;IACVC,aAAa;IACbC,eAAe;IACfC,WAAW;IACXC,UAAU;IACVC,gBAAgB;IAChBC,cAAc,EAAEC,gBAAgB;IAChCC,aAAa,EAAEC,eAAe;IAC9BC,iBAAiB;IACjBC,eAAe;IACfC;EACF,CAAC,GAAGlI,gBAAgB,CAAC,CAAC;EAEtB,MAAMmI,QAAQ,GAAI3B,cAAc,CAAC4B,IAAI,GAAGhB,KAAK,CAACiB,MAAM,GAAI,GAAG;;EAE3D;EACA,MAAMC,cAAc,GAAG1J,WAAW,CAAC,MAAM;IACvC,IAAI,CAACyH,SAAS,CAACkC,OAAO,EAAE;IACxBrB,cAAc,CAAC,IAAI,CAAC;IACpBc,iBAAiB,CAAC3B,SAAS,EAAE,GAAG,CAAC;EACnC,CAAC,EAAE,CAAC2B,iBAAiB,CAAC,CAAC;;EAEvB;EACA,MAAMQ,gBAAgB,GAAG5J,WAAW,CAAC,OAAO6J,QAAQ,EAAEC,SAAS,EAAEC,UAAU,KAAK;IAC9E,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,+BAA+BJ,QAAQ,oBAAoBE,UAAU,EAAE,CAAC;MAEpF,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAAC,0CAA0C,EAAE;QACvEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,SAAS,EAAEZ,QAAQ;UACnBC,SAAS,EAAEA,SAAS;UACpBC,UAAU,EAAEA,UAAU;UACtBW,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACpC,CAAC;MACH,CAAC,CAAC;MAEF,IAAIV,QAAQ,CAACW,EAAE,EAAE;QACf,MAAMC,MAAM,GAAG,MAAMZ,QAAQ,CAACa,IAAI,CAAC,CAAC;QACpCf,OAAO,CAACC,GAAG,CAAC,0BAA0Ba,MAAM,CAACE,OAAO,EAAE,CAAC;QACvD,OAAO,IAAI;MACb,CAAC,MAAM;QACLhB,OAAO,CAAC/F,KAAK,CAAC,gCAAgC,CAAC;QAC/C,OAAO,KAAK;MACd;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd+F,OAAO,CAAC/F,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO,KAAK;IACd;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMgH,QAAQ,GAAGjL,WAAW,CAAC,MAAM;IACjC,IAAI0H,gBAAgB,GAAGc,KAAK,CAACiB,MAAM,GAAG,CAAC,EAAE;MACvCvB,iBAAiB,CAAC,OAAO,CAAC;MAC1BP,mBAAmB,CAACuD,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACrClD,aAAa,CAACkD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACxD,gBAAgB,GAAG;MAAK,CAAC,CAAC,CAAC;MAC9DyD,UAAU,CAAC,MAAMjD,iBAAiB,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;IAChD;EACF,CAAC,EAAE,CAACR,gBAAgB,EAAEc,KAAK,CAACiB,MAAM,CAAC,CAAC;;EAEpC;EACAxJ,SAAS,CAAC,MAAM;IAAA,IAAAmL,gBAAA;IACdpB,OAAO,CAACC,GAAG,CAAC,yBAAyBpB,WAAW,iBAAiBJ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4C,IAAI,gBAAgB3C,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE4C,IAAI,gBAAgB5C,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEqB,UAAU,EAAE,CAAC;IAE3J,IAAIlB,WAAW,IAAIJ,WAAW,IAAI,CAAAC,UAAU,aAAVA,UAAU,wBAAA0C,gBAAA,GAAV1C,UAAU,CAAE4C,IAAI,cAAAF,gBAAA,uBAAhBA,gBAAA,CAAkBG,WAAW,CAAC,CAAC,MAAK9C,WAAW,CAAC4C,IAAI,CAACE,WAAW,CAAC,CAAC,EAAE;MACpGvB,OAAO,CAACC,GAAG,CAAC,2BAA2BxB,WAAW,CAAC4C,IAAI,oBAAoB3C,UAAU,CAACqB,UAAU,EAAE,CAAC;;MAEnG;MACA,IAAI,CAACnC,cAAc,CAAC4D,GAAG,CAAC9D,gBAAgB,CAAC,EAAE;QACzCsC,OAAO,CAACC,GAAG,CAAC,6BAA6BxB,WAAW,CAAC4C,IAAI,oBAAoB3C,UAAU,CAACqB,UAAU,EAAE,CAAC;;QAErG;QACA,MAAM0B,qBAAqB,GAAG,MAAAA,CAAA,KAAY;UACxC,IAAI1C,gBAAgB,IAAI,CAAAL,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEqB,UAAU,KAAI,GAAG,EAAE;YACrD,MAAM2B,KAAK,GAAG,MAAM9B,gBAAgB,CAACnB,WAAW,CAAC4C,IAAI,EAAEtC,gBAAgB,EAAEL,UAAU,CAACqB,UAAU,CAAC;YAC/F,IAAI2B,KAAK,EAAE;cACT1B,OAAO,CAACC,GAAG,CAAC,0CAA0CxB,WAAW,CAAC4C,IAAI,EAAE,CAAC;YAC3E;UACF;QACF,CAAC;;QAED;QACAI,qBAAqB,CAAC,CAAC;;QAEvB;QACA,IAAI,CAAC9C,aAAa,IAAI5E,WAAW,EAAE;UACjCiG,OAAO,CAACC,GAAG,CAAC,uCAAuCxB,WAAW,CAAC4C,IAAI,KAAK,CAAC;UACzEpC,gBAAgB,CAACR,WAAW,CAAC4C,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;;UAE1C;UACAF,UAAU,CAAC,MAAM;YACfhC,eAAe,CAAC,CAAC;YACjBa,OAAO,CAACC,GAAG,CAAC,wCAAwCxB,WAAW,CAAC4C,IAAI,EAAE,CAAC;UACzE,CAAC,EAAE,IAAI,CAAC;QACV;;QAEA;QACArD,aAAa,CAACkD,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACxD,gBAAgB,GAAG;QAAU,CAAC,CAAC,CAAC;QACnEG,iBAAiB,CAACqD,IAAI,IAAI,IAAIpD,GAAG,CAAC,CAAC,GAAGoD,IAAI,EAAExD,gBAAgB,CAAC,CAAC,CAAC;;QAE/D;QACA,IAAIH,gBAAgB,EAAE;UACpB,MAAMoE,iBAAiB,GAAG/D,cAAc,CAAC4B,IAAI,GAAG,CAAC;UACjDjC,gBAAgB,CAACH,KAAK,EAAEuE,iBAAiB,EAAEnD,KAAK,CAACiB,MAAM,CAAC;QAC1D;;QAEA;QACA0B,UAAU,CAAC,MAAM;UACf,IAAIzD,gBAAgB,GAAGc,KAAK,CAACiB,MAAM,GAAG,CAAC,EAAE;YACvCwB,QAAQ,CAAC,CAAC;UACZ,CAAC,MAAM;YACL;YACA7C,iBAAiB,CAAC,IAAI,CAAC;YACvB,IAAIb,gBAAgB,EAAE;cACpBA,gBAAgB,CAACH,KAAK,EAAEoB,KAAK,CAACiB,MAAM,EAAEjB,KAAK,CAACiB,MAAM,CAAC;YACrD;UACF;QACF,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLO,OAAO,CAACC,GAAG,CAAC,WAAWvC,gBAAgB,oBAAoB,CAAC;MAC9D;IACF;IACF;EACA,CAAC,EAAE,CAACmB,WAAW,EAAEJ,WAAW,EAAEC,UAAU,EAAEhB,gBAAgB,EAAEc,KAAK,CAACiB,MAAM,EAAErC,KAAK,EAAEG,gBAAgB,EAAEoB,aAAa,EAAE5E,WAAW,EAAEkF,gBAAgB,EAAEE,eAAe,EAAEvB,cAAc,EAAEgC,gBAAgB,EAAEb,gBAAgB,CAAC,CAAC;;EAEtN;EACA9I,SAAS,CAAC,MAAM;IACd,IAAI8D,WAAW,IAAIqD,KAAK,EAAE;MACxBkC,QAAQ,CAAClC,KAAK,CAAC;IACjB;EACF,CAAC,EAAE,CAACrD,WAAW,EAAEqD,KAAK,EAAEkC,QAAQ,CAAC,CAAC;;EAElC;EACArJ,SAAS,CAAC,MAAM;IACd,IAAI8D,WAAW,IAAI0D,SAAS,CAACkC,OAAO,IAAI,CAACtB,WAAW,EAAE;MACpDqB,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAC3F,WAAW,EAAE2F,cAAc,EAAErB,WAAW,CAAC,CAAC;;EAE9C;EACApI,SAAS,CAAC,MAAM;IACd,IAAI8D,WAAW,IAAI0E,WAAW,EAAE;MAC9BuB,OAAO,CAACC,GAAG,CAAC,8BAA8BxB,WAAW,CAAC4C,IAAI,EAAE,CAAC;MAC7DpC,gBAAgB,CAACR,WAAW,CAAC4C,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;IAC7C;EACF,CAAC,EAAE,CAACtH,WAAW,EAAE0E,WAAW,EAAEQ,gBAAgB,CAAC,CAAC;EAEhD,MAAM2C,QAAQ,GAAG5L,WAAW,CAAC,MAAM;IACjC,IAAI0H,gBAAgB,GAAG,CAAC,EAAE;MACxBQ,iBAAiB,CAAC,MAAM,CAAC;MACzBP,mBAAmB,CAACuD,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACrClD,aAAa,CAACkD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACxD,gBAAgB,GAAG;MAAK,CAAC,CAAC,CAAC;MAC9DyD,UAAU,CAAC,MAAMjD,iBAAiB,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;IAChD;EACF,CAAC,EAAE,CAACR,gBAAgB,CAAC,CAAC;EAEtB,MAAMmE,SAAS,GAAG7L,WAAW,CAAC,MAAM;IAClCgI,aAAa,CAACkD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACxD,gBAAgB,GAAG;IAAK,CAAC,CAAC,CAAC;IAC9DG,iBAAiB,CAACqD,IAAI,IAAI;MACxB,MAAMY,MAAM,GAAG,IAAIhE,GAAG,CAACoD,IAAI,CAAC;MAC5BY,MAAM,CAACC,MAAM,CAACrE,gBAAgB,CAAC;MAC/B,OAAOoE,MAAM;IACf,CAAC,CAAC;EACJ,CAAC,EAAE,CAACpE,gBAAgB,CAAC,CAAC;EAEtB,MAAMsE,mBAAmB,GAAGA,CAAA,KAAM;IAChC5D,iBAAiB,CAAC,KAAK,CAAC;IACxBf,MAAM,CAAC,CAAC;EACV,CAAC;EAED,MAAM4E,eAAe,GAAGA,CAAA,KAAM;IAC5B7D,iBAAiB,CAAC,KAAK,CAAC;IACxB;IACAf,MAAM,CAAC,CAAC;EACV,CAAC;EAED,IAAI,CAACkB,SAAS,IAAI,CAACE,WAAW,EAAE;IAC9B,oBACE1G,OAAA,CAACP,SAAS;MAAA0K,QAAA,eACRnK,OAAA;QAAKoK,KAAK,EAAE;UAAEC,KAAK,EAAE,OAAO;UAAEC,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBACnEnK,OAAA;UAAAmK,QAAA,EAAI;QAAe;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxB3K,OAAA;UAAQ4K,OAAO,EAAEtF,MAAO;UAAA6E,QAAA,EAAC;QAAO;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEhB;EAEA,MAAME,sBAAsB,GAAGhF,cAAc,CAAC4D,GAAG,CAAC9D,gBAAgB,CAAC;EACnE,MAAMmF,gBAAgB,GAAG9E,UAAU,CAACL,gBAAgB,CAAC;EAErD,oBACE3F,OAAA,CAACG,eAAe;IAAAgK,QAAA,gBACdnK,OAAA,CAACW,YAAY;MAAC8G,IAAI,EAAC,IAAI;MAAA0C,QAAA,gBACrBnK,OAAA,CAACe,gBAAgB;QAACyC,OAAO,EAAC,OAAO;QAACiE,IAAI,EAAC,IAAI;QAACmD,OAAO,EAAEtF,MAAO;QAAA6E,QAAA,gBAC1DnK,OAAA,CAAC1B,SAAS;UAACmJ,IAAI,EAAE;QAAG;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kBAEzB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAkB,CAAC,eAEnB3K,OAAA,CAACiB,eAAe;QAAAkJ,QAAA,gBACdnK,OAAA,CAACsB,gBAAgB;UAAC+D,KAAK,EAAE,CAAE;UAAA8E,QAAA,GAAC,QACpB,EAAC9E,KAAK,EAAC,IAAE,EAACmB,SAAS,CAAC8C,IAAI;QAAA;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACnB3K,OAAA,CAACyB,gBAAgB;UAACgG,IAAI,EAAC,IAAI;UAAA0C,QAAA,EACxB3D,SAAS,CAAChH;QAAK;UAAAgL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAElB3K,OAAA,CAAC8B,sBAAsB;QACrBE,WAAW,EAAEA,WAAY;QACzB4I,OAAO,EAAE,CAAC5I,WAAW,GAAGsF,eAAe,GAAGyD,SAAU;QAAAZ,QAAA,GAEnDnI,WAAW,gBAAGhC,OAAA,CAACrB,IAAI;UAAC8I,IAAI,EAAE;QAAG;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG3K,OAAA,CAACpB,OAAO;UAAC6I,IAAI,EAAE;QAAG;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzD3K,OAAA;UAAMgL,SAAS,EAAC,iBAAiB;UAAAb,QAAA,EAC9BnI,WAAW,GAAG,WAAW,GAAG;QAAc;UAAAwI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,EACN,CAAC3I,WAAW,iBAAIhC,OAAA,CAACnB,SAAS;UAAC4I,IAAI,EAAE;QAAG;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC,eAEf3K,OAAA,CAACoC,WAAW;MAAA+H,QAAA,gBACVnK,OAAA,CAACuC,gBAAgB;QAAA4H,QAAA,gBACfnK,OAAA,CAACyC,eAAe;UAAA0H,QAAA,gBACdnK,OAAA,CAAC2C,aAAa;YAAAwH,QAAA,EAAC;UAAc;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,eAC7C3K,OAAA,CAAC8C,WAAW;YAAAqH,QAAA,eACVnK,OAAA,CAACgD,YAAY;cAACoH,KAAK,EAAE;gBAAEa,KAAK,EAAE,GAAGzD,QAAQ;cAAI;YAAE;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACd3K,OAAA,CAACkD,YAAY;YAAAiH,QAAA,GACVtE,cAAc,CAAC4B,IAAI,EAAC,MAAI,EAAChB,KAAK,CAACiB,MAAM,EAAC,oBAAkB,EAACwD,IAAI,CAACC,KAAK,CAAC3D,QAAQ,CAAC,EAAC,IACjF;UAAA;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAElB3K,OAAA,CAACZ,SAAS;UACRmK,IAAI,EAAE7C,WAAY;UAClB0E,UAAU,EAAEzF,gBAAgB,GAAG,CAAE;UACjC0F,UAAU,EAAE5E,KAAK,CAACiB,MAAO;UACzB4D,SAAS,EAAER,gBAAgB,KAAK,SAAU;UAC1CS,WAAW,EAAET,gBAAgB,KAAK,WAAY;UAC9CU,WAAW,EAAExJ,WAAW,IAAI,CAAC6I,sBAAuB;UACpD3E,cAAc,EAAEA,cAAe;UAC/BsB,QAAQ,EAAG7B,gBAAgB,GAAGc,KAAK,CAACiB,MAAM,GAAI;QAAI;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eAEF3K,OAAA,CAACoD,QAAQ;UAAA+G,QAAA,gBACPnK,OAAA,CAACsD,aAAa;YACZsH,OAAO,EAAEf,QAAS;YAClB4B,QAAQ,EAAE9F,gBAAgB,KAAK,CAAE;YAAAwE,QAAA,gBAEjCnK,OAAA,CAAC1B,SAAS;cAACmJ,IAAI,EAAE;YAAG;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAEzB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,EAEfE,sBAAsB,gBACrB7K,OAAA,CAACsD,aAAa;YACZE,OAAO,EAAC,SAAS;YACjBoH,OAAO,EAAE1B,QAAS;YAClBuC,QAAQ,EAAE9F,gBAAgB,KAAKc,KAAK,CAACiB,MAAM,GAAG,CAAE;YAAAyC,QAAA,gBAEhDnK,OAAA,CAAClB,WAAW;cAAC2I,IAAI,EAAE;YAAG;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAE3B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,gBAEhB3K,OAAA,CAACsD,aAAa;YACZsH,OAAO,EAAEd,SAAU;YACnB2B,QAAQ,EAAE,CAACzJ,WAAY;YAAAmI,QAAA,gBAEvBnK,OAAA,CAACxB,SAAS;cAACiJ,IAAI,EAAE;YAAG;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,SAEzB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAChB,eAED3K,OAAA,CAACsD,aAAa;YACZsH,OAAO,EAAE1B,QAAS;YAClBuC,QAAQ,EAAE9F,gBAAgB,KAAKc,KAAK,CAACiB,MAAM,GAAG,CAAE;YAAAyC,QAAA,GACjD,MAEC,eAAAnK,OAAA,CAACzB,UAAU;cAACkJ,IAAI,EAAE;YAAG;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAEnB3K,OAAA,CAAC0D,aAAa;QAAAyG,QAAA,gBACZnK,OAAA,CAAC4D,WAAW;UAAAuG,QAAA,gBACVnK,OAAA,CAACtB,MAAM;YAAC+I,IAAI,EAAE;UAAG;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEtB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAEd3K,OAAA,CAAC8D,eAAe;UAAAqG,QAAA,gBACdnK,OAAA,CAAC3B,MAAM;YACLqN,GAAG,EAAEhG,SAAU;YACfiG,KAAK,EAAE,KAAM;YACbV,KAAK,EAAC,MAAM;YACZW,MAAM,EAAC,MAAM;YACbC,gBAAgB,EAAC,YAAY;YAC7BC,gBAAgB,EAAE;cAChBb,KAAK,EAAE,GAAG;cACVW,MAAM,EAAE,GAAG;cACXG,UAAU,EAAE;YACd;UAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAED9D,eAAe,iBACd7G,OAAA,CAACgE,aAAa;YAAAmG,QAAA,EACXtD;UAAe;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAChB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC,eAElB3K,OAAA,CAACkE,iBAAiB;UAAAiG,QAAA,gBAChBnK,OAAA,CAACoE,cAAc;YAAA+F,QAAA,EACZxD,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAE4C,IAAI,GAAG,aAAa5C,UAAU,CAAC4C,IAAI,EAAE,GAAG;UAA8B;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,EAChB,CAAAhE,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEqB,UAAU,kBACrBhI,OAAA,CAACsE,cAAc;YAAA6F,QAAA,GAAC,cACF,EAACe,IAAI,CAACC,KAAK,CAACxE,UAAU,CAACqB,UAAU,GAAG,GAAG,CAAC,EAAC,GACvD;UAAA;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAgB,CACjB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACgB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAEbvE,cAAc,iBACbpG,OAAA,CAACwE,eAAe;MAAA2F,QAAA,eACdnK,OAAA,CAAC0E,YAAY;QAAAyF,QAAA,gBACXnK,OAAA,CAACjB,MAAM;UAAC0I,IAAI,EAAE,EAAG;UAAC2C,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAE2B,YAAY,EAAE;UAAO;QAAE;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvE3K,OAAA,CAAC4E,UAAU;UAAAuF,QAAA,EAAC;QAAkB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC3C3K,OAAA,CAAC+E,SAAS;UAAAoF,QAAA,GAAC,uDAC4C,EAAC9E,KAAK,EAAC,IAAE,EAACmB,SAAS,CAAC8C,IAAI,EAAC,wBAC1D,EAAC7C,KAAK,CAACiB,MAAM,EAAC,uBACpC;QAAA;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACZ3K,OAAA,CAACkF,YAAY;UAAAiF,QAAA,gBACXnK,OAAA,CAACsD,aAAa;YAACsH,OAAO,EAAEX,mBAAoB;YAAAE,QAAA,gBAC1CnK,OAAA,CAACvB,IAAI;cAACgJ,IAAI,EAAE;YAAG;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kBAEpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,EACftF,KAAK,GAAG,CAAC,iBACRrF,OAAA,CAACsD,aAAa;YAACE,OAAO,EAAC,SAAS;YAACoH,OAAO,EAAEV,eAAgB;YAAAC,QAAA,gBACxDnK,OAAA,CAAChB,MAAM;cAACyI,IAAI,EAAE;YAAG;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAEtB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAChB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAClB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACc,CAAC;AAEtB,CAAC;AAAClF,EAAA,CAlXIL,iBAAiB;EAAA,QAgCjB/F,gBAAgB;AAAA;AAAA4M,IAAA,GAhChB7G,iBAAiB;AAoXvB,eAAeA,iBAAiB;AAAC,IAAA3E,EAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAG,GAAA,EAAAK,GAAA,EAAAM,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAA8G,IAAA;AAAAC,YAAA,CAAAzL,EAAA;AAAAyL,YAAA,CAAApL,GAAA;AAAAoL,YAAA,CAAAlL,GAAA;AAAAkL,YAAA,CAAA7K,GAAA;AAAA6K,YAAA,CAAA1K,GAAA;AAAA0K,YAAA,CAAArK,GAAA;AAAAqK,YAAA,CAAA/J,GAAA;AAAA+J,YAAA,CAAA5J,GAAA;AAAA4J,YAAA,CAAA1J,GAAA;AAAA0J,YAAA,CAAAxJ,GAAA;AAAAwJ,YAAA,CAAArJ,GAAA;AAAAqJ,YAAA,CAAAnJ,IAAA;AAAAmJ,YAAA,CAAAjJ,IAAA;AAAAiJ,YAAA,CAAA/I,IAAA;AAAA+I,YAAA,CAAA7I,IAAA;AAAA6I,YAAA,CAAAzI,IAAA;AAAAyI,YAAA,CAAAvI,IAAA;AAAAuI,YAAA,CAAArI,IAAA;AAAAqI,YAAA,CAAAnI,IAAA;AAAAmI,YAAA,CAAAjI,IAAA;AAAAiI,YAAA,CAAA/H,IAAA;AAAA+H,YAAA,CAAA7H,IAAA;AAAA6H,YAAA,CAAA3H,IAAA;AAAA2H,YAAA,CAAAzH,IAAA;AAAAyH,YAAA,CAAAvH,IAAA;AAAAuH,YAAA,CAAApH,IAAA;AAAAoH,YAAA,CAAAjH,IAAA;AAAAiH,YAAA,CAAA/G,IAAA;AAAA+G,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}