{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2.034 2.681a.498.498 0 0 1 .647-.647l9 3.5a.5.5 0 0 1-.033.944L8.204 7.545a1 1 0 0 0-.66.66l-1.066 3.443a.5.5 0 0 1-.944.033z\",\n  key: \"11pp1i\"\n}], [\"circle\", {\n  cx: \"16\",\n  cy: \"16\",\n  r: \"6\",\n  key: \"qoo3c4\"\n}], [\"path\", {\n  d: \"m11.8 11.8 8.4 8.4\",\n  key: \"oogvdj\"\n}]];\nconst MousePointerBan = createLucideIcon(\"mouse-pointer-ban\", __iconNode);\nexport { __iconNode, MousePointerBan as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "Mouse<PERSON>ointerBan", "createLucideIcon"], "sources": ["D:\\ASL\\ASL-Training\\node_modules\\lucide-react\\src\\icons\\mouse-pointer-ban.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M2.034 2.681a.498.498 0 0 1 .647-.647l9 3.5a.5.5 0 0 1-.033.944L8.204 7.545a1 1 0 0 0-.66.66l-1.066 3.443a.5.5 0 0 1-.944.033z',\n      key: '11pp1i',\n    },\n  ],\n  ['circle', { cx: '16', cy: '16', r: '6', key: 'qoo3c4' }],\n  ['path', { d: 'm11.8 11.8 8.4 8.4', key: 'oogvdj' }],\n];\n\n/**\n * @component @name MousePointerBan\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMi4wMzQgMi42ODFhLjQ5OC40OTggMCAwIDEgLjY0Ny0uNjQ3bDkgMy41YS41LjUgMCAwIDEtLjAzMy45NDRMOC4yMDQgNy41NDVhMSAxIDAgMCAwLS42Ni42NmwtMS4wNjYgMy40NDNhLjUuNSAwIDAgMS0uOTQ0LjAzM3oiIC8+CiAgPGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iNiIgLz4KICA8cGF0aCBkPSJtMTEuOCAxMS44IDguNCA4LjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/mouse-pointer-ban\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MousePointerBan = createLucideIcon('mouse-pointer-ban', __iconNode);\n\nexport default MousePointerBan;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,oBAAsB;EAAAC,GAAA,EAAK;AAAU,GACrD;AAaM,MAAAI,eAAA,GAAkBC,gBAAiB,sBAAqBP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}