{"ast": null, "code": "var _jsxFileName = \"D:\\\\ASL\\\\ASL-Training\\\\src\\\\components\\\\TrainingPage.js\",\n  _s = $RefreshSig$();\nimport { useState, useRef, useCallback, useEffect } from 'react';\nimport styled from 'styled-components';\nimport Webcam from 'react-webcam';\nimport { Brain, Camera, ArrowLeft, Play, Square, Download, Eye, Target, Wifi, WifiOff, RefreshCw } from 'lucide-react';\nimport { useSignDetection } from '../hooks/useSignDetection';\nimport LevelSelector from './LevelSelector';\nimport FlashCardTraining from './FlashCardTraining';\nimport config from '../config';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TrainingContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow-x: hidden;\n\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n`;\n_c = TrainingContainer;\nconst Navigation = styled.nav`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: var(--bg-glass);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-neural);\n  padding: var(--space-4) 0;\n  transition: var(--transition-normal);\n`;\n_c2 = Navigation;\nconst NavContainer = styled.div`\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  @media (max-width: 768px) {\n    padding: 0 var(--space-4);\n  }\n`;\n_c3 = NavContainer;\nconst Logo = styled.div`\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n    gap: var(--space-2);\n  }\n`;\n_c4 = Logo;\nconst LogoIcon = styled.div`\n  width: 40px;\n  height: 40px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    width: 36px;\n    height: 36px;\n  }\n`;\n_c5 = LogoIcon;\nconst BackButton = styled.button`\n  background: var(--bg-glass);\n  color: var(--text-secondary);\n  border: 1px solid var(--border-neural);\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-xl);\n  font-size: 0.9rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  backdrop-filter: blur(10px);\n\n  &:hover {\n    background: var(--primary-50);\n    color: var(--primary-600);\n    border-color: var(--primary-300);\n    transform: translateY(-1px);\n    box-shadow: var(--shadow-lg);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-2) var(--space-4);\n    font-size: 0.85rem;\n  }\n`;\n_c6 = BackButton;\nconst PageTitle = styled.h1`\n  font-family: var(--font-primary);\n  font-size: 2.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  text-align: center;\n  margin-bottom: var(--space-4);\n  letter-spacing: -0.02em;\n\n  @media (max-width: 768px) {\n    font-size: 2rem;\n  }\n`;\n_c7 = PageTitle;\nconst PageSubtitle = styled.p`\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  text-align: center;\n  margin-bottom: var(--space-16);\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n\n  @media (max-width: 768px) {\n    margin-bottom: var(--space-12);\n    font-size: 1rem;\n  }\n`;\n_c8 = PageSubtitle;\nconst StatusBadge = styled.div`\n  display: inline-flex;\n  align-items: center;\n  gap: var(--space-2);\n  background: var(--bg-glass);\n  border: 1px solid var(--border-neural);\n  border-radius: var(--radius-full);\n  padding: var(--space-2) var(--space-4);\n  margin-bottom: var(--space-8);\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: var(--text-accent);\n  backdrop-filter: blur(10px);\n  box-shadow: var(--shadow-glow);\n\n  @media (max-width: 768px) {\n    font-size: 0.8rem;\n    padding: var(--space-2) var(--space-3);\n  }\n`;\n_c9 = StatusBadge;\nconst MainContent = styled.main`\n  padding: var(--space-20) var(--space-4) var(--space-16);\n  max-width: 1200px;\n  margin: 0 auto;\n\n  @media (max-width: 768px) {\n    padding: var(--space-12) var(--space-3) var(--space-8);\n    max-width: 100%;\n  }\n`;\n_c0 = MainContent;\nconst TrainingGrid = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--space-8);\n  max-width: 1200px;\n  margin: 0 auto var(--space-12);\n\n  @media (max-width: 1024px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-4);\n  }\n\n  @media (max-width: 768px) {\n    gap: var(--space-3);\n    margin: 0 auto var(--space-6);\n    grid-template-areas: \n      \"sign\"\n      \"camera\";\n  }\n`;\n_c1 = TrainingGrid;\nconst CameraSection = styled.div`\n  background: var(--bg-glass);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-10);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-lg);\n  transition: var(--transition-normal);\n  position: relative;\n  overflow: hidden;\n\n  @media (max-width: 768px) {\n    padding: var(--space-6);\n    border-radius: var(--radius-xl);\n    grid-area: camera;\n  }\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 4px;\n    background: var(--bg-neural);\n    transform: scaleX(0);\n    transition: var(--transition-normal);\n  }\n\n  &:hover {\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n    border-color: var(--primary-300);\n\n    &::before {\n      transform: scaleX(1);\n    }\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-8);\n  }\n`;\n_c10 = CameraSection;\nconst SectionTitle = styled.h2`\n  font-family: var(--font-primary);\n  font-size: 1.25rem;\n  margin-bottom: var(--space-6);\n  color: var(--text-primary);\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    margin-bottom: var(--space-4);\n  }\n`;\n_c11 = SectionTitle;\nconst SectionIcon = styled.div`\n  width: 36px;\n  height: 36px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    width: 32px;\n    height: 32px;\n  }\n`;\n_c12 = SectionIcon;\nconst WebcamContainer = styled.div`\n  position: relative;\n  border-radius: var(--radius-2xl);\n  overflow: hidden;\n  background: var(--neural-100);\n  aspect-ratio: 4/3;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 3px solid var(--border-neural);\n  margin-bottom: var(--space-6);\n  box-shadow: var(--shadow-lg);\n\n  @media (max-width: 768px) {\n    aspect-ratio: 3/4;\n    margin-bottom: var(--space-4);\n    border-radius: var(--radius-xl);\n    border-width: 2px;\n  }\n`;\n_c13 = WebcamContainer;\nconst StyledWebcam = styled(Webcam)`\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n`;\n_c14 = StyledWebcam;\nconst RecordingOverlay = styled.div`\n  position: absolute;\n  top: var(--space-4);\n  right: var(--space-4);\n  background: ${props => props.isRecording ? 'var(--error-500)' : 'var(--neural-600)'};\n  color: white;\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-full);\n  font-size: 0.9rem;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  box-shadow: var(--shadow-lg);\n  animation: ${props => props.isRecording ? 'pulse 1.5s infinite' : 'none'};\n\n  @keyframes pulse {\n    0%, 100% { opacity: 1; transform: scale(1); }\n    50% { opacity: 0.8; transform: scale(1.05); }\n  }\n`;\n_c15 = RecordingOverlay;\nconst SignSection = styled.div`\n  background: var(--bg-primary);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-8);\n  border: 1px solid var(--border-light);\n  box-shadow: var(--shadow-lg);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  transition: all 0.3s ease;\n\n  &:hover {\n    box-shadow: var(--shadow-xl);\n    border-color: var(--primary-200);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-6);\n    grid-area: sign;\n  }\n`;\n_c16 = SignSection;\nconst SignSelector = styled.select`\n  width: 100%;\n  padding: var(--space-3) var(--space-4);\n  border: 2px solid var(--border-light);\n  border-radius: var(--radius-lg);\n  background: var(--bg-primary);\n  color: var(--text-primary);\n  font-size: 1rem;\n  font-weight: 500;\n  margin-bottom: var(--space-4);\n  cursor: pointer;\n  transition: var(--transition-normal);\n\n  &:focus {\n    outline: none;\n    border-color: var(--primary-500);\n    box-shadow: 0 0 0 3px var(--primary-100);\n  }\n\n  &:hover {\n    border-color: var(--primary-300);\n  }\n\n  option {\n    padding: var(--space-2);\n    background: var(--bg-primary);\n    color: var(--text-primary);\n  }\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    padding: var(--space-4);\n    margin-bottom: var(--space-3);\n  }\n`;\n_c17 = SignSelector;\nconst SignDisplay = styled.div`\n  width: 300px;\n  height: 300px;\n  background: var(--primary-50);\n  border-radius: var(--radius-2xl);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: var(--space-6);\n  border: 2px solid var(--primary-200);\n  transition: all 0.3s ease;\n  overflow: hidden;\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: var(--radius-xl);\n  }\n\n  &:hover {\n    transform: scale(1.02);\n    border-color: var(--primary-300);\n  }\n\n  @media (max-width: 768px) {\n    width: 250px;\n    height: 250px;\n  }\n`;\n_c18 = SignDisplay;\nconst SignName = styled.h3`\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  margin-bottom: var(--space-3);\n  color: var(--text-primary);\n  font-weight: 700;\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n  }\n`;\n_c19 = SignName;\nconst SignDescription = styled.p`\n  text-align: center;\n  line-height: 1.6;\n  color: var(--text-secondary);\n  font-size: 0.9rem;\n  font-weight: 400;\n  max-width: 280px;\n`;\n_c20 = SignDescription;\nconst TopControlsSection = styled.div`\n  display: flex;\n  justify-content: center;\n  gap: var(--space-4);\n  margin-bottom: var(--space-8);\n  padding: var(--space-4);\n  background: var(--bg-glass);\n  border-radius: var(--radius-xl);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(10px);\n  box-shadow: var(--shadow-lg);\n\n  @media (max-width: 768px) {\n    flex-direction: column;\n    align-items: center;\n    gap: var(--space-3);\n    margin-bottom: var(--space-6);\n    padding: var(--space-3);\n  }\n`;\n_c21 = TopControlsSection;\nconst RecordingStatus = styled.div`\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  padding: var(--space-2) var(--space-3);\n  border-radius: var(--radius-lg);\n  font-size: 0.875rem;\n  font-weight: 500;\n  background: ${props => props.isRecording ? 'var(--error-50)' : 'var(--success-50)'};\n  color: ${props => props.isRecording ? 'var(--error-700)' : 'var(--success-700)'};\n  border: 1px solid ${props => props.isRecording ? 'var(--error-200)' : 'var(--success-200)'};\n  white-space: nowrap;\n\n  @media (max-width: 768px) {\n    font-size: 0.8rem;\n    padding: var(--space-1) var(--space-2);\n  }\n`;\n_c22 = RecordingStatus;\nconst ControlButton = styled.button`\n  background: ${props => props.variant === 'primary' ? 'var(--primary-600)' : props.variant === 'retry' ? 'var(--warning-500)' : 'var(--bg-primary)'};\n  border: ${props => props.variant === 'primary' || props.variant === 'retry' ? 'none' : '1px solid var(--border-medium)'};\n  color: ${props => props.variant === 'primary' || props.variant === 'retry' ? 'white' : 'var(--text-primary)'};\n  padding: ${props => props.compact ? 'var(--space-2) var(--space-4)' : 'var(--space-3) var(--space-6)'};\n  border-radius: var(--radius-lg);\n  cursor: pointer;\n  font-size: ${props => props.compact ? '0.8rem' : '0.9rem'};\n  font-weight: 600;\n  transition: all 0.2s ease;\n  min-width: ${props => props.compact ? '120px' : '160px'};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--space-2);\n\n  @media (max-width: 768px) {\n    padding: ${props => props.compact ? 'var(--space-3) var(--space-5)' : 'var(--space-4) var(--space-8)'};\n    font-size: ${props => props.compact ? '0.9rem' : '1rem'};\n    min-width: ${props => props.compact ? '140px' : '180px'};\n    border-radius: var(--radius-xl);\n  }\n  box-shadow: ${props => props.variant === 'primary' || props.variant === 'retry' ? 'var(--shadow-lg)' : 'var(--shadow-sm)'};\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: ${props => props.variant === 'primary' || props.variant === 'retry' ? 'var(--shadow-xl)' : 'var(--shadow-md)'};\n    background: ${props => props.variant === 'primary' ? 'var(--primary-700)' : props.variant === 'retry' ? 'var(--warning-600)' : 'var(--gray-50)'};\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none;\n  }\n\n  @media (max-width: 768px) {\n    width: 100%;\n    max-width: ${props => props.compact ? '200px' : '280px'};\n  }\n`;\n_c23 = ControlButton;\nconst StatusMessage = styled.div`\n  text-align: center;\n  margin-top: var(--space-6);\n  padding: var(--space-4) var(--space-6);\n  border-radius: var(--radius-lg);\n  background: ${props => props.type === 'success' ? 'var(--success-500)' : props.type === 'error' ? 'var(--error-500)' : 'var(--primary-600)'};\n  color: white;\n  font-weight: 500;\n  font-size: 0.875rem;\n  max-width: 400px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n_c24 = StatusMessage;\nconst RecordingsSection = styled.div`\n  margin-top: var(--space-16);\n  background: var(--bg-secondary);\n  padding: var(--space-12) var(--space-4);\n  border-radius: var(--radius-2xl);\n  max-width: 1200px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n_c25 = RecordingsSection;\nconst RecordingsTitle = styled.h3`\n  font-family: var(--font-primary);\n  color: var(--text-primary);\n  margin-bottom: var(--space-8);\n  font-size: 1.5rem;\n  font-weight: 600;\n  text-align: center;\n`;\n_c26 = RecordingsTitle;\nconst RecordingsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: var(--space-6);\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-4);\n  }\n`;\n_c27 = RecordingsGrid;\nconst RecordingCard = styled.div`\n  background: var(--bg-primary);\n  padding: var(--space-6);\n  border-radius: var(--radius-xl);\n  border: 1px solid var(--border-light);\n  box-shadow: var(--shadow-sm);\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: translateY(-2px);\n    border-color: var(--primary-200);\n    box-shadow: var(--shadow-lg);\n  }\n`;\n_c28 = RecordingCard;\nconst RecordingTitle = styled.p`\n  margin: 0 0 var(--space-2) 0;\n  color: var(--text-primary);\n  font-weight: 600;\n  font-size: 1rem;\n  font-family: var(--font-primary);\n`;\n_c29 = RecordingTitle;\nconst RecordingTime = styled.p`\n  margin: 0 0 var(--space-4) 0;\n  font-size: 0.8rem;\n  color: var(--text-tertiary);\n`;\n_c30 = RecordingTime;\nconst DownloadButton = styled.button`\n  background: var(--primary-600);\n  border: none;\n  border-radius: var(--radius-lg);\n  padding: var(--space-2) var(--space-4);\n  color: white;\n  cursor: pointer;\n  font-size: 0.8rem;\n  font-weight: 500;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  margin: 0 auto;\n\n  &:hover {\n    background: var(--primary-700);\n    transform: translateY(-1px);\n  }\n`;\n_c31 = DownloadButton;\nconst PredictionDisplay = styled.div`\n  background: var(--bg-glass);\n  border: 2px solid ${props => {\n  if (props.matched) return 'var(--success-400)';\n  if (props.isStale) return 'var(--warning-300)';\n  return 'var(--border-light)';\n}};\n  border-radius: var(--radius-xl);\n  padding: var(--space-4);\n  margin-bottom: var(--space-4);\n  text-align: center;\n  transition: var(--transition-normal);\n  backdrop-filter: blur(10px);\n  opacity: ${props => props.isStale ? 0.7 : 1};\n  min-height: 80px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n\n  ${props => props.matched && `\n    background: var(--success-50);\n    box-shadow: 0 0 20px var(--success-200);\n    animation: pulse 1s ease-in-out;\n  `}\n\n  ${props => props.isStale && `\n    background: var(--warning-50);\n  `}\n\n  @keyframes pulse {\n    0%, 100% { transform: scale(1); }\n    50% { transform: scale(1.02); }\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-3);\n    margin-bottom: var(--space-3);\n    min-height: 70px;\n  }\n`;\n_c32 = PredictionDisplay;\nconst PredictionText = styled.div`\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: ${props => {\n  if (props.matched) return 'var(--success-700)';\n  if (props.isStale) return 'var(--warning-700)';\n  return 'var(--text-primary)';\n}};\n  margin-bottom: var(--space-2);\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n  }\n`;\n_c33 = PredictionText;\nconst ConfidenceBar = styled.div`\n  width: 100%;\n  height: 8px;\n  background: var(--bg-secondary);\n  border-radius: var(--radius-full);\n  overflow: hidden;\n  margin-top: var(--space-2);\n`;\n_c34 = ConfidenceBar;\nconst ConfidenceFill = styled.div`\n  height: 100%;\n  background: ${props => {\n  if (props.confidence > 0.8) return 'var(--success-500)';\n  if (props.confidence > 0.6) return 'var(--warning-500)';\n  return 'var(--error-500)';\n}};\n  width: ${props => props.confidence * 100}%;\n  transition: width 0.3s ease;\n`;\n_c35 = ConfidenceFill;\nconst ConnectionStatus = styled.div`\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  padding: var(--space-2) var(--space-3);\n  border-radius: var(--radius-lg);\n  font-size: 0.875rem;\n  font-weight: 500;\n  background: ${props => props.connected ? 'var(--success-50)' : 'var(--error-50)'};\n  color: ${props => props.connected ? 'var(--success-700)' : 'var(--error-700)'};\n  border: 1px solid ${props => props.connected ? 'var(--success-200)' : 'var(--error-200)'};\n`;\n\n// Sign language data with GIFs (100 signs, model-predictable)\n_c36 = ConnectionStatus;\nconst signLanguageData = {\n  \"TV\": {\n    name: \"TV\",\n    gif: \"https://lifeprint.com/asl101/gifs/t/tv.gif\",\n    description: \"Sign for TV.\"\n  },\n  \"after\": {\n    name: \"After\",\n    gif: \"https://lifeprint.com/asl101/gifs/a/after-over-across.gif\",\n    description: \"Sign for after.\"\n  },\n  \"airplane\": {\n    name: \"Airplane\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/a/airplane-flying.gif\",\n    description: \"Sign for airplane.\"\n  },\n  \"all\": {\n    name: \"All\",\n    gif: \"https://lifeprint.com/asl101/gifs/a/all-whole.gif\",\n    description: \"Sign for all.\"\n  },\n  \"alligator\": {\n    name: \"Alligator\",\n    gif: \"https://lifeprint.com/asl101/gifs/a/alligator.gif\",\n    description: \"Sign for alligator.\"\n  },\n  \"animal\": {\n    name: \"Animal\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/animal.gif\",\n    description: \"Sign for animal.\"\n  },\n  \"another\": {\n    name: \"Another\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/another.gif\",\n    description: \"Sign for another.\"\n  },\n  \"any\": {\n    name: \"Any\",\n    gif: \"https://lifeprint.com/asl101/gifs/a/any.gif\",\n    description: \"Sign for any.\"\n  },\n  \"apple\": {\n    name: \"Apple\",\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\n    description: \"Sign for apple.\"\n  },\n  \"arm\": {\n    name: \"Arm\",\n    gif: \"https://th.bing.com/th/id/OIP.KkJLqfbp6XEHiIe-jOUb2AHaEc?w=251&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for arm.\"\n  },\n  \"aunt\": {\n    name: \"Aunt\",\n    gif: \"https://th.bing.com/th/id/OIP.Yz5UUZdNTrVWXf72we_N6wHaHa?rs=1&pid=ImgDetMain\",\n    description: \"Sign for aunt.\"\n  },\n  \"awake\": {\n    name: \"Awake\",\n    gif: \"https://th.bing.com/th/id/OIP.XcgdjGKBo8LynmiAw-tDCQHaE-?w=235&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for awake.\"\n  },\n  \"backyard\": {\n    name: \"Backyard\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/backyard.gif\",\n    description: \"Sign for backyard.\"\n  },\n  \"bad\": {\n    name: \"Bad\",\n    gif: \"https://media.giphy.com/media/v1.Y2lkPTc5MGI3NjExeThwMm9rZGVkejE2N2RhMWp0NTY3Z2pwNHBqZXFlOTF3ZGd0cG1zZSZlcD12MV9naWZzX3NlYXJjaCZjdD1n/l0MYL34CVEqLK27yU/giphy.gif\",\n    description: \"Sign for bad.\"\n  },\n  \"balloon\": {\n    name: \"Balloon\",\n    gif: \"https://media.giphy.com/media/26FL9yfajyobRXJde/giphy.gif\",\n    description: \"Sign for balloon.\"\n  },\n  \"bath\": {\n    name: \"Bath\",\n    gif: \"https://media.giphy.com/media/l0MYPjjoeJbZVPmNO/giphy.gif\",\n    description: \"Sign for bath.\"\n  },\n  // \"because\": { name: \"Because\", gif: \"https://lifeprint.com/asl101/gifs-animated/because.gif\", description: \"Sign for because.\" },\n  \"bed\": {\n    name: \"Bed\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/bed-1.gif\",\n    description: \"Sign for bed.\"\n  },\n  \"bedroom\": {\n    name: \"Bedroom\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/bedroom.gif\",\n    description: \"Sign for bedroom.\"\n  },\n  \"bee\": {\n    name: \"Bee\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/bee.gif\",\n    description: \"Sign for bee.\"\n  },\n  \"before\": {\n    name: \"Before\",\n    gif: \"https://th.bing.com/th/id/OIP.0EvzUY4jH2cDCa4nNcRw4wHaE-?w=267&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for before.\"\n  },\n  // \"beside\": { name: \"Beside\", gif: \"https://lifeprint.com/asl101/gifs/b/beside.gif\", description: \"Sign for beside.\" },\n  \"better\": {\n    name: \"Better\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/better.gif\",\n    description: \"Sign for better.\"\n  },\n  \"bird\": {\n    name: \"Bird\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/bird.gif\",\n    description: \"Sign for bird.\"\n  },\n  \"black\": {\n    name: \"Black\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=52tGw7%2fGcx2HtwMm9rZGVkejE2N2RhMWp0NTY3Z2pwNHBqZXFlOTF3ZGd0cG1zZSZlcD12MV9naWZzX3NlYXJjaCZjdD1n/l0MYL34CVEqLK27yU/giphy.gif\",\n    description: \"Sign for black.\"\n  },\n  \"blow\": {\n    name: \"Blow\",\n    gif: \"https://th.bing.com/th/id/OIP.rJg-otMBtvfj1T1HkSKugwHaEc?w=304&h=182&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for blow.\"\n  },\n  \"blue\": {\n    name: \"Blue\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/blue-1.gif\",\n    description: \"Sign for blue.\"\n  },\n  \"boat\": {\n    name: \"Boat\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/boat-ship.gif\",\n    description: \"Sign for boat.\"\n  },\n  \"book\": {\n    name: \"Book\",\n    gif: \"https://media.giphy.com/media/l0MYL43dl4pQEn3uE/giphy.gif\",\n    description: \"Sign for book.\"\n  },\n  \"boy\": {\n    name: \"Boy\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/boy.gif\",\n    description: \"Sign for boy.\"\n  },\n  \"brother\": {\n    name: \"Brother\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/brother.gif\",\n    description: \"Sign for brother.\"\n  },\n  \"brown\": {\n    name: \"Brown\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/brown.gif\",\n    description: \"Sign for brown.\"\n  },\n  \"bug\": {\n    name: \"Bug\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/bug.gif\",\n    description: \"Sign for bug.\"\n  },\n  \"bye\": {\n    name: \"Bye\",\n    gif: \"https://c.tenor.com/vME77PObDN8AAAAC/asl-bye-asl-goodbye.gif\",\n    description: \"Sign for bye.\"\n  },\n  \"callonphone\": {\n    name: \"Call on phone\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/c/call-hearing.gif\",\n    description: \"Sign for call on phone.\"\n  },\n  \"can\": {\n    name: \"Can\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/can.gif\",\n    description: \"Sign for can.\"\n  },\n  \"car\": {\n    name: \"Car\",\n    gif: \"https://th.bing.com/th/id/OIP.wxw32OaIdqFt8f_ucHVoRgHaEH?w=308&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for car.\"\n  },\n  \"carrot\": {\n    name: \"Carrot\",\n    gif: \"https://media.giphy.com/media/l0HlDdvqxs1jsRtiU/giphy.gif\",\n    description: \"Sign for carrot.\"\n  },\n  \"cat\": {\n    name: \"Cat\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/cat-02.gif\",\n    description: \"Sign for cat.\"\n  },\n  \"cereal\": {\n    name: \"Cereal\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=wPMg%2fK1dYTfR%2bw&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fcereal.gif&ehk=RpDS3wWZM4eryawaxA1wAvWwM0EM%2fdGgJkWY2ce1KFs%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for cereal.\"\n  },\n  \"chair\": {\n    name: \"Chair\",\n    gif: \"https://th.bing.com/th/id/OIP.5kr1MkVLnuN2Z9Jkw-0QpAHaE-?w=237&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for chair.\"\n  },\n  \"cheek\": {\n    name: \"Cheek\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/cheek.gif\",\n    description: \"Sign for cheek.\"\n  },\n  \"child\": {\n    name: \"Child\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/child.gif\",\n    description: \"Sign for child.\"\n  },\n  \"chin\": {\n    name: \"Chin\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/chin.gif\",\n    description: \"Sign for chin.\"\n  },\n  \"chocolate\": {\n    name: \"Chocolate\",\n    gif: \"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fi.pinimg.com%2foriginals%2f9f%2fa2%2fb5%2f9fa2b5064a72b5e46202d20848f1bf21.gif&ehk=izvOlFp25%2fx5NVTCmqVz0UOnZNOWy%2fAJJtzAhkZ8nTg%3d\",\n    description: \"Sign for chocolate.\"\n  },\n  \"clean\": {\n    name: \"Clean\",\n    gif: \"https://media.giphy.com/media/3o7TKoturrdpf5Muwo/giphy.gif\",\n    description: \"Sign for clean.\"\n  },\n  \"close\": {\n    name: \"Close\",\n    gif: \"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia2.giphy.com%2fmedia%2fl4JyZuXNGxS3Yydeo%2fgiphy.gif%3fcid%3d790b7611318eb5b864ad67b3cecb35b9d81240a50d251bb0%26rid%3dgiphy.gif%26ct%3dg&ehk=A6wfp3Afm3rFCPLWSjgQd6JVjmRSBNBlk9vd0jVNgJc%3d\",\n    description: \"Sign for close.\"\n  },\n  \"closet\": {\n    name: \"Closet\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/closet.gif\",\n    description: \"Sign for closet.\"\n  },\n  \"cloud\": {\n    name: \"Cloud\",\n    gif: \"https://th.bing.com/th/id/OIP.hMO89bV2zwVcIVIa7FOT5QHaEc?rs=1&pid=ImgDetMain\",\n    description: \"Sign for cloud.\"\n  },\n  \"clown\": {\n    name: \"Clown\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=OPrV3%2b1Zkelr2A&pid=ImgRaw&r=0\",\n    description: \"Sign for clown.\"\n  },\n  \"cow\": {\n    name: \"Cow\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/cow.gif\",\n    description: \"Sign for cow.\"\n  },\n  \"cowboy\": {\n    name: \"Cowboy\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/cowboy.gif\",\n    description: \"Sign for cowboy.\"\n  },\n  \"cry\": {\n    name: \"Cry\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/c/cry-tears.gif\",\n    description: \"Sign for cry.\"\n  },\n  \"cut\": {\n    name: \"Cut\",\n    gif: \"https://th.bing.com/th/id/OIP.ZtKu3hlJ6pduArqfgEcyUgHaE-?w=248&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for cut.\"\n  },\n  \"cute\": {\n    name: \"Cute\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/cute-sugar.gif\",\n    description: \"Sign for cute.\"\n  },\n  \"dad\": {\n    name: \"Dad\",\n    gif: \"https://media.giphy.com/media/l0MYQcLKwtl5v6H1S/giphy.gif\",\n    description: \"Sign for dad.\"\n  },\n  \"dance\": {\n    name: \"Dance\",\n    gif: \"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia.giphy.com%2fmedia%2f3o7TKMspYQjQTbOz2U%2fgiphy.gif&ehk=h%2bdBHCxuoOT89ovSy5uTk6MCL9acaBEV6ld9lrVDRF4%3d\",\n    description: \"Sign for dance.\"\n  },\n  \"dirty\": {\n    name: \"Dirty\",\n    gif: \"https://th.bing.com/th/id/OIP.wRA7r1OPPUuEoLL4Hds9jAHaHa?rs=1&pid=ImgDetMain\",\n    description: \"Sign for dirty.\"\n  },\n  \"dog\": {\n    name: \"Dog\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=uVshGsOHXgldoQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fd%2fdog1.gif&ehk=Xnfrvg0xwy1u%2fae9vWdKYMT25%2bF6qoteW2FThCbVrOA%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for dog.\"\n  },\n  \"doll\": {\n    name: \"Doll\",\n    gif: \"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fwww.lifeprint.com%2fasl101%2fgifs-animated%2fdoll.gif&ehk=hPI0Fzzl9CGOrgQYS2Z53a5YdYgjxYFeOIGghGAEZYU%3d\",\n    description: \"Sign for doll.\"\n  },\n  \"donkey\": {\n    name: \"Donkey\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/d/donkey-1h.gif\",\n    description: \"Sign for donkey.\"\n  },\n  \"down\": {\n    name: \"Down\",\n    gif: \"https://th.bing.com/th/id/OIP.CZlW6IpZUdgspxVpW6PZ8QHaE-?w=250&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for down.\"\n  },\n  \"drawer\": {\n    name: \"Drawer\",\n    gif: \"https://th.bing.com/th/id/OIP.8yooqOFFixqki7j28PVpYQHaE-?w=234&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for drawer.\"\n  },\n  \"drink\": {\n    name: \"Drink\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/d/drink-c.gif\",\n    description: \"Sign for drink.\"\n  },\n  \"drop\": {\n    name: \"Drop\",\n    gif: \"https://th.bing.com/th/id/OIP.XQJn0tOccOUmG8OZHz8X9gHaE-?w=247&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for drop.\"\n  },\n  \"dry\": {\n    name: \"Dry\",\n    gif: \"https://th.bing.com/th/id/OIP.A0oQgM0IGtwZjfz1Caj-AgHaE-?w=268&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for dry.\"\n  },\n  \"dryer\": {\n    name: \"Dryer\",\n    gif: \"https://lifeprint.com/asl101/gifs/d/dryer.gif\",\n    description: \"Sign for dryer.\"\n  },\n  \"duck\": {\n    name: \"Duck\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=ZetjiJ3WOhOXrQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fd%2fduck.gif&ehk=STeui62x5lieai0VcyeZkX2t8rILR%2f8GR5F3x2xJ5tw%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for duck.\"\n  },\n  \"ear\": {\n    name: \"Ear\",\n    gif: \"https://lifeprint.com/asl101/signjpegs/e/ears.h3.jpg\",\n    description: \"Sign for ear.\"\n  },\n  \"elephant\": {\n    name: \"Elephant\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/elephant.gif\",\n    description: \"Sign for elephant.\"\n  },\n  \"empty\": {\n    name: \"Empty\",\n    gif: \"https://lifeprint.com/images-signs/empty.gif\",\n    description: \"Sign for empty.\"\n  },\n  \"every\": {\n    name: \"Every\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/every.gif\",\n    description: \"Sign for every.\"\n  },\n  \"eye\": {\n    name: \"Eye\",\n    gif: \"https://lifeprint.com/asl101/gifs/e/eyes.gif\",\n    description: \"Sign for eye.\"\n  },\n  \"face\": {\n    name: \"Face\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/face.gif\",\n    description: \"Sign for face.\"\n  },\n  \"fall\": {\n    name: \"Fall\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/fall.gif\",\n    description: \"Sign for fall.\"\n  },\n  \"farm\": {\n    name: \"Farm\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=IO%2brRd7xNmCQBQ&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2ffarm.gif&ehk=aOO01Vk8fbE84nLfNNnOVL3kUdyWJtLaTEcwePgbP9A%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for farm.\"\n  },\n  \"fast\": {\n    name: \"Fast\",\n    gif: \"https://th.bing.com/th/id/OIP.YX_BqT1FjGm8HeM4k4WFAgAAAA?rs=1&pid=ImgDetMain\",\n    description: \"Sign for fast.\"\n  },\n  \"feet\": {\n    name: \"Feet\",\n    gif: \"https://th.bing.com/th/id/OIP.RaYFj5lvSS6NeIna8NtmZQHaE-?w=247&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for feet.\"\n  },\n  \"find\": {\n    name: \"Find\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/f/find-pick.gif\",\n    description: \"Sign for find.\"\n  },\n  \"fine\": {\n    name: \"Fine\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=Qpm%2bw3fHTAWj1A&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2ff%2ffine.gif&ehk=mGMZf4l%2bLZMq4atRomNJSvrSjYgFe%2bRVCm1dYLh5J3I%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for fine.\"\n  },\n  \"finger\": {\n    name: \"Finger\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/finger.gif\",\n    description: \"Sign for finger.\"\n  },\n  \"finish\": {\n    name: \"Finish\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=34j4pW2f3E5TtQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2ff%2ffinish.gif&ehk=xNk24Jbe3t0moSmcmUftmZzCRgHIxsarq3W9E7kGmPM%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for finish.\"\n  },\n  \"fireman\": {\n    name: \"Fireman\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/fireman-c2.gif\",\n    description: \"Sign for fireman.\"\n  },\n  \"first\": {\n    name: \"First\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/first.gif\",\n    description: \"Sign for first.\"\n  },\n  \"fish\": {\n    name: \"Fish\",\n    gif: \"https://th.bing.com/th/id/OIP.Lzhd7lIIa-V4H3faS1d3mQHaHa?rs=1&pid=ImgDetMain\",\n    description: \"Sign for fish.\"\n  },\n  \"flag\": {\n    name: \"Flag\",\n    gif: \"https://th.bing.com/th/id/OIP.3LqQWEnK4TG0lohgQ3G5uAHaE-?w=263&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for flag.\"\n  },\n  \"flower\": {\n    name: \"Flower\",\n    gif: \"https://media.giphy.com/media/3o7TKGkqPpLUdFiFPy/giphy.gif\",\n    description: \"Sign for flower.\"\n  },\n  \"food\": {\n    name: \"Food\",\n    gif: \"https://i.pinimg.com/originals/cc/bb/0c/ccbb0c143db0b51e9947a5966db42fd8.gif\",\n    description: \"Sign for food.\"\n  },\n  \"for\": {\n    name: \"For\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/for.gif\",\n    description: \"Sign for for.\"\n  },\n  \"frenchfries\": {\n    name: \"French Fries\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/f/french-fries.gif\",\n    description: \"Sign for french fries.\"\n  },\n  \"frog\": {\n    name: \"Frog\",\n    gif: \"https://media.giphy.com/media/l0HlKl64lIvTjZ7QA/giphy.gif\",\n    description: \"Sign for frog.\"\n  },\n  \"garbage\": {\n    name: \"Garbage\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=78iU%2fDx85Ut9fA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fg%2fgarbage.gif&ehk=lafY%2f1y5WEEfr04p6Uq4waDP9iV7bJB5r2k3RYGOhWY%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for garbage.\"\n  },\n  \"gift\": {\n    name: \"Gift\",\n    gif: \"https://www.babysignlanguage.com/signs/gift.gif\",\n    description: \"Sign for gift.\"\n  },\n  \"giraffe\": {\n    name: \"Giraffe\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/g/giraffe.gif\",\n    description: \"Sign for giraffe.\"\n  },\n  \"girl\": {\n    name: \"Girl\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=yDsGUPEaDyeSlA&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fgirl.gif&ehk=zdVxVSayRBDn67vVCpMhUH6UmzUQE8vaY7%2bv8jedvs8%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for girl.\"\n  },\n  \"give\": {\n    name: \"Give\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/g/give-x-two-handed.gif\",\n    description: \"Sign for give.\"\n  },\n  \"glasswindow\": {\n    name: \"Glass Window\",\n    gif: \"https://lifeprint.com/asl101/gifs/g/glass.gif\",\n    description: \"Sign for glass window.\"\n  },\n  \"go\": {\n    name: \"Go\",\n    gif: \"https://media.giphy.com/media/l3vRdVMMN9VsW5a0w/giphy.gif\",\n    description: \"Sign for go.\"\n  },\n  \"goose\": {\n    name: \"Goose\",\n    gif: \"https://www.babysignlanguage.com/signs/goose.gif\",\n    description: \"Sign for goose.\"\n  },\n  \"grandma\": {\n    name: \"Grandma\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/g/grandma.gif\",\n    description: \"Sign for grandma.\"\n  },\n  \"grandpa\": {\n    name: \"Grandpa\",\n    gif: \"https://th.bing.com/th/id/OIP.yyLPc-rWg0PMNbrwjeQQngHaE-?w=238&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for grandpa.\"\n  },\n  \"grass\": {\n    name: \"Grass\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=uGZNVzt6tISwHA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs-animated%2fgrass.gif&ehk=VKQd9cvBrglo47EhogWYL9rOiZZsEJ7Yqt%2bgJ8N99yQ%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for grass.\"\n  },\n  \"green\": {\n    name: \"Green\",\n    gif: \"https://i.pinimg.com/originals/cb/7f/75/cb7f757ffb79cb3d1309c9ad785e83a1.gif\",\n    description: \"Sign for green.\"\n  },\n  \"gum\": {\n    name: \"Gum\",\n    gif: \"https://lifeprint.com/asl101/gifs/g/gum.gif\",\n    description: \"Sign for gum.\"\n  },\n  \"hair\": {\n    name: \"Hair\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/h/hair-g-version.gif\",\n    description: \"Sign for hair.\"\n  },\n  \"happy\": {\n    name: \"Happy\",\n    gif: \"https://media0.giphy.com/media/3o7TKFpahYpUp4g0N2/giphy.gif?cid=790b7611bca188a92e2e759876756ef62ba95b7cdd337c77&rid=giphy.gif&ct=g\",\n    description: \"Sign for happy.\"\n  },\n  \"hat\": {\n    name: \"Hat\",\n    gif: \"https://th.bing.com/th/id/OIP.QyFdqn-0ZqUwNfE6jbzKWAHaE-?w=258&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for hat.\"\n  },\n  \"hate\": {\n    name: \"Hate\",\n    gif: \"https://media.giphy.com/media/l0MYPiNw8l2LAPJXW/giphy.gif\",\n    description: \"Sign for hate.\"\n  },\n  \"have\": {\n    name: \"Have\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=q5Ei%2b7oJb7Uzyw&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhave.gif&ehk=H9yIaJxFVejkfHpkhTUipBRv9CW63KBFy6QW5cdbkKw%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for have.\"\n  },\n  \"haveto\": {\n    name: \"Have to\",\n    gif: \"https://lifeprint.com/asl101/gifs/h/have-to.gif\",\n    description: \"Sign for have to.\"\n  },\n  \"head\": {\n    name: \"Head\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=OcbJdRbpEFsWXQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fsignjpegs%2fh%2fhead-1.jpg&ehk=RPBV45fSrLDEWYiZvRuZs2c1JNrL4WzdqLSNMFIF3Rs%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for head.\"\n  },\n  \"hear\": {\n    name: \"Hear\",\n    gif: \"https://www.lifeprint.com/asl101/signjpegs/h/hear.h4.jpg\",\n    description: \"Sign for hear.\"\n  },\n  \"helicopter\": {\n    name: \"Helicopter\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=5uhWxBaByliWA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhelicopter.gif&ehk=mwAyT82RBoeYDe7yaHA1jL3%2f30dUksltmv4dF7YGf%2bU%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for helicopter.\"\n  },\n  \"hello\": {\n    name: \"Hello\",\n    gif: \"https://media.giphy.com/media/3o7TKNKOfKlIhbD3gY/giphy.gif\",\n    description: \"Sign for hello.\"\n  },\n  \"hen\": {\n    name: \"Hen\",\n    gif: \"https://media0.giphy.com/media/26hisADhtILiu1J3W/giphy.gif?cid=790b76112d512b94e1647afb111c8d77f92ae31f37864f2&rid=giphy.gif&ct=g\",\n    description: \"Sign for hen.\"\n  },\n  \"hesheit\": {\n    name: \"He/She/It\",\n    gif: \"https://lifeprint.com/asl101/gifs/h/he-she-it.gif\",\n    description: \"Sign for he/she/it.\"\n  },\n  \"hide\": {\n    name: \"Hide\",\n    gif: \"https://lifeprint.com/asl101/gifs/h/hide.gif\",\n    description: \"Sign for hide.\"\n  },\n  \"high\": {\n    name: \"High\",\n    gif: \"https://lifeprint.com/asl101/gifs/h/high.gif\",\n    description: \"Sign for high.\"\n  },\n  \"home\": {\n    name: \"Home\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=%2bnBd%2foQjxnoPfg&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhome-2.gif&ehk=7yD%2f%2fh6JN1Y4D4BOrUjgKW4Jccy2Y4GVYLf%2fzyk%2b5YY%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for home.\"\n  },\n  \"horse\": {\n    name: \"Horse\",\n    gif: \"https://media.giphy.com/media/l0HlM5HffraiQaHUk/giphy.gif\",\n    description: \"Sign for horse.\"\n  },\n  \"hot\": {\n    name: \"Hot\",\n    gif: \"https://media.giphy.com/media/3o6Zt99k5aDok347bG/giphy.gif\",\n    description: \"Sign for hot.\"\n  },\n  \"hungry\": {\n    name: \"Hungry\",\n    gif: \"https://media.giphy.com/media/l3vR0xkdFEz4tnfTq/giphy.gif\",\n    description: \"Sign for hungry.\"\n  },\n  \"icecream\": {\n    name: \"Ice Cream\",\n    gif: \"https://media.giphy.com/media/3o7TKp6yVibVMhBSLu/giphy.gif\",\n    description: \"Sign for ice cream.\"\n  },\n  \"if\": {\n    name: \"If\",\n    gif: \"https://lifeprint.com/asl101/gifs/i/if.gif\",\n    description: \"Sign for if.\"\n  },\n  \"into\": {\n    name: \"Into\",\n    gif: \"https://lifeprint.com/asl101/gifs/i/into.gif\",\n    description: \"Sign for into.\"\n  },\n  \"jacket\": {\n    name: \"Jacket\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/c/coat.gif\",\n    description: \"Sign for jacket.\"\n  },\n  \"jeans\": {\n    name: \"Jeans\",\n    gif: \"https://lifeprint.com/asl101/gifs/j/jeans.gif\",\n    description: \"Sign for jeans.\"\n  },\n  \"jump\": {\n    name: \"Jump\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/jump.gif\",\n    description: \"Sign for jump.\"\n  },\n  \"kiss\": {\n    name: \"Kiss\",\n    gif: \"https://i.gifer.com/PxGY.gif\",\n    description: \"Sign for kiss.\"\n  },\n  \"kitty\": {\n    name: \"Kitty\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/cat-02.gif\",\n    description: \"Sign for kitty.\"\n  },\n  \"lamp\": {\n    name: \"Lamp\",\n    gif: \"https://lifeprint.com/asl101/gifs/l/lamp.gif\",\n    description: \"Sign for lamp.\"\n  },\n  \"later\": {\n    name: \"Later\",\n    gif: \"https://media3.giphy.com/media/l0MYHTyMzMRcikIxi/giphy.gif?cid=790b761128cd39f9baa06dbeb4e099d13e3516763d5f0952&rid=giphy.gif&ct=g\",\n    description: \"Sign for later.\"\n  },\n  \"like\": {\n    name: \"Like\",\n    gif: \"https://lifeprint.com/asl101/gifs/l/like.gif\",\n    description: \"Sign for like.\"\n  },\n  \"lion\": {\n    name: \"Lion\",\n    gif: \"https://th.bing.com/th/id/OIP.8sDkvbXdMKVmCNlDV79WpAHaE-?w=247&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for lion.\"\n  },\n  \"lips\": {\n    name: \"Lips\",\n    gif: \"https://lifeprint.com/asl101/gifs/l/lips.gif\",\n    description: \"Sign for lips.\"\n  },\n  \"listen\": {\n    name: \"Listen\",\n    gif: \"https://th.bing.com/th/id/OIP.VjsXAad6abRwkCla83kbZQHaEc?w=284&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for listen.\"\n  },\n  \"look\": {\n    name: \"Look\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=pYhzip7LqNs7qw&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fl%2flook-at-1.gif&ehk=rFJ7dBrMGFDK0nHLzrOPAzROVE7yqyDEcb%2btLqKqYOI%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for look.\"\n  },\n  \"loud\": {\n    name: \"Loud\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/loud.gif\",\n    description: \"Sign for loud.\"\n  },\n  \"mad\": {\n    name: \"Mad\",\n    gif: \"https://lifeprint.com/asl101/gifs/m/mad.gif\",\n    description: \"Sign for mad.\"\n  },\n  \"make\": {\n    name: \"Make\",\n    gif: \"https://th.bing.com/th/id/OIP.CPz7T2bH107Tu-DBnHvatAHaEc?w=313&h=188&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for make.\"\n  },\n  \"man\": {\n    name: \"Man\",\n    gif: \"https://lifeprint.com/asl101/gifs/m/man.gif\",\n    description: \"Sign for man.\"\n  },\n  \"many\": {\n    name: \"Many\",\n    gif: \"https://lifeprint.com/asl101/gifs/m/many.gif\",\n    description: \"Sign for many.\"\n  },\n  \"milk\": {\n    name: \"Milk\",\n    gif: \"https://lifeprint.com/asl101/gifs/m/milk.gif\",\n    description: \"Sign for milk.\"\n  },\n  \"minemy\": {\n    name: \"Mine/My\",\n    gif: \"https://th.bing.com/th/id/OIP.VBkNZsR_pK7KUoCNiWYMdgHaEc?w=288&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for mine/my.\"\n  },\n  \"mitten\": {\n    name: \"Mitten\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/mittens.gif\",\n    description: \"Sign for mitten.\"\n  },\n  \"mom\": {\n    name: \"Mom\",\n    gif: \"https://lifeprint.com/asl101/gifs/m/mom.gif\",\n    description: \"Sign for mom.\"\n  },\n  \"moon\": {\n    name: \"Moon\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=XbVhBJtkANrG9g&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fm%2fmoon.gif&ehk=YSDvFeUSTa9X1BEJhDjdnLC4c7zWn8z7Hj%2fMkkLUyFE%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for moon.\"\n  },\n  \"morning\": {\n    name: \"Morning\",\n    gif: \"https://media0.giphy.com/media/3o6ZtrcJ9GCXGGw0ww/source.gif\",\n    description: \"Sign for morning.\"\n  },\n  \"mouse\": {\n    name: \"Mouse\",\n    gif: \"https://lifeprint.com/asl101/gifs/m/mouse.gif\",\n    description: \"Sign for mouse.\"\n  },\n  \"mouth\": {\n    name: \"Mouth\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/mouth.gif\",\n    description: \"Sign for mouth.\"\n  },\n  \"nap\": {\n    name: \"Nap\",\n    gif: \"https://lifeprint.com/asl101/gifs/n/nap.gif\",\n    description: \"Sign for nap.\"\n  },\n  \"napkin\": {\n    name: \"Napkin\",\n    gif: \"https://lifeprint.com/asl101/gifs/n/napkin.gif\",\n    description: \"Sign for napkin.\"\n  },\n  \"night\": {\n    name: \"Night\",\n    gif: \"https://lifeprint.com/asl101/gifs/n/night.gif\",\n    description: \"Sign for night.\"\n  },\n  \"no\": {\n    name: \"No\",\n    gif: \"https://lifeprint.com/asl101/gifs/n/no-2-movement.gif\",\n    description: \"Sign for no.\"\n  },\n  \"noisy\": {\n    name: \"Noisy\",\n    gif: \"https://lifeprint.com/asl101/gifs/n/noisy.gif\",\n    description: \"Sign for noisy.\"\n  },\n  \"nose\": {\n    name: \"Nose\",\n    gif: \"https://lifeprint.com/asl101/signjpegs/n/nose.h1.jpg\",\n    description: \"Sign for nose.\"\n  },\n  \"not\": {\n    name: \"Not\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=6%2bbZ2jRA%2famQ4Q&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fn%2fnot-negative.gif&ehk=%2bppuO9P0%2fpdzrrdNO4FXpxdIGs8jgY%2fj%2b1ZCwdbDWO4%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for not.\"\n  },\n  \"now\": {\n    name: \"Now\",\n    gif: \"https://lifeprint.com/asl101/gifs/n/now.gif\",\n    description: \"Sign for now.\"\n  },\n  \"nuts\": {\n    name: \"Nuts\",\n    gif: \"https://th.bing.com/th/id/OIP.wRnQjn9j2vfFfzAnRR205QHaE-?w=276&h=185&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for nuts.\"\n  },\n  \"old\": {\n    name: \"Old\",\n    gif: \"https://lifeprint.com/asl101/gifs/o/old.gif\",\n    description: \"Sign for old.\"\n  },\n  \"on\": {\n    name: \"On\",\n    gif: \"https://lifeprint.com/asl101/gifs/o/on-onto.gif\",\n    description: \"Sign for on.\"\n  },\n  \"open\": {\n    name: \"Open\",\n    gif: \"https://th.bing.com/th/id/OIP.BeMiGXQFuYk_6ZrgG3iqzQHaE-?w=264&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for open.\"\n  },\n  \"orange\": {\n    name: \"Orange\",\n    gif: \"https://lifeprint.com/asl101/gifs/o/orange.gif\",\n    description: \"Sign for orange.\"\n  },\n  \"outside\": {\n    name: \"Outside\",\n    gif: \"https://lifeprint.com/asl101/gifs/o/outside.gif\",\n    description: \"Sign for outside.\"\n  },\n  \"owie\": {\n    name: \"Owie\",\n    gif: \"https://lifeprint.com/asl101/gifs/o/owie.gif\",\n    description: \"Sign for owie.\"\n  },\n  \"owl\": {\n    name: \"Owl\",\n    gif: \"https://lifeprint.com/asl101/gifs/o/owl.gif\",\n    description: \"Sign for owl.\"\n  },\n  \"pajamas\": {\n    name: \"Pajamas\",\n    gif: \"https://lifeprint.com/asl101/gifs/p/pajamas.gif\",\n    description: \"Sign for pajamas.\"\n  },\n  \"pen\": {\n    name: \"Pen\",\n    gif: \"https://lifeprint.com/asl101/gifs/p/pen.gif\",\n    description: \"Sign for pen.\"\n  },\n  \"pencil\": {\n    name: \"Pencil\",\n    gif: \"https://lifeprint.com/asl101/gifs/p/pencil-2.gif\",\n    description: \"Sign for pencil.\"\n  },\n  \"penny\": {\n    name: \"Penny\",\n    gif: \"https://lifeprint.com/asl101/gifs/p/penny.gif\",\n    description: \"Sign for penny.\"\n  },\n  \"person\": {\n    name: \"Person\",\n    gif: \"https://lifeprint.com/asl101/gifs/p/person.gif\",\n    description: \"Sign for person.\"\n  },\n  \"pig\": {\n    name: \"Pig\",\n    gif: \"https://lifeprint.com/asl101/gifs/p/pig.gif\",\n    description: \"Sign for pig.\"\n  },\n  \"pizza\": {\n    name: \"Pizza\",\n    gif: \"https://lifeprint.com/asl101/gifs/p/pizza.gif\",\n    description: \"Sign for pizza.\"\n  },\n  \"please\": {\n    name: \"Please\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/pleasecloseup.gif\",\n    description: \"Sign for please.\"\n  },\n  \"police\": {\n    name: \"Police\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=icjjfUg15cqgLw&pid=ImgRaw&r=0\",\n    description: \"Sign for police.\"\n  },\n  \"pool\": {\n    name: \"Pool\",\n    gif: \"https://th.bing.com/th/id/OIP.dhcMKyW2psDcA5uwsRaRagHaEc?w=276&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for pool.\"\n  },\n  \"potty\": {\n    name: \"Potty\",\n    gif: \"https://th.bing.com/th/id/OIP.YcNMUjCg6f95xdgN5rnenwHaE-?w=264&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for potty.\"\n  },\n  \"pretend\": {\n    name: \"Pretend\",\n    gif: \"https://lifeprint.com/asl101/gifs/p/pretend.gif\",\n    description: \"Sign for pretend.\"\n  },\n  \"pretty\": {\n    name: \"Pretty\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/beautiful.gif\",\n    description: \"Sign for pretty.\"\n  },\n  \"puppy\": {\n    name: \"Puppy\",\n    gif: \"https://lifeprint.com/asl101/gifs/p/puppy.gif\",\n    description: \"Sign for puppy.\"\n  },\n  \"puzzle\": {\n    name: \"Puzzle\",\n    gif: \"https://res.cloudinary.com/spiralyze/image/upload/f_auto,w_auto/BabySignLanguage/DictionaryPages/puzzle.svg\",\n    description: \"Sign for puzzle.\"\n  },\n  \"quiet\": {\n    name: \"Quiet\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/quiet-03.gif\",\n    description: \"Sign for quiet.\"\n  },\n  \"radio\": {\n    name: \"Radio\",\n    gif: \"https://i.pinimg.com/originals/6d/5e/5e/6d5e5e2f78f80e9006293df853a2ba3b.gif\",\n    description: \"Sign for radio.\"\n  },\n  \"rain\": {\n    name: \"Rain\",\n    gif: \"https://lifeprint.com/asl101/gifs/r/rain.gif\",\n    description: \"Sign for rain.\"\n  },\n  \"read\": {\n    name: \"Read\",\n    gif: \"https://lifeprint.com/asl101/gifs/r/read.gif\",\n    description: \"Sign for read.\"\n  },\n  \"red\": {\n    name: \"Red\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/red.gif\",\n    description: \"Sign for red.\"\n  },\n  \"refrigerator\": {\n    name: \"Refrigerator\",\n    gif: \"https://lifeprint.com/asl101/gifs/r/refrigerator-r-e-f.gif\",\n    description: \"Sign for refrigerator.\"\n  },\n  \"ride\": {\n    name: \"Ride\",\n    gif: \"https://lifeprint.com/asl101/gifs/r/ride.gif\",\n    description: \"Sign for ride.\"\n  },\n  \"room\": {\n    name: \"Room\",\n    gif: \"https://lifeprint.com/asl101/gifs/r/room-box.gif\",\n    description: \"Sign for room.\"\n  },\n  \"sad\": {\n    name: \"Sad\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/sad.gif\",\n    description: \"Sign for sad.\"\n  },\n  \"same\": {\n    name: \"Same\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/same-similar.gif\",\n    description: \"Sign for same.\"\n  },\n  \"say\": {\n    name: \"Say\",\n    gif: \"https://asl.signlanguage.io/words/say/say-in-asl-a0a5e00000a44k0.jpg\",\n    description: \"Sign for say.\"\n  },\n  \"scissors\": {\n    name: \"Scissors\",\n    gif: \"https://i.makeagif.com/media/4-17-2023/pl4M4F.gif\",\n    description: \"Sign for scissors.\"\n  },\n  \"see\": {\n    name: \"See\",\n    gif: \"https://lifeprint.com/asl101/gifs/l/look-at-2.gif\",\n    description: \"Sign for see.\"\n  },\n  \"shhh\": {\n    name: \"Shhh\",\n    gif: \"https://lifeprint.com/asl101/signjpegs/s/shhh.jpg\",\n    description: \"Sign for shhh.\"\n  },\n  \"shirt\": {\n    name: \"Shirt\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/shirt-volunteer.gif\",\n    description: \"Sign for shirt.\"\n  },\n  \"shoe\": {\n    name: \"Shoe\",\n    gif: \"https://media.giphy.com/media/3o7TKC4StpZKa6d2y4/giphy.gif\",\n    description: \"Sign for shoe.\"\n  },\n  \"shower\": {\n    name: \"Shower\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/shower.gif\",\n    description: \"Sign for shower.\"\n  },\n  \"sick\": {\n    name: \"Sick\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/sick.gif\",\n    description: \"Sign for sick.\"\n  },\n  \"sleep\": {\n    name: \"Sleep\",\n    gif: \"https://media4.giphy.com/media/3o7TKnRuBdakLslcaI/200.gif?cid=790b76110d8f185a9713f36dd65a0df801576e01b403c95c&rid=200.gif&ct=g\",\n    description: \"Sign for sleep.\"\n  },\n  \"sleepy\": {\n    name: \"Sleepy\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=zdWvzvABcDHTdw&riu=http%3a%2f%2fwww.lifeprint.com%2fasl101%2fgifs-animated%2fsleepy.gif&ehk=zLqDFJMAs2nqG02RbbR6mEMvux4h85JGzls4uwgrePQ%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for sleepy.\"\n  },\n  \"smile\": {\n    name: \"Smile\",\n    gif: \"https://th.bing.com/th/id/OIP.dpce-bMAh-1jorUrPQFW4AHaE-?w=263&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for smile.\"\n  },\n  \"snack\": {\n    name: \"Snack\",\n    gif: \"https://media.giphy.com/media/26ybw1E1GTKzLuKDS/giphy.gif\",\n    description: \"Sign for snack.\"\n  },\n  \"snow\": {\n    name: \"Snow\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/snow.gif\",\n    description: \"Sign for snow.\"\n  },\n  \"stairs\": {\n    name: \"Stairs\",\n    gif: \"https://th.bing.com/th/id/OIP.8BtYhPXXDQHRqodMyyy3HgHaEc?w=288&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for stairs.\"\n  },\n  \"stay\": {\n    name: \"Stay\",\n    gif: \"https://i.pinimg.com/originals/f5/29/8e/f5298eaa46b91cd6de2a32bd76aadffc.gif\",\n    description: \"Sign for stay.\"\n  },\n  \"sticky\": {\n    name: \"Sticky\",\n    gif: \"https://th.bing.com/th/id/OIP.fffIgrX_DBAjxGMkskvTvQHaE-?w=240&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for sticky.\"\n  },\n  \"store\": {\n    name: \"Store\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=x7oUPJGckc7QDg&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fstore.gif&ehk=P7beooAyFUst%2bbVtqIqINeQGP0%2bIUlNSPXc1Du5zWfQ%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for store.\"\n  },\n  \"story\": {\n    name: \"Story\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/story.gif\",\n    description: \"Sign for story.\"\n  },\n  \"stuck\": {\n    name: \"Stuck\",\n    gif: \"https://lifeprint.com/asl101/signjpegs/s/stuck.2.jpg\",\n    description: \"Sign for stuck.\"\n  },\n  \"sun\": {\n    name: \"Sun\",\n    gif: \"https://media.giphy.com/media/3o6Zt7merN2zxEtNRK/giphy.gif\",\n    description: \"Sign for sun.\"\n  },\n  \"table\": {\n    name: \"Table\",\n    gif: \"https://lifeprint.com/asl101/gifs/t/table.gif\",\n    description: \"Sign for table.\"\n  },\n  \"talk\": {\n    name: \"Talk\",\n    gif: \"https://lifeprint.com/asl101/gifs/t/talk.gif\",\n    description: \"Sign for talk.\"\n  },\n  \"taste\": {\n    name: \"Taste\",\n    gif: \"https://lifeprint.com/asl101/gifs/t/taste.gif\",\n    description: \"Sign for taste.\"\n  },\n  \"thankyou\": {\n    name: \"Thank You\",\n    gif: \"https://lifeprint.com/asl101/gifs/t/thank-you.gif\",\n    description: \"Sign for thank you.\"\n  },\n  \"that\": {\n    name: \"That\",\n    gif: \"https://i.ytimg.com/vi/81Wr75AFDnQ/maxresdefault.jpg\",\n    description: \"Sign for that.\"\n  },\n  \"there\": {\n    name: \"There\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/there.gif\",\n    description: \"Sign for there.\"\n  },\n  \"think\": {\n    name: \"Think\",\n    gif: \"https://lifeprint.com/asl101/gifs/t/think.gif\",\n    description: \"Sign for think.\"\n  },\n  \"thirsty\": {\n    name: \"Thirsty\",\n    gif: \"https://media.giphy.com/media/l3vR0sYheBulL1P7W/giphy.gif\",\n    description: \"Sign for thirsty.\"\n  },\n  \"tiger\": {\n    name: \"Tiger\",\n    gif: \"https://lifeprint.com/asl101/gifs/t/tiger.gif\",\n    description: \"Sign for tiger.\"\n  },\n  \"time\": {\n    name: \"Time\",\n    gif: \"https://lifeprint.com/asl101/gifs/t/time-1.gif\",\n    description: \"Sign for time.\"\n  },\n  \"tomorrow\": {\n    name: \"Tomorrow\",\n    gif: \"https://lifeprint.com/asl101/gifs/t/tomorrow.gif\",\n    description: \"Sign for tomorrow.\"\n  },\n  \"tongue\": {\n    name: \"Tongue\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=ZJJ2Ixdj0l0b5A&riu=http%3a%2f%2fwww.aslsearch.com%2fsigns%2fimages%2ftongue.jpg&ehk=MxZVUjfqPa3klIauPGpReg%2fYgnJUyIjlxOOvCYYG0hc%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for tongue.\"\n  },\n  \"tooth\": {\n    name: \"Tooth\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=ZF%2fsFUXvt5czGA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fsignjpegs%2ft%2fteeth1.jpg&ehk=vI5eDlD4HZWXhK1PQOQz4nA5e6oguHgeXqDo%2fcdcWg4%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for tooth.\"\n  },\n  \"toothbrush\": {\n    name: \"Toothbrush\",\n    gif: \"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia.giphy.com%2fmedia%2fl3vR0Rq2HVL2KHLUI%2fgiphy.gif&ehk=eC0Sq9sHjrrOrkyJvOogQbXVkTOL5OPCeyVymejL0RU%3d\",\n    description: \"Sign for toothbrush.\"\n  },\n  \"touch\": {\n    name: \"Touch\",\n    gif: \"https://th.bing.com/th/id/OIP.imGRfqjCtcHhof6Lc_0QJQHaE-?w=230&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Sign for touch.\"\n  },\n  \"toy\": {\n    name: \"Toy\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/play-02.gif\",\n    description: \"Sign for toy.\"\n  },\n  \"tree\": {\n    name: \"Tree\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/tree.gif\",\n    description: \"Sign for tree.\"\n  },\n  \"uncle\": {\n    name: \"Uncle\",\n    gif: \"https://lifeprint.com/asl101/gifs/u/uncle.gif\",\n    description: \"Sign for uncle.\"\n  },\n  \"underwear\": {\n    name: \"Underwear\",\n    gif: \"https://th.bing.com/th/id/OIP.c8g9T_lOhbZWRvKAA12J8wHaEO?pid=ImgDet&w=310&h=177&rs=1\",\n    description: \"Sign for underwear.\"\n  },\n  \"up\": {\n    name: \"Up\",\n    gif: \"https://www.babysignlanguage.com/signs/up.gif\",\n    description: \"Sign for up.\"\n  },\n  \"vacuum\": {\n    name: \"Vacuum\",\n    gif: \"https://www.babysignlanguage.com/signs/vacuum.gif\",\n    description: \"Sign for vacuum.\"\n  },\n  \"wait\": {\n    name: \"Wait\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/wait.gif\",\n    description: \"Sign for wait.\"\n  },\n  \"wake\": {\n    name: \"Wake\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/wake-up.gif\",\n    description: \"Sign for wake.\"\n  },\n  \"water\": {\n    name: \"Water\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/water-2.gif\",\n    description: \"Sign for water.\"\n  },\n  \"wet\": {\n    name: \"Wet\",\n    gif: \"https://www.babysignlanguage.com/signs/wet.gif\",\n    description: \"Sign for wet.\"\n  },\n  \"weus\": {\n    name: \"We/Us\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/we-us.gif\",\n    description: \"Sign for we/us.\"\n  },\n  \"where\": {\n    name: \"Where\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/where.gif\",\n    description: \"Sign for where.\"\n  },\n  \"white\": {\n    name: \"White\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/white.gif\",\n    description: \"Sign for white.\"\n  },\n  \"who\": {\n    name: \"Who\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/who.gif\",\n    description: \"Sign for who.\"\n  },\n  \"why\": {\n    name: \"Why\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/why.gif\",\n    description: \"Sign for why.\"\n  },\n  \"will\": {\n    name: \"Will\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/future.gif\",\n    description: \"Sign for will.\"\n  },\n  \"wolf\": {\n    name: \"Wolf\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/wolf-side-view.gif\",\n    description: \"Sign for wolf.\"\n  },\n  \"yellow\": {\n    name: \"Yellow\",\n    gif: \"https://lifeprint.com/asl101/gifs/y/yellow.gif\",\n    description: \"Sign for yellow.\"\n  },\n  \"yes\": {\n    name: \"Yes\",\n    gif: \"https://media.tenor.com/oYIirlyIih0AAAAC/yes-asl.gif\",\n    description: \"Sign for yes.\"\n  },\n  \"yesterday\": {\n    name: \"Yesterday\",\n    gif: \"https://lifeprint.com/asl101/gifs/y/yesterday.gif\",\n    description: \"Sign for yesterday.\"\n  },\n  \"yourself\": {\n    name: \"Yourself\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/s/self-myself.gif\",\n    description: \"Sign for yourself.\"\n  },\n  \"yucky\": {\n    name: \"Yucky\",\n    gif: \"https://i.pinimg.com/originals/7f/66/7f/7f667f7eeb92c994829dcaf52c5bcf2d.gif\",\n    description: \"Sign for yucky.\"\n  },\n  \"zebra\": {\n    name: \"Zebra\",\n    gif: \"https://lifeprint.com/asl101/gifs/z/zebra-stripes-two-hands.gif\",\n    description: \"Sign for zebra.\"\n  },\n  \"zipper\": {\n    name: \"Zipper\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=qPRTVGd2SzUBxw&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fzipper.gif&ehk=IGx68sSokNwU21zu3Z2D%2blmeehKYxpSNhX2VnrvQqYE%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Sign for zipper.\"\n  }\n};\nconst TrainingPage = ({\n  onBackToHome\n}) => {\n  _s();\n  // New flash card system state\n  const [currentView, setCurrentView] = useState('levels'); // 'levels' or 'training'\n  const [selectedLevel, setSelectedLevel] = useState(null);\n  const [userProgress, setUserProgress] = useState(() => {\n    // Load progress from localStorage\n    const saved = localStorage.getItem('asl-training-progress');\n    return saved ? JSON.parse(saved) : {};\n  });\n\n  // Legacy state (keeping for backward compatibility during transition)\n  const [currentSign, setCurrentSign] = useState('hello');\n  const [status, setStatus] = useState('');\n  const [isCapturing, setIsCapturing] = useState(false);\n  const [lastRecordingStatus, setLastRecordingStatus] = useState('');\n  // eslint-disable-next-line no-unused-vars\n  const [recordedVideos, setRecordedVideos] = useState([]);\n  const [imgError, setImgError] = useState(false);\n  const webcamRef = useRef(null);\n  const autoRecordTimeoutRef = useRef(null);\n  const matchCountRef = useRef(0);\n\n  // Use sign detection hook\n  const {\n    isConnected,\n    prediction,\n    isAIRecording,\n    recordingStatus,\n    signMatched,\n    targetSign,\n    startRecording: startAIRecording,\n    stopRecording: stopAIRecording,\n    startFrameCapture,\n    retryConnection\n  } = useSignDetection();\n  const handleSignChange = useCallback(event => {\n    setCurrentSign(event.target.value);\n    setImgError(false);\n  }, []);\n  const startDetection = useCallback(() => {\n    if (!webcamRef.current) {\n      setStatus('Camera not available');\n      return;\n    }\n    setIsCapturing(true);\n    startFrameCapture(webcamRef, 100); // Send frame every 100ms\n    setStatus('AI detection started');\n  }, [startFrameCapture]);\n  const startManualRecording = useCallback(() => {\n    if (!isConnected) {\n      setStatus('AI backend not connected');\n      return;\n    }\n    if (!webcamRef.current) {\n      setStatus('Camera not available');\n      return;\n    }\n    if (isAIRecording) {\n      setStatus('Already recording...');\n      return;\n    }\n\n    // Start manual 3-second recording with selected sign name\n    const selectedSignName = signLanguageData[currentSign].name;\n    setStatus(`🎬 Starting 3-second recording for \"${selectedSignName}\"...`);\n    setLastRecordingStatus(`🎬 Recording \"${selectedSignName}\"...`);\n    startAIRecording(selectedSignName, true); // Pass true to immediately start recording session\n\n    // Auto-stop after 3 seconds\n    autoRecordTimeoutRef.current = setTimeout(() => {\n      stopAIRecording();\n      setStatus(`✅ Recording complete! \"${selectedSignName}\" saved to recordings folder with landmark data`);\n      setLastRecordingStatus(`✅ Recording saved: \"${selectedSignName}\" (3 seconds)`);\n    }, 3000);\n\n    // Also start frame capture if not already started\n    if (!isCapturing) {\n      startDetection();\n    }\n  }, [currentSign, isConnected, isCapturing, startDetection, isAIRecording, startAIRecording, stopAIRecording]);\n  const stopManualRecording = useCallback(() => {\n    // Stop current recording\n    if (isAIRecording) {\n      stopAIRecording();\n      setLastRecordingStatus(`✅ Recording saved: \"${signLanguageData[currentSign].name}\" (Manual stop)`);\n    }\n    matchCountRef.current = 0;\n    if (autoRecordTimeoutRef.current) {\n      clearTimeout(autoRecordTimeoutRef.current);\n    }\n    setStatus('Manual recording stopped');\n  }, [stopAIRecording, isAIRecording, currentSign]);\n  const downloadRecording = video => {\n    const a = document.createElement('a');\n    a.href = video.url;\n    a.download = `sign_${video.sign}_${video.timestamp}.webm`;\n    a.click();\n  };\n\n  // Auto-start detection when connected\n  useEffect(() => {\n    if (isConnected && webcamRef.current && !isCapturing) {\n      startDetection();\n    }\n  }, [isConnected, startDetection, isCapturing]);\n\n  // Update last recording status when recordingStatus changes\n  useEffect(() => {\n    if (recordingStatus && recordingStatus.includes('saved')) {\n      setLastRecordingStatus(recordingStatus);\n    }\n  }, [recordingStatus]);\n\n  // Always-on auto-recording logic - records when confidence >= 50%\n  useEffect(() => {\n    if (!prediction || !isConnected) {\n      matchCountRef.current = 0;\n      return;\n    }\n    const predictedSign = prediction.sign.toLowerCase();\n    const targetSignLower = signLanguageData[currentSign].name.toLowerCase();\n    const confidence = prediction.confidence;\n\n    // Auto-record when sign matches with >= 50% confidence\n    if (predictedSign === targetSignLower && confidence >= 0.5) {\n      matchCountRef.current += 1;\n\n      // Start recording after 2 consecutive matches to avoid false positives\n      if (matchCountRef.current >= 2 && !isAIRecording) {\n        setStatus(`🎬 Auto-recording \"${signLanguageData[currentSign].name}\"... (${Math.round(confidence * 100)}% confidence)`);\n        startAIRecording(signLanguageData[currentSign].name, false); // Auto-recording doesn't start session immediately\n\n        // Auto-stop recording after 3 seconds\n        autoRecordTimeoutRef.current = setTimeout(() => {\n          stopAIRecording();\n          setStatus(`✅ Auto-recording complete! \"${signLanguageData[currentSign].name}\" saved to recordings folder with landmark data`);\n          setLastRecordingStatus(`✅ Auto-recording saved: \"${signLanguageData[currentSign].name}\" (3 seconds)`);\n          matchCountRef.current = 0;\n        }, 3000);\n      }\n    } else {\n      // Reset match count if sign doesn't match or confidence is too low\n      matchCountRef.current = 0;\n    }\n    return () => {\n      if (autoRecordTimeoutRef.current) {\n        clearTimeout(autoRecordTimeoutRef.current);\n      }\n    };\n  }, [prediction, currentSign, isAIRecording, startAIRecording, stopAIRecording, isConnected]);\n\n  // New flash card system handlers\n  const handleLevelSelect = level => {\n    setSelectedLevel(level);\n    setCurrentView('training');\n  };\n  const handleBackToLevels = () => {\n    setCurrentView('levels');\n    setSelectedLevel(null);\n  };\n  const handleProgressUpdate = (level, completed, total) => {\n    const newProgress = {\n      ...userProgress,\n      [level]: {\n        completed,\n        total\n      }\n    };\n    setUserProgress(newProgress);\n    // Save to localStorage\n    localStorage.setItem('asl-training-progress', JSON.stringify(newProgress));\n  };\n\n  // Render new flash card system\n  if (currentView === 'levels') {\n    return /*#__PURE__*/_jsxDEV(LevelSelector, {\n      currentLevel: selectedLevel,\n      userProgress: userProgress,\n      onLevelSelect: handleLevelSelect\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1187,\n      columnNumber: 7\n    }, this);\n  }\n  if (currentView === 'training' && selectedLevel) {\n    return /*#__PURE__*/_jsxDEV(FlashCardTraining, {\n      level: selectedLevel,\n      onBack: handleBackToLevels,\n      userProgress: userProgress,\n      onProgressUpdate: handleProgressUpdate\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1197,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Legacy training page (fallback - can be removed later)\n  return /*#__PURE__*/_jsxDEV(TrainingContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Navigation, {\n      children: /*#__PURE__*/_jsxDEV(NavContainer, {\n        children: [/*#__PURE__*/_jsxDEV(Logo, {\n          children: [/*#__PURE__*/_jsxDEV(LogoIcon, {\n            children: /*#__PURE__*/_jsxDEV(Brain, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1213,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1212,\n            columnNumber: 13\n          }, this), \"ASL Neural\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(BackButton, {\n          onClick: onBackToHome,\n          children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1218,\n            columnNumber: 13\n          }, this), \"Back to Home\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1217,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1210,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1209,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginBottom: 'var(--space-12)'\n        },\n        children: /*#__PURE__*/_jsxDEV(StatusBadge, {\n          children: [/*#__PURE__*/_jsxDEV(Eye, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1227,\n            columnNumber: 13\n          }, this), \"Neural Vision Active\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1226,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1225,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PageTitle, {\n        children: \"AI Training Session\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1232,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PageSubtitle, {\n        children: \"Experience real-time neural network analysis as our AI learns from your sign language practice\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1233,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TopControlsSection, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 'var(--space-4)',\n            flexWrap: 'wrap',\n            justifyContent: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ControlButton, {\n            variant: \"primary\",\n            compact: true,\n            onClick: isAIRecording ? stopManualRecording : startManualRecording,\n            children: isAIRecording ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Square, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1246,\n                columnNumber: 19\n              }, this), \"Stop Recording\"]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Play, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1251,\n                columnNumber: 19\n              }, this), \"Record 3s Video\"]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1239,\n            columnNumber: 13\n          }, this), lastRecordingStatus && /*#__PURE__*/_jsxDEV(RecordingStatus, {\n            isRecording: isAIRecording,\n            children: lastRecordingStatus\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1258,\n            columnNumber: 15\n          }, this), !isConnected && /*#__PURE__*/_jsxDEV(ControlButton, {\n            variant: \"retry\",\n            compact: true,\n            onClick: retryConnection,\n            children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1269,\n              columnNumber: 17\n            }, this), \"Retry Connection\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1264,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1238,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1237,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TrainingGrid, {\n        children: [/*#__PURE__*/_jsxDEV(SignSection, {\n          children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n            children: [/*#__PURE__*/_jsxDEV(SectionIcon, {\n              children: /*#__PURE__*/_jsxDEV(Target, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1280,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1279,\n              columnNumber: 15\n            }, this), \"Select a Sign\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1278,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SignSelector, {\n            value: currentSign,\n            onChange: handleSignChange,\n            disabled: isAIRecording,\n            children: Object.keys(signLanguageData).map(signKey => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: signKey,\n              children: signLanguageData[signKey].name\n            }, signKey, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1290,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1284,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SignDisplay, {\n            children: !imgError ? /*#__PURE__*/_jsxDEV(\"img\", {\n              src: signLanguageData[currentSign].gif,\n              alt: signLanguageData[currentSign].name,\n              onError: () => setImgError(true),\n              style: {\n                display: imgError ? 'none' : 'block'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1297,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                fontSize: '3rem',\n                width: '100%',\n                height: '100%',\n                alignItems: 'center',\n                justifyContent: 'center'\n              },\n              children: \"\\uD83D\\uDCF7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1304,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1295,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SignName, {\n            children: signLanguageData[currentSign].name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1309,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SignDescription, {\n            children: signLanguageData[currentSign].description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1310,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1277,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CameraSection, {\n          children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n            children: [/*#__PURE__*/_jsxDEV(SectionIcon, {\n              children: /*#__PURE__*/_jsxDEV(Camera, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1318,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1317,\n              columnNumber: 15\n            }, this), \"Neural Vision Feed\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1316,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ConnectionStatus, {\n            connected: isConnected,\n            children: [isConnected ? /*#__PURE__*/_jsxDEV(Wifi, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1324,\n              columnNumber: 30\n            }, this) : /*#__PURE__*/_jsxDEV(WifiOff, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1324,\n              columnNumber: 51\n            }, this), isConnected ? 'AI Connected' : 'AI Disconnected']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1323,\n            columnNumber: 13\n          }, this), prediction && /*#__PURE__*/_jsxDEV(PredictionDisplay, {\n            matched: signMatched,\n            isStale: prediction.isStale,\n            children: [/*#__PURE__*/_jsxDEV(PredictionText, {\n              matched: signMatched,\n              isStale: prediction.isStale,\n              children: [\"Detected: \", prediction.sign, prediction.isStale && ' (previous)']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1330,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ConfidenceBar, {\n              children: /*#__PURE__*/_jsxDEV(ConfidenceFill, {\n                confidence: prediction.confidence\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1335,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1334,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                marginTop: '8px',\n                color: 'var(--text-secondary)'\n              },\n              children: [\"Confidence: \", Math.round(prediction.confidence * 100), \"%\", signMatched && targetSign && /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: 'var(--success-600)',\n                  marginLeft: '8px'\n                },\n                children: \"\\u2713 Match! Recording...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1340,\n                columnNumber: 21\n              }, this), !isAIRecording && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: 'var(--primary-600)',\n                  marginTop: '4px'\n                },\n                children: [\"\\uD83C\\uDFAF Auto-recording active: Perform \\\"\", signLanguageData[currentSign].name, \"\\\" sign (\\u226550% confidence)\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1347,\n                  columnNumber: 23\n                }, this), \"\\uD83D\\uDCA1 Or click \\\"Record 3 Seconds\\\" for manual recording\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1345,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1337,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1329,\n            columnNumber: 15\n          }, this), !prediction && /*#__PURE__*/_jsxDEV(PredictionDisplay, {\n            children: [/*#__PURE__*/_jsxDEV(PredictionText, {\n              children: [\"\\uD83C\\uDFAF Ready to detect \\\"\", signLanguageData[currentSign].name, \"\\\"\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1357,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                color: 'var(--text-secondary)'\n              },\n              children: \"Auto-recording is active. Perform the sign with \\u226550% confidence to trigger recording.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1360,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1356,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(WebcamContainer, {\n            children: [/*#__PURE__*/_jsxDEV(StyledWebcam, {\n              ref: webcamRef,\n              audio: false,\n              screenshotFormat: \"image/jpeg\",\n              videoConstraints: {\n                width: 640,\n                height: 480,\n                facingMode: \"user\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1366,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(RecordingOverlay, {\n              isRecording: isAIRecording,\n              children: isAIRecording ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '8px',\n                    height: '8px',\n                    borderRadius: '50%',\n                    backgroundColor: 'white',\n                    marginRight: '4px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1379,\n                  columnNumber: 21\n                }, this), \"Recording\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Eye, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1390,\n                  columnNumber: 21\n                }, this), \"Ready\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1376,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1365,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1315,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1276,\n        columnNumber: 9\n      }, this), (status || recordingStatus) && /*#__PURE__*/_jsxDEV(StatusMessage, {\n        type: (status || recordingStatus).includes('error') ? 'error' : (status || recordingStatus).includes('success') ? 'success' : 'info',\n        children: recordingStatus || status\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1400,\n        columnNumber: 11\n      }, this), recordedVideos.length > 0 && /*#__PURE__*/_jsxDEV(RecordingsSection, {\n        children: [/*#__PURE__*/_jsxDEV(RecordingsTitle, {\n          children: \"Your Practice Recordings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1407,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(RecordingsGrid, {\n          children: recordedVideos.map(video => /*#__PURE__*/_jsxDEV(RecordingCard, {\n            children: [/*#__PURE__*/_jsxDEV(RecordingTitle, {\n              children: video.sign\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1411,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(RecordingTime, {\n              children: new Date(video.timestamp).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1412,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(DownloadButton, {\n              onClick: () => downloadRecording(video),\n              children: [/*#__PURE__*/_jsxDEV(Download, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1416,\n                columnNumber: 21\n              }, this), \"Download\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1415,\n              columnNumber: 19\n            }, this)]\n          }, video.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1410,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1408,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1406,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1224,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1208,\n    columnNumber: 5\n  }, this);\n};\n_s(TrainingPage, \"QdFnCADRDLsIKLNo2uS4/Uww2u8=\", false, function () {\n  return [useSignDetection];\n});\n_c37 = TrainingPage;\nexport default TrainingPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32, _c33, _c34, _c35, _c36, _c37;\n$RefreshReg$(_c, \"TrainingContainer\");\n$RefreshReg$(_c2, \"Navigation\");\n$RefreshReg$(_c3, \"NavContainer\");\n$RefreshReg$(_c4, \"Logo\");\n$RefreshReg$(_c5, \"LogoIcon\");\n$RefreshReg$(_c6, \"BackButton\");\n$RefreshReg$(_c7, \"PageTitle\");\n$RefreshReg$(_c8, \"PageSubtitle\");\n$RefreshReg$(_c9, \"StatusBadge\");\n$RefreshReg$(_c0, \"MainContent\");\n$RefreshReg$(_c1, \"TrainingGrid\");\n$RefreshReg$(_c10, \"CameraSection\");\n$RefreshReg$(_c11, \"SectionTitle\");\n$RefreshReg$(_c12, \"SectionIcon\");\n$RefreshReg$(_c13, \"WebcamContainer\");\n$RefreshReg$(_c14, \"StyledWebcam\");\n$RefreshReg$(_c15, \"RecordingOverlay\");\n$RefreshReg$(_c16, \"SignSection\");\n$RefreshReg$(_c17, \"SignSelector\");\n$RefreshReg$(_c18, \"SignDisplay\");\n$RefreshReg$(_c19, \"SignName\");\n$RefreshReg$(_c20, \"SignDescription\");\n$RefreshReg$(_c21, \"TopControlsSection\");\n$RefreshReg$(_c22, \"RecordingStatus\");\n$RefreshReg$(_c23, \"ControlButton\");\n$RefreshReg$(_c24, \"StatusMessage\");\n$RefreshReg$(_c25, \"RecordingsSection\");\n$RefreshReg$(_c26, \"RecordingsTitle\");\n$RefreshReg$(_c27, \"RecordingsGrid\");\n$RefreshReg$(_c28, \"RecordingCard\");\n$RefreshReg$(_c29, \"RecordingTitle\");\n$RefreshReg$(_c30, \"RecordingTime\");\n$RefreshReg$(_c31, \"DownloadButton\");\n$RefreshReg$(_c32, \"PredictionDisplay\");\n$RefreshReg$(_c33, \"PredictionText\");\n$RefreshReg$(_c34, \"ConfidenceBar\");\n$RefreshReg$(_c35, \"ConfidenceFill\");\n$RefreshReg$(_c36, \"ConnectionStatus\");\n$RefreshReg$(_c37, \"TrainingPage\");", "map": {"version": 3, "names": ["useState", "useRef", "useCallback", "useEffect", "styled", "Webcam", "Brain", "Camera", "ArrowLeft", "Play", "Square", "Download", "Eye", "Target", "Wifi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RefreshCw", "useSignDetection", "LevelSelector", "FlashCardTraining", "config", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TrainingContainer", "div", "_c", "Navigation", "nav", "_c2", "NavContainer", "_c3", "Logo", "_c4", "LogoIcon", "_c5", "BackButton", "button", "_c6", "Page<PERSON><PERSON>le", "h1", "_c7", "PageSubtitle", "p", "_c8", "StatusBadge", "_c9", "MainContent", "main", "_c0", "TrainingGrid", "_c1", "CameraSection", "_c10", "SectionTitle", "h2", "_c11", "SectionIcon", "_c12", "WebcamContainer", "_c13", "StyledWebcam", "_c14", "RecordingOverlay", "props", "isRecording", "_c15", "SignSection", "_c16", "SignSelector", "select", "_c17", "SignDisplay", "_c18", "SignName", "h3", "_c19", "SignDescription", "_c20", "TopControlsSection", "_c21", "RecordingStatus", "_c22", "ControlButton", "variant", "compact", "_c23", "StatusMessage", "type", "_c24", "RecordingsSection", "_c25", "RecordingsTitle", "_c26", "RecordingsGrid", "_c27", "RecordingCard", "_c28", "RecordingTitle", "_c29", "RecordingTime", "_c30", "DownloadButton", "_c31", "PredictionDisplay", "matched", "isStale", "_c32", "PredictionText", "_c33", "ConfidenceBar", "_c34", "ConfidenceFill", "confidence", "_c35", "ConnectionStatus", "connected", "_c36", "signLanguageData", "name", "gif", "description", "TrainingPage", "onBackToHome", "_s", "current<PERSON>iew", "set<PERSON><PERSON><PERSON>View", "selectedLevel", "setSelectedLevel", "userProgress", "setUserProgress", "saved", "localStorage", "getItem", "JSON", "parse", "currentSign", "setCurrentSign", "status", "setStatus", "isCapturing", "setIsCapturing", "lastRecordingStatus", "setLastRecordingStatus", "recordedVideos", "setRecordedVideos", "imgError", "setImgError", "webcamRef", "autoRecordTimeoutRef", "matchCountRef", "isConnected", "prediction", "isAIRecording", "recordingStatus", "signMatched", "targetSign", "startRecording", "startAIRecording", "stopRecording", "stopAIRecording", "startFrameCapture", "retryConnection", "handleSignChange", "event", "target", "value", "startDetection", "current", "startManualRecording", "selectedSignName", "setTimeout", "stopManualRecording", "clearTimeout", "downloadRecording", "video", "a", "document", "createElement", "href", "url", "download", "sign", "timestamp", "click", "includes", "predictedSign", "toLowerCase", "targetSignLower", "Math", "round", "handleLevelSelect", "level", "handleBackToLevels", "handleProgressUpdate", "completed", "total", "newProgress", "setItem", "stringify", "currentLevel", "onLevelSelect", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onBack", "onProgressUpdate", "children", "size", "onClick", "style", "textAlign", "marginBottom", "display", "alignItems", "gap", "flexWrap", "justifyContent", "onChange", "disabled", "Object", "keys", "map", "sign<PERSON><PERSON>", "src", "alt", "onError", "fontSize", "width", "height", "marginTop", "color", "marginLeft", "ref", "audio", "screenshotFormat", "videoConstraints", "facingMode", "borderRadius", "backgroundColor", "marginRight", "length", "Date", "toLocaleString", "id", "_c37", "$RefreshReg$"], "sources": ["D:/ASL/ASL-Training/src/components/TrainingPage.js"], "sourcesContent": ["import { useState, useRef, useCallback, useEffect } from 'react';\nimport styled from 'styled-components';\nimport Webcam from 'react-webcam';\nimport {\n  Brain,\n  Camera,\n  ArrowLeft,\n  Play,\n  Square,\n  Download,\n  Eye,\n  Target,\n  Wifi,\n  WifiOff,\n  RefreshCw\n} from 'lucide-react';\nimport { useSignDetection } from '../hooks/useSignDetection';\nimport LevelSelector from './LevelSelector';\nimport FlashCardTraining from './FlashCardTraining';\nimport config from '../config';\n\nconst TrainingContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow-x: hidden;\n\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n`;\n\nconst Navigation = styled.nav`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: var(--bg-glass);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-neural);\n  padding: var(--space-4) 0;\n  transition: var(--transition-normal);\n`;\n\nconst NavContainer = styled.div`\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  @media (max-width: 768px) {\n    padding: 0 var(--space-4);\n  }\n`;\n\nconst Logo = styled.div`\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n    gap: var(--space-2);\n  }\n`;\n\nconst LogoIcon = styled.div`\n  width: 40px;\n  height: 40px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    width: 36px;\n    height: 36px;\n  }\n`;\n\nconst BackButton = styled.button`\n  background: var(--bg-glass);\n  color: var(--text-secondary);\n  border: 1px solid var(--border-neural);\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-xl);\n  font-size: 0.9rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  backdrop-filter: blur(10px);\n\n  &:hover {\n    background: var(--primary-50);\n    color: var(--primary-600);\n    border-color: var(--primary-300);\n    transform: translateY(-1px);\n    box-shadow: var(--shadow-lg);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-2) var(--space-4);\n    font-size: 0.85rem;\n  }\n`;\n\nconst PageTitle = styled.h1`\n  font-family: var(--font-primary);\n  font-size: 2.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  text-align: center;\n  margin-bottom: var(--space-4);\n  letter-spacing: -0.02em;\n\n  @media (max-width: 768px) {\n    font-size: 2rem;\n  }\n`;\n\nconst PageSubtitle = styled.p`\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  text-align: center;\n  margin-bottom: var(--space-16);\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n\n  @media (max-width: 768px) {\n    margin-bottom: var(--space-12);\n    font-size: 1rem;\n  }\n`;\n\nconst StatusBadge = styled.div`\n  display: inline-flex;\n  align-items: center;\n  gap: var(--space-2);\n  background: var(--bg-glass);\n  border: 1px solid var(--border-neural);\n  border-radius: var(--radius-full);\n  padding: var(--space-2) var(--space-4);\n  margin-bottom: var(--space-8);\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: var(--text-accent);\n  backdrop-filter: blur(10px);\n  box-shadow: var(--shadow-glow);\n\n  @media (max-width: 768px) {\n    font-size: 0.8rem;\n    padding: var(--space-2) var(--space-3);\n  }\n`;\n\nconst MainContent = styled.main`\n  padding: var(--space-20) var(--space-4) var(--space-16);\n  max-width: 1200px;\n  margin: 0 auto;\n\n  @media (max-width: 768px) {\n    padding: var(--space-12) var(--space-3) var(--space-8);\n    max-width: 100%;\n  }\n`;\n\nconst TrainingGrid = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--space-8);\n  max-width: 1200px;\n  margin: 0 auto var(--space-12);\n\n  @media (max-width: 1024px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-4);\n  }\n\n  @media (max-width: 768px) {\n    gap: var(--space-3);\n    margin: 0 auto var(--space-6);\n    grid-template-areas: \n      \"sign\"\n      \"camera\";\n  }\n`;\n\nconst CameraSection = styled.div`\n  background: var(--bg-glass);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-10);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-lg);\n  transition: var(--transition-normal);\n  position: relative;\n  overflow: hidden;\n\n  @media (max-width: 768px) {\n    padding: var(--space-6);\n    border-radius: var(--radius-xl);\n    grid-area: camera;\n  }\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 4px;\n    background: var(--bg-neural);\n    transform: scaleX(0);\n    transition: var(--transition-normal);\n  }\n\n  &:hover {\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n    border-color: var(--primary-300);\n\n    &::before {\n      transform: scaleX(1);\n    }\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-8);\n  }\n`;\n\nconst SectionTitle = styled.h2`\n  font-family: var(--font-primary);\n  font-size: 1.25rem;\n  margin-bottom: var(--space-6);\n  color: var(--text-primary);\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    margin-bottom: var(--space-4);\n  }\n`;\n\nconst SectionIcon = styled.div`\n  width: 36px;\n  height: 36px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    width: 32px;\n    height: 32px;\n  }\n`;\n\nconst WebcamContainer = styled.div`\n  position: relative;\n  border-radius: var(--radius-2xl);\n  overflow: hidden;\n  background: var(--neural-100);\n  aspect-ratio: 4/3;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 3px solid var(--border-neural);\n  margin-bottom: var(--space-6);\n  box-shadow: var(--shadow-lg);\n\n  @media (max-width: 768px) {\n    aspect-ratio: 3/4;\n    margin-bottom: var(--space-4);\n    border-radius: var(--radius-xl);\n    border-width: 2px;\n  }\n`;\n\nconst StyledWebcam = styled(Webcam)`\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n`;\n\nconst RecordingOverlay = styled.div`\n  position: absolute;\n  top: var(--space-4);\n  right: var(--space-4);\n  background: ${props => props.isRecording ?\n    'var(--error-500)' :\n    'var(--neural-600)'\n  };\n  color: white;\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-full);\n  font-size: 0.9rem;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  box-shadow: var(--shadow-lg);\n  animation: ${props => props.isRecording ? 'pulse 1.5s infinite' : 'none'};\n\n  @keyframes pulse {\n    0%, 100% { opacity: 1; transform: scale(1); }\n    50% { opacity: 0.8; transform: scale(1.05); }\n  }\n`;\n\nconst SignSection = styled.div`\n  background: var(--bg-primary);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-8);\n  border: 1px solid var(--border-light);\n  box-shadow: var(--shadow-lg);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  transition: all 0.3s ease;\n\n  &:hover {\n    box-shadow: var(--shadow-xl);\n    border-color: var(--primary-200);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-6);\n    grid-area: sign;\n  }\n`;\n\nconst SignSelector = styled.select`\n  width: 100%;\n  padding: var(--space-3) var(--space-4);\n  border: 2px solid var(--border-light);\n  border-radius: var(--radius-lg);\n  background: var(--bg-primary);\n  color: var(--text-primary);\n  font-size: 1rem;\n  font-weight: 500;\n  margin-bottom: var(--space-4);\n  cursor: pointer;\n  transition: var(--transition-normal);\n\n  &:focus {\n    outline: none;\n    border-color: var(--primary-500);\n    box-shadow: 0 0 0 3px var(--primary-100);\n  }\n\n  &:hover {\n    border-color: var(--primary-300);\n  }\n\n  option {\n    padding: var(--space-2);\n    background: var(--bg-primary);\n    color: var(--text-primary);\n  }\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    padding: var(--space-4);\n    margin-bottom: var(--space-3);\n  }\n`;\n\nconst SignDisplay = styled.div`\n  width: 300px;\n  height: 300px;\n  background: var(--primary-50);\n  border-radius: var(--radius-2xl);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: var(--space-6);\n  border: 2px solid var(--primary-200);\n  transition: all 0.3s ease;\n  overflow: hidden;\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: var(--radius-xl);\n  }\n\n  &:hover {\n    transform: scale(1.02);\n    border-color: var(--primary-300);\n  }\n\n  @media (max-width: 768px) {\n    width: 250px;\n    height: 250px;\n  }\n`;\n\nconst SignName = styled.h3`\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  margin-bottom: var(--space-3);\n  color: var(--text-primary);\n  font-weight: 700;\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n  }\n`;\n\nconst SignDescription = styled.p`\n  text-align: center;\n  line-height: 1.6;\n  color: var(--text-secondary);\n  font-size: 0.9rem;\n  font-weight: 400;\n  max-width: 280px;\n`;\n\nconst TopControlsSection = styled.div`\n  display: flex;\n  justify-content: center;\n  gap: var(--space-4);\n  margin-bottom: var(--space-8);\n  padding: var(--space-4);\n  background: var(--bg-glass);\n  border-radius: var(--radius-xl);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(10px);\n  box-shadow: var(--shadow-lg);\n\n  @media (max-width: 768px) {\n    flex-direction: column;\n    align-items: center;\n    gap: var(--space-3);\n    margin-bottom: var(--space-6);\n    padding: var(--space-3);\n  }\n`;\n\nconst RecordingStatus = styled.div`\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  padding: var(--space-2) var(--space-3);\n  border-radius: var(--radius-lg);\n  font-size: 0.875rem;\n  font-weight: 500;\n  background: ${props => props.isRecording ? 'var(--error-50)' : 'var(--success-50)'};\n  color: ${props => props.isRecording ? 'var(--error-700)' : 'var(--success-700)'};\n  border: 1px solid ${props => props.isRecording ? 'var(--error-200)' : 'var(--success-200)'};\n  white-space: nowrap;\n\n  @media (max-width: 768px) {\n    font-size: 0.8rem;\n    padding: var(--space-1) var(--space-2);\n  }\n`;\n\nconst ControlButton = styled.button`\n  background: ${props => props.variant === 'primary'\n    ? 'var(--primary-600)'\n    : props.variant === 'retry'\n    ? 'var(--warning-500)'\n    : 'var(--bg-primary)'};\n  border: ${props => props.variant === 'primary' || props.variant === 'retry'\n    ? 'none'\n    : '1px solid var(--border-medium)'};\n  color: ${props => props.variant === 'primary' || props.variant === 'retry'\n    ? 'white'\n    : 'var(--text-primary)'};\n  padding: ${props => props.compact\n    ? 'var(--space-2) var(--space-4)'\n    : 'var(--space-3) var(--space-6)'};\n  border-radius: var(--radius-lg);\n  cursor: pointer;\n  font-size: ${props => props.compact ? '0.8rem' : '0.9rem'};\n  font-weight: 600;\n  transition: all 0.2s ease;\n  min-width: ${props => props.compact ? '120px' : '160px'};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--space-2);\n\n  @media (max-width: 768px) {\n    padding: ${props => props.compact\n      ? 'var(--space-3) var(--space-5)'\n      : 'var(--space-4) var(--space-8)'};\n    font-size: ${props => props.compact ? '0.9rem' : '1rem'};\n    min-width: ${props => props.compact ? '140px' : '180px'};\n    border-radius: var(--radius-xl);\n  }\n  box-shadow: ${props => props.variant === 'primary' || props.variant === 'retry'\n    ? 'var(--shadow-lg)'\n    : 'var(--shadow-sm)'};\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: ${props => props.variant === 'primary' || props.variant === 'retry'\n      ? 'var(--shadow-xl)'\n      : 'var(--shadow-md)'};\n    background: ${props => props.variant === 'primary'\n      ? 'var(--primary-700)'\n      : props.variant === 'retry'\n      ? 'var(--warning-600)'\n      : 'var(--gray-50)'};\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none;\n  }\n\n  @media (max-width: 768px) {\n    width: 100%;\n    max-width: ${props => props.compact ? '200px' : '280px'};\n  }\n`;\n\nconst StatusMessage = styled.div`\n  text-align: center;\n  margin-top: var(--space-6);\n  padding: var(--space-4) var(--space-6);\n  border-radius: var(--radius-lg);\n  background: ${props =>\n    props.type === 'success' ? 'var(--success-500)' :\n    props.type === 'error' ? 'var(--error-500)' :\n    'var(--primary-600)'\n  };\n  color: white;\n  font-weight: 500;\n  font-size: 0.875rem;\n  max-width: 400px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n\nconst RecordingsSection = styled.div`\n  margin-top: var(--space-16);\n  background: var(--bg-secondary);\n  padding: var(--space-12) var(--space-4);\n  border-radius: var(--radius-2xl);\n  max-width: 1200px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n\nconst RecordingsTitle = styled.h3`\n  font-family: var(--font-primary);\n  color: var(--text-primary);\n  margin-bottom: var(--space-8);\n  font-size: 1.5rem;\n  font-weight: 600;\n  text-align: center;\n`;\n\nconst RecordingsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: var(--space-6);\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-4);\n  }\n`;\n\nconst RecordingCard = styled.div`\n  background: var(--bg-primary);\n  padding: var(--space-6);\n  border-radius: var(--radius-xl);\n  border: 1px solid var(--border-light);\n  box-shadow: var(--shadow-sm);\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: translateY(-2px);\n    border-color: var(--primary-200);\n    box-shadow: var(--shadow-lg);\n  }\n`;\n\nconst RecordingTitle = styled.p`\n  margin: 0 0 var(--space-2) 0;\n  color: var(--text-primary);\n  font-weight: 600;\n  font-size: 1rem;\n  font-family: var(--font-primary);\n`;\n\nconst RecordingTime = styled.p`\n  margin: 0 0 var(--space-4) 0;\n  font-size: 0.8rem;\n  color: var(--text-tertiary);\n`;\n\nconst DownloadButton = styled.button`\n  background: var(--primary-600);\n  border: none;\n  border-radius: var(--radius-lg);\n  padding: var(--space-2) var(--space-4);\n  color: white;\n  cursor: pointer;\n  font-size: 0.8rem;\n  font-weight: 500;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  margin: 0 auto;\n\n  &:hover {\n    background: var(--primary-700);\n    transform: translateY(-1px);\n  }\n`;\n\nconst PredictionDisplay = styled.div`\n  background: var(--bg-glass);\n  border: 2px solid ${props => {\n    if (props.matched) return 'var(--success-400)';\n    if (props.isStale) return 'var(--warning-300)';\n    return 'var(--border-light)';\n  }};\n  border-radius: var(--radius-xl);\n  padding: var(--space-4);\n  margin-bottom: var(--space-4);\n  text-align: center;\n  transition: var(--transition-normal);\n  backdrop-filter: blur(10px);\n  opacity: ${props => props.isStale ? 0.7 : 1};\n  min-height: 80px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n\n  ${props => props.matched && `\n    background: var(--success-50);\n    box-shadow: 0 0 20px var(--success-200);\n    animation: pulse 1s ease-in-out;\n  `}\n\n  ${props => props.isStale && `\n    background: var(--warning-50);\n  `}\n\n  @keyframes pulse {\n    0%, 100% { transform: scale(1); }\n    50% { transform: scale(1.02); }\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-3);\n    margin-bottom: var(--space-3);\n    min-height: 70px;\n  }\n`;\n\nconst PredictionText = styled.div`\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: ${props => {\n    if (props.matched) return 'var(--success-700)';\n    if (props.isStale) return 'var(--warning-700)';\n    return 'var(--text-primary)';\n  }};\n  margin-bottom: var(--space-2);\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n  }\n`;\n\nconst ConfidenceBar = styled.div`\n  width: 100%;\n  height: 8px;\n  background: var(--bg-secondary);\n  border-radius: var(--radius-full);\n  overflow: hidden;\n  margin-top: var(--space-2);\n`;\n\nconst ConfidenceFill = styled.div`\n  height: 100%;\n  background: ${props => {\n    if (props.confidence > 0.8) return 'var(--success-500)';\n    if (props.confidence > 0.6) return 'var(--warning-500)';\n    return 'var(--error-500)';\n  }};\n  width: ${props => (props.confidence * 100)}%;\n  transition: width 0.3s ease;\n`;\n\nconst ConnectionStatus = styled.div`\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  padding: var(--space-2) var(--space-3);\n  border-radius: var(--radius-lg);\n  font-size: 0.875rem;\n  font-weight: 500;\n  background: ${props => props.connected ? 'var(--success-50)' : 'var(--error-50)'};\n  color: ${props => props.connected ? 'var(--success-700)' : 'var(--error-700)'};\n  border: 1px solid ${props => props.connected ? 'var(--success-200)' : 'var(--error-200)'};\n`;\n\n// Sign language data with GIFs (100 signs, model-predictable)\nconst signLanguageData = {\n  \"TV\": { name: \"TV\", gif: \"https://lifeprint.com/asl101/gifs/t/tv.gif\", description: \"Sign for TV.\" },\n  \"after\": { name: \"After\", gif: \"https://lifeprint.com/asl101/gifs/a/after-over-across.gif\", description: \"Sign for after.\" },\n  \"airplane\": { name: \"Airplane\", gif: \"https://www.lifeprint.com/asl101/gifs/a/airplane-flying.gif\", description: \"Sign for airplane.\" },\n  \"all\": { name: \"All\", gif: \"https://lifeprint.com/asl101/gifs/a/all-whole.gif\", description: \"Sign for all.\" },\n  \"alligator\": { name: \"Alligator\", gif: \"https://lifeprint.com/asl101/gifs/a/alligator.gif\", description: \"Sign for alligator.\" },\n  \"animal\": { name: \"Animal\", gif: \"https://www.lifeprint.com/asl101/gifs-animated/animal.gif\", description: \"Sign for animal.\" },\n  \"another\": { name: \"Another\", gif: \"https://lifeprint.com/asl101/gifs-animated/another.gif\", description: \"Sign for another.\" },\n  \"any\": { name: \"Any\", gif: \"https://lifeprint.com/asl101/gifs/a/any.gif\", description: \"Sign for any.\" },\n  \"apple\": { name: \"Apple\", gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\", description: \"Sign for apple.\" },\n  \"arm\": { name: \"Arm\", gif: \"https://th.bing.com/th/id/OIP.KkJLqfbp6XEHiIe-jOUb2AHaEc?w=251&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for arm.\" },\n  \"aunt\": { name: \"Aunt\", gif: \"https://th.bing.com/th/id/OIP.Yz5UUZdNTrVWXf72we_N6wHaHa?rs=1&pid=ImgDetMain\", description: \"Sign for aunt.\" },\n  \"awake\": { name: \"Awake\", gif: \"https://th.bing.com/th/id/OIP.XcgdjGKBo8LynmiAw-tDCQHaE-?w=235&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for awake.\" },\n  \"backyard\": { name: \"Backyard\", gif: \"https://lifeprint.com/asl101/gifs/b/backyard.gif\", description: \"Sign for backyard.\" },\n  \"bad\": { name: \"Bad\", gif: \"https://media.giphy.com/media/v1.Y2lkPTc5MGI3NjExeThwMm9rZGVkejE2N2RhMWp0NTY3Z2pwNHBqZXFlOTF3ZGd0cG1zZSZlcD12MV9naWZzX3NlYXJjaCZjdD1n/l0MYL34CVEqLK27yU/giphy.gif\", description: \"Sign for bad.\" },\n  \"balloon\": { name: \"Balloon\", gif: \"https://media.giphy.com/media/26FL9yfajyobRXJde/giphy.gif\", description: \"Sign for balloon.\" },\n  \"bath\": { name: \"Bath\", gif: \"https://media.giphy.com/media/l0MYPjjoeJbZVPmNO/giphy.gif\", description: \"Sign for bath.\" },\n  // \"because\": { name: \"Because\", gif: \"https://lifeprint.com/asl101/gifs-animated/because.gif\", description: \"Sign for because.\" },\n  \"bed\": { name: \"Bed\", gif: \"https://lifeprint.com/asl101/gifs/b/bed-1.gif\", description: \"Sign for bed.\" },\n  \"bedroom\": { name: \"Bedroom\", gif: \"https://lifeprint.com/asl101/gifs/b/bedroom.gif\", description: \"Sign for bedroom.\" },\n  \"bee\": { name: \"Bee\", gif: \"https://lifeprint.com/asl101/gifs/b/bee.gif\", description: \"Sign for bee.\" },\n  \"before\": { name: \"Before\", gif: \"https://th.bing.com/th/id/OIP.0EvzUY4jH2cDCa4nNcRw4wHaE-?w=267&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for before.\" },\n  // \"beside\": { name: \"Beside\", gif: \"https://lifeprint.com/asl101/gifs/b/beside.gif\", description: \"Sign for beside.\" },\n  \"better\": { name: \"Better\", gif: \"https://lifeprint.com/asl101/gifs/b/better.gif\", description: \"Sign for better.\" },\n  \"bird\": { name: \"Bird\", gif: \"https://lifeprint.com/asl101/gifs/b/bird.gif\", description: \"Sign for bird.\" },\n  \"black\": { name: \"Black\", gif: \"https://th.bing.com/th/id/R.********************************?rik=52tGw7%2fGcx2HtwMm9rZGVkejE2N2RhMWp0NTY3Z2pwNHBqZXFlOTF3ZGd0cG1zZSZlcD12MV9naWZzX3NlYXJjaCZjdD1n/l0MYL34CVEqLK27yU/giphy.gif\", description: \"Sign for black.\" },\n  \"blow\": { name: \"Blow\", gif: \"https://th.bing.com/th/id/OIP.rJg-otMBtvfj1T1HkSKugwHaEc?w=304&h=182&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for blow.\" },\n  \"blue\": { name: \"Blue\", gif: \"https://lifeprint.com/asl101/gifs/b/blue-1.gif\", description: \"Sign for blue.\" },\n  \"boat\": { name: \"Boat\", gif: \"https://lifeprint.com/asl101/gifs/b/boat-ship.gif\", description: \"Sign for boat.\" },\n  \"book\": { name: \"Book\", gif: \"https://media.giphy.com/media/l0MYL43dl4pQEn3uE/giphy.gif\", description: \"Sign for book.\" },\n  \"boy\": { name: \"Boy\", gif: \"https://lifeprint.com/asl101/gifs/b/boy.gif\", description: \"Sign for boy.\" },\n  \"brother\": { name: \"Brother\", gif: \"https://lifeprint.com/asl101/gifs/b/brother.gif\", description: \"Sign for brother.\" },\n  \"brown\": { name: \"Brown\", gif: \"https://lifeprint.com/asl101/gifs/b/brown.gif\", description: \"Sign for brown.\" },\n  \"bug\": { name: \"Bug\", gif: \"https://lifeprint.com/asl101/gifs/b/bug.gif\", description: \"Sign for bug.\" },\n  \"bye\": { name: \"Bye\", gif: \"https://c.tenor.com/vME77PObDN8AAAAC/asl-bye-asl-goodbye.gif\", description: \"Sign for bye.\" },\n  \"callonphone\": { name: \"Call on phone\", gif: \"https://www.lifeprint.com/asl101/gifs/c/call-hearing.gif\", description: \"Sign for call on phone.\" },\n  \"can\": { name: \"Can\", gif: \"https://lifeprint.com/asl101/gifs/c/can.gif\", description: \"Sign for can.\" },\n  \"car\": { name: \"Car\", gif: \"https://th.bing.com/th/id/OIP.wxw32OaIdqFt8f_ucHVoRgHaEH?w=308&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for car.\" },\n  \"carrot\": { name: \"Carrot\", gif: \"https://media.giphy.com/media/l0HlDdvqxs1jsRtiU/giphy.gif\", description: \"Sign for carrot.\" },\n  \"cat\": { name: \"Cat\", gif: \"https://lifeprint.com/asl101/gifs-animated/cat-02.gif\", description: \"Sign for cat.\" },\n  \"cereal\": { name: \"Cereal\", gif: \"https://th.bing.com/th/id/R.********************************?rik=wPMg%2fK1dYTfR%2bw&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fcereal.gif&ehk=RpDS3wWZM4eryawaxA1wAvWwM0EM%2fdGgJkWY2ce1KFs%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for cereal.\" },\n  \"chair\": { name: \"Chair\", gif: \"https://th.bing.com/th/id/OIP.5kr1MkVLnuN2Z9Jkw-0QpAHaE-?w=237&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for chair.\" },\n  \"cheek\": { name: \"Cheek\", gif: \"https://lifeprint.com/asl101/gifs/c/cheek.gif\", description: \"Sign for cheek.\" },\n  \"child\": { name: \"Child\", gif: \"https://lifeprint.com/asl101/gifs/c/child.gif\", description: \"Sign for child.\" },\n  \"chin\": { name: \"Chin\", gif: \"https://lifeprint.com/asl101/gifs/c/chin.gif\", description: \"Sign for chin.\" },\n  \"chocolate\": { name: \"Chocolate\", gif: \"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fi.pinimg.com%2foriginals%2f9f%2fa2%2fb5%2f9fa2b5064a72b5e46202d20848f1bf21.gif&ehk=izvOlFp25%2fx5NVTCmqVz0UOnZNOWy%2fAJJtzAhkZ8nTg%3d\", description: \"Sign for chocolate.\" },\n  \"clean\": { name: \"Clean\", gif: \"https://media.giphy.com/media/3o7TKoturrdpf5Muwo/giphy.gif\", description: \"Sign for clean.\" },\n  \"close\": { name: \"Close\", gif: \"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia2.giphy.com%2fmedia%2fl4JyZuXNGxS3Yydeo%2fgiphy.gif%3fcid%3d790b7611318eb5b864ad67b3cecb35b9d81240a50d251bb0%26rid%3dgiphy.gif%26ct%3dg&ehk=A6wfp3Afm3rFCPLWSjgQd6JVjmRSBNBlk9vd0jVNgJc%3d\", description: \"Sign for close.\" },\n  \"closet\": { name: \"Closet\", gif: \"https://lifeprint.com/asl101/gifs/c/closet.gif\", description: \"Sign for closet.\" },\n  \"cloud\": { name: \"Cloud\", gif: \"https://th.bing.com/th/id/OIP.hMO89bV2zwVcIVIa7FOT5QHaEc?rs=1&pid=ImgDetMain\", description: \"Sign for cloud.\" },\n  \"clown\": { name: \"Clown\", gif: \"https://th.bing.com/th/id/R.********************************?rik=OPrV3%2b1Zkelr2A&pid=ImgRaw&r=0\", description: \"Sign for clown.\" },\n  \"cow\": { name: \"Cow\", gif: \"https://lifeprint.com/asl101/gifs/c/cow.gif\", description: \"Sign for cow.\" },\n  \"cowboy\": { name: \"Cowboy\", gif: \"https://lifeprint.com/asl101/gifs/c/cowboy.gif\", description: \"Sign for cowboy.\" },\n  \"cry\": { name: \"Cry\", gif: \"https://www.lifeprint.com/asl101/gifs/c/cry-tears.gif\", description: \"Sign for cry.\" },\n  \"cut\": { name: \"Cut\", gif: \"https://th.bing.com/th/id/OIP.ZtKu3hlJ6pduArqfgEcyUgHaE-?w=248&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for cut.\" },\n  \"cute\": { name: \"Cute\", gif: \"https://lifeprint.com/asl101/gifs/c/cute-sugar.gif\", description: \"Sign for cute.\" },\n  \"dad\": { name: \"Dad\", gif: \"https://media.giphy.com/media/l0MYQcLKwtl5v6H1S/giphy.gif\", description: \"Sign for dad.\" },\n  \"dance\": { name: \"Dance\", gif: \"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia.giphy.com%2fmedia%2f3o7TKMspYQjQTbOz2U%2fgiphy.gif&ehk=h%2bdBHCxuoOT89ovSy5uTk6MCL9acaBEV6ld9lrVDRF4%3d\", description: \"Sign for dance.\" },\n  \"dirty\": { name: \"Dirty\", gif: \"https://th.bing.com/th/id/OIP.wRA7r1OPPUuEoLL4Hds9jAHaHa?rs=1&pid=ImgDetMain\", description: \"Sign for dirty.\" },\n  \"dog\": { name: \"Dog\", gif: \"https://th.bing.com/th/id/R.********************************?rik=uVshGsOHXgldoQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fd%2fdog1.gif&ehk=Xnfrvg0xwy1u%2fae9vWdKYMT25%2bF6qoteW2FThCbVrOA%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for dog.\" },\n  \"doll\": { name: \"Doll\", gif: \"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fwww.lifeprint.com%2fasl101%2fgifs-animated%2fdoll.gif&ehk=hPI0Fzzl9CGOrgQYS2Z53a5YdYgjxYFeOIGghGAEZYU%3d\", description: \"Sign for doll.\" },\n  \"donkey\": { name: \"Donkey\", gif: \"https://www.lifeprint.com/asl101/gifs/d/donkey-1h.gif\", description: \"Sign for donkey.\" },\n  \"down\": { name: \"Down\", gif: \"https://th.bing.com/th/id/OIP.CZlW6IpZUdgspxVpW6PZ8QHaE-?w=250&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for down.\" },\n  \"drawer\": { name: \"Drawer\", gif: \"https://th.bing.com/th/id/OIP.8yooqOFFixqki7j28PVpYQHaE-?w=234&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for drawer.\" },\n  \"drink\": { name: \"Drink\", gif: \"https://www.lifeprint.com/asl101/gifs/d/drink-c.gif\", description: \"Sign for drink.\" },\n  \"drop\": { name: \"Drop\", gif: \"https://th.bing.com/th/id/OIP.XQJn0tOccOUmG8OZHz8X9gHaE-?w=247&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for drop.\" },\n  \"dry\": { name: \"Dry\", gif: \"https://th.bing.com/th/id/OIP.A0oQgM0IGtwZjfz1Caj-AgHaE-?w=268&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for dry.\" },\n  \"dryer\": { name: \"Dryer\", gif: \"https://lifeprint.com/asl101/gifs/d/dryer.gif\", description: \"Sign for dryer.\" },\n  \"duck\": { name: \"Duck\", gif: \"https://th.bing.com/th/id/R.********************************?rik=ZetjiJ3WOhOXrQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fd%2fduck.gif&ehk=STeui62x5lieai0VcyeZkX2t8rILR%2f8GR5F3x2xJ5tw%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for duck.\" },\n  \"ear\": { name: \"Ear\", gif: \"https://lifeprint.com/asl101/signjpegs/e/ears.h3.jpg\", description: \"Sign for ear.\" },\n  \"elephant\": { name: \"Elephant\", gif: \"https://lifeprint.com/asl101/gifs-animated/elephant.gif\", description: \"Sign for elephant.\" },\n  \"empty\": { name: \"Empty\", gif: \"https://lifeprint.com/images-signs/empty.gif\", description: \"Sign for empty.\" },\n  \"every\": { name: \"Every\", gif: \"https://lifeprint.com/asl101/gifs-animated/every.gif\", description: \"Sign for every.\" },\n  \"eye\": { name: \"Eye\", gif: \"https://lifeprint.com/asl101/gifs/e/eyes.gif\", description: \"Sign for eye.\" },\n  \"face\": { name: \"Face\", gif: \"https://lifeprint.com/asl101/gifs/f/face.gif\", description: \"Sign for face.\" },\n  \"fall\": { name: \"Fall\", gif: \"https://lifeprint.com/asl101/gifs/f/fall.gif\", description: \"Sign for fall.\" },\n  \"farm\": { name: \"Farm\", gif: \"https://th.bing.com/th/id/R.********************************?rik=IO%2brRd7xNmCQBQ&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2ffarm.gif&ehk=aOO01Vk8fbE84nLfNNnOVL3kUdyWJtLaTEcwePgbP9A%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for farm.\" },\n  \"fast\": { name: \"Fast\", gif: \"https://th.bing.com/th/id/OIP.YX_BqT1FjGm8HeM4k4WFAgAAAA?rs=1&pid=ImgDetMain\", description: \"Sign for fast.\" },\n  \"feet\": { name: \"Feet\", gif: \"https://th.bing.com/th/id/OIP.RaYFj5lvSS6NeIna8NtmZQHaE-?w=247&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for feet.\" },\n  \"find\": { name: \"Find\", gif: \"https://www.lifeprint.com/asl101/gifs/f/find-pick.gif\", description: \"Sign for find.\" },\n  \"fine\": { name: \"Fine\", gif: \"https://th.bing.com/th/id/R.********************************?rik=Qpm%2bw3fHTAWj1A&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2ff%2ffine.gif&ehk=mGMZf4l%2bLZMq4atRomNJSvrSjYgFe%2bRVCm1dYLh5J3I%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for fine.\" },\n  \"finger\": { name: \"Finger\", gif: \"https://lifeprint.com/asl101/gifs/f/finger.gif\", description: \"Sign for finger.\" },\n  \"finish\": { name: \"Finish\", gif: \"https://th.bing.com/th/id/R.********************************?rik=34j4pW2f3E5TtQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2ff%2ffinish.gif&ehk=xNk24Jbe3t0moSmcmUftmZzCRgHIxsarq3W9E7kGmPM%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for finish.\" },\n  \"fireman\": { name: \"Fireman\", gif: \"https://lifeprint.com/asl101/gifs/f/fireman-c2.gif\", description: \"Sign for fireman.\" },\n  \"first\": { name: \"First\", gif: \"https://lifeprint.com/asl101/gifs/f/first.gif\", description: \"Sign for first.\" },\n  \"fish\": { name: \"Fish\", gif: \"https://th.bing.com/th/id/OIP.Lzhd7lIIa-V4H3faS1d3mQHaHa?rs=1&pid=ImgDetMain\", description: \"Sign for fish.\" },\n  \"flag\": { name: \"Flag\", gif: \"https://th.bing.com/th/id/OIP.3LqQWEnK4TG0lohgQ3G5uAHaE-?w=263&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for flag.\" },\n  \"flower\": { name: \"Flower\", gif: \"https://media.giphy.com/media/3o7TKGkqPpLUdFiFPy/giphy.gif\", description: \"Sign for flower.\" },\n  \"food\": { name: \"Food\", gif: \"https://i.pinimg.com/originals/cc/bb/0c/ccbb0c143db0b51e9947a5966db42fd8.gif\", description: \"Sign for food.\" },\n  \"for\": { name: \"For\", gif: \"https://lifeprint.com/asl101/gifs/f/for.gif\", description: \"Sign for for.\" },\n  \"frenchfries\": { name: \"French Fries\", gif: \"https://www.lifeprint.com/asl101/gifs/f/french-fries.gif\", description: \"Sign for french fries.\" },\n  \"frog\": { name: \"Frog\", gif: \"https://media.giphy.com/media/l0HlKl64lIvTjZ7QA/giphy.gif\", description: \"Sign for frog.\" },\n  \"garbage\": { name: \"Garbage\", gif: \"https://th.bing.com/th/id/R.********************************?rik=78iU%2fDx85Ut9fA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fg%2fgarbage.gif&ehk=lafY%2f1y5WEEfr04p6Uq4waDP9iV7bJB5r2k3RYGOhWY%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for garbage.\" },\n  \"gift\": { name: \"Gift\", gif: \"https://www.babysignlanguage.com/signs/gift.gif\", description: \"Sign for gift.\" },\n  \"giraffe\": { name: \"Giraffe\", gif: \"https://www.lifeprint.com/asl101/gifs/g/giraffe.gif\", description: \"Sign for giraffe.\" },\n  \"girl\": { name: \"Girl\", gif: \"https://th.bing.com/th/id/R.********************************?rik=yDsGUPEaDyeSlA&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fgirl.gif&ehk=zdVxVSayRBDn67vVCpMhUH6UmzUQE8vaY7%2bv8jedvs8%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for girl.\" },\n  \"give\": { name: \"Give\", gif: \"https://www.lifeprint.com/asl101/gifs/g/give-x-two-handed.gif\", description: \"Sign for give.\" },\n  \"glasswindow\": { name: \"Glass Window\", gif: \"https://lifeprint.com/asl101/gifs/g/glass.gif\", description: \"Sign for glass window.\" },\n  \"go\": { name: \"Go\", gif: \"https://media.giphy.com/media/l3vRdVMMN9VsW5a0w/giphy.gif\", description: \"Sign for go.\" },\n  \"goose\": { name: \"Goose\", gif: \"https://www.babysignlanguage.com/signs/goose.gif\", description: \"Sign for goose.\" },\n  \"grandma\": { name: \"Grandma\", gif: \"https://www.lifeprint.com/asl101/gifs/g/grandma.gif\", description: \"Sign for grandma.\" },\n  \"grandpa\": { name: \"Grandpa\", gif: \"https://th.bing.com/th/id/OIP.yyLPc-rWg0PMNbrwjeQQngHaE-?w=238&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for grandpa.\" },\n  \"grass\": { name: \"Grass\", gif: \"https://th.bing.com/th/id/R.********************************?rik=uGZNVzt6tISwHA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs-animated%2fgrass.gif&ehk=VKQd9cvBrglo47EhogWYL9rOiZZsEJ7Yqt%2bgJ8N99yQ%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for grass.\" },\n  \"green\": { name: \"Green\", gif: \"https://i.pinimg.com/originals/cb/7f/75/cb7f757ffb79cb3d1309c9ad785e83a1.gif\", description: \"Sign for green.\" },\n  \"gum\": { name: \"Gum\", gif: \"https://lifeprint.com/asl101/gifs/g/gum.gif\", description: \"Sign for gum.\" },\n  \"hair\": { name: \"Hair\", gif: \"https://www.lifeprint.com/asl101/gifs/h/hair-g-version.gif\", description: \"Sign for hair.\" },\n  \"happy\": { name: \"Happy\", gif: \"https://media0.giphy.com/media/3o7TKFpahYpUp4g0N2/giphy.gif?cid=790b7611bca188a92e2e759876756ef62ba95b7cdd337c77&rid=giphy.gif&ct=g\", description: \"Sign for happy.\" },\n  \"hat\": { name: \"Hat\", gif: \"https://th.bing.com/th/id/OIP.QyFdqn-0ZqUwNfE6jbzKWAHaE-?w=258&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for hat.\" },\n  \"hate\": { name: \"Hate\", gif: \"https://media.giphy.com/media/l0MYPiNw8l2LAPJXW/giphy.gif\", description: \"Sign for hate.\" },\n  \"have\": { name: \"Have\", gif: \"https://th.bing.com/th/id/R.********************************?rik=q5Ei%2b7oJb7Uzyw&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhave.gif&ehk=H9yIaJxFVejkfHpkhTUipBRv9CW63KBFy6QW5cdbkKw%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for have.\" },\n  \"haveto\": { name: \"Have to\", gif: \"https://lifeprint.com/asl101/gifs/h/have-to.gif\", description: \"Sign for have to.\" },\n  \"head\": { name: \"Head\", gif: \"https://th.bing.com/th/id/R.********************************?rik=OcbJdRbpEFsWXQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fsignjpegs%2fh%2fhead-1.jpg&ehk=RPBV45fSrLDEWYiZvRuZs2c1JNrL4WzdqLSNMFIF3Rs%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for head.\" },\n  \"hear\": { name: \"Hear\", gif: \"https://www.lifeprint.com/asl101/signjpegs/h/hear.h4.jpg\", description: \"Sign for hear.\" },\n  \"helicopter\": { name: \"Helicopter\", gif: \"https://th.bing.com/th/id/R.********************************?rik=5uhWxBaByliWA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhelicopter.gif&ehk=mwAyT82RBoeYDe7yaHA1jL3%2f30dUksltmv4dF7YGf%2bU%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for helicopter.\" },\n  \"hello\": { name: \"Hello\", gif: \"https://media.giphy.com/media/3o7TKNKOfKlIhbD3gY/giphy.gif\", description: \"Sign for hello.\" },\n  \"hen\": { name: \"Hen\", gif: \"https://media0.giphy.com/media/26hisADhtILiu1J3W/giphy.gif?cid=790b76112d512b94e1647afb111c8d77f92ae31f37864f2&rid=giphy.gif&ct=g\", description: \"Sign for hen.\" },\n  \"hesheit\": { name: \"He/She/It\", gif: \"https://lifeprint.com/asl101/gifs/h/he-she-it.gif\", description: \"Sign for he/she/it.\" },\n  \"hide\": { name: \"Hide\", gif: \"https://lifeprint.com/asl101/gifs/h/hide.gif\", description: \"Sign for hide.\" },\n  \"high\": { name: \"High\", gif: \"https://lifeprint.com/asl101/gifs/h/high.gif\", description: \"Sign for high.\" },\n  \"home\": { name: \"Home\", gif: \"https://th.bing.com/th/id/R.********************************?rik=%2bnBd%2foQjxnoPfg&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhome-2.gif&ehk=7yD%2f%2fh6JN1Y4D4BOrUjgKW4Jccy2Y4GVYLf%2fzyk%2b5YY%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for home.\" },\n  \"horse\": { name: \"Horse\", gif: \"https://media.giphy.com/media/l0HlM5HffraiQaHUk/giphy.gif\", description: \"Sign for horse.\" },\n  \"hot\": { name: \"Hot\", gif: \"https://media.giphy.com/media/3o6Zt99k5aDok347bG/giphy.gif\", description: \"Sign for hot.\" },\n  \"hungry\": { name: \"Hungry\", gif: \"https://media.giphy.com/media/l3vR0xkdFEz4tnfTq/giphy.gif\", description: \"Sign for hungry.\" },\n  \"icecream\": { name: \"Ice Cream\", gif: \"https://media.giphy.com/media/3o7TKp6yVibVMhBSLu/giphy.gif\", description: \"Sign for ice cream.\" },\n  \"if\": { name: \"If\", gif: \"https://lifeprint.com/asl101/gifs/i/if.gif\", description: \"Sign for if.\" },\n  \"into\": { name: \"Into\", gif: \"https://lifeprint.com/asl101/gifs/i/into.gif\", description: \"Sign for into.\" },\n  \"jacket\": { name: \"Jacket\", gif: \"https://www.lifeprint.com/asl101/gifs/c/coat.gif\", description: \"Sign for jacket.\" },\n  \"jeans\": { name: \"Jeans\", gif: \"https://lifeprint.com/asl101/gifs/j/jeans.gif\", description: \"Sign for jeans.\" },\n  \"jump\": { name: \"Jump\", gif: \"https://lifeprint.com/asl101/gifs-animated/jump.gif\", description: \"Sign for jump.\" },\n  \"kiss\": { name: \"Kiss\", gif: \"https://i.gifer.com/PxGY.gif\", description: \"Sign for kiss.\" },\n  \"kitty\": { name: \"Kitty\", gif: \"https://lifeprint.com/asl101/gifs-animated/cat-02.gif\", description: \"Sign for kitty.\" },\n  \"lamp\": { name: \"Lamp\", gif: \"https://lifeprint.com/asl101/gifs/l/lamp.gif\", description: \"Sign for lamp.\" },\n  \"later\": { name: \"Later\", gif: \"https://media3.giphy.com/media/l0MYHTyMzMRcikIxi/giphy.gif?cid=790b761128cd39f9baa06dbeb4e099d13e3516763d5f0952&rid=giphy.gif&ct=g\", description: \"Sign for later.\" },\n  \"like\": { name: \"Like\", gif: \"https://lifeprint.com/asl101/gifs/l/like.gif\", description: \"Sign for like.\" },\n  \"lion\": { name: \"Lion\", gif: \"https://th.bing.com/th/id/OIP.8sDkvbXdMKVmCNlDV79WpAHaE-?w=247&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for lion.\" },\n  \"lips\": { name: \"Lips\", gif: \"https://lifeprint.com/asl101/gifs/l/lips.gif\", description: \"Sign for lips.\" },\n  \"listen\": { name: \"Listen\", gif: \"https://th.bing.com/th/id/OIP.VjsXAad6abRwkCla83kbZQHaEc?w=284&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for listen.\" },\n  \"look\": { name: \"Look\", gif: \"https://th.bing.com/th/id/R.********************************?rik=pYhzip7LqNs7qw&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fl%2flook-at-1.gif&ehk=rFJ7dBrMGFDK0nHLzrOPAzROVE7yqyDEcb%2btLqKqYOI%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for look.\" },\n  \"loud\": { name: \"Loud\", gif: \"https://lifeprint.com/asl101/gifs-animated/loud.gif\", description: \"Sign for loud.\" },\n  \"mad\": { name: \"Mad\", gif: \"https://lifeprint.com/asl101/gifs/m/mad.gif\", description: \"Sign for mad.\" },\n  \"make\": { name: \"Make\", gif: \"https://th.bing.com/th/id/OIP.CPz7T2bH107Tu-DBnHvatAHaEc?w=313&h=188&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for make.\" },\n  \"man\": { name: \"Man\", gif: \"https://lifeprint.com/asl101/gifs/m/man.gif\", description: \"Sign for man.\" },\n  \"many\": { name: \"Many\", gif: \"https://lifeprint.com/asl101/gifs/m/many.gif\", description: \"Sign for many.\" },\n  \"milk\": { name: \"Milk\", gif: \"https://lifeprint.com/asl101/gifs/m/milk.gif\", description: \"Sign for milk.\" },\n  \"minemy\": { name: \"Mine/My\", gif: \"https://th.bing.com/th/id/OIP.VBkNZsR_pK7KUoCNiWYMdgHaEc?w=288&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for mine/my.\" },\n  \"mitten\": { name: \"Mitten\", gif: \"https://lifeprint.com/asl101/gifs-animated/mittens.gif\", description: \"Sign for mitten.\" },\n  \"mom\": { name: \"Mom\", gif: \"https://lifeprint.com/asl101/gifs/m/mom.gif\", description: \"Sign for mom.\" },\n  \"moon\": { name: \"Moon\", gif: \"https://th.bing.com/th/id/R.********************************?rik=XbVhBJtkANrG9g&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fm%2fmoon.gif&ehk=YSDvFeUSTa9X1BEJhDjdnLC4c7zWn8z7Hj%2fMkkLUyFE%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for moon.\" },\n  \"morning\": { name: \"Morning\", gif: \"https://media0.giphy.com/media/3o6ZtrcJ9GCXGGw0ww/source.gif\", description: \"Sign for morning.\" },\n  \"mouse\": { name: \"Mouse\", gif: \"https://lifeprint.com/asl101/gifs/m/mouse.gif\", description: \"Sign for mouse.\" },\n  \"mouth\": { name: \"Mouth\", gif: \"https://lifeprint.com/asl101/gifs-animated/mouth.gif\", description: \"Sign for mouth.\" },\n  \"nap\": { name: \"Nap\", gif: \"https://lifeprint.com/asl101/gifs/n/nap.gif\", description: \"Sign for nap.\" },\n  \"napkin\": { name: \"Napkin\", gif: \"https://lifeprint.com/asl101/gifs/n/napkin.gif\", description: \"Sign for napkin.\" },\n  \"night\": { name: \"Night\", gif: \"https://lifeprint.com/asl101/gifs/n/night.gif\", description: \"Sign for night.\" },\n  \"no\": { name: \"No\", gif: \"https://lifeprint.com/asl101/gifs/n/no-2-movement.gif\", description: \"Sign for no.\" },\n  \"noisy\": { name: \"Noisy\", gif: \"https://lifeprint.com/asl101/gifs/n/noisy.gif\", description: \"Sign for noisy.\" },\n  \"nose\": { name: \"Nose\", gif: \"https://lifeprint.com/asl101/signjpegs/n/nose.h1.jpg\", description: \"Sign for nose.\" },\n  \"not\": { name: \"Not\", gif: \"https://th.bing.com/th/id/R.********************************?rik=6%2bbZ2jRA%2famQ4Q&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fn%2fnot-negative.gif&ehk=%2bppuO9P0%2fpdzrrdNO4FXpxdIGs8jgY%2fj%2b1ZCwdbDWO4%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for not.\" },\n  \"now\": { name: \"Now\", gif: \"https://lifeprint.com/asl101/gifs/n/now.gif\", description: \"Sign for now.\" },\n  \"nuts\": { name: \"Nuts\", gif: \"https://th.bing.com/th/id/OIP.wRnQjn9j2vfFfzAnRR205QHaE-?w=276&h=185&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for nuts.\" },\n  \"old\": { name: \"Old\", gif: \"https://lifeprint.com/asl101/gifs/o/old.gif\", description: \"Sign for old.\" },\n  \"on\": { name: \"On\", gif: \"https://lifeprint.com/asl101/gifs/o/on-onto.gif\", description: \"Sign for on.\" },\n  \"open\": { name: \"Open\", gif: \"https://th.bing.com/th/id/OIP.BeMiGXQFuYk_6ZrgG3iqzQHaE-?w=264&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for open.\" },\n  \"orange\": { name: \"Orange\", gif: \"https://lifeprint.com/asl101/gifs/o/orange.gif\", description: \"Sign for orange.\" },\n  \"outside\": { name: \"Outside\", gif: \"https://lifeprint.com/asl101/gifs/o/outside.gif\", description: \"Sign for outside.\" },\n  \"owie\": { name: \"Owie\", gif: \"https://lifeprint.com/asl101/gifs/o/owie.gif\", description: \"Sign for owie.\" },\n  \"owl\": { name: \"Owl\", gif: \"https://lifeprint.com/asl101/gifs/o/owl.gif\", description: \"Sign for owl.\" },\n  \"pajamas\": { name: \"Pajamas\", gif: \"https://lifeprint.com/asl101/gifs/p/pajamas.gif\", description: \"Sign for pajamas.\" },\n  \"pen\": { name: \"Pen\", gif: \"https://lifeprint.com/asl101/gifs/p/pen.gif\", description: \"Sign for pen.\" },\n  \"pencil\": { name: \"Pencil\", gif: \"https://lifeprint.com/asl101/gifs/p/pencil-2.gif\", description: \"Sign for pencil.\" },\n  \"penny\": { name: \"Penny\", gif: \"https://lifeprint.com/asl101/gifs/p/penny.gif\", description: \"Sign for penny.\" },\n  \"person\": { name: \"Person\", gif: \"https://lifeprint.com/asl101/gifs/p/person.gif\", description: \"Sign for person.\" },\n  \"pig\": { name: \"Pig\", gif: \"https://lifeprint.com/asl101/gifs/p/pig.gif\", description: \"Sign for pig.\" },\n  \"pizza\": { name: \"Pizza\", gif: \"https://lifeprint.com/asl101/gifs/p/pizza.gif\", description: \"Sign for pizza.\" },\n  \"please\": { name: \"Please\", gif: \"https://lifeprint.com/asl101/gifs-animated/pleasecloseup.gif\", description: \"Sign for please.\" },\n  \"police\": { name: \"Police\", gif: \"https://th.bing.com/th/id/R.********************************?rik=icjjfUg15cqgLw&pid=ImgRaw&r=0\", description: \"Sign for police.\" },\n  \"pool\": { name: \"Pool\", gif: \"https://th.bing.com/th/id/OIP.dhcMKyW2psDcA5uwsRaRagHaEc?w=276&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for pool.\" },\n  \"potty\": { name: \"Potty\", gif: \"https://th.bing.com/th/id/OIP.YcNMUjCg6f95xdgN5rnenwHaE-?w=264&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for potty.\" },\n  \"pretend\": { name: \"Pretend\", gif: \"https://lifeprint.com/asl101/gifs/p/pretend.gif\", description: \"Sign for pretend.\" },\n  \"pretty\": { name: \"Pretty\", gif: \"https://lifeprint.com/asl101/gifs/b/beautiful.gif\", description: \"Sign for pretty.\" },\n  \"puppy\": { name: \"Puppy\", gif: \"https://lifeprint.com/asl101/gifs/p/puppy.gif\", description: \"Sign for puppy.\" },\n  \"puzzle\": { name: \"Puzzle\", gif: \"https://res.cloudinary.com/spiralyze/image/upload/f_auto,w_auto/BabySignLanguage/DictionaryPages/puzzle.svg\", description: \"Sign for puzzle.\" },\n  \"quiet\": { name: \"Quiet\", gif: \"https://lifeprint.com/asl101/gifs-animated/quiet-03.gif\", description: \"Sign for quiet.\" },\n  \"radio\": { name: \"Radio\", gif: \"https://i.pinimg.com/originals/6d/5e/5e/6d5e5e2f78f80e9006293df853a2ba3b.gif\", description: \"Sign for radio.\" },\n  \"rain\": { name: \"Rain\", gif: \"https://lifeprint.com/asl101/gifs/r/rain.gif\", description: \"Sign for rain.\" },\n  \"read\": { name: \"Read\", gif: \"https://lifeprint.com/asl101/gifs/r/read.gif\", description: \"Sign for read.\" },\n  \"red\": { name: \"Red\", gif: \"https://lifeprint.com/asl101/gifs-animated/red.gif\", description: \"Sign for red.\" },\n  \"refrigerator\": { name: \"Refrigerator\", gif: \"https://lifeprint.com/asl101/gifs/r/refrigerator-r-e-f.gif\", description: \"Sign for refrigerator.\" },\n  \"ride\": { name: \"Ride\", gif: \"https://lifeprint.com/asl101/gifs/r/ride.gif\", description: \"Sign for ride.\" },\n  \"room\": { name: \"Room\", gif: \"https://lifeprint.com/asl101/gifs/r/room-box.gif\", description: \"Sign for room.\" },\n  \"sad\": { name: \"Sad\", gif: \"https://lifeprint.com/asl101/gifs/s/sad.gif\", description: \"Sign for sad.\" },\n  \"same\": { name: \"Same\", gif: \"https://lifeprint.com/asl101/gifs/s/same-similar.gif\", description: \"Sign for same.\" },\n  \"say\": { name: \"Say\", gif: \"https://asl.signlanguage.io/words/say/say-in-asl-a0a5e00000a44k0.jpg\", description: \"Sign for say.\" },\n  \"scissors\": { name: \"Scissors\", gif: \"https://i.makeagif.com/media/4-17-2023/pl4M4F.gif\", description: \"Sign for scissors.\" },\n  \"see\": { name: \"See\", gif: \"https://lifeprint.com/asl101/gifs/l/look-at-2.gif\", description: \"Sign for see.\" },\n  \"shhh\": { name: \"Shhh\", gif: \"https://lifeprint.com/asl101/signjpegs/s/shhh.jpg\", description: \"Sign for shhh.\" },\n  \"shirt\": { name: \"Shirt\", gif: \"https://lifeprint.com/asl101/gifs/s/shirt-volunteer.gif\", description: \"Sign for shirt.\" },\n  \"shoe\": { name: \"Shoe\", gif: \"https://media.giphy.com/media/3o7TKC4StpZKa6d2y4/giphy.gif\", description: \"Sign for shoe.\" },\n  \"shower\": { name: \"Shower\", gif: \"https://lifeprint.com/asl101/gifs/s/shower.gif\", description: \"Sign for shower.\" },\n  \"sick\": { name: \"Sick\", gif: \"https://lifeprint.com/asl101/gifs/s/sick.gif\", description: \"Sign for sick.\" },\n  \"sleep\": { name: \"Sleep\", gif: \"https://media4.giphy.com/media/3o7TKnRuBdakLslcaI/200.gif?cid=790b76110d8f185a9713f36dd65a0df801576e01b403c95c&rid=200.gif&ct=g\", description: \"Sign for sleep.\" },\n  \"sleepy\": { name: \"Sleepy\", gif: \"https://th.bing.com/th/id/R.********************************?rik=zdWvzvABcDHTdw&riu=http%3a%2f%2fwww.lifeprint.com%2fasl101%2fgifs-animated%2fsleepy.gif&ehk=zLqDFJMAs2nqG02RbbR6mEMvux4h85JGzls4uwgrePQ%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for sleepy.\" },\n  \"smile\": { name: \"Smile\", gif: \"https://th.bing.com/th/id/OIP.dpce-bMAh-1jorUrPQFW4AHaE-?w=263&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for smile.\" },\n  \"snack\": { name: \"Snack\", gif: \"https://media.giphy.com/media/26ybw1E1GTKzLuKDS/giphy.gif\", description: \"Sign for snack.\" },\n  \"snow\": { name: \"Snow\", gif: \"https://lifeprint.com/asl101/gifs/s/snow.gif\", description: \"Sign for snow.\" },\n  \"stairs\": { name: \"Stairs\", gif: \"https://th.bing.com/th/id/OIP.8BtYhPXXDQHRqodMyyy3HgHaEc?w=288&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for stairs.\" },\n  \"stay\": { name: \"Stay\", gif: \"https://i.pinimg.com/originals/f5/29/8e/f5298eaa46b91cd6de2a32bd76aadffc.gif\", description: \"Sign for stay.\" },\n  \"sticky\": { name: \"Sticky\", gif: \"https://th.bing.com/th/id/OIP.fffIgrX_DBAjxGMkskvTvQHaE-?w=240&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for sticky.\" },\n  \"store\": { name: \"Store\", gif: \"https://th.bing.com/th/id/R.********************************?rik=x7oUPJGckc7QDg&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fstore.gif&ehk=P7beooAyFUst%2bbVtqIqINeQGP0%2bIUlNSPXc1Du5zWfQ%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for store.\" },\n  \"story\": { name: \"Story\", gif: \"https://lifeprint.com/asl101/gifs/s/story.gif\", description: \"Sign for story.\" },\n  \"stuck\": { name: \"Stuck\", gif: \"https://lifeprint.com/asl101/signjpegs/s/stuck.2.jpg\", description: \"Sign for stuck.\" },\n  \"sun\": { name: \"Sun\", gif: \"https://media.giphy.com/media/3o6Zt7merN2zxEtNRK/giphy.gif\", description: \"Sign for sun.\" },\n  \"table\": { name: \"Table\", gif: \"https://lifeprint.com/asl101/gifs/t/table.gif\", description: \"Sign for table.\" },\n  \"talk\": { name: \"Talk\", gif: \"https://lifeprint.com/asl101/gifs/t/talk.gif\", description: \"Sign for talk.\" },\n  \"taste\": { name: \"Taste\", gif: \"https://lifeprint.com/asl101/gifs/t/taste.gif\", description: \"Sign for taste.\" },\n  \"thankyou\": { name: \"Thank You\", gif: \"https://lifeprint.com/asl101/gifs/t/thank-you.gif\", description: \"Sign for thank you.\" },\n  \"that\": { name: \"That\", gif: \"https://i.ytimg.com/vi/81Wr75AFDnQ/maxresdefault.jpg\", description: \"Sign for that.\" },\n  \"there\": { name: \"There\", gif: \"https://lifeprint.com/asl101/gifs-animated/there.gif\", description: \"Sign for there.\" },\n  \"think\": { name: \"Think\", gif: \"https://lifeprint.com/asl101/gifs/t/think.gif\", description: \"Sign for think.\" },\n  \"thirsty\": { name: \"Thirsty\", gif: \"https://media.giphy.com/media/l3vR0sYheBulL1P7W/giphy.gif\", description: \"Sign for thirsty.\" },\n  \"tiger\": { name: \"Tiger\", gif: \"https://lifeprint.com/asl101/gifs/t/tiger.gif\", description: \"Sign for tiger.\" },\n  \"time\": { name: \"Time\", gif: \"https://lifeprint.com/asl101/gifs/t/time-1.gif\", description: \"Sign for time.\" },\n  \"tomorrow\": { name: \"Tomorrow\", gif: \"https://lifeprint.com/asl101/gifs/t/tomorrow.gif\", description: \"Sign for tomorrow.\" },\n  \"tongue\": { name: \"Tongue\", gif: \"https://th.bing.com/th/id/R.********************************?rik=ZJJ2Ixdj0l0b5A&riu=http%3a%2f%2fwww.aslsearch.com%2fsigns%2fimages%2ftongue.jpg&ehk=MxZVUjfqPa3klIauPGpReg%2fYgnJUyIjlxOOvCYYG0hc%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for tongue.\" },\n  \"tooth\": { name: \"Tooth\", gif: \"https://th.bing.com/th/id/R.********************************?rik=ZF%2fsFUXvt5czGA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fsignjpegs%2ft%2fteeth1.jpg&ehk=vI5eDlD4HZWXhK1PQOQz4nA5e6oguHgeXqDo%2fcdcWg4%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for tooth.\" },\n  \"toothbrush\": { name: \"Toothbrush\", gif: \"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia.giphy.com%2fmedia%2fl3vR0Rq2HVL2KHLUI%2fgiphy.gif&ehk=eC0Sq9sHjrrOrkyJvOogQbXVkTOL5OPCeyVymejL0RU%3d\", description: \"Sign for toothbrush.\" },\n  \"touch\": { name: \"Touch\", gif: \"https://th.bing.com/th/id/OIP.imGRfqjCtcHhof6Lc_0QJQHaE-?w=230&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\", description: \"Sign for touch.\" },\n  \"toy\": { name: \"Toy\", gif: \"https://lifeprint.com/asl101/gifs-animated/play-02.gif\", description: \"Sign for toy.\" },\n  \"tree\": { name: \"Tree\", gif: \"https://lifeprint.com/asl101/gifs-animated/tree.gif\", description: \"Sign for tree.\" },\n  \"uncle\": { name: \"Uncle\", gif: \"https://lifeprint.com/asl101/gifs/u/uncle.gif\", description: \"Sign for uncle.\" },\n  \"underwear\": { name: \"Underwear\", gif: \"https://th.bing.com/th/id/OIP.c8g9T_lOhbZWRvKAA12J8wHaEO?pid=ImgDet&w=310&h=177&rs=1\", description: \"Sign for underwear.\" },\n  \"up\": { name: \"Up\", gif: \"https://www.babysignlanguage.com/signs/up.gif\", description: \"Sign for up.\" },\n  \"vacuum\": { name: \"Vacuum\", gif: \"https://www.babysignlanguage.com/signs/vacuum.gif\", description: \"Sign for vacuum.\" },\n  \"wait\": { name: \"Wait\", gif: \"https://lifeprint.com/asl101/gifs/w/wait.gif\", description: \"Sign for wait.\" },\n  \"wake\": { name: \"Wake\", gif: \"https://lifeprint.com/asl101/gifs/w/wake-up.gif\", description: \"Sign for wake.\" },\n  \"water\": { name: \"Water\", gif: \"https://lifeprint.com/asl101/gifs/w/water-2.gif\", description: \"Sign for water.\" },\n  \"wet\": { name: \"Wet\", gif: \"https://www.babysignlanguage.com/signs/wet.gif\", description: \"Sign for wet.\" },\n  \"weus\": { name: \"We/Us\", gif: \"https://lifeprint.com/asl101/gifs/w/we-us.gif\", description: \"Sign for we/us.\" },\n  \"where\": { name: \"Where\", gif: \"https://lifeprint.com/asl101/gifs/w/where.gif\", description: \"Sign for where.\" },\n  \"white\": { name: \"White\", gif: \"https://lifeprint.com/asl101/gifs/w/white.gif\", description: \"Sign for white.\" },\n  \"who\": { name: \"Who\", gif: \"https://lifeprint.com/asl101/gifs/w/who.gif\", description: \"Sign for who.\" },\n  \"why\": { name: \"Why\", gif: \"https://lifeprint.com/asl101/gifs/w/why.gif\", description: \"Sign for why.\" },\n  \"will\": { name: \"Will\", gif: \"https://lifeprint.com/asl101/gifs/f/future.gif\", description: \"Sign for will.\" },\n  \"wolf\": { name: \"Wolf\", gif: \"https://lifeprint.com/asl101/gifs/w/wolf-side-view.gif\", description: \"Sign for wolf.\" },\n  \"yellow\": { name: \"Yellow\", gif: \"https://lifeprint.com/asl101/gifs/y/yellow.gif\", description: \"Sign for yellow.\" },\n  \"yes\": { name: \"Yes\", gif: \"https://media.tenor.com/oYIirlyIih0AAAAC/yes-asl.gif\", description: \"Sign for yes.\" },\n  \"yesterday\": { name: \"Yesterday\", gif: \"https://lifeprint.com/asl101/gifs/y/yesterday.gif\", description: \"Sign for yesterday.\" },\n  \"yourself\": { name: \"Yourself\", gif: \"https://www.lifeprint.com/asl101/gifs/s/self-myself.gif\", description: \"Sign for yourself.\" },\n  \"yucky\": { name: \"Yucky\", gif: \"https://i.pinimg.com/originals/7f/66/7f/7f667f7eeb92c994829dcaf52c5bcf2d.gif\", description: \"Sign for yucky.\" },\n  \"zebra\": { name: \"Zebra\", gif: \"https://lifeprint.com/asl101/gifs/z/zebra-stripes-two-hands.gif\", description: \"Sign for zebra.\" },\n  \"zipper\": { name: \"Zipper\", gif: \"https://th.bing.com/th/id/R.********************************?rik=qPRTVGd2SzUBxw&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fzipper.gif&ehk=IGx68sSokNwU21zu3Z2D%2blmeehKYxpSNhX2VnrvQqYE%3d&risl=&pid=ImgRaw&r=0\", description: \"Sign for zipper.\" }\n};\n\nconst TrainingPage = ({ onBackToHome }) => {\n  // New flash card system state\n  const [currentView, setCurrentView] = useState('levels'); // 'levels' or 'training'\n  const [selectedLevel, setSelectedLevel] = useState(null);\n  const [userProgress, setUserProgress] = useState(() => {\n    // Load progress from localStorage\n    const saved = localStorage.getItem('asl-training-progress');\n    return saved ? JSON.parse(saved) : {};\n  });\n\n  // Legacy state (keeping for backward compatibility during transition)\n  const [currentSign, setCurrentSign] = useState('hello');\n  const [status, setStatus] = useState('');\n  const [isCapturing, setIsCapturing] = useState(false);\n  const [lastRecordingStatus, setLastRecordingStatus] = useState('');\n  // eslint-disable-next-line no-unused-vars\n  const [recordedVideos, setRecordedVideos] = useState([]);\n  const [imgError, setImgError] = useState(false);\n\n  const webcamRef = useRef(null);\n  const autoRecordTimeoutRef = useRef(null);\n  const matchCountRef = useRef(0);\n\n  // Use sign detection hook\n  const {\n    isConnected,\n    prediction,\n    isAIRecording,\n    recordingStatus,\n    signMatched,\n    targetSign,\n    startRecording: startAIRecording,\n    stopRecording: stopAIRecording,\n    startFrameCapture,\n    retryConnection\n  } = useSignDetection();\n\n  const handleSignChange = useCallback((event) => {\n    setCurrentSign(event.target.value);\n    setImgError(false);\n  }, []);\n\n  const startDetection = useCallback(() => {\n    if (!webcamRef.current) {\n      setStatus('Camera not available');\n      return;\n    }\n\n    setIsCapturing(true);\n    startFrameCapture(webcamRef, 100); // Send frame every 100ms\n    setStatus('AI detection started');\n  }, [startFrameCapture]);\n\n  const startManualRecording = useCallback(() => {\n    if (!isConnected) {\n      setStatus('AI backend not connected');\n      return;\n    }\n\n    if (!webcamRef.current) {\n      setStatus('Camera not available');\n      return;\n    }\n\n    if (isAIRecording) {\n      setStatus('Already recording...');\n      return;\n    }\n\n    // Start manual 3-second recording with selected sign name\n    const selectedSignName = signLanguageData[currentSign].name;\n    setStatus(`🎬 Starting 3-second recording for \"${selectedSignName}\"...`);\n    setLastRecordingStatus(`🎬 Recording \"${selectedSignName}\"...`);\n    startAIRecording(selectedSignName, true); // Pass true to immediately start recording session\n\n    // Auto-stop after 3 seconds\n    autoRecordTimeoutRef.current = setTimeout(() => {\n      stopAIRecording();\n      setStatus(`✅ Recording complete! \"${selectedSignName}\" saved to recordings folder with landmark data`);\n      setLastRecordingStatus(`✅ Recording saved: \"${selectedSignName}\" (3 seconds)`);\n    }, 3000);\n\n    // Also start frame capture if not already started\n    if (!isCapturing) {\n      startDetection();\n    }\n  }, [currentSign, isConnected, isCapturing, startDetection, isAIRecording, startAIRecording, stopAIRecording]);\n\n  const stopManualRecording = useCallback(() => {\n    // Stop current recording\n    if (isAIRecording) {\n      stopAIRecording();\n      setLastRecordingStatus(`✅ Recording saved: \"${signLanguageData[currentSign].name}\" (Manual stop)`);\n    }\n    matchCountRef.current = 0;\n    if (autoRecordTimeoutRef.current) {\n      clearTimeout(autoRecordTimeoutRef.current);\n    }\n    setStatus('Manual recording stopped');\n  }, [stopAIRecording, isAIRecording, currentSign]);\n\n  const downloadRecording = (video) => {\n    const a = document.createElement('a');\n    a.href = video.url;\n    a.download = `sign_${video.sign}_${video.timestamp}.webm`;\n    a.click();\n  };\n\n  // Auto-start detection when connected\n  useEffect(() => {\n    if (isConnected && webcamRef.current && !isCapturing) {\n      startDetection();\n    }\n  }, [isConnected, startDetection, isCapturing]);\n\n  // Update last recording status when recordingStatus changes\n  useEffect(() => {\n    if (recordingStatus && recordingStatus.includes('saved')) {\n      setLastRecordingStatus(recordingStatus);\n    }\n  }, [recordingStatus]);\n\n  // Always-on auto-recording logic - records when confidence >= 50%\n  useEffect(() => {\n    if (!prediction || !isConnected) {\n      matchCountRef.current = 0;\n      return;\n    }\n\n    const predictedSign = prediction.sign.toLowerCase();\n    const targetSignLower = signLanguageData[currentSign].name.toLowerCase();\n    const confidence = prediction.confidence;\n\n    // Auto-record when sign matches with >= 50% confidence\n    if (predictedSign === targetSignLower && confidence >= 0.5) {\n      matchCountRef.current += 1;\n\n      // Start recording after 2 consecutive matches to avoid false positives\n      if (matchCountRef.current >= 2 && !isAIRecording) {\n        setStatus(`🎬 Auto-recording \"${signLanguageData[currentSign].name}\"... (${Math.round(confidence * 100)}% confidence)`);\n        startAIRecording(signLanguageData[currentSign].name, false); // Auto-recording doesn't start session immediately\n\n        // Auto-stop recording after 3 seconds\n        autoRecordTimeoutRef.current = setTimeout(() => {\n          stopAIRecording();\n          setStatus(`✅ Auto-recording complete! \"${signLanguageData[currentSign].name}\" saved to recordings folder with landmark data`);\n          setLastRecordingStatus(`✅ Auto-recording saved: \"${signLanguageData[currentSign].name}\" (3 seconds)`);\n          matchCountRef.current = 0;\n        }, 3000);\n      }\n    } else {\n      // Reset match count if sign doesn't match or confidence is too low\n      matchCountRef.current = 0;\n    }\n\n    return () => {\n      if (autoRecordTimeoutRef.current) {\n        clearTimeout(autoRecordTimeoutRef.current);\n      }\n    };\n  }, [prediction, currentSign, isAIRecording, startAIRecording, stopAIRecording, isConnected]);\n\n  // New flash card system handlers\n  const handleLevelSelect = (level) => {\n    setSelectedLevel(level);\n    setCurrentView('training');\n  };\n\n  const handleBackToLevels = () => {\n    setCurrentView('levels');\n    setSelectedLevel(null);\n  };\n\n  const handleProgressUpdate = (level, completed, total) => {\n    const newProgress = {\n      ...userProgress,\n      [level]: { completed, total }\n    };\n    setUserProgress(newProgress);\n    // Save to localStorage\n    localStorage.setItem('asl-training-progress', JSON.stringify(newProgress));\n  };\n\n  // Render new flash card system\n  if (currentView === 'levels') {\n    return (\n      <LevelSelector\n        currentLevel={selectedLevel}\n        userProgress={userProgress}\n        onLevelSelect={handleLevelSelect}\n      />\n    );\n  }\n\n  if (currentView === 'training' && selectedLevel) {\n    return (\n      <FlashCardTraining\n        level={selectedLevel}\n        onBack={handleBackToLevels}\n        userProgress={userProgress}\n        onProgressUpdate={handleProgressUpdate}\n      />\n    );\n  }\n\n  // Legacy training page (fallback - can be removed later)\n  return (\n    <TrainingContainer>\n      <Navigation>\n        <NavContainer>\n          <Logo>\n            <LogoIcon>\n              <Brain size={24} />\n            </LogoIcon>\n            ASL Neural\n          </Logo>\n          <BackButton onClick={onBackToHome}>\n            <ArrowLeft size={18} />\n            Back to Home\n          </BackButton>\n        </NavContainer>\n      </Navigation>\n\n      <MainContent>\n        <div style={{ textAlign: 'center', marginBottom: 'var(--space-12)' }}>\n          <StatusBadge>\n            <Eye size={16} />\n            Neural Vision Active\n          </StatusBadge>\n        </div>\n\n        <PageTitle>AI Training Session</PageTitle>\n        <PageSubtitle>\n          Experience real-time neural network analysis as our AI learns from your sign language practice\n        </PageSubtitle>\n\n        <TopControlsSection>\n          <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-4)', flexWrap: 'wrap', justifyContent: 'center' }}>\n            <ControlButton\n              variant=\"primary\"\n              compact\n              onClick={isAIRecording ? stopManualRecording : startManualRecording}\n            >\n              {isAIRecording ? (\n                <>\n                  <Square size={16} />\n                  Stop Recording\n                </>\n              ) : (\n                <>\n                  <Play size={16} />\n                  Record 3s Video\n                </>\n              )}\n            </ControlButton>\n\n            {lastRecordingStatus && (\n              <RecordingStatus isRecording={isAIRecording}>\n                {lastRecordingStatus}\n              </RecordingStatus>\n            )}\n\n            {!isConnected && (\n              <ControlButton\n                variant=\"retry\"\n                compact\n                onClick={retryConnection}\n              >\n                <RefreshCw size={16} />\n                Retry Connection\n              </ControlButton>\n            )}\n          </div>\n        </TopControlsSection>\n\n        <TrainingGrid>\n          <SignSection>\n            <SectionTitle>\n              <SectionIcon>\n                <Target size={24} />\n              </SectionIcon>\n              Select a Sign\n            </SectionTitle>\n            <SignSelector\n              value={currentSign}\n              onChange={handleSignChange}\n              disabled={isAIRecording}\n            >\n              {Object.keys(signLanguageData).map(signKey => (\n                <option key={signKey} value={signKey}>\n                  {signLanguageData[signKey].name}\n                </option>\n              ))}\n            </SignSelector>\n            <SignDisplay>\n              {!imgError ? (\n                <img\n                  src={signLanguageData[currentSign].gif}\n                  alt={signLanguageData[currentSign].name}\n                  onError={() => setImgError(true)}\n                  style={{ display: imgError ? 'none' : 'block' }}\n                />\n              ) : (\n                <div style={{display: 'flex', fontSize: '3rem', width: '100%', height: '100%', alignItems: 'center', justifyContent: 'center'}}>\n                  📷\n                </div>\n              )}\n            </SignDisplay>\n            <SignName>{signLanguageData[currentSign].name}</SignName>\n            <SignDescription>\n              {signLanguageData[currentSign].description}\n            </SignDescription>\n          </SignSection>\n\n          <CameraSection>\n            <SectionTitle>\n              <SectionIcon>\n                <Camera size={24} />\n              </SectionIcon>\n              Neural Vision Feed\n            </SectionTitle>\n\n            <ConnectionStatus connected={isConnected}>\n              {isConnected ? <Wifi size={16} /> : <WifiOff size={16} />}\n              {isConnected ? 'AI Connected' : 'AI Disconnected'}\n            </ConnectionStatus>\n\n            {prediction && (\n              <PredictionDisplay matched={signMatched} isStale={prediction.isStale}>\n                <PredictionText matched={signMatched} isStale={prediction.isStale}>\n                  Detected: {prediction.sign}\n                  {prediction.isStale && ' (previous)'}\n                </PredictionText>\n                <ConfidenceBar>\n                  <ConfidenceFill confidence={prediction.confidence} />\n                </ConfidenceBar>\n                <div style={{ fontSize: '0.875rem', marginTop: '8px', color: 'var(--text-secondary)' }}>\n                  Confidence: {Math.round(prediction.confidence * 100)}%\n                  {signMatched && targetSign && (\n                    <span style={{ color: 'var(--success-600)', marginLeft: '8px' }}>\n                      ✓ Match! Recording...\n                    </span>\n                  )}\n                  {!isAIRecording && (\n                    <div style={{ color: 'var(--primary-600)', marginTop: '4px' }}>\n                      🎯 Auto-recording active: Perform \"{signLanguageData[currentSign].name}\" sign (≥50% confidence)\n                      <br />\n                      💡 Or click \"Record 3 Seconds\" for manual recording\n                    </div>\n                  )}\n                </div>\n              </PredictionDisplay>\n            )}\n\n            {!prediction && (\n              <PredictionDisplay>\n                <PredictionText>\n                  🎯 Ready to detect \"{signLanguageData[currentSign].name}\"\n                </PredictionText>\n                <div style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>\n                  Auto-recording is active. Perform the sign with ≥50% confidence to trigger recording.\n                </div>\n              </PredictionDisplay>\n            )}\n            <WebcamContainer>\n              <StyledWebcam\n                ref={webcamRef}\n                audio={false}\n                screenshotFormat=\"image/jpeg\"\n                videoConstraints={{\n                  width: 640,\n                  height: 480,\n                  facingMode: \"user\"\n                }}\n              />\n              <RecordingOverlay isRecording={isAIRecording}>\n                {isAIRecording ? (\n                  <>\n                    <div style={{\n                      width: '8px',\n                      height: '8px',\n                      borderRadius: '50%',\n                      backgroundColor: 'white',\n                      marginRight: '4px'\n                    }} />\n                    Recording\n                  </>\n                ) : (\n                  <>\n                    <Eye size={16} />\n                    Ready\n                  </>\n                )}\n              </RecordingOverlay>\n            </WebcamContainer>\n          </CameraSection>\n        </TrainingGrid>\n\n        {(status || recordingStatus) && (\n          <StatusMessage type={(status || recordingStatus).includes('error') ? 'error' : (status || recordingStatus).includes('success') ? 'success' : 'info'}>\n            {recordingStatus || status}\n          </StatusMessage>\n        )}\n\n        {recordedVideos.length > 0 && (\n          <RecordingsSection>\n            <RecordingsTitle>Your Practice Recordings</RecordingsTitle>\n            <RecordingsGrid>\n              {recordedVideos.map((video) => (\n                <RecordingCard key={video.id}>\n                  <RecordingTitle>{video.sign}</RecordingTitle>\n                  <RecordingTime>\n                    {new Date(video.timestamp).toLocaleString()}\n                  </RecordingTime>\n                  <DownloadButton onClick={() => downloadRecording(video)}>\n                    <Download size={16} />\n                    Download\n                  </DownloadButton>\n                </RecordingCard>\n              ))}\n            </RecordingsGrid>\n          </RecordingsSection>\n        )}\n      </MainContent>\n    </TrainingContainer>\n  );\n};\n\nexport default TrainingPage; "], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AAChE,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,cAAc;AACjC,SACEC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,IAAI,EACJC,MAAM,EACNC,QAAQ,EACRC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,OAAO,EACPC,SAAS,QACJ,cAAc;AACrB,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,MAAM,MAAM,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/B,MAAMC,iBAAiB,GAAGrB,MAAM,CAACsB,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAnBIF,iBAAiB;AAqBvB,MAAMG,UAAU,GAAGxB,MAAM,CAACyB,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAXIF,UAAU;AAahB,MAAMG,YAAY,GAAG3B,MAAM,CAACsB,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACM,GAAA,GAXID,YAAY;AAalB,MAAME,IAAI,GAAG7B,MAAM,CAACsB,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GAhBID,IAAI;AAkBV,MAAME,QAAQ,GAAG/B,MAAM,CAACsB,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GAfID,QAAQ;AAiBd,MAAME,UAAU,GAAGjC,MAAM,CAACkC,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GA3BIF,UAAU;AA6BhB,MAAMG,SAAS,GAAGpC,MAAM,CAACqC,EAAE;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAfIF,SAAS;AAiBf,MAAMG,YAAY,GAAGvC,MAAM,CAACwC,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAdIF,YAAY;AAgBlB,MAAMG,WAAW,GAAG1C,MAAM,CAACsB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACqB,GAAA,GAnBID,WAAW;AAqBjB,MAAME,WAAW,GAAG5C,MAAM,CAAC6C,IAAI;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GATIF,WAAW;AAWjB,MAAMG,YAAY,GAAG/C,MAAM,CAACsB,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC0B,GAAA,GAnBID,YAAY;AAqBlB,MAAME,aAAa,GAAGjD,MAAM,CAACsB,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC4B,IAAA,GAzCID,aAAa;AA2CnB,MAAME,YAAY,GAAGnD,MAAM,CAACoD,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAdIF,YAAY;AAgBlB,MAAMG,WAAW,GAAGtD,MAAM,CAACsB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACiC,IAAA,GAfID,WAAW;AAiBjB,MAAME,eAAe,GAAGxD,MAAM,CAACsB,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmC,IAAA,GAnBID,eAAe;AAqBrB,MAAME,YAAY,GAAG1D,MAAM,CAACC,MAAM,CAAC;AACnC;AACA;AACA;AACA,CAAC;AAAC0D,IAAA,GAJID,YAAY;AAMlB,MAAME,gBAAgB,GAAG5D,MAAM,CAACsB,GAAG;AACnC;AACA;AACA;AACA,gBAAgBuC,KAAK,IAAIA,KAAK,CAACC,WAAW,GACtC,kBAAkB,GAClB,mBAAmB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eACeD,KAAK,IAAIA,KAAK,CAACC,WAAW,GAAG,qBAAqB,GAAG,MAAM;AAC1E;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAvBIH,gBAAgB;AAyBtB,MAAMI,WAAW,GAAGhE,MAAM,CAACsB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC2C,IAAA,GArBID,WAAW;AAuBjB,MAAME,YAAY,GAAGlE,MAAM,CAACmE,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAlCIF,YAAY;AAoClB,MAAMG,WAAW,GAAGrE,MAAM,CAACsB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgD,IAAA,GA7BID,WAAW;AA+BjB,MAAME,QAAQ,GAAGvE,MAAM,CAACwE,EAAE;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAVIF,QAAQ;AAYd,MAAMG,eAAe,GAAG1E,MAAM,CAACwC,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmC,IAAA,GAPID,eAAe;AASrB,MAAME,kBAAkB,GAAG5E,MAAM,CAACsB,GAAG;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuD,IAAA,GAnBID,kBAAkB;AAqBxB,MAAME,eAAe,GAAG9E,MAAM,CAACsB,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBuC,KAAK,IAAIA,KAAK,CAACC,WAAW,GAAG,iBAAiB,GAAG,mBAAmB;AACpF,WAAWD,KAAK,IAAIA,KAAK,CAACC,WAAW,GAAG,kBAAkB,GAAG,oBAAoB;AACjF,sBAAsBD,KAAK,IAAIA,KAAK,CAACC,WAAW,GAAG,kBAAkB,GAAG,oBAAoB;AAC5F;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACiB,IAAA,GAjBID,eAAe;AAmBrB,MAAME,aAAa,GAAGhF,MAAM,CAACkC,MAAM;AACnC,gBAAgB2B,KAAK,IAAIA,KAAK,CAACoB,OAAO,KAAK,SAAS,GAC9C,oBAAoB,GACpBpB,KAAK,CAACoB,OAAO,KAAK,OAAO,GACzB,oBAAoB,GACpB,mBAAmB;AACzB,YAAYpB,KAAK,IAAIA,KAAK,CAACoB,OAAO,KAAK,SAAS,IAAIpB,KAAK,CAACoB,OAAO,KAAK,OAAO,GACvE,MAAM,GACN,gCAAgC;AACtC,WAAWpB,KAAK,IAAIA,KAAK,CAACoB,OAAO,KAAK,SAAS,IAAIpB,KAAK,CAACoB,OAAO,KAAK,OAAO,GACtE,OAAO,GACP,qBAAqB;AAC3B,aAAapB,KAAK,IAAIA,KAAK,CAACqB,OAAO,GAC7B,+BAA+B,GAC/B,+BAA+B;AACrC;AACA;AACA,eAAerB,KAAK,IAAIA,KAAK,CAACqB,OAAO,GAAG,QAAQ,GAAG,QAAQ;AAC3D;AACA;AACA,eAAerB,KAAK,IAAIA,KAAK,CAACqB,OAAO,GAAG,OAAO,GAAG,OAAO;AACzD;AACA;AACA;AACA;AACA;AACA;AACA,eAAerB,KAAK,IAAIA,KAAK,CAACqB,OAAO,GAC7B,+BAA+B,GAC/B,+BAA+B;AACvC,iBAAiBrB,KAAK,IAAIA,KAAK,CAACqB,OAAO,GAAG,QAAQ,GAAG,MAAM;AAC3D,iBAAiBrB,KAAK,IAAIA,KAAK,CAACqB,OAAO,GAAG,OAAO,GAAG,OAAO;AAC3D;AACA;AACA,gBAAgBrB,KAAK,IAAIA,KAAK,CAACoB,OAAO,KAAK,SAAS,IAAIpB,KAAK,CAACoB,OAAO,KAAK,OAAO,GAC3E,kBAAkB,GAClB,kBAAkB;AACxB;AACA;AACA;AACA,kBAAkBpB,KAAK,IAAIA,KAAK,CAACoB,OAAO,KAAK,SAAS,IAAIpB,KAAK,CAACoB,OAAO,KAAK,OAAO,GAC3E,kBAAkB,GAClB,kBAAkB;AAC1B,kBAAkBpB,KAAK,IAAIA,KAAK,CAACoB,OAAO,KAAK,SAAS,GAC9C,oBAAoB,GACpBpB,KAAK,CAACoB,OAAO,KAAK,OAAO,GACzB,oBAAoB,GACpB,gBAAgB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiBpB,KAAK,IAAIA,KAAK,CAACqB,OAAO,GAAG,OAAO,GAAG,OAAO;AAC3D;AACA,CAAC;AAACC,IAAA,GA5DIH,aAAa;AA8DnB,MAAMI,aAAa,GAAGpF,MAAM,CAACsB,GAAG;AAChC;AACA;AACA;AACA;AACA,gBAAgBuC,KAAK,IACjBA,KAAK,CAACwB,IAAI,KAAK,SAAS,GAAG,oBAAoB,GAC/CxB,KAAK,CAACwB,IAAI,KAAK,OAAO,GAAG,kBAAkB,GAC3C,oBAAoB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,CACC;AAACC,IAAA,GAhBIF,aAAa;AAkBnB,MAAMG,iBAAiB,GAAGvF,MAAM,CAACsB,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkE,IAAA,GARID,iBAAiB;AAUvB,MAAME,eAAe,GAAGzF,MAAM,CAACwE,EAAE;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkB,IAAA,GAPID,eAAe;AASrB,MAAME,cAAc,GAAG3F,MAAM,CAACsB,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsE,IAAA,GATID,cAAc;AAWpB,MAAME,aAAa,GAAG7F,MAAM,CAACsB,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACwE,IAAA,GAbID,aAAa;AAenB,MAAME,cAAc,GAAG/F,MAAM,CAACwC,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACwD,IAAA,GANID,cAAc;AAQpB,MAAME,aAAa,GAAGjG,MAAM,CAACwC,CAAC;AAC9B;AACA;AACA;AACA,CAAC;AAAC0D,IAAA,GAJID,aAAa;AAMnB,MAAME,cAAc,GAAGnG,MAAM,CAACkC,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkE,IAAA,GAnBID,cAAc;AAqBpB,MAAME,iBAAiB,GAAGrG,MAAM,CAACsB,GAAG;AACpC;AACA,sBAAsBuC,KAAK,IAAI;EAC3B,IAAIA,KAAK,CAACyC,OAAO,EAAE,OAAO,oBAAoB;EAC9C,IAAIzC,KAAK,CAAC0C,OAAO,EAAE,OAAO,oBAAoB;EAC9C,OAAO,qBAAqB;AAC9B,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA,aAAa1C,KAAK,IAAIA,KAAK,CAAC0C,OAAO,GAAG,GAAG,GAAG,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA,IAAI1C,KAAK,IAAIA,KAAK,CAACyC,OAAO,IAAI;AAC9B;AACA;AACA;AACA,GAAG;AACH;AACA,IAAIzC,KAAK,IAAIA,KAAK,CAAC0C,OAAO,IAAI;AAC9B;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAvCIH,iBAAiB;AAyCvB,MAAMI,cAAc,GAAGzG,MAAM,CAACsB,GAAG;AACjC;AACA;AACA,WAAWuC,KAAK,IAAI;EAChB,IAAIA,KAAK,CAACyC,OAAO,EAAE,OAAO,oBAAoB;EAC9C,IAAIzC,KAAK,CAAC0C,OAAO,EAAE,OAAO,oBAAoB;EAC9C,OAAO,qBAAqB;AAC9B,CAAC;AACH;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,IAAA,GAbID,cAAc;AAepB,MAAME,aAAa,GAAG3G,MAAM,CAACsB,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsF,IAAA,GAPID,aAAa;AASnB,MAAME,cAAc,GAAG7G,MAAM,CAACsB,GAAG;AACjC;AACA,gBAAgBuC,KAAK,IAAI;EACrB,IAAIA,KAAK,CAACiD,UAAU,GAAG,GAAG,EAAE,OAAO,oBAAoB;EACvD,IAAIjD,KAAK,CAACiD,UAAU,GAAG,GAAG,EAAE,OAAO,oBAAoB;EACvD,OAAO,kBAAkB;AAC3B,CAAC;AACH,WAAWjD,KAAK,IAAKA,KAAK,CAACiD,UAAU,GAAG,GAAI;AAC5C;AACA,CAAC;AAACC,IAAA,GATIF,cAAc;AAWpB,MAAMG,gBAAgB,GAAGhH,MAAM,CAACsB,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBuC,KAAK,IAAIA,KAAK,CAACoD,SAAS,GAAG,mBAAmB,GAAG,iBAAiB;AAClF,WAAWpD,KAAK,IAAIA,KAAK,CAACoD,SAAS,GAAG,oBAAoB,GAAG,kBAAkB;AAC/E,sBAAsBpD,KAAK,IAAIA,KAAK,CAACoD,SAAS,GAAG,oBAAoB,GAAG,kBAAkB;AAC1F,CAAC;;AAED;AAAAC,IAAA,GAbMF,gBAAgB;AActB,MAAMG,gBAAgB,GAAG;EACvB,IAAI,EAAE;IAAEC,IAAI,EAAE,IAAI;IAAEC,GAAG,EAAE,4CAA4C;IAAEC,WAAW,EAAE;EAAe,CAAC;EACpG,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC5H,UAAU,EAAE;IAAEF,IAAI,EAAE,UAAU;IAAEC,GAAG,EAAE,6DAA6D;IAAEC,WAAW,EAAE;EAAqB,CAAC;EACvI,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,mDAAmD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC9G,WAAW,EAAE;IAAEF,IAAI,EAAE,WAAW;IAAEC,GAAG,EAAE,mDAAmD;IAAEC,WAAW,EAAE;EAAsB,CAAC;EAChI,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAmB,CAAC;EAC/H,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,wDAAwD;IAAEC,WAAW,EAAE;EAAoB,CAAC;EAC/H,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC5H,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC7J,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8EAA8E;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5I,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnK,UAAU,EAAE;IAAEF,IAAI,EAAE,UAAU;IAAEC,GAAG,EAAE,kDAAkD;IAAEC,WAAW,EAAE;EAAqB,CAAC;EAC5H,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,mKAAmK;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC9N,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAoB,CAAC;EAClI,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACzH;EACA,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC1G,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,iDAAiD;IAAEC,WAAW,EAAE;EAAoB,CAAC;EACxH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACtK;EACA,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACpH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+LAA+L;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChQ,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAChK,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC9G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,mDAAmD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACjH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACzH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,iDAAiD;IAAEC,WAAW,EAAE;EAAoB,CAAC;EACxH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,8DAA8D;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACzH,aAAa,EAAE;IAAEF,IAAI,EAAE,eAAe;IAAEC,GAAG,EAAE,0DAA0D;IAAEC,WAAW,EAAE;EAA0B,CAAC;EACjJ,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC7J,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAmB,CAAC;EAC/H,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,uDAAuD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAClH,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,8NAA8N;IAAEC,WAAW,EAAE;EAAmB,CAAC;EAClS,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnK,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,WAAW,EAAE;IAAEF,IAAI,EAAE,WAAW;IAAEC,GAAG,EAAE,kOAAkO;IAAEC,WAAW,EAAE;EAAsB,CAAC;EAC/S,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,4DAA4D;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC7H,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,4RAA4R;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC7V,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACpH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,8EAA8E;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC/I,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnK,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACpH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,uDAAuD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAClH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC7J,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,oDAAoD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAClH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACtH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,0MAA0M;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC3Q,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,8EAA8E;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC/I,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,2NAA2N;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACtR,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,qMAAqM;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACnQ,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,uDAAuD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EAC3H,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAChK,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACtK,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,qDAAqD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACtH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAChK,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC7J,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,yNAAyN;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACvR,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,sDAAsD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACjH,UAAU,EAAE;IAAEF,IAAI,EAAE,UAAU;IAAEC,GAAG,EAAE,yDAAyD;IAAEC,WAAW,EAAE;EAAqB,CAAC;EACnI,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC/G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,sDAAsD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACvH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACzG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,wNAAwN;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACtR,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8EAA8E;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5I,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAChK,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,uDAAuD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACrH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,6NAA6N;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC3R,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACpH,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,yNAAyN;IAAEC,WAAW,EAAE;EAAmB,CAAC;EAC7R,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,oDAAoD;IAAEC,WAAW,EAAE;EAAoB,CAAC;EAC3H,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8EAA8E;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5I,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAChK,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,4DAA4D;IAAEC,WAAW,EAAE;EAAmB,CAAC;EAChI,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8EAA8E;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5I,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,aAAa,EAAE;IAAEF,IAAI,EAAE,cAAc;IAAEC,GAAG,EAAE,0DAA0D;IAAEC,WAAW,EAAE;EAAyB,CAAC;EAC/I,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACzH,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,8NAA8N;IAAEC,WAAW,EAAE;EAAoB,CAAC;EACrS,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,iDAAiD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,qDAAqD;IAAEC,WAAW,EAAE;EAAoB,CAAC;EAC5H,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,wNAAwN;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACtR,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,+DAA+D;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC7H,aAAa,EAAE;IAAEF,IAAI,EAAE,cAAc;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAyB,CAAC;EACpI,IAAI,EAAE;IAAEF,IAAI,EAAE,IAAI;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAe,CAAC;EACnH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,kDAAkD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnH,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,qDAAqD;IAAEC,WAAW,EAAE;EAAoB,CAAC;EAC5H,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAoB,CAAC;EACzK,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+NAA+N;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChS,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,8EAA8E;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC/I,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,4DAA4D;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC1H,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,qIAAqI;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACtM,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC7J,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACzH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,yNAAyN;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACvR,QAAQ,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,iDAAiD;IAAEC,WAAW,EAAE;EAAoB,CAAC;EACvH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8NAA8N;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5R,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,0DAA0D;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACxH,YAAY,EAAE;IAAEF,IAAI,EAAE,YAAY;IAAEC,GAAG,EAAE,gOAAgO;IAAEC,WAAW,EAAE;EAAuB,CAAC;EAChT,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,4DAA4D;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC7H,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,mIAAmI;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC9L,SAAS,EAAE;IAAEF,IAAI,EAAE,WAAW;IAAEC,GAAG,EAAE,mDAAmD;IAAEC,WAAW,EAAE;EAAsB,CAAC;EAC9H,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,qOAAqO;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACnS,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC5H,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,4DAA4D;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACvH,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAmB,CAAC;EAC/H,UAAU,EAAE;IAAEF,IAAI,EAAE,WAAW;IAAEC,GAAG,EAAE,4DAA4D;IAAEC,WAAW,EAAE;EAAsB,CAAC;EACxI,IAAI,EAAE;IAAEF,IAAI,EAAE,IAAI;IAAEC,GAAG,EAAE,4CAA4C;IAAEC,WAAW,EAAE;EAAe,CAAC;EACpG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,kDAAkD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACtH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,qDAAqD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACnH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8BAA8B;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5F,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,uDAAuD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACxH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,oIAAoI;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACrM,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAChK,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACtK,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8NAA8N;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5R,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,qDAAqD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACnH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAChK,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,QAAQ,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAoB,CAAC;EACxK,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,wDAAwD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EAC5H,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,yNAAyN;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACvR,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,8DAA8D;IAAEC,WAAW,EAAE;EAAoB,CAAC;EACrI,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,sDAAsD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACvH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACpH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,IAAI,EAAE;IAAEF,IAAI,EAAE,IAAI;IAAEC,GAAG,EAAE,uDAAuD;IAAEC,WAAW,EAAE;EAAe,CAAC;EAC/G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,sDAAsD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACpH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,2OAA2O;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACtS,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAChK,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,IAAI,EAAE;IAAEF,IAAI,EAAE,IAAI;IAAEC,GAAG,EAAE,iDAAiD;IAAEC,WAAW,EAAE;EAAe,CAAC;EACzG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAChK,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACpH,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,iDAAiD;IAAEC,WAAW,EAAE;EAAoB,CAAC;EACxH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,iDAAiD;IAAEC,WAAW,EAAE;EAAoB,CAAC;EACxH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,kDAAkD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACtH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACpH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,8DAA8D;IAAEC,WAAW,EAAE;EAAmB,CAAC;EAClI,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gGAAgG;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACpK,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAChK,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnK,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,iDAAiD;IAAEC,WAAW,EAAE;EAAoB,CAAC;EACxH,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,mDAAmD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACvH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,6GAA6G;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACjL,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,yDAAyD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC1H,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,8EAA8E;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC/I,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,oDAAoD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC/G,cAAc,EAAE;IAAEF,IAAI,EAAE,cAAc;IAAEC,GAAG,EAAE,4DAA4D;IAAEC,WAAW,EAAE;EAAyB,CAAC;EAClJ,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,kDAAkD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAChH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,sDAAsD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACpH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,sEAAsE;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACjI,UAAU,EAAE;IAAEF,IAAI,EAAE,UAAU;IAAEC,GAAG,EAAE,mDAAmD;IAAEC,WAAW,EAAE;EAAqB,CAAC;EAC7H,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,mDAAmD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC9G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,mDAAmD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACjH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,yDAAyD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC1H,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,4DAA4D;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC1H,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACpH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,iIAAiI;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAClM,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,kOAAkO;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACtS,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnK,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC5H,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACtK,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8EAA8E;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5I,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACtK,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,2NAA2N;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC5R,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,sDAAsD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACvH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,4DAA4D;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACvH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,UAAU,EAAE;IAAEF,IAAI,EAAE,WAAW;IAAEC,GAAG,EAAE,mDAAmD;IAAEC,WAAW,EAAE;EAAsB,CAAC;EAC/H,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,sDAAsD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACpH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,sDAAsD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACvH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAoB,CAAC;EAClI,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC9G,UAAU,EAAE;IAAEF,IAAI,EAAE,UAAU;IAAEC,GAAG,EAAE,kDAAkD;IAAEC,WAAW,EAAE;EAAqB,CAAC;EAC5H,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,4NAA4N;IAAEC,WAAW,EAAE;EAAmB,CAAC;EAChS,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,kOAAkO;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnS,YAAY,EAAE;IAAEF,IAAI,EAAE,YAAY;IAAEC,GAAG,EAAE,uMAAuM;IAAEC,WAAW,EAAE;EAAuB,CAAC;EACvR,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,kGAAkG;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnK,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,wDAAwD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACnH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,qDAAqD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACnH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,WAAW,EAAE;IAAEF,IAAI,EAAE,WAAW;IAAEC,GAAG,EAAE,sFAAsF;IAAEC,WAAW,EAAE;EAAsB,CAAC;EACnK,IAAI,EAAE;IAAEF,IAAI,EAAE,IAAI;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAe,CAAC;EACvG,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,mDAAmD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACvH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC5G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,iDAAiD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,iDAAiD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAClH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,MAAM,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC/G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAChH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACxG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC9G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,wDAAwD;IAAEC,WAAW,EAAE;EAAiB,CAAC;EACtH,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACpH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,sDAAsD;IAAEC,WAAW,EAAE;EAAgB,CAAC;EACjH,WAAW,EAAE;IAAEF,IAAI,EAAE,WAAW;IAAEC,GAAG,EAAE,mDAAmD;IAAEC,WAAW,EAAE;EAAsB,CAAC;EAChI,UAAU,EAAE;IAAEF,IAAI,EAAE,UAAU;IAAEC,GAAG,EAAE,yDAAyD;IAAEC,WAAW,EAAE;EAAqB,CAAC;EACnI,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,8EAA8E;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAC/I,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,iEAAiE;IAAEC,WAAW,EAAE;EAAkB,CAAC;EAClI,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,0NAA0N;IAAEC,WAAW,EAAE;EAAmB;AAC/R,CAAC;AAED,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACzC;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG/H,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACgI,aAAa,EAAEC,gBAAgB,CAAC,GAAGjI,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACkI,YAAY,EAAEC,eAAe,CAAC,GAAGnI,QAAQ,CAAC,MAAM;IACrD;IACA,MAAMoI,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;IAC3D,OAAOF,KAAK,GAAGG,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC,GAAG,CAAC,CAAC;EACvC,CAAC,CAAC;;EAEF;EACA,MAAM,CAACK,WAAW,EAAEC,cAAc,CAAC,GAAG1I,QAAQ,CAAC,OAAO,CAAC;EACvD,MAAM,CAAC2I,MAAM,EAAEC,SAAS,CAAC,GAAG5I,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC6I,WAAW,EAAEC,cAAc,CAAC,GAAG9I,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+I,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhJ,QAAQ,CAAC,EAAE,CAAC;EAClE;EACA,MAAM,CAACiJ,cAAc,EAAEC,iBAAiB,CAAC,GAAGlJ,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACmJ,QAAQ,EAAEC,WAAW,CAAC,GAAGpJ,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAMqJ,SAAS,GAAGpJ,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMqJ,oBAAoB,GAAGrJ,MAAM,CAAC,IAAI,CAAC;EACzC,MAAMsJ,aAAa,GAAGtJ,MAAM,CAAC,CAAC,CAAC;;EAE/B;EACA,MAAM;IACJuJ,WAAW;IACXC,UAAU;IACVC,aAAa;IACbC,eAAe;IACfC,WAAW;IACXC,UAAU;IACVC,cAAc,EAAEC,gBAAgB;IAChCC,aAAa,EAAEC,eAAe;IAC9BC,iBAAiB;IACjBC;EACF,CAAC,GAAGlJ,gBAAgB,CAAC,CAAC;EAEtB,MAAMmJ,gBAAgB,GAAGlK,WAAW,CAAEmK,KAAK,IAAK;IAC9C3B,cAAc,CAAC2B,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;IAClCnB,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMoB,cAAc,GAAGtK,WAAW,CAAC,MAAM;IACvC,IAAI,CAACmJ,SAAS,CAACoB,OAAO,EAAE;MACtB7B,SAAS,CAAC,sBAAsB,CAAC;MACjC;IACF;IAEAE,cAAc,CAAC,IAAI,CAAC;IACpBoB,iBAAiB,CAACb,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC;IACnCT,SAAS,CAAC,sBAAsB,CAAC;EACnC,CAAC,EAAE,CAACsB,iBAAiB,CAAC,CAAC;EAEvB,MAAMQ,oBAAoB,GAAGxK,WAAW,CAAC,MAAM;IAC7C,IAAI,CAACsJ,WAAW,EAAE;MAChBZ,SAAS,CAAC,0BAA0B,CAAC;MACrC;IACF;IAEA,IAAI,CAACS,SAAS,CAACoB,OAAO,EAAE;MACtB7B,SAAS,CAAC,sBAAsB,CAAC;MACjC;IACF;IAEA,IAAIc,aAAa,EAAE;MACjBd,SAAS,CAAC,sBAAsB,CAAC;MACjC;IACF;;IAEA;IACA,MAAM+B,gBAAgB,GAAGpD,gBAAgB,CAACkB,WAAW,CAAC,CAACjB,IAAI;IAC3DoB,SAAS,CAAC,uCAAuC+B,gBAAgB,MAAM,CAAC;IACxE3B,sBAAsB,CAAC,iBAAiB2B,gBAAgB,MAAM,CAAC;IAC/DZ,gBAAgB,CAACY,gBAAgB,EAAE,IAAI,CAAC,CAAC,CAAC;;IAE1C;IACArB,oBAAoB,CAACmB,OAAO,GAAGG,UAAU,CAAC,MAAM;MAC9CX,eAAe,CAAC,CAAC;MACjBrB,SAAS,CAAC,0BAA0B+B,gBAAgB,iDAAiD,CAAC;MACtG3B,sBAAsB,CAAC,uBAAuB2B,gBAAgB,eAAe,CAAC;IAChF,CAAC,EAAE,IAAI,CAAC;;IAER;IACA,IAAI,CAAC9B,WAAW,EAAE;MAChB2B,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAC/B,WAAW,EAAEe,WAAW,EAAEX,WAAW,EAAE2B,cAAc,EAAEd,aAAa,EAAEK,gBAAgB,EAAEE,eAAe,CAAC,CAAC;EAE7G,MAAMY,mBAAmB,GAAG3K,WAAW,CAAC,MAAM;IAC5C;IACA,IAAIwJ,aAAa,EAAE;MACjBO,eAAe,CAAC,CAAC;MACjBjB,sBAAsB,CAAC,uBAAuBzB,gBAAgB,CAACkB,WAAW,CAAC,CAACjB,IAAI,iBAAiB,CAAC;IACpG;IACA+B,aAAa,CAACkB,OAAO,GAAG,CAAC;IACzB,IAAInB,oBAAoB,CAACmB,OAAO,EAAE;MAChCK,YAAY,CAACxB,oBAAoB,CAACmB,OAAO,CAAC;IAC5C;IACA7B,SAAS,CAAC,0BAA0B,CAAC;EACvC,CAAC,EAAE,CAACqB,eAAe,EAAEP,aAAa,EAAEjB,WAAW,CAAC,CAAC;EAEjD,MAAMsC,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMC,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGJ,KAAK,CAACK,GAAG;IAClBJ,CAAC,CAACK,QAAQ,GAAG,QAAQN,KAAK,CAACO,IAAI,IAAIP,KAAK,CAACQ,SAAS,OAAO;IACzDP,CAAC,CAACQ,KAAK,CAAC,CAAC;EACX,CAAC;;EAED;EACAtL,SAAS,CAAC,MAAM;IACd,IAAIqJ,WAAW,IAAIH,SAAS,CAACoB,OAAO,IAAI,CAAC5B,WAAW,EAAE;MACpD2B,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAChB,WAAW,EAAEgB,cAAc,EAAE3B,WAAW,CAAC,CAAC;;EAE9C;EACA1I,SAAS,CAAC,MAAM;IACd,IAAIwJ,eAAe,IAAIA,eAAe,CAAC+B,QAAQ,CAAC,OAAO,CAAC,EAAE;MACxD1C,sBAAsB,CAACW,eAAe,CAAC;IACzC;EACF,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;;EAErB;EACAxJ,SAAS,CAAC,MAAM;IACd,IAAI,CAACsJ,UAAU,IAAI,CAACD,WAAW,EAAE;MAC/BD,aAAa,CAACkB,OAAO,GAAG,CAAC;MACzB;IACF;IAEA,MAAMkB,aAAa,GAAGlC,UAAU,CAAC8B,IAAI,CAACK,WAAW,CAAC,CAAC;IACnD,MAAMC,eAAe,GAAGtE,gBAAgB,CAACkB,WAAW,CAAC,CAACjB,IAAI,CAACoE,WAAW,CAAC,CAAC;IACxE,MAAM1E,UAAU,GAAGuC,UAAU,CAACvC,UAAU;;IAExC;IACA,IAAIyE,aAAa,KAAKE,eAAe,IAAI3E,UAAU,IAAI,GAAG,EAAE;MAC1DqC,aAAa,CAACkB,OAAO,IAAI,CAAC;;MAE1B;MACA,IAAIlB,aAAa,CAACkB,OAAO,IAAI,CAAC,IAAI,CAACf,aAAa,EAAE;QAChDd,SAAS,CAAC,sBAAsBrB,gBAAgB,CAACkB,WAAW,CAAC,CAACjB,IAAI,SAASsE,IAAI,CAACC,KAAK,CAAC7E,UAAU,GAAG,GAAG,CAAC,eAAe,CAAC;QACvH6C,gBAAgB,CAACxC,gBAAgB,CAACkB,WAAW,CAAC,CAACjB,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;;QAE7D;QACA8B,oBAAoB,CAACmB,OAAO,GAAGG,UAAU,CAAC,MAAM;UAC9CX,eAAe,CAAC,CAAC;UACjBrB,SAAS,CAAC,+BAA+BrB,gBAAgB,CAACkB,WAAW,CAAC,CAACjB,IAAI,iDAAiD,CAAC;UAC7HwB,sBAAsB,CAAC,4BAA4BzB,gBAAgB,CAACkB,WAAW,CAAC,CAACjB,IAAI,eAAe,CAAC;UACrG+B,aAAa,CAACkB,OAAO,GAAG,CAAC;QAC3B,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,MAAM;MACL;MACAlB,aAAa,CAACkB,OAAO,GAAG,CAAC;IAC3B;IAEA,OAAO,MAAM;MACX,IAAInB,oBAAoB,CAACmB,OAAO,EAAE;QAChCK,YAAY,CAACxB,oBAAoB,CAACmB,OAAO,CAAC;MAC5C;IACF,CAAC;EACH,CAAC,EAAE,CAAChB,UAAU,EAAEhB,WAAW,EAAEiB,aAAa,EAAEK,gBAAgB,EAAEE,eAAe,EAAET,WAAW,CAAC,CAAC;;EAE5F;EACA,MAAMwC,iBAAiB,GAAIC,KAAK,IAAK;IACnChE,gBAAgB,CAACgE,KAAK,CAAC;IACvBlE,cAAc,CAAC,UAAU,CAAC;EAC5B,CAAC;EAED,MAAMmE,kBAAkB,GAAGA,CAAA,KAAM;IAC/BnE,cAAc,CAAC,QAAQ,CAAC;IACxBE,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMkE,oBAAoB,GAAGA,CAACF,KAAK,EAAEG,SAAS,EAAEC,KAAK,KAAK;IACxD,MAAMC,WAAW,GAAG;MAClB,GAAGpE,YAAY;MACf,CAAC+D,KAAK,GAAG;QAAEG,SAAS;QAAEC;MAAM;IAC9B,CAAC;IACDlE,eAAe,CAACmE,WAAW,CAAC;IAC5B;IACAjE,YAAY,CAACkE,OAAO,CAAC,uBAAuB,EAAEhE,IAAI,CAACiE,SAAS,CAACF,WAAW,CAAC,CAAC;EAC5E,CAAC;;EAED;EACA,IAAIxE,WAAW,KAAK,QAAQ,EAAE;IAC5B,oBACExG,OAAA,CAACJ,aAAa;MACZuL,YAAY,EAAEzE,aAAc;MAC5BE,YAAY,EAAEA,YAAa;MAC3BwE,aAAa,EAAEV;IAAkB;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC;EAEN;EAEA,IAAIhF,WAAW,KAAK,UAAU,IAAIE,aAAa,EAAE;IAC/C,oBACE1G,OAAA,CAACH,iBAAiB;MAChB8K,KAAK,EAAEjE,aAAc;MACrB+E,MAAM,EAAEb,kBAAmB;MAC3BhE,YAAY,EAAEA,YAAa;MAC3B8E,gBAAgB,EAAEb;IAAqB;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC;EAEN;;EAEA;EACA,oBACExL,OAAA,CAACG,iBAAiB;IAAAwL,QAAA,gBAChB3L,OAAA,CAACM,UAAU;MAAAqL,QAAA,eACT3L,OAAA,CAACS,YAAY;QAAAkL,QAAA,gBACX3L,OAAA,CAACW,IAAI;UAAAgL,QAAA,gBACH3L,OAAA,CAACa,QAAQ;YAAA8K,QAAA,eACP3L,OAAA,CAAChB,KAAK;cAAC4M,IAAI,EAAE;YAAG;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,cAEb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPxL,OAAA,CAACe,UAAU;UAAC8K,OAAO,EAAEvF,YAAa;UAAAqF,QAAA,gBAChC3L,OAAA,CAACd,SAAS;YAAC0M,IAAI,EAAE;UAAG;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEzB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEbxL,OAAA,CAAC0B,WAAW;MAAAiK,QAAA,gBACV3L,OAAA;QAAK8L,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAkB,CAAE;QAAAL,QAAA,eACnE3L,OAAA,CAACwB,WAAW;UAAAmK,QAAA,gBACV3L,OAAA,CAACV,GAAG;YAACsM,IAAI,EAAE;UAAG;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,wBAEnB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eAENxL,OAAA,CAACkB,SAAS;QAAAyK,QAAA,EAAC;MAAmB;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eAC1CxL,OAAA,CAACqB,YAAY;QAAAsK,QAAA,EAAC;MAEd;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eAEfxL,OAAA,CAAC0D,kBAAkB;QAAAiI,QAAA,eACjB3L,OAAA;UAAK8L,KAAK,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE,gBAAgB;YAAEC,QAAQ,EAAE,MAAM;YAAEC,cAAc,EAAE;UAAS,CAAE;UAAAV,QAAA,gBACvH3L,OAAA,CAAC8D,aAAa;YACZC,OAAO,EAAC,SAAS;YACjBC,OAAO;YACP6H,OAAO,EAAEzD,aAAa,GAAGmB,mBAAmB,GAAGH,oBAAqB;YAAAuC,QAAA,EAEnEvD,aAAa,gBACZpI,OAAA,CAAAE,SAAA;cAAAyL,QAAA,gBACE3L,OAAA,CAACZ,MAAM;gBAACwM,IAAI,EAAE;cAAG;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,kBAEtB;YAAA,eAAE,CAAC,gBAEHxL,OAAA,CAAAE,SAAA;cAAAyL,QAAA,gBACE3L,OAAA,CAACb,IAAI;gBAACyM,IAAI,EAAE;cAAG;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAEpB;YAAA,eAAE;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACY,CAAC,EAEf/D,mBAAmB,iBAClBzH,OAAA,CAAC4D,eAAe;YAAChB,WAAW,EAAEwF,aAAc;YAAAuD,QAAA,EACzClE;UAAmB;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAClB,EAEA,CAACtD,WAAW,iBACXlI,OAAA,CAAC8D,aAAa;YACZC,OAAO,EAAC,OAAO;YACfC,OAAO;YACP6H,OAAO,EAAEhD,eAAgB;YAAA8C,QAAA,gBAEzB3L,OAAA,CAACN,SAAS;cAACkM,IAAI,EAAE;YAAG;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,oBAEzB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAChB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAErBxL,OAAA,CAAC6B,YAAY;QAAA8J,QAAA,gBACX3L,OAAA,CAAC8C,WAAW;UAAA6I,QAAA,gBACV3L,OAAA,CAACiC,YAAY;YAAA0J,QAAA,gBACX3L,OAAA,CAACoC,WAAW;cAAAuJ,QAAA,eACV3L,OAAA,CAACT,MAAM;gBAACqM,IAAI,EAAE;cAAG;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,iBAEhB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACfxL,OAAA,CAACgD,YAAY;YACXiG,KAAK,EAAE9B,WAAY;YACnBmF,QAAQ,EAAExD,gBAAiB;YAC3ByD,QAAQ,EAAEnE,aAAc;YAAAuD,QAAA,EAEvBa,MAAM,CAACC,IAAI,CAACxG,gBAAgB,CAAC,CAACyG,GAAG,CAACC,OAAO,iBACxC3M,OAAA;cAAsBiJ,KAAK,EAAE0D,OAAQ;cAAAhB,QAAA,EAClC1F,gBAAgB,CAAC0G,OAAO,CAAC,CAACzG;YAAI,GADpByG,OAAO;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEZ,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,eACfxL,OAAA,CAACmD,WAAW;YAAAwI,QAAA,EACT,CAAC9D,QAAQ,gBACR7H,OAAA;cACE4M,GAAG,EAAE3G,gBAAgB,CAACkB,WAAW,CAAC,CAAChB,GAAI;cACvC0G,GAAG,EAAE5G,gBAAgB,CAACkB,WAAW,CAAC,CAACjB,IAAK;cACxC4G,OAAO,EAAEA,CAAA,KAAMhF,WAAW,CAAC,IAAI,CAAE;cACjCgE,KAAK,EAAE;gBAAEG,OAAO,EAAEpE,QAAQ,GAAG,MAAM,GAAG;cAAQ;YAAE;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,gBAEFxL,OAAA;cAAK8L,KAAK,EAAE;gBAACG,OAAO,EAAE,MAAM;gBAAEc,QAAQ,EAAE,MAAM;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,MAAM,EAAE,MAAM;gBAAEf,UAAU,EAAE,QAAQ;gBAAEG,cAAc,EAAE;cAAQ,CAAE;cAAAV,QAAA,EAAC;YAEhI;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,eACdxL,OAAA,CAACqD,QAAQ;YAAAsI,QAAA,EAAE1F,gBAAgB,CAACkB,WAAW,CAAC,CAACjB;UAAI;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACzDxL,OAAA,CAACwD,eAAe;YAAAmI,QAAA,EACb1F,gBAAgB,CAACkB,WAAW,CAAC,CAACf;UAAW;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAEdxL,OAAA,CAAC+B,aAAa;UAAA4J,QAAA,gBACZ3L,OAAA,CAACiC,YAAY;YAAA0J,QAAA,gBACX3L,OAAA,CAACoC,WAAW;cAAAuJ,QAAA,eACV3L,OAAA,CAACf,MAAM;gBAAC2M,IAAI,EAAE;cAAG;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,sBAEhB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eAEfxL,OAAA,CAAC8F,gBAAgB;YAACC,SAAS,EAAEmC,WAAY;YAAAyD,QAAA,GACtCzD,WAAW,gBAAGlI,OAAA,CAACR,IAAI;cAACoM,IAAI,EAAE;YAAG;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGxL,OAAA,CAACP,OAAO;cAACmM,IAAI,EAAE;YAAG;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACxDtD,WAAW,GAAG,cAAc,GAAG,iBAAiB;UAAA;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,EAElBrD,UAAU,iBACTnI,OAAA,CAACmF,iBAAiB;YAACC,OAAO,EAAEkD,WAAY;YAACjD,OAAO,EAAE8C,UAAU,CAAC9C,OAAQ;YAAAsG,QAAA,gBACnE3L,OAAA,CAACuF,cAAc;cAACH,OAAO,EAAEkD,WAAY;cAACjD,OAAO,EAAE8C,UAAU,CAAC9C,OAAQ;cAAAsG,QAAA,GAAC,YACvD,EAACxD,UAAU,CAAC8B,IAAI,EACzB9B,UAAU,CAAC9C,OAAO,IAAI,aAAa;YAAA;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACjBxL,OAAA,CAACyF,aAAa;cAAAkG,QAAA,eACZ3L,OAAA,CAAC2F,cAAc;gBAACC,UAAU,EAAEuC,UAAU,CAACvC;cAAW;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eAChBxL,OAAA;cAAK8L,KAAK,EAAE;gBAAEiB,QAAQ,EAAE,UAAU;gBAAEG,SAAS,EAAE,KAAK;gBAAEC,KAAK,EAAE;cAAwB,CAAE;cAAAxB,QAAA,GAAC,cAC1E,EAACnB,IAAI,CAACC,KAAK,CAACtC,UAAU,CAACvC,UAAU,GAAG,GAAG,CAAC,EAAC,GACrD,EAAC0C,WAAW,IAAIC,UAAU,iBACxBvI,OAAA;gBAAM8L,KAAK,EAAE;kBAAEqB,KAAK,EAAE,oBAAoB;kBAAEC,UAAU,EAAE;gBAAM,CAAE;gBAAAzB,QAAA,EAAC;cAEjE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP,EACA,CAACpD,aAAa,iBACbpI,OAAA;gBAAK8L,KAAK,EAAE;kBAAEqB,KAAK,EAAE,oBAAoB;kBAAED,SAAS,EAAE;gBAAM,CAAE;gBAAAvB,QAAA,GAAC,gDAC1B,EAAC1F,gBAAgB,CAACkB,WAAW,CAAC,CAACjB,IAAI,EAAC,gCACvE,eAAAlG,OAAA;kBAAAqL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,mEAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACW,CACpB,EAEA,CAACrD,UAAU,iBACVnI,OAAA,CAACmF,iBAAiB;YAAAwG,QAAA,gBAChB3L,OAAA,CAACuF,cAAc;cAAAoG,QAAA,GAAC,iCACM,EAAC1F,gBAAgB,CAACkB,WAAW,CAAC,CAACjB,IAAI,EAAC,IAC1D;YAAA;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAgB,CAAC,eACjBxL,OAAA;cAAK8L,KAAK,EAAE;gBAAEiB,QAAQ,EAAE,UAAU;gBAAEI,KAAK,EAAE;cAAwB,CAAE;cAAAxB,QAAA,EAAC;YAEtE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACW,CACpB,eACDxL,OAAA,CAACsC,eAAe;YAAAqJ,QAAA,gBACd3L,OAAA,CAACwC,YAAY;cACX6K,GAAG,EAAEtF,SAAU;cACfuF,KAAK,EAAE,KAAM;cACbC,gBAAgB,EAAC,YAAY;cAC7BC,gBAAgB,EAAE;gBAChBR,KAAK,EAAE,GAAG;gBACVC,MAAM,EAAE,GAAG;gBACXQ,UAAU,EAAE;cACd;YAAE;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFxL,OAAA,CAAC0C,gBAAgB;cAACE,WAAW,EAAEwF,aAAc;cAAAuD,QAAA,EAC1CvD,aAAa,gBACZpI,OAAA,CAAAE,SAAA;gBAAAyL,QAAA,gBACE3L,OAAA;kBAAK8L,KAAK,EAAE;oBACVkB,KAAK,EAAE,KAAK;oBACZC,MAAM,EAAE,KAAK;oBACbS,YAAY,EAAE,KAAK;oBACnBC,eAAe,EAAE,OAAO;oBACxBC,WAAW,EAAE;kBACf;gBAAE;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,aAEP;cAAA,eAAE,CAAC,gBAEHxL,OAAA,CAAAE,SAAA;gBAAAyL,QAAA,gBACE3L,OAAA,CAACV,GAAG;kBAACsM,IAAI,EAAE;gBAAG;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,SAEnB;cAAA,eAAE;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EAEd,CAACnE,MAAM,IAAIgB,eAAe,kBACzBrI,OAAA,CAACkE,aAAa;QAACC,IAAI,EAAE,CAACkD,MAAM,IAAIgB,eAAe,EAAE+B,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG,CAAC/C,MAAM,IAAIgB,eAAe,EAAE+B,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,MAAO;QAAAuB,QAAA,EACjJtD,eAAe,IAAIhB;MAAM;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAChB,EAEA7D,cAAc,CAACkG,MAAM,GAAG,CAAC,iBACxB7N,OAAA,CAACqE,iBAAiB;QAAAsH,QAAA,gBAChB3L,OAAA,CAACuE,eAAe;UAAAoH,QAAA,EAAC;QAAwB;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAiB,CAAC,eAC3DxL,OAAA,CAACyE,cAAc;UAAAkH,QAAA,EACZhE,cAAc,CAAC+E,GAAG,CAAEhD,KAAK,iBACxB1J,OAAA,CAAC2E,aAAa;YAAAgH,QAAA,gBACZ3L,OAAA,CAAC6E,cAAc;cAAA8G,QAAA,EAAEjC,KAAK,CAACO;YAAI;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiB,CAAC,eAC7CxL,OAAA,CAAC+E,aAAa;cAAA4G,QAAA,EACX,IAAImC,IAAI,CAACpE,KAAK,CAACQ,SAAS,CAAC,CAAC6D,cAAc,CAAC;YAAC;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eAChBxL,OAAA,CAACiF,cAAc;cAAC4G,OAAO,EAAEA,CAAA,KAAMpC,iBAAiB,CAACC,KAAK,CAAE;cAAAiC,QAAA,gBACtD3L,OAAA,CAACX,QAAQ;gBAACuM,IAAI,EAAE;cAAG;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAExB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAgB,CAAC;UAAA,GARC9B,KAAK,CAACsE,EAAE;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASb,CAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACpB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAExB,CAAC;AAACjF,EAAA,CA1aIF,YAAY;EAAA,QAmCZ1G,gBAAgB;AAAA;AAAAsO,IAAA,GAnChB5H,YAAY;AA4alB,eAAeA,YAAY;AAAC,IAAAhG,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAiI,IAAA;AAAAC,YAAA,CAAA7N,EAAA;AAAA6N,YAAA,CAAA1N,GAAA;AAAA0N,YAAA,CAAAxN,GAAA;AAAAwN,YAAA,CAAAtN,GAAA;AAAAsN,YAAA,CAAApN,GAAA;AAAAoN,YAAA,CAAAjN,GAAA;AAAAiN,YAAA,CAAA9M,GAAA;AAAA8M,YAAA,CAAA3M,GAAA;AAAA2M,YAAA,CAAAzM,GAAA;AAAAyM,YAAA,CAAAtM,GAAA;AAAAsM,YAAA,CAAApM,GAAA;AAAAoM,YAAA,CAAAlM,IAAA;AAAAkM,YAAA,CAAA/L,IAAA;AAAA+L,YAAA,CAAA7L,IAAA;AAAA6L,YAAA,CAAA3L,IAAA;AAAA2L,YAAA,CAAAzL,IAAA;AAAAyL,YAAA,CAAArL,IAAA;AAAAqL,YAAA,CAAAnL,IAAA;AAAAmL,YAAA,CAAAhL,IAAA;AAAAgL,YAAA,CAAA9K,IAAA;AAAA8K,YAAA,CAAA3K,IAAA;AAAA2K,YAAA,CAAAzK,IAAA;AAAAyK,YAAA,CAAAvK,IAAA;AAAAuK,YAAA,CAAArK,IAAA;AAAAqK,YAAA,CAAAjK,IAAA;AAAAiK,YAAA,CAAA9J,IAAA;AAAA8J,YAAA,CAAA5J,IAAA;AAAA4J,YAAA,CAAA1J,IAAA;AAAA0J,YAAA,CAAAxJ,IAAA;AAAAwJ,YAAA,CAAAtJ,IAAA;AAAAsJ,YAAA,CAAApJ,IAAA;AAAAoJ,YAAA,CAAAlJ,IAAA;AAAAkJ,YAAA,CAAAhJ,IAAA;AAAAgJ,YAAA,CAAA5I,IAAA;AAAA4I,YAAA,CAAA1I,IAAA;AAAA0I,YAAA,CAAAxI,IAAA;AAAAwI,YAAA,CAAArI,IAAA;AAAAqI,YAAA,CAAAlI,IAAA;AAAAkI,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}