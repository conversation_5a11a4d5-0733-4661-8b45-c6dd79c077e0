import React, { useState, useRef, useCallback, useEffect } from 'react';
import styled, { keyframes } from 'styled-components';
import Webcam from 'react-webcam';
import {
  ArrowLeft,
  ArrowRight,
  RotateCcw,
  Home,
  Camera,
  Wifi,
  WifiOff,
  RefreshCw,
  CheckCircle,
  Trophy,
  Target,
  Zap,
  Sparkles,
  Award
} from 'lucide-react';
import Flash<PERSON>ard from './FlashCard';
import { useSignDetection } from '../hooks/useSignDetection';
import { getSignsForLevel, getLevelInfo } from '../data/signLevels';
import { theme } from '../styles/theme';
import { Container, Card, Button, Heading, Text, Badge } from './ui/ModernComponents';

// Animations
const fadeIn = keyframes`
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
`;

const celebration = keyframes`
  0%, 100% { transform: scale(1) rotate(0deg); }
  25% { transform: scale(1.1) rotate(-5deg); }
  75% { transform: scale(1.1) rotate(5deg); }
`;

// Modern Styled Components
const ModernContainer = styled.div`
  min-height: 100vh;
  background: var(--bg-primary);
  padding: ${theme.spacing[4]};
  position: relative;
  overflow-x: hidden;

  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
  }

  > * {
    position: relative;
    z-index: 1;
  }

  @media (max-width: ${theme.breakpoints.md}) {
    padding: ${theme.spacing[3]};
  }

  @media (max-width: ${theme.breakpoints.sm}) {
    padding: ${theme.spacing[2]};
    min-height: 100dvh; /* Use dynamic viewport height for mobile */
  }
`;
const Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${theme.spacing[8]};
  
  @media (max-width: ${theme.breakpoints.md}) {
    flex-direction: column;
    gap: ${theme.spacing[4]};
  }
`;

const ModernHeader = styled(Card)`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${theme.spacing[6]};
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: ${theme.shadows.xl};

  @media (max-width: ${theme.breakpoints.md}) {
    flex-direction: column;
    gap: ${theme.spacing[4]};
    text-align: center;
    margin-bottom: ${theme.spacing[5]};
  }

  @media (max-width: ${theme.breakpoints.sm}) {
    margin-bottom: ${theme.spacing[4]};
    padding: ${theme.spacing[3]};
  }

  @media (max-width: ${theme.breakpoints.sm}) {
    margin-bottom: ${theme.spacing[4]};
  }
`;

const ModernBackButton = styled(Button)`
  background: rgba(59, 130, 246, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  color: #3b82f6;
  font-weight: 600;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    background: rgba(59, 130, 246, 0.25);
    border-color: rgba(59, 130, 246, 0.5);
    color: #2563eb;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
  }

  @media (max-width: ${theme.breakpoints.md}) {
    width: 100%;
  }

  @media (max-width: ${theme.breakpoints.sm}) {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }
`;

const ModernLevelInfo = styled.div`
  text-align: center;
  color: ${theme.colors.text.primary};

  @media (max-width: ${theme.breakpoints.md}) {
    order: -1;
  }
`;

const ModernLevelTitle = styled(Heading)`
  margin-bottom: ${theme.spacing[1]};
  background: ${theme.colors.gradients.primary};
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
`;

const ModernLevelTheme = styled(Text)`
  opacity: 0.8;
  font-weight: ${theme.typography.fontWeight.medium};
`;

const ModernConnectionStatus = styled(Badge)`
  background: ${props => props.isConnected
    ? 'rgba(34, 197, 94, 0.15)'
    : 'rgba(239, 68, 68, 0.15)'
  };
  color: ${props => props.isConnected
    ? theme.colors.success[700]
    : theme.colors.error[700]
  };
  border: 1px solid ${props => props.isConnected
    ? theme.colors.success[200]
    : theme.colors.error[200]
  };
  backdrop-filter: blur(10px);
  cursor: ${props => props.isConnected ? 'default' : 'pointer'};

  &:hover {
    background: ${props => props.isConnected
      ? 'rgba(34, 197, 94, 0.2)'
      : 'rgba(239, 68, 68, 0.2)'
    };
  }

  @media (max-width: ${theme.breakpoints.md}) {
    width: 100%;
    justify-content: center;
  }

  @media (max-width: ${theme.breakpoints.sm}) {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    
    .connection-text {
      display: none; /* Hide text on mobile, show only icon */
    }
  }
`;

const MainContent = styled.div`
  display: grid;
  grid-template-columns: 1fr 500px;
  gap: ${theme.spacing[6]};
  max-width: 1400px;
  margin: 0 auto;
  align-items: start;

  @media (max-width: ${theme.breakpoints.xl}) {
    grid-template-columns: 1fr 450px;
    gap: ${theme.spacing[5]};
    max-width: 1200px;
  }

  @media (max-width: ${theme.breakpoints.lg}) {
    grid-template-columns: 1fr;
    gap: ${theme.spacing[6]};
    max-width: 800px;
  }

  @media (max-width: ${theme.breakpoints.sm}) {
    /* On mobile, show flash card first, then camera */
    display: flex;
    flex-direction: column;
    gap: ${theme.spacing[4]};
    max-width: 100%;
  }
`;

const FlashCardSection = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: ${fadeIn} 0.6s ease;
  position: sticky;
  top: ${theme.spacing[8]};
  
  @media (max-width: ${theme.breakpoints.lg}) {
    position: static;
  }
`;

const ProgressSection = styled.div`
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  text-align: center;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  width: 100%;
  max-width: 480px;
  margin-left: auto;
  margin-right: auto;
  
  @media (max-width: ${theme.breakpoints.lg}) {
    max-width: 420px;
    padding: 0.875rem;
    margin-bottom: 1.5rem;
    border-radius: 18px;
  }
  
  @media (max-width: ${theme.breakpoints.md}) {
    max-width: 380px;
    padding: 0.75rem;
    margin-bottom: 1.5rem;
    border-radius: 16px;
  }
  
  @media (max-width: ${theme.breakpoints.sm}) {
    max-width: 100%;
    padding: 0.75rem;
    margin-bottom: 1.5rem;
    border-radius: 16px;
  }
`;

const ProgressTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.75rem;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 14px;
  background: #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 0.75rem;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
  
  @media (max-width: ${theme.breakpoints.lg}) {
    height: 12px;
    margin-bottom: 0.75rem;
  }
  
  @media (max-width: ${theme.breakpoints.sm}) {
    height: 10px;
    margin-bottom: 0.75rem;
  }
`;

const ProgressFill = styled.div`
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #10b981);
  border-radius: 8px;
  transition: width 0.5s ease;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  
  @media (max-width: ${theme.breakpoints.lg}) {
    border-radius: 7px;
  }
  
  @media (max-width: ${theme.breakpoints.sm}) {
    border-radius: 6px;
  }
`;

const ProgressText = styled.div`
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 600;
`;

const Controls = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  justify-content: center;
  
  @media (max-width: ${theme.breakpoints.lg}) {
    gap: 0.75rem;
    margin-top: 1.5rem;
  }
  
  @media (max-width: 768px) {
    gap: 0.75rem;
    margin-top: 1.5rem;
    flex-wrap: wrap;
  }

  @media (max-width: ${theme.breakpoints.sm}) {
    gap: 0.5rem;
    margin-top: 1rem;
    padding: 0 ${theme.spacing[2]};
  }
`;

const ControlButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 48px; /* Minimum touch target size */
  touch-action: manipulation; /* Optimize for touch */
  
  @media (max-width: ${theme.breakpoints.lg}) {
    padding: 1rem 2rem;
    font-size: 1rem;
    min-height: 48px;
  }
  
  @media (max-width: ${theme.breakpoints.sm}) {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    min-height: 44px;
  }
  
  ${props => {
    if (props.variant === 'primary') {
      return `
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        color: white;
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
      `;
    }
    if (props.variant === 'success') {
      return `
        background: linear-gradient(135deg, #10b981, #34d399);
        color: white;
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }
      `;
    }
    return `
      background: rgba(255, 255, 255, 0.9);
      color: #64748b;
      &:hover {
        background: white;
        transform: translateY(-2px);
      }
    `;
  }}
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
  }
  
  @media (max-width: 768px) {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
  }
`;

const CameraSection = styled(Card)`
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  padding: 1.5rem;

  @media (max-width: ${theme.breakpoints.lg}) {
    order: 1; /* Show after flash card on mobile */
    padding: 1.25rem;
    border-radius: 18px;
  }

  @media (max-width: ${theme.breakpoints.sm}) {
    order: 2; /* Ensure camera comes after flash card on mobile */
    margin: 0 -${theme.spacing[2]}; /* Full width on mobile */
    border-radius: 16px;
    padding: 1rem;
  }
`;

const CameraTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  @media (max-width: ${theme.breakpoints.lg}) {
    font-size: 1.125rem;
    margin-bottom: 0.75rem;
    gap: 0.5rem;
  }
`;

const WebcamContainer = styled.div`
  position: relative;
  border-radius: 18px;
  overflow: hidden;
  background: #000;
  margin-bottom: 1rem;
  aspect-ratio: 4/3; /* Maintain aspect ratio */
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.1);
  
  @media (max-width: ${theme.breakpoints.lg}) {
    border-radius: 16px;
    margin-bottom: 0.75rem;
  }
  
  @media (max-width: ${theme.breakpoints.sm}) {
    border-radius: 12px;
    margin: 0 -${theme.spacing[2]} 1rem -${theme.spacing[2]}; /* Full width on mobile */
  }
`;

const StatusOverlay = styled.div`
  position: absolute;
  top: 1rem;
  left: 1rem;
  right: 1rem;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.75rem;
  border-radius: 8px;
  font-weight: 600;
  text-align: center;
  font-size: 0.875rem;
  
  @media (max-width: ${theme.breakpoints.sm}) {
    top: 0.5rem;
    left: 0.5rem;
    right: 0.5rem;
    padding: 0.5rem;
    font-size: 0.75rem;
  }
`;

const PredictionDisplay = styled.div`
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-radius: 14px;
  padding: 1.25rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  
  @media (max-width: ${theme.breakpoints.lg}) {
    padding: 1rem;
    border-radius: 12px;
  }
  
  @media (max-width: ${theme.breakpoints.sm}) {
    padding: 0.75rem;
    margin: 0 ${theme.spacing[2]};
    border-radius: 12px;
  }
`;

const PredictionText = styled.div`
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.75rem;
  
  @media (max-width: ${theme.breakpoints.lg}) {
    font-size: 1.125rem;
    margin-bottom: 0.5rem;
  }
`;

const ConfidenceText = styled.div`
  font-size: 0.875rem;
  color: #64748b;
`;

const CompletionModal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: ${fadeIn} 0.3s ease;
`;

const ModalContent = styled.div`
  background: white;
  border-radius: 24px;
  padding: 3rem;
  text-align: center;
  max-width: 500px;
  margin: 1rem;
  animation: ${celebration} 0.6s ease;
`;

const ModalTitle = styled.h2`
  font-size: 2.5rem;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 1rem;
`;

const ModalText = styled.p`
  font-size: 1.125rem;
  color: #64748b;
  margin-bottom: 2rem;
  line-height: 1.6;
`;

const ModalButtons = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: center;
`;

const FlashCardTraining = ({ 
  level, 
  onBack, 
  userProgress = {}, 
  onProgressUpdate 
}) => {
  const webcamRef = useRef(null);
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [completedCards, setCompletedCards] = useState(new Set());
  const [cardStates, setCardStates] = useState({});
  const [slideDirection, setSlideDirection] = useState(null);
  const [showCompletion, setShowCompletion] = useState(false);
  const [isCapturing, setIsCapturing] = useState(false);

  const levelInfo = getLevelInfo(level);
  const signs = getSignsForLevel(level);
  const currentSign = signs[currentCardIndex];

  // Use sign detection hook
  const {
    isConnected,
    prediction,
    isAIRecording,
    recordingStatus,
    signMatched,
    targetSign,
    currentKeypoints,
    startRecording: startAIRecording,
    stopRecording: stopAIRecording,
    startFrameCapture,
    retryConnection,
    setLevel
  } = useSignDetection();

  const progress = (completedCards.size / signs.length) * 100;

  // Start detection when connected
  const startDetection = useCallback(() => {
    if (!webcamRef.current) return;
    setIsCapturing(true);
    startFrameCapture(webcamRef, 100);
  }, [startFrameCapture]);

  // Save training data when sign is detected correctly
  const saveTrainingData = useCallback(async (signName, keypoints, confidence) => {
    try {
      console.log(`💾 Saving training data for ${signName} with confidence ${confidence}`);
      
      const response = await fetch('http://localhost:8000/save-training-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sign_name: signName,
          keypoints: keypoints,
          confidence: confidence,
          timestamp: new Date().toISOString()
        })
      });

      if (response.ok) {
        const result = await response.json();
        console.log(`✅ Training data saved: ${result.message}`);
        return true;
      } else {
        console.error('❌ Failed to save training data');
        return false;
      }
    } catch (error) {
      console.error('❌ Error saving training data:', error);
      return false;
    }
  }, []);

  const nextCard = useCallback(() => {
    if (currentCardIndex < signs.length - 1) {
      setSlideDirection('right');
      setCurrentCardIndex(prev => prev + 1);
      setCardStates(prev => ({ ...prev, [currentCardIndex]: null }));
      setTimeout(() => setSlideDirection(null), 500);
    }
  }, [currentCardIndex, signs.length]);

  // Handle sign detection success with automatic recording and training data saving
  useEffect(() => {
    console.log(`🔍 Debug: signMatched=${signMatched}, currentSign=${currentSign?.name}, prediction=${prediction?.sign}, confidence=${prediction?.confidence}`);
    
    if (signMatched && currentSign && prediction?.sign?.toLowerCase() === currentSign.name.toLowerCase()) {
      console.log(`✅ Sign match confirmed: ${currentSign.name} with confidence ${prediction.confidence}`);
      
      // Only proceed if this card hasn't been completed yet
      if (!completedCards.has(currentCardIndex)) {
        console.log(`🎯 Correct sign detected: ${currentSign.name} with confidence ${prediction.confidence}`);
        
        // Save training data immediately when sign is detected correctly
        const saveTrainingDataAsync = async () => {
          if (currentKeypoints && prediction?.confidence >= 0.5) {
            const saved = await saveTrainingData(currentSign.name, currentKeypoints, prediction.confidence);
            if (saved) {
              console.log(`✅ Training data saved successfully for ${currentSign.name}`);
            }
          }
        };
        
        // Save training data
        saveTrainingDataAsync();

        // Start automatic recording for additional training data
        if (!isAIRecording && isConnected) {
          console.log(`🎬 Starting automatic recording for ${currentSign.name}...`);
          startAIRecording(currentSign.name, true); // Start immediate recording session

          // Stop recording after 3 seconds
          setTimeout(() => {
            stopAIRecording();
            console.log(`✅ Automatic recording completed for: ${currentSign.name}`);
          }, 3000);
        }

        // Mark card as completed
        setCardStates(prev => ({ ...prev, [currentCardIndex]: 'correct' }));
        setCompletedCards(prev => new Set([...prev, currentCardIndex]));

        // Update progress
        if (onProgressUpdate) {
          const newCompletedCount = completedCards.size + 1;
          onProgressUpdate(level, newCompletedCount, signs.length);
        }

        // Auto-advance after 2 seconds (allowing time for user to see success)
        setTimeout(() => {
          if (currentCardIndex < signs.length - 1) {
            nextCard();
          } else {
            // Level completed
            setShowCompletion(true);
            if (onProgressUpdate) {
              onProgressUpdate(level, signs.length, signs.length);
            }
          }
        }, 2000);
      } else {
        console.log(`⚠️ Card ${currentCardIndex} already completed`);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [signMatched, currentSign, prediction, currentCardIndex, signs.length, level, onProgressUpdate, isAIRecording, isConnected, startAIRecording, stopAIRecording, completedCards, saveTrainingData, currentKeypoints]);

  // Set level when connected
  useEffect(() => {
    if (isConnected && level) {
      setLevel(level);
    }
  }, [isConnected, level, setLevel]);

  // Auto-start detection when connected
  useEffect(() => {
    if (isConnected && webcamRef.current && !isCapturing) {
      startDetection();
    }
  }, [isConnected, startDetection, isCapturing]);

  // Set target sign when current sign changes
  useEffect(() => {
    if (isConnected && currentSign) {
      console.log(`🎯 Setting target sign to: ${currentSign.name}`);
      startAIRecording(currentSign.name, false); // Set target sign without starting session
    }
  }, [isConnected, currentSign, startAIRecording]);

  const prevCard = useCallback(() => {
    if (currentCardIndex > 0) {
      setSlideDirection('left');
      setCurrentCardIndex(prev => prev - 1);
      setCardStates(prev => ({ ...prev, [currentCardIndex]: null }));
      setTimeout(() => setSlideDirection(null), 500);
    }
  }, [currentCardIndex]);

  const retryCard = useCallback(() => {
    setCardStates(prev => ({ ...prev, [currentCardIndex]: null }));
    setCompletedCards(prev => {
      const newSet = new Set(prev);
      newSet.delete(currentCardIndex);
      return newSet;
    });
  }, [currentCardIndex]);

  const handleLevelComplete = () => {
    setShowCompletion(false);
    onBack();
  };

  const handleNextLevel = () => {
    setShowCompletion(false);
    // This would typically navigate to the next level
    onBack();
  };

  if (!levelInfo || !currentSign) {
    return (
      <Container>
        <div style={{ color: 'white', textAlign: 'center', padding: '2rem' }}>
          <h2>Level not found</h2>
          <button onClick={onBack}>Go Back</button>
        </div>
      </Container>
    );
  }

  const isCurrentCardCompleted = completedCards.has(currentCardIndex);
  const currentCardState = cardStates[currentCardIndex];

  return (
    <ModernContainer>
      <ModernHeader size="md">
        <ModernBackButton variant="ghost" size="md" onClick={onBack}>
          <ArrowLeft size={20} />
          Back to Levels
        </ModernBackButton>

        <ModernLevelInfo>
          <ModernLevelTitle level={3}>
            Level {level}: {levelInfo.name}
          </ModernLevelTitle>
          <ModernLevelTheme size="lg">
            {levelInfo.theme}
          </ModernLevelTheme>
        </ModernLevelInfo>

        <ModernConnectionStatus
          isConnected={isConnected}
          onClick={!isConnected ? retryConnection : undefined}
        >
          {isConnected ? <Wifi size={18} /> : <WifiOff size={18} />}
          <span className="connection-text">
            {isConnected ? 'Connected' : 'Disconnected'}
          </span>
          {!isConnected && <RefreshCw size={14} />}
        </ModernConnectionStatus>
      </ModernHeader>

      <MainContent>
        <FlashCardSection>
          <ProgressSection>
            <ProgressTitle>Level Progress</ProgressTitle>
            <ProgressBar>
              <ProgressFill style={{ width: `${progress}%` }} />
            </ProgressBar>
            <ProgressText>
              {completedCards.size} of {signs.length} signs completed ({Math.round(progress)}%)
            </ProgressText>
          </ProgressSection>

          <FlashCard
            sign={currentSign}
            cardNumber={currentCardIndex + 1}
            totalCards={signs.length}
            isCorrect={currentCardState === 'correct'}
            isIncorrect={currentCardState === 'incorrect'}
            isDetecting={isConnected && !isCurrentCardCompleted}
            slideDirection={slideDirection}
            progress={(currentCardIndex / signs.length) * 100}
          />

          <Controls>
            <ControlButton
              onClick={prevCard}
              disabled={currentCardIndex === 0}
            >
              <ArrowLeft size={20} />
              Previous
            </ControlButton>

            {isCurrentCardCompleted ? (
              <ControlButton
                variant="success"
                onClick={nextCard}
                disabled={currentCardIndex === signs.length - 1}
              >
                <CheckCircle size={20} />
                Next Card
              </ControlButton>
            ) : (
              <ControlButton
                onClick={retryCard}
                disabled={!isConnected}
              >
                <RotateCcw size={20} />
                Retry
              </ControlButton>
            )}

            <ControlButton
              onClick={nextCard}
              disabled={currentCardIndex === signs.length - 1}
            >
              Next
              <ArrowRight size={20} />
            </ControlButton>
          </Controls>
        </FlashCardSection>

        <CameraSection>
          <CameraTitle>
            <Camera size={24} />
            Camera Feed
          </CameraTitle>

          <WebcamContainer>
            <Webcam
              ref={webcamRef}
              audio={false}
              width="100%"
              height="auto"
              screenshotFormat="image/jpeg"
              videoConstraints={{
                width: 640,
                height: 480,
                facingMode: "user",
                frameRate: { ideal: 30, max: 30 },
                aspectRatio: 4/3
              }}
              onUserMedia={() => {
                console.log('Camera access granted in FlashCard');
              }}
              onUserMediaError={(error) => {
                console.error('Camera access error in FlashCard:', error);
              }}
              mirrored={true}
              forceScreenshotSourceSize={true}
            />

            {recordingStatus && (
              <StatusOverlay>
                {recordingStatus}
              </StatusOverlay>
            )}
          </WebcamContainer>

          <PredictionDisplay>
            <PredictionText>
              {prediction?.sign ? `Detected: ${prediction.sign}` : 'Show the sign to get started'}
            </PredictionText>
            {prediction?.confidence && (
              <ConfidenceText>
                Confidence: {Math.round(prediction.confidence * 100)}%
              </ConfidenceText>
            )}
          </PredictionDisplay>
        </CameraSection>
      </MainContent>

      {showCompletion && (
        <CompletionModal>
          <ModalContent>
            <Trophy size={80} style={{ color: '#f59e0b', marginBottom: '1rem' }} />
            <ModalTitle>🎉 Level Complete!</ModalTitle>
            <ModalText>
              Congratulations! You've successfully completed Level {level}: {levelInfo.name}.
              You've mastered all {signs.length} signs in this level!
            </ModalText>
            <ModalButtons>
              <ControlButton onClick={handleLevelComplete}>
                <Home size={20} />
                Back to Levels
              </ControlButton>
              {level < 5 && (
                <ControlButton variant="primary" onClick={handleNextLevel}>
                  <Target size={20} />
                  Next Level
                </ControlButton>
              )}
            </ModalButtons>
          </ModalContent>
        </CompletionModal>
      )}
    </ModernContainer>
  );
};

export default FlashCardTraining;
