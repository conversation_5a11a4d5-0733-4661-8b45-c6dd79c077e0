{"ast": null, "code": "var _jsxFileName = \"D:\\\\ASL\\\\training-frontend\\\\src\\\\components\\\\HomePage.js\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomeContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow: hidden;\n`;\n_c = HomeContainer;\nconst Navigation = styled.nav`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-light);\n  padding: var(--space-4) 0;\n\n  @media (max-width: 768px) {\n    padding: var(--space-3) 0;\n  }\n`;\n_c2 = Navigation;\nconst NavContainer = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 var(--space-4);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n`;\n_c3 = NavContainer;\nconst Logo = styled.div`\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: var(--text-primary);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n\n  &::before {\n    content: '🤟';\n    font-size: 1.75rem;\n  }\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n\n    &::before {\n      font-size: 1.5rem;\n    }\n  }\n`;\n_c4 = Logo;\nconst NavLinks = styled.div`\n  display: flex;\n  align-items: center;\n  gap: var(--space-6);\n\n  @media (max-width: 768px) {\n    gap: var(--space-4);\n  }\n`;\n_c5 = NavLinks;\nconst NavLink = styled.a`\n  color: var(--text-secondary);\n  text-decoration: none;\n  font-weight: 500;\n  font-size: 0.875rem;\n  transition: color 0.2s ease;\n\n  &:hover {\n    color: var(--primary-600);\n  }\n\n  @media (max-width: 768px) {\n    font-size: 0.8rem;\n  }\n`;\n_c6 = NavLink;\nconst HeroSection = styled.section`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: var(--space-20) var(--space-4) var(--space-16);\n  text-align: center;\n  position: relative;\n\n  @media (max-width: 768px) {\n    padding: var(--space-16) var(--space-4) var(--space-12);\n    min-height: calc(100vh - 80px);\n  }\n`;\n_c7 = HeroSection;\nconst HeroContent = styled.div`\n  max-width: 800px;\n  width: 100%;\n  position: relative;\n  z-index: 2;\n`;\n_c8 = HeroContent;\nconst HeroTitle = styled.h1`\n  font-family: var(--font-primary);\n  font-size: 3.75rem;\n  font-weight: 800;\n  color: var(--text-primary);\n  margin-bottom: var(--space-6);\n  line-height: 1.1;\n  letter-spacing: -0.02em;\n\n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n    margin-bottom: var(--space-4);\n  }\n\n  @media (max-width: 480px) {\n    font-size: 2rem;\n  }\n`;\n_c9 = HeroTitle;\nconst HeroSubtitle = styled.p`\n  font-size: 1.25rem;\n  font-weight: 400;\n  color: var(--text-secondary);\n  margin-bottom: var(--space-8);\n  line-height: 1.6;\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    margin-bottom: var(--space-6);\n  }\n\n  @media (max-width: 480px) {\n    font-size: 1rem;\n  }\n`;\n_c0 = HeroSubtitle;\nconst HeroDescription = styled.p`\n  font-size: 1rem;\n  line-height: 1.7;\n  color: var(--text-tertiary);\n  margin-bottom: var(--space-10);\n  max-width: 500px;\n  margin-left: auto;\n  margin-right: auto;\n\n  @media (max-width: 768px) {\n    font-size: 0.9rem;\n    margin-bottom: var(--space-8);\n    line-height: 1.6;\n  }\n`;\n_c1 = HeroDescription;\nconst HeroActions = styled.div`\n  display: flex;\n  gap: var(--space-4);\n  justify-content: center;\n  align-items: center;\n  flex-wrap: wrap;\n  margin-bottom: var(--space-12);\n\n  @media (max-width: 768px) {\n    gap: var(--space-3);\n    margin-bottom: var(--space-8);\n  }\n`;\n_c10 = HeroActions;\nconst PrimaryButton = styled.button`\n  background: var(--primary-600);\n  color: white;\n  border: none;\n  padding: var(--space-4) var(--space-8);\n  border-radius: var(--radius-lg);\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  box-shadow: var(--shadow-lg);\n\n  &:hover {\n    background: var(--primary-700);\n    transform: translateY(-2px);\n    box-shadow: var(--shadow-xl);\n  }\n\n  &:active {\n    transform: translateY(0);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-3) var(--space-6);\n    font-size: 0.9rem;\n  }\n`;\n_c11 = PrimaryButton;\nconst SecondaryButton = styled.button`\n  background: transparent;\n  color: var(--text-primary);\n  border: 2px solid var(--border-medium);\n  padding: var(--space-4) var(--space-8);\n  border-radius: var(--radius-lg);\n  font-size: 1rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n\n  &:hover {\n    border-color: var(--primary-600);\n    color: var(--primary-600);\n    background: var(--primary-50);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-3) var(--space-6);\n    font-size: 0.9rem;\n  }\n`;\n_c12 = SecondaryButton;\nconst FeaturesSection = styled.section`\n  padding: var(--space-20) var(--space-4);\n  background: var(--bg-secondary);\n\n  @media (max-width: 768px) {\n    padding: var(--space-16) var(--space-4);\n  }\n`;\n_c13 = FeaturesSection;\nconst FeaturesContainer = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  text-align: center;\n`;\n_c14 = FeaturesContainer;\nconst SectionTitle = styled.h2`\n  font-family: var(--font-primary);\n  font-size: 2.5rem;\n  font-weight: 700;\n  color: var(--text-primary);\n  margin-bottom: var(--space-4);\n\n  @media (max-width: 768px) {\n    font-size: 2rem;\n  }\n`;\n_c15 = SectionTitle;\nconst SectionSubtitle = styled.p`\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  margin-bottom: var(--space-12);\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n\n  @media (max-width: 768px) {\n    margin-bottom: var(--space-10);\n  }\n`;\n_c16 = SectionSubtitle;\nconst FeatureGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\n  gap: var(--space-8);\n  margin-bottom: var(--space-16);\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-6);\n  }\n`;\n_c17 = FeatureGrid;\nconst FeatureCard = styled.div`\n  background: var(--bg-primary);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-8);\n  border: 1px solid var(--border-light);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  text-align: center;\n  position: relative;\n  overflow: hidden;\n  box-shadow: var(--shadow-sm);\n\n  &:hover {\n    transform: translateY(-4px);\n    box-shadow: var(--shadow-xl);\n    border-color: var(--primary-200);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-6);\n  }\n`;\n_c18 = FeatureCard;\nconst FeatureIcon = styled.div`\n  font-size: 3rem;\n  margin-bottom: var(--space-4);\n\n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n  }\n`;\n_c19 = FeatureIcon;\nconst FeatureTitle = styled.h3`\n  font-size: 1.25rem;\n  margin-bottom: var(--space-3);\n  color: var(--text-primary);\n  font-weight: 600;\n  font-family: var(--font-primary);\n`;\n_c20 = FeatureTitle;\nconst FeatureDescription = styled.p`\n  font-size: 0.9rem;\n  color: var(--text-secondary);\n  line-height: 1.6;\n  font-weight: 400;\n`;\n_c21 = FeatureDescription;\nconst HeroStats = styled.div`\n  display: flex;\n  justify-content: center;\n  gap: var(--space-8);\n  margin-top: var(--space-8);\n\n  @media (max-width: 768px) {\n    gap: var(--space-6);\n    flex-wrap: wrap;\n  }\n`;\n_c22 = HeroStats;\nconst StatItem = styled.div`\n  text-align: center;\n`;\n_c23 = StatItem;\nconst StatNumber = styled.div`\n  font-size: 2rem;\n  font-weight: 700;\n  color: var(--primary-600);\n  font-family: var(--font-primary);\n\n  @media (max-width: 768px) {\n    font-size: 1.5rem;\n  }\n`;\n_c24 = StatNumber;\nconst StatLabel = styled.div`\n  font-size: 0.875rem;\n  color: var(--text-tertiary);\n  font-weight: 500;\n  margin-top: var(--space-1);\n`;\n_c25 = StatLabel;\nconst HomePage = ({\n  onStartTraining\n}) => {\n  return /*#__PURE__*/_jsxDEV(HomeContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Navigation, {\n      children: /*#__PURE__*/_jsxDEV(NavContainer, {\n        children: [/*#__PURE__*/_jsxDEV(Logo, {\n          children: \"ASL Trainer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(NavLinks, {\n          children: [/*#__PURE__*/_jsxDEV(NavLink, {\n            href: \"#features\",\n            children: \"Features\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NavLink, {\n            href: \"#about\",\n            children: \"About\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NavLink, {\n            href: \"#contact\",\n            children: \"Contact\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HeroSection, {\n      children: /*#__PURE__*/_jsxDEV(HeroContent, {\n        children: [/*#__PURE__*/_jsxDEV(HeroTitle, {\n          children: \"Master Sign Language with AI\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(HeroSubtitle, {\n          children: \"Transform your learning journey with our innovative platform that combines real-time practice with AI-powered feedback\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(HeroDescription, {\n          children: \"Practice sign language using your camera while contributing to AI advancement. Every session helps improve detection algorithms for the deaf community.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(HeroActions, {\n          children: [/*#__PURE__*/_jsxDEV(PrimaryButton, {\n            onClick: onStartTraining,\n            children: \"\\uD83D\\uDE80 Start Training\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n            children: \"\\uD83D\\uDCD6 Learn More\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(HeroStats, {\n          children: [/*#__PURE__*/_jsxDEV(StatItem, {\n            children: [/*#__PURE__*/_jsxDEV(StatNumber, {\n              children: \"10K+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n              children: \"Active Learners\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n            children: [/*#__PURE__*/_jsxDEV(StatNumber, {\n              children: \"50+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n              children: \"Sign Patterns\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n            children: [/*#__PURE__*/_jsxDEV(StatNumber, {\n              children: \"95%\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n              children: \"Accuracy Rate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FeaturesSection, {\n      id: \"features\",\n      children: /*#__PURE__*/_jsxDEV(FeaturesContainer, {\n        children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: \"Why Choose ASL Trainer?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SectionSubtitle, {\n          children: \"Experience the future of sign language learning with our cutting-edge features\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FeatureGrid, {\n          children: [/*#__PURE__*/_jsxDEV(FeatureCard, {\n            children: [/*#__PURE__*/_jsxDEV(FeatureIcon, {\n              children: \"\\uD83D\\uDCF9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureTitle, {\n              children: \"Real-time Practice\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureDescription, {\n              children: \"Use your device's camera for interactive sign language practice with instant visual feedback and gesture recognition\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureCard, {\n            children: [/*#__PURE__*/_jsxDEV(FeatureIcon, {\n              children: \"\\uD83E\\uDD16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureTitle, {\n              children: \"AI-Powered Learning\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureDescription, {\n              children: \"Advanced machine learning algorithms provide personalized feedback, track your progress, and adapt to your learning pace\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureCard, {\n            children: [/*#__PURE__*/_jsxDEV(FeatureIcon, {\n              children: \"\\uD83C\\uDF0D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureTitle, {\n              children: \"Community Impact\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureDescription, {\n              children: \"Your practice sessions contribute to improving AI models that benefit the entire deaf and hard-of-hearing community\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureCard, {\n            children: [/*#__PURE__*/_jsxDEV(FeatureIcon, {\n              children: \"\\uD83D\\uDCF1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureTitle, {\n              children: \"Cross-Platform\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureDescription, {\n              children: \"Works seamlessly on desktop and mobile devices, allowing you to practice anywhere, anytime with responsive design\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureCard, {\n            children: [/*#__PURE__*/_jsxDEV(FeatureIcon, {\n              children: \"\\uD83C\\uDFAF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureTitle, {\n              children: \"Personalized Learning\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureDescription, {\n              children: \"Adaptive learning paths that adjust to your skill level and provide targeted exercises for continuous improvement\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureCard, {\n            children: [/*#__PURE__*/_jsxDEV(FeatureIcon, {\n              children: \"\\uD83D\\uDD12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureTitle, {\n              children: \"Privacy First\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureDescription, {\n              children: \"Your recordings are processed locally and stored securely, ensuring your privacy while you learn and practice\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 418,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 417,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 367,\n    columnNumber: 5\n  }, this);\n};\n_c26 = HomePage;\nexport default HomePage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26;\n$RefreshReg$(_c, \"HomeContainer\");\n$RefreshReg$(_c2, \"Navigation\");\n$RefreshReg$(_c3, \"NavContainer\");\n$RefreshReg$(_c4, \"Logo\");\n$RefreshReg$(_c5, \"NavLinks\");\n$RefreshReg$(_c6, \"NavLink\");\n$RefreshReg$(_c7, \"HeroSection\");\n$RefreshReg$(_c8, \"HeroContent\");\n$RefreshReg$(_c9, \"HeroTitle\");\n$RefreshReg$(_c0, \"HeroSubtitle\");\n$RefreshReg$(_c1, \"HeroDescription\");\n$RefreshReg$(_c10, \"HeroActions\");\n$RefreshReg$(_c11, \"PrimaryButton\");\n$RefreshReg$(_c12, \"SecondaryButton\");\n$RefreshReg$(_c13, \"FeaturesSection\");\n$RefreshReg$(_c14, \"FeaturesContainer\");\n$RefreshReg$(_c15, \"SectionTitle\");\n$RefreshReg$(_c16, \"SectionSubtitle\");\n$RefreshReg$(_c17, \"FeatureGrid\");\n$RefreshReg$(_c18, \"FeatureCard\");\n$RefreshReg$(_c19, \"FeatureIcon\");\n$RefreshReg$(_c20, \"FeatureTitle\");\n$RefreshReg$(_c21, \"FeatureDescription\");\n$RefreshReg$(_c22, \"HeroStats\");\n$RefreshReg$(_c23, \"StatItem\");\n$RefreshReg$(_c24, \"StatNumber\");\n$RefreshReg$(_c25, \"StatLabel\");\n$RefreshReg$(_c26, \"HomePage\");", "map": {"version": 3, "names": ["React", "styled", "jsxDEV", "_jsxDEV", "HomeContainer", "div", "_c", "Navigation", "nav", "_c2", "NavContainer", "_c3", "Logo", "_c4", "NavLinks", "_c5", "NavLink", "a", "_c6", "HeroSection", "section", "_c7", "Hero<PERSON><PERSON><PERSON>", "_c8", "<PERSON><PERSON><PERSON><PERSON>", "h1", "_c9", "HeroSubtitle", "p", "_c0", "HeroDescription", "_c1", "HeroActions", "_c10", "PrimaryButton", "button", "_c11", "SecondaryButton", "_c12", "FeaturesSection", "_c13", "FeaturesContainer", "_c14", "SectionTitle", "h2", "_c15", "SectionSubtitle", "_c16", "FeatureGrid", "_c17", "FeatureCard", "_c18", "FeatureIcon", "_c19", "FeatureTitle", "h3", "_c20", "FeatureDescription", "_c21", "HeroStats", "_c22", "StatItem", "_c23", "StatNumber", "_c24", "StatLabel", "_c25", "HomePage", "onStartTraining", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "onClick", "id", "_c26", "$RefreshReg$"], "sources": ["D:/ASL/training-frontend/src/components/HomePage.js"], "sourcesContent": ["import React from 'react';\r\nimport styled from 'styled-components';\r\n\r\nconst HomeContainer = styled.div`\r\n  min-height: 100vh;\r\n  background: var(--bg-primary);\r\n  position: relative;\r\n  overflow: hidden;\r\n`;\r\n\r\nconst Navigation = styled.nav`\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 50;\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(20px);\r\n  border-bottom: 1px solid var(--border-light);\r\n  padding: var(--space-4) 0;\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-3) 0;\r\n  }\r\n`;\r\n\r\nconst NavContainer = styled.div`\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 var(--space-4);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n`;\r\n\r\nconst Logo = styled.div`\r\n  font-family: var(--font-primary);\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n  color: var(--text-primary);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n\r\n  &::before {\r\n    content: '🤟';\r\n    font-size: 1.75rem;\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.25rem;\r\n\r\n    &::before {\r\n      font-size: 1.5rem;\r\n    }\r\n  }\r\n`;\r\n\r\nconst NavLinks = styled.div`\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-6);\r\n\r\n  @media (max-width: 768px) {\r\n    gap: var(--space-4);\r\n  }\r\n`;\r\n\r\nconst NavLink = styled.a`\r\n  color: var(--text-secondary);\r\n  text-decoration: none;\r\n  font-weight: 500;\r\n  font-size: 0.875rem;\r\n  transition: color 0.2s ease;\r\n\r\n  &:hover {\r\n    color: var(--primary-600);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 0.8rem;\r\n  }\r\n`;\r\n\r\nconst HeroSection = styled.section`\r\n  min-height: 100vh;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: var(--space-20) var(--space-4) var(--space-16);\r\n  text-align: center;\r\n  position: relative;\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-16) var(--space-4) var(--space-12);\r\n    min-height: calc(100vh - 80px);\r\n  }\r\n`;\r\n\r\nconst HeroContent = styled.div`\r\n  max-width: 800px;\r\n  width: 100%;\r\n  position: relative;\r\n  z-index: 2;\r\n`;\r\n\r\nconst HeroTitle = styled.h1`\r\n  font-family: var(--font-primary);\r\n  font-size: 3.75rem;\r\n  font-weight: 800;\r\n  color: var(--text-primary);\r\n  margin-bottom: var(--space-6);\r\n  line-height: 1.1;\r\n  letter-spacing: -0.02em;\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 2.5rem;\r\n    margin-bottom: var(--space-4);\r\n  }\r\n\r\n  @media (max-width: 480px) {\r\n    font-size: 2rem;\r\n  }\r\n`;\r\n\r\nconst HeroSubtitle = styled.p`\r\n  font-size: 1.25rem;\r\n  font-weight: 400;\r\n  color: var(--text-secondary);\r\n  margin-bottom: var(--space-8);\r\n  line-height: 1.6;\r\n  max-width: 600px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.125rem;\r\n    margin-bottom: var(--space-6);\r\n  }\r\n\r\n  @media (max-width: 480px) {\r\n    font-size: 1rem;\r\n  }\r\n`;\r\n\r\nconst HeroDescription = styled.p`\r\n  font-size: 1rem;\r\n  line-height: 1.7;\r\n  color: var(--text-tertiary);\r\n  margin-bottom: var(--space-10);\r\n  max-width: 500px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 0.9rem;\r\n    margin-bottom: var(--space-8);\r\n    line-height: 1.6;\r\n  }\r\n`;\r\n\r\nconst HeroActions = styled.div`\r\n  display: flex;\r\n  gap: var(--space-4);\r\n  justify-content: center;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  margin-bottom: var(--space-12);\r\n\r\n  @media (max-width: 768px) {\r\n    gap: var(--space-3);\r\n    margin-bottom: var(--space-8);\r\n  }\r\n`;\r\n\r\nconst PrimaryButton = styled.button`\r\n  background: var(--primary-600);\r\n  color: white;\r\n  border: none;\r\n  padding: var(--space-4) var(--space-8);\r\n  border-radius: var(--radius-lg);\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  box-shadow: var(--shadow-lg);\r\n\r\n  &:hover {\r\n    background: var(--primary-700);\r\n    transform: translateY(-2px);\r\n    box-shadow: var(--shadow-xl);\r\n  }\r\n\r\n  &:active {\r\n    transform: translateY(0);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-3) var(--space-6);\r\n    font-size: 0.9rem;\r\n  }\r\n`;\r\n\r\nconst SecondaryButton = styled.button`\r\n  background: transparent;\r\n  color: var(--text-primary);\r\n  border: 2px solid var(--border-medium);\r\n  padding: var(--space-4) var(--space-8);\r\n  border-radius: var(--radius-lg);\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n\r\n  &:hover {\r\n    border-color: var(--primary-600);\r\n    color: var(--primary-600);\r\n    background: var(--primary-50);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-3) var(--space-6);\r\n    font-size: 0.9rem;\r\n  }\r\n`;\r\n\r\nconst FeaturesSection = styled.section`\r\n  padding: var(--space-20) var(--space-4);\r\n  background: var(--bg-secondary);\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-16) var(--space-4);\r\n  }\r\n`;\r\n\r\nconst FeaturesContainer = styled.div`\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  text-align: center;\r\n`;\r\n\r\nconst SectionTitle = styled.h2`\r\n  font-family: var(--font-primary);\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  color: var(--text-primary);\r\n  margin-bottom: var(--space-4);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 2rem;\r\n  }\r\n`;\r\n\r\nconst SectionSubtitle = styled.p`\r\n  font-size: 1.125rem;\r\n  color: var(--text-secondary);\r\n  margin-bottom: var(--space-12);\r\n  max-width: 600px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n\r\n  @media (max-width: 768px) {\r\n    margin-bottom: var(--space-10);\r\n  }\r\n`;\r\n\r\nconst FeatureGrid = styled.div`\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\r\n  gap: var(--space-8);\r\n  margin-bottom: var(--space-16);\r\n\r\n  @media (max-width: 768px) {\r\n    grid-template-columns: 1fr;\r\n    gap: var(--space-6);\r\n  }\r\n`;\r\n\r\nconst FeatureCard = styled.div`\r\n  background: var(--bg-primary);\r\n  border-radius: var(--radius-2xl);\r\n  padding: var(--space-8);\r\n  border: 1px solid var(--border-light);\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  text-align: center;\r\n  position: relative;\r\n  overflow: hidden;\r\n  box-shadow: var(--shadow-sm);\r\n\r\n  &:hover {\r\n    transform: translateY(-4px);\r\n    box-shadow: var(--shadow-xl);\r\n    border-color: var(--primary-200);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-6);\r\n  }\r\n`;\r\n\r\nconst FeatureIcon = styled.div`\r\n  font-size: 3rem;\r\n  margin-bottom: var(--space-4);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 2.5rem;\r\n  }\r\n`;\r\n\r\nconst FeatureTitle = styled.h3`\r\n  font-size: 1.25rem;\r\n  margin-bottom: var(--space-3);\r\n  color: var(--text-primary);\r\n  font-weight: 600;\r\n  font-family: var(--font-primary);\r\n`;\r\n\r\nconst FeatureDescription = styled.p`\r\n  font-size: 0.9rem;\r\n  color: var(--text-secondary);\r\n  line-height: 1.6;\r\n  font-weight: 400;\r\n`;\r\n\r\nconst HeroStats = styled.div`\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: var(--space-8);\r\n  margin-top: var(--space-8);\r\n\r\n  @media (max-width: 768px) {\r\n    gap: var(--space-6);\r\n    flex-wrap: wrap;\r\n  }\r\n`;\r\n\r\nconst StatItem = styled.div`\r\n  text-align: center;\r\n`;\r\n\r\nconst StatNumber = styled.div`\r\n  font-size: 2rem;\r\n  font-weight: 700;\r\n  color: var(--primary-600);\r\n  font-family: var(--font-primary);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.5rem;\r\n  }\r\n`;\r\n\r\nconst StatLabel = styled.div`\r\n  font-size: 0.875rem;\r\n  color: var(--text-tertiary);\r\n  font-weight: 500;\r\n  margin-top: var(--space-1);\r\n`;\r\n\r\nconst HomePage = ({ onStartTraining }) => {\r\n  return (\r\n    <HomeContainer>\r\n      <Navigation>\r\n        <NavContainer>\r\n          <Logo>ASL Trainer</Logo>\r\n          <NavLinks>\r\n            <NavLink href=\"#features\">Features</NavLink>\r\n            <NavLink href=\"#about\">About</NavLink>\r\n            <NavLink href=\"#contact\">Contact</NavLink>\r\n          </NavLinks>\r\n        </NavContainer>\r\n      </Navigation>\r\n\r\n      <HeroSection>\r\n        <HeroContent>\r\n          <HeroTitle>Master Sign Language with AI</HeroTitle>\r\n          <HeroSubtitle>\r\n            Transform your learning journey with our innovative platform that combines\r\n            real-time practice with AI-powered feedback\r\n          </HeroSubtitle>\r\n          <HeroDescription>\r\n            Practice sign language using your camera while contributing to AI advancement.\r\n            Every session helps improve detection algorithms for the deaf community.\r\n          </HeroDescription>\r\n\r\n          <HeroActions>\r\n            <PrimaryButton onClick={onStartTraining}>\r\n              🚀 Start Training\r\n            </PrimaryButton>\r\n            <SecondaryButton>\r\n              📖 Learn More\r\n            </SecondaryButton>\r\n          </HeroActions>\r\n\r\n          <HeroStats>\r\n            <StatItem>\r\n              <StatNumber>10K+</StatNumber>\r\n              <StatLabel>Active Learners</StatLabel>\r\n            </StatItem>\r\n            <StatItem>\r\n              <StatNumber>50+</StatNumber>\r\n              <StatLabel>Sign Patterns</StatLabel>\r\n            </StatItem>\r\n            <StatItem>\r\n              <StatNumber>95%</StatNumber>\r\n              <StatLabel>Accuracy Rate</StatLabel>\r\n            </StatItem>\r\n          </HeroStats>\r\n        </HeroContent>\r\n      </HeroSection>\r\n\r\n      <FeaturesSection id=\"features\">\r\n        <FeaturesContainer>\r\n          <SectionTitle>Why Choose ASL Trainer?</SectionTitle>\r\n          <SectionSubtitle>\r\n            Experience the future of sign language learning with our cutting-edge features\r\n          </SectionSubtitle>\r\n\r\n          <FeatureGrid>\r\n            <FeatureCard>\r\n              <FeatureIcon>📹</FeatureIcon>\r\n              <FeatureTitle>Real-time Practice</FeatureTitle>\r\n              <FeatureDescription>\r\n                Use your device's camera for interactive sign language practice with instant visual feedback and gesture recognition\r\n              </FeatureDescription>\r\n            </FeatureCard>\r\n\r\n            <FeatureCard>\r\n              <FeatureIcon>🤖</FeatureIcon>\r\n              <FeatureTitle>AI-Powered Learning</FeatureTitle>\r\n              <FeatureDescription>\r\n                Advanced machine learning algorithms provide personalized feedback, track your progress, and adapt to your learning pace\r\n              </FeatureDescription>\r\n            </FeatureCard>\r\n\r\n            <FeatureCard>\r\n              <FeatureIcon>🌍</FeatureIcon>\r\n              <FeatureTitle>Community Impact</FeatureTitle>\r\n              <FeatureDescription>\r\n                Your practice sessions contribute to improving AI models that benefit the entire deaf and hard-of-hearing community\r\n              </FeatureDescription>\r\n            </FeatureCard>\r\n\r\n            <FeatureCard>\r\n              <FeatureIcon>📱</FeatureIcon>\r\n              <FeatureTitle>Cross-Platform</FeatureTitle>\r\n              <FeatureDescription>\r\n                Works seamlessly on desktop and mobile devices, allowing you to practice anywhere, anytime with responsive design\r\n              </FeatureDescription>\r\n            </FeatureCard>\r\n\r\n            <FeatureCard>\r\n              <FeatureIcon>🎯</FeatureIcon>\r\n              <FeatureTitle>Personalized Learning</FeatureTitle>\r\n              <FeatureDescription>\r\n                Adaptive learning paths that adjust to your skill level and provide targeted exercises for continuous improvement\r\n              </FeatureDescription>\r\n            </FeatureCard>\r\n\r\n            <FeatureCard>\r\n              <FeatureIcon>🔒</FeatureIcon>\r\n              <FeatureTitle>Privacy First</FeatureTitle>\r\n              <FeatureDescription>\r\n                Your recordings are processed locally and stored securely, ensuring your privacy while you learn and practice\r\n              </FeatureDescription>\r\n            </FeatureCard>\r\n          </FeatureGrid>\r\n        </FeaturesContainer>\r\n      </FeaturesSection>\r\n    </HomeContainer>\r\n  );\r\n};\r\n\r\nexport default HomePage; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,aAAa,GAAGH,MAAM,CAACI,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,aAAa;AAOnB,MAAMG,UAAU,GAAGN,MAAM,CAACO,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAdIF,UAAU;AAgBhB,MAAMG,YAAY,GAAGT,MAAM,CAACI,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACM,GAAA,GAPID,YAAY;AASlB,MAAME,IAAI,GAAGX,MAAM,CAACI,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GArBID,IAAI;AAuBV,MAAME,QAAQ,GAAGb,MAAM,CAACI,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GARID,QAAQ;AAUd,MAAME,OAAO,GAAGf,MAAM,CAACgB,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAdIF,OAAO;AAgBb,MAAMG,WAAW,GAAGlB,MAAM,CAACmB,OAAO;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAbIF,WAAW;AAejB,MAAMG,WAAW,GAAGrB,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACkB,GAAA,GALID,WAAW;AAOjB,MAAME,SAAS,GAAGvB,MAAM,CAACwB,EAAE;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAjBIF,SAAS;AAmBf,MAAMG,YAAY,GAAG1B,MAAM,CAAC2B,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAlBIF,YAAY;AAoBlB,MAAMG,eAAe,GAAG7B,MAAM,CAAC2B,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAdID,eAAe;AAgBrB,MAAME,WAAW,GAAG/B,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC4B,IAAA,GAZID,WAAW;AAcjB,MAAME,aAAa,GAAGjC,MAAM,CAACkC,MAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GA7BIF,aAAa;AA+BnB,MAAMG,eAAe,GAAGpC,MAAM,CAACkC,MAAM;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,IAAA,GAxBID,eAAe;AA0BrB,MAAME,eAAe,GAAGtC,MAAM,CAACmB,OAAO;AACtC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACoB,IAAA,GAPID,eAAe;AASrB,MAAME,iBAAiB,GAAGxC,MAAM,CAACI,GAAG;AACpC;AACA;AACA;AACA,CAAC;AAACqC,IAAA,GAJID,iBAAiB;AAMvB,MAAME,YAAY,GAAG1C,MAAM,CAAC2C,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAVIF,YAAY;AAYlB,MAAMG,eAAe,GAAG7C,MAAM,CAAC2B,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmB,IAAA,GAXID,eAAe;AAarB,MAAME,WAAW,GAAG/C,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC4C,IAAA,GAVID,WAAW;AAYjB,MAAME,WAAW,GAAGjD,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC8C,IAAA,GApBID,WAAW;AAsBjB,MAAME,WAAW,GAAGnD,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgD,IAAA,GAPID,WAAW;AASjB,MAAME,YAAY,GAAGrD,MAAM,CAACsD,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GANIF,YAAY;AAQlB,MAAMG,kBAAkB,GAAGxD,MAAM,CAAC2B,CAAC;AACnC;AACA;AACA;AACA;AACA,CAAC;AAAC8B,IAAA,GALID,kBAAkB;AAOxB,MAAME,SAAS,GAAG1D,MAAM,CAACI,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuD,IAAA,GAVID,SAAS;AAYf,MAAME,QAAQ,GAAG5D,MAAM,CAACI,GAAG;AAC3B;AACA,CAAC;AAACyD,IAAA,GAFID,QAAQ;AAId,MAAME,UAAU,GAAG9D,MAAM,CAACI,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC2D,IAAA,GATID,UAAU;AAWhB,MAAME,SAAS,GAAGhE,MAAM,CAACI,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAAC6D,IAAA,GALID,SAAS;AAOf,MAAME,QAAQ,GAAGA,CAAC;EAAEC;AAAgB,CAAC,KAAK;EACxC,oBACEjE,OAAA,CAACC,aAAa;IAAAiE,QAAA,gBACZlE,OAAA,CAACI,UAAU;MAAA8D,QAAA,eACTlE,OAAA,CAACO,YAAY;QAAA2D,QAAA,gBACXlE,OAAA,CAACS,IAAI;UAAAyD,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxBtE,OAAA,CAACW,QAAQ;UAAAuD,QAAA,gBACPlE,OAAA,CAACa,OAAO;YAAC0D,IAAI,EAAC,WAAW;YAAAL,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eAC5CtE,OAAA,CAACa,OAAO;YAAC0D,IAAI,EAAC,QAAQ;YAAAL,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACtCtE,OAAA,CAACa,OAAO;YAAC0D,IAAI,EAAC,UAAU;YAAAL,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEbtE,OAAA,CAACgB,WAAW;MAAAkD,QAAA,eACVlE,OAAA,CAACmB,WAAW;QAAA+C,QAAA,gBACVlE,OAAA,CAACqB,SAAS;UAAA6C,QAAA,EAAC;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACnDtE,OAAA,CAACwB,YAAY;UAAA0C,QAAA,EAAC;QAGd;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACftE,OAAA,CAAC2B,eAAe;UAAAuC,QAAA,EAAC;QAGjB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAiB,CAAC,eAElBtE,OAAA,CAAC6B,WAAW;UAAAqC,QAAA,gBACVlE,OAAA,CAAC+B,aAAa;YAACyC,OAAO,EAAEP,eAAgB;YAAAC,QAAA,EAAC;UAEzC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,eAChBtE,OAAA,CAACkC,eAAe;YAAAgC,QAAA,EAAC;UAEjB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAiB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAEdtE,OAAA,CAACwD,SAAS;UAAAU,QAAA,gBACRlE,OAAA,CAAC0D,QAAQ;YAAAQ,QAAA,gBACPlE,OAAA,CAAC4D,UAAU;cAAAM,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7BtE,OAAA,CAAC8D,SAAS;cAAAI,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACXtE,OAAA,CAAC0D,QAAQ;YAAAQ,QAAA,gBACPlE,OAAA,CAAC4D,UAAU;cAAAM,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC5BtE,OAAA,CAAC8D,SAAS;cAAAI,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACXtE,OAAA,CAAC0D,QAAQ;YAAAQ,QAAA,gBACPlE,OAAA,CAAC4D,UAAU;cAAAM,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC5BtE,OAAA,CAAC8D,SAAS;cAAAI,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEdtE,OAAA,CAACoC,eAAe;MAACqC,EAAE,EAAC,UAAU;MAAAP,QAAA,eAC5BlE,OAAA,CAACsC,iBAAiB;QAAA4B,QAAA,gBAChBlE,OAAA,CAACwC,YAAY;UAAA0B,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACpDtE,OAAA,CAAC2C,eAAe;UAAAuB,QAAA,EAAC;QAEjB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAiB,CAAC,eAElBtE,OAAA,CAAC6C,WAAW;UAAAqB,QAAA,gBACVlE,OAAA,CAAC+C,WAAW;YAAAmB,QAAA,gBACVlE,OAAA,CAACiD,WAAW;cAAAiB,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC7BtE,OAAA,CAACmD,YAAY;cAAAe,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC,eAC/CtE,OAAA,CAACsD,kBAAkB;cAAAY,QAAA,EAAC;YAEpB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEdtE,OAAA,CAAC+C,WAAW;YAAAmB,QAAA,gBACVlE,OAAA,CAACiD,WAAW;cAAAiB,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC7BtE,OAAA,CAACmD,YAAY;cAAAe,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC,eAChDtE,OAAA,CAACsD,kBAAkB;cAAAY,QAAA,EAAC;YAEpB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEdtE,OAAA,CAAC+C,WAAW;YAAAmB,QAAA,gBACVlE,OAAA,CAACiD,WAAW;cAAAiB,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC7BtE,OAAA,CAACmD,YAAY;cAAAe,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC,eAC7CtE,OAAA,CAACsD,kBAAkB;cAAAY,QAAA,EAAC;YAEpB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEdtE,OAAA,CAAC+C,WAAW;YAAAmB,QAAA,gBACVlE,OAAA,CAACiD,WAAW;cAAAiB,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC7BtE,OAAA,CAACmD,YAAY;cAAAe,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC,eAC3CtE,OAAA,CAACsD,kBAAkB;cAAAY,QAAA,EAAC;YAEpB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEdtE,OAAA,CAAC+C,WAAW;YAAAmB,QAAA,gBACVlE,OAAA,CAACiD,WAAW;cAAAiB,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC7BtE,OAAA,CAACmD,YAAY;cAAAe,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC,eAClDtE,OAAA,CAACsD,kBAAkB;cAAAY,QAAA,EAAC;YAEpB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEdtE,OAAA,CAAC+C,WAAW;YAAAmB,QAAA,gBACVlE,OAAA,CAACiD,WAAW;cAAAiB,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC7BtE,OAAA,CAACmD,YAAY;cAAAe,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC,eAC1CtE,OAAA,CAACsD,kBAAkB;cAAAY,QAAA,EAAC;YAEpB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEpB,CAAC;AAACI,IAAA,GAhHIV,QAAQ;AAkHd,eAAeA,QAAQ;AAAC,IAAA7D,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAW,IAAA;AAAAC,YAAA,CAAAxE,EAAA;AAAAwE,YAAA,CAAArE,GAAA;AAAAqE,YAAA,CAAAnE,GAAA;AAAAmE,YAAA,CAAAjE,GAAA;AAAAiE,YAAA,CAAA/D,GAAA;AAAA+D,YAAA,CAAA5D,GAAA;AAAA4D,YAAA,CAAAzD,GAAA;AAAAyD,YAAA,CAAAvD,GAAA;AAAAuD,YAAA,CAAApD,GAAA;AAAAoD,YAAA,CAAAjD,GAAA;AAAAiD,YAAA,CAAA/C,GAAA;AAAA+C,YAAA,CAAA7C,IAAA;AAAA6C,YAAA,CAAA1C,IAAA;AAAA0C,YAAA,CAAAxC,IAAA;AAAAwC,YAAA,CAAAtC,IAAA;AAAAsC,YAAA,CAAApC,IAAA;AAAAoC,YAAA,CAAAjC,IAAA;AAAAiC,YAAA,CAAA/B,IAAA;AAAA+B,YAAA,CAAA7B,IAAA;AAAA6B,YAAA,CAAA3B,IAAA;AAAA2B,YAAA,CAAAzB,IAAA;AAAAyB,YAAA,CAAAtB,IAAA;AAAAsB,YAAA,CAAApB,IAAA;AAAAoB,YAAA,CAAAlB,IAAA;AAAAkB,YAAA,CAAAhB,IAAA;AAAAgB,YAAA,CAAAd,IAAA;AAAAc,YAAA,CAAAZ,IAAA;AAAAY,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}