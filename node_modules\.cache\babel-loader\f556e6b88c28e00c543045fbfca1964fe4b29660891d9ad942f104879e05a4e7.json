{"ast": null, "code": "import _objectSpread from\"D:/ASL/ASL-Training/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import _taggedTemplateLiteral from\"D:/ASL/ASL-Training/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6,_templateObject7,_templateObject8,_templateObject9,_templateObject0,_templateObject1,_templateObject10,_templateObject11,_templateObject12,_templateObject13,_templateObject14,_templateObject15,_templateObject16,_templateObject17,_templateObject18,_templateObject19,_templateObject20,_templateObject21,_templateObject22,_templateObject23,_templateObject24,_templateObject25,_templateObject26,_templateObject27,_templateObject28,_templateObject29;import React,{useState,useRef,useCallback,useEffect}from'react';import styled,{keyframes}from'styled-components';import Webcam from'react-webcam';import{<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,RotateCcw,Home,Camera,Wifi,WifiOff,RefreshCw,CheckCircle,Trophy,Target,Zap,Sparkles,Award}from'lucide-react';import FlashCard from'./FlashCard';import{useSignDetection}from'../hooks/useSignDetection';import{getSignsForLevel,getLevelInfo}from'../data/signLevels';import{theme}from'../styles/theme';import{Container,Card,Button,Heading,Text,Badge}from'./ui/ModernComponents';// Animations\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const fadeIn=keyframes(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  from { opacity: 0; transform: translateY(20px); }\\n  to { opacity: 1; transform: translateY(0); }\\n\"])));const celebration=keyframes(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  0%, 100% { transform: scale(1) rotate(0deg); }\\n  25% { transform: scale(1.1) rotate(-5deg); }\\n  75% { transform: scale(1.1) rotate(5deg); }\\n\"])));// Modern Styled Components\nconst ModernContainer=styled.div(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  min-height: 100vh;\\n  background: \",\";\\n  padding: \",\";\\n  position: relative;\\n\\n  &::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: 0;\\n    right: 0;\\n    bottom: 0;\\n    background:\\n      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\\n      radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),\\n      radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);\\n    pointer-events: none;\\n  }\\n\\n  > * {\\n    position: relative;\\n    z-index: 1;\\n  }\\n\\n  @media (max-width: \",\") {\\n    padding: \",\";\\n  }\\n\\n  @media (max-width: \",\") {\\n    padding: \",\";\\n  }\\n\"])),theme.colors.gradients.primary,theme.spacing[4],theme.breakpoints.md,theme.spacing[3],theme.breakpoints.sm,theme.spacing[2]);const Header=styled.div(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: \",\";\\n  \\n  @media (max-width: \",\") {\\n    flex-direction: column;\\n    gap: \",\";\\n  }\\n\"])),theme.spacing[8],theme.breakpoints.md,theme.spacing[4]);const ModernHeader=styled(Card)(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: \",\";\\n  background: rgba(255, 255, 255, 0.95);\\n  backdrop-filter: blur(20px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  box-shadow: \",\";\\n\\n  @media (max-width: \",\") {\\n    flex-direction: column;\\n    gap: \",\";\\n    text-align: center;\\n    margin-bottom: \",\";\\n  }\\n\\n  @media (max-width: \",\") {\\n    margin-bottom: \",\";\\n  }\\n\"])),theme.spacing[8],theme.shadows.xl,theme.breakpoints.md,theme.spacing[4],theme.spacing[6],theme.breakpoints.sm,theme.spacing[4]);const ModernBackButton=styled(Button)(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  background: rgba(255, 255, 255, 0.15);\\n  backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  color: \",\";\\n\\n  &:hover:not(:disabled) {\\n    background: rgba(255, 255, 255, 0.25);\\n    transform: translateY(-2px);\\n    box-shadow: \",\";\\n  }\\n\\n  @media (max-width: \",\") {\\n    width: 100%;\\n  }\\n\"])),theme.colors.text.inverse,theme.shadows.lg,theme.breakpoints.md);const ModernLevelInfo=styled.div(_templateObject7||(_templateObject7=_taggedTemplateLiteral([\"\\n  text-align: center;\\n  color: \",\";\\n\\n  @media (max-width: \",\") {\\n    order: -1;\\n  }\\n\"])),theme.colors.text.primary,theme.breakpoints.md);const ModernLevelTitle=styled(Heading)(_templateObject8||(_templateObject8=_taggedTemplateLiteral([\"\\n  margin-bottom: \",\";\\n  background: \",\";\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n\"])),theme.spacing[1],theme.colors.gradients.primary);const ModernLevelTheme=styled(Text)(_templateObject9||(_templateObject9=_taggedTemplateLiteral([\"\\n  opacity: 0.8;\\n  font-weight: \",\";\\n\"])),theme.typography.fontWeight.medium);const ModernConnectionStatus=styled(Badge)(_templateObject0||(_templateObject0=_taggedTemplateLiteral([\"\\n  background: \",\";\\n  color: \",\";\\n  border: 1px solid \",\";\\n  backdrop-filter: blur(10px);\\n  cursor: \",\";\\n\\n  &:hover {\\n    background: \",\";\\n  }\\n\\n  @media (max-width: \",\") {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n\"])),props=>props.isConnected?'rgba(34, 197, 94, 0.15)':'rgba(239, 68, 68, 0.15)',props=>props.isConnected?theme.colors.success[700]:theme.colors.error[700],props=>props.isConnected?theme.colors.success[200]:theme.colors.error[200],props=>props.isConnected?'default':'pointer',props=>props.isConnected?'rgba(34, 197, 94, 0.2)':'rgba(239, 68, 68, 0.2)',theme.breakpoints.md);const MainContent=styled.div(_templateObject1||(_templateObject1=_taggedTemplateLiteral([\"\\n  display: grid;\\n  grid-template-columns: 1fr 400px;\\n  gap: \",\";\\n  max-width: 1400px;\\n  margin: 0 auto;\\n\\n  @media (max-width: \",\") {\\n    grid-template-columns: 1fr;\\n    gap: \",\";\\n  }\\n\\n  @media (max-width: \",\") {\\n    /* On mobile, show flash card first, then camera */\\n    display: flex;\\n    flex-direction: column;\\n    gap: \",\";\\n  }\\n\"])),theme.spacing[8],theme.breakpoints.lg,theme.spacing[6],theme.breakpoints.sm,theme.spacing[4]);const FlashCardSection=styled.div(_templateObject10||(_templateObject10=_taggedTemplateLiteral([\"\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  animation: \",\" 0.6s ease;\\n\"])),fadeIn);const ProgressSection=styled.div(_templateObject11||(_templateObject11=_taggedTemplateLiteral([\"\\n  background: rgba(255, 255, 255, 0.95);\\n  border-radius: 20px;\\n  padding: 1.5rem;\\n  margin-bottom: 2rem;\\n  text-align: center;\\n\"])));const ProgressTitle=styled.h3(_templateObject12||(_templateObject12=_taggedTemplateLiteral([\"\\n  font-size: 1.25rem;\\n  font-weight: 700;\\n  color: #1e293b;\\n  margin-bottom: 1rem;\\n\"])));const ProgressBar=styled.div(_templateObject13||(_templateObject13=_taggedTemplateLiteral([\"\\n  width: 100%;\\n  height: 12px;\\n  background: #e2e8f0;\\n  border-radius: 6px;\\n  overflow: hidden;\\n  margin-bottom: 1rem;\\n\"])));const ProgressFill=styled.div(_templateObject14||(_templateObject14=_taggedTemplateLiteral([\"\\n  height: 100%;\\n  background: linear-gradient(90deg, #10b981, #34d399);\\n  border-radius: 6px;\\n  transition: width 0.5s ease;\\n\"])));const ProgressText=styled.div(_templateObject15||(_templateObject15=_taggedTemplateLiteral([\"\\n  font-size: 1rem;\\n  color: #64748b;\\n  font-weight: 600;\\n\"])));const Controls=styled.div(_templateObject16||(_templateObject16=_taggedTemplateLiteral([\"\\n  display: flex;\\n  gap: 1rem;\\n  margin-top: 2rem;\\n  justify-content: center;\\n  \\n  @media (max-width: 768px) {\\n    gap: 0.75rem;\\n    margin-top: 1.5rem;\\n  }\\n\"])));const ControlButton=styled.button(_templateObject17||(_templateObject17=_taggedTemplateLiteral([\"\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 1rem 2rem;\\n  border: none;\\n  border-radius: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  \\n  \",\"\\n  \\n  &:disabled {\\n    opacity: 0.5;\\n    cursor: not-allowed;\\n    transform: none !important;\\n  }\\n  \\n  @media (max-width: 768px) {\\n    padding: 0.75rem 1.5rem;\\n    font-size: 0.875rem;\\n  }\\n\"])),props=>{if(props.variant==='primary'){return\"\\n        background: linear-gradient(135deg, #3b82f6, #8b5cf6);\\n        color: white;\\n        &:hover {\\n          transform: translateY(-2px);\\n          box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);\\n        }\\n      \";}if(props.variant==='success'){return\"\\n        background: linear-gradient(135deg, #10b981, #34d399);\\n        color: white;\\n        &:hover {\\n          transform: translateY(-2px);\\n          box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);\\n        }\\n      \";}return\"\\n      background: rgba(255, 255, 255, 0.9);\\n      color: #64748b;\\n      &:hover {\\n        background: white;\\n        transform: translateY(-2px);\\n      }\\n    \";});const CameraSection=styled(Card)(_templateObject18||(_templateObject18=_taggedTemplateLiteral([\"\\n  background: rgba(255, 255, 255, 0.95);\\n  backdrop-filter: blur(20px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n\\n  @media (max-width: \",\") {\\n    order: 1; /* Show after flash card on mobile */\\n  }\\n\\n  @media (max-width: \",\") {\\n    order: 2; /* Ensure camera comes after flash card on mobile */\\n  }\\n\"])),theme.breakpoints.lg,theme.breakpoints.sm);const CameraTitle=styled.h3(_templateObject19||(_templateObject19=_taggedTemplateLiteral([\"\\n  font-size: 1.25rem;\\n  font-weight: 700;\\n  color: #1e293b;\\n  margin-bottom: 1rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n\"])));const WebcamContainer=styled.div(_templateObject20||(_templateObject20=_taggedTemplateLiteral([\"\\n  position: relative;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  background: #000;\\n  margin-bottom: 1rem;\\n\"])));const StatusOverlay=styled.div(_templateObject21||(_templateObject21=_taggedTemplateLiteral([\"\\n  position: absolute;\\n  top: 1rem;\\n  left: 1rem;\\n  right: 1rem;\\n  background: rgba(0, 0, 0, 0.8);\\n  color: white;\\n  padding: 0.75rem;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  text-align: center;\\n\"])));const PredictionDisplay=styled.div(_templateObject22||(_templateObject22=_taggedTemplateLiteral([\"\\n  background: #f8fafc;\\n  border-radius: 12px;\\n  padding: 1rem;\\n  text-align: center;\\n\"])));const PredictionText=styled.div(_templateObject23||(_templateObject23=_taggedTemplateLiteral([\"\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  color: #1e293b;\\n  margin-bottom: 0.5rem;\\n\"])));const ConfidenceText=styled.div(_templateObject24||(_templateObject24=_taggedTemplateLiteral([\"\\n  font-size: 0.875rem;\\n  color: #64748b;\\n\"])));const CompletionModal=styled.div(_templateObject25||(_templateObject25=_taggedTemplateLiteral([\"\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(0, 0, 0, 0.8);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 1000;\\n  animation: \",\" 0.3s ease;\\n\"])),fadeIn);const ModalContent=styled.div(_templateObject26||(_templateObject26=_taggedTemplateLiteral([\"\\n  background: white;\\n  border-radius: 24px;\\n  padding: 3rem;\\n  text-align: center;\\n  max-width: 500px;\\n  margin: 1rem;\\n  animation: \",\" 0.6s ease;\\n\"])),celebration);const ModalTitle=styled.h2(_templateObject27||(_templateObject27=_taggedTemplateLiteral([\"\\n  font-size: 2.5rem;\\n  font-weight: 800;\\n  color: #1e293b;\\n  margin-bottom: 1rem;\\n\"])));const ModalText=styled.p(_templateObject28||(_templateObject28=_taggedTemplateLiteral([\"\\n  font-size: 1.125rem;\\n  color: #64748b;\\n  margin-bottom: 2rem;\\n  line-height: 1.6;\\n\"])));const ModalButtons=styled.div(_templateObject29||(_templateObject29=_taggedTemplateLiteral([\"\\n  display: flex;\\n  gap: 1rem;\\n  justify-content: center;\\n\"])));const FlashCardTraining=_ref=>{let{level,onBack,userProgress={},onProgressUpdate}=_ref;const webcamRef=useRef(null);const[currentCardIndex,setCurrentCardIndex]=useState(0);const[completedCards,setCompletedCards]=useState(new Set());const[cardStates,setCardStates]=useState({});const[slideDirection,setSlideDirection]=useState(null);const[showCompletion,setShowCompletion]=useState(false);const[isCapturing,setIsCapturing]=useState(false);const levelInfo=getLevelInfo(level);const signs=getSignsForLevel(level);const currentSign=signs[currentCardIndex];// Use sign detection hook\nconst{isConnected,prediction,isAIRecording,recordingStatus,signMatched,targetSign,currentKeypoints,startRecording:startAIRecording,stopRecording:stopAIRecording,startFrameCapture,retryConnection,setLevel}=useSignDetection();const progress=completedCards.size/signs.length*100;// Start detection when connected\nconst startDetection=useCallback(()=>{if(!webcamRef.current)return;setIsCapturing(true);startFrameCapture(webcamRef,100);},[startFrameCapture]);// Save training data when sign is detected correctly\nconst saveTrainingData=useCallback(async(signName,keypoints,confidence)=>{try{console.log(\"\\uD83D\\uDCBE Saving training data for \".concat(signName,\" with confidence \").concat(confidence));const response=await fetch('http://localhost:8000/save-training-data',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({sign_name:signName,keypoints:keypoints,confidence:confidence,timestamp:new Date().toISOString()})});if(response.ok){const result=await response.json();console.log(\"\\u2705 Training data saved: \".concat(result.message));return true;}else{console.error('❌ Failed to save training data');return false;}}catch(error){console.error('❌ Error saving training data:',error);return false;}},[]);// Handle sign detection success with automatic recording and training data saving\nuseEffect(()=>{var _prediction$sign;if(signMatched&&currentSign&&(prediction===null||prediction===void 0?void 0:(_prediction$sign=prediction.sign)===null||_prediction$sign===void 0?void 0:_prediction$sign.toLowerCase())===currentSign.name.toLowerCase()){// Only proceed if this card hasn't been completed yet\nif(!completedCards.has(currentCardIndex)){console.log(\"\\uD83C\\uDFAF Correct sign detected: \".concat(currentSign.name,\" with confidence \").concat(prediction.confidence));// Save training data immediately when sign is detected correctly\nconst saveTrainingDataAsync=async()=>{if(currentKeypoints&&(prediction===null||prediction===void 0?void 0:prediction.confidence)>=0.5){const saved=await saveTrainingData(currentSign.name,currentKeypoints,prediction.confidence);if(saved){console.log(\"\\u2705 Training data saved successfully for \".concat(currentSign.name));}}};// Save training data\nsaveTrainingDataAsync();// Start automatic recording for additional training data\nif(!isAIRecording&&isConnected){console.log(\"\\uD83C\\uDFAC Starting automatic recording for \".concat(currentSign.name,\"...\"));startAIRecording(currentSign.name,true);// Start immediate recording session\n// Stop recording after 3 seconds\nsetTimeout(()=>{stopAIRecording();console.log(\"\\u2705 Automatic recording completed for: \".concat(currentSign.name));},3000);}// Mark card as completed\nsetCardStates(prev=>_objectSpread(_objectSpread({},prev),{},{[currentCardIndex]:'correct'}));setCompletedCards(prev=>new Set([...prev,currentCardIndex]));// Update progress\nif(onProgressUpdate){const newCompletedCount=completedCards.size+1;onProgressUpdate(level,newCompletedCount,signs.length);}// Auto-advance after 2 seconds (allowing time for user to see success)\nsetTimeout(()=>{if(currentCardIndex<signs.length-1){nextCard();}else{// Level completed\nsetShowCompletion(true);if(onProgressUpdate){onProgressUpdate(level,signs.length,signs.length);}}},2000);}}},[signMatched,currentSign,prediction,currentCardIndex,signs.length,level,onProgressUpdate,isAIRecording,isConnected,startAIRecording,stopAIRecording,completedCards,saveTrainingData]);// Set level when connected\nuseEffect(()=>{if(isConnected&&level){setLevel(level);}},[isConnected,level,setLevel]);// Auto-start detection when connected\nuseEffect(()=>{if(isConnected&&webcamRef.current&&!isCapturing){startDetection();}},[isConnected,startDetection,isCapturing]);const nextCard=useCallback(()=>{if(currentCardIndex<signs.length-1){setSlideDirection('right');setCurrentCardIndex(prev=>prev+1);setCardStates(prev=>_objectSpread(_objectSpread({},prev),{},{[currentCardIndex]:null}));setTimeout(()=>setSlideDirection(null),500);}},[currentCardIndex,signs.length]);const prevCard=useCallback(()=>{if(currentCardIndex>0){setSlideDirection('left');setCurrentCardIndex(prev=>prev-1);setCardStates(prev=>_objectSpread(_objectSpread({},prev),{},{[currentCardIndex]:null}));setTimeout(()=>setSlideDirection(null),500);}},[currentCardIndex]);const retryCard=useCallback(()=>{setCardStates(prev=>_objectSpread(_objectSpread({},prev),{},{[currentCardIndex]:null}));setCompletedCards(prev=>{const newSet=new Set(prev);newSet.delete(currentCardIndex);return newSet;});},[currentCardIndex]);const handleLevelComplete=()=>{setShowCompletion(false);onBack();};const handleNextLevel=()=>{setShowCompletion(false);// This would typically navigate to the next level\nonBack();};if(!levelInfo||!currentSign){return/*#__PURE__*/_jsx(Container,{children:/*#__PURE__*/_jsxs(\"div\",{style:{color:'white',textAlign:'center',padding:'2rem'},children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Level not found\"}),/*#__PURE__*/_jsx(\"button\",{onClick:onBack,children:\"Go Back\"})]})});}const isCurrentCardCompleted=completedCards.has(currentCardIndex);const currentCardState=cardStates[currentCardIndex];return/*#__PURE__*/_jsxs(ModernContainer,{children:[/*#__PURE__*/_jsxs(ModernHeader,{size:\"md\",children:[/*#__PURE__*/_jsxs(ModernBackButton,{variant:\"ghost\",size:\"md\",onClick:onBack,children:[/*#__PURE__*/_jsx(ArrowLeft,{size:20}),\"Back to Levels\"]}),/*#__PURE__*/_jsxs(ModernLevelInfo,{children:[/*#__PURE__*/_jsxs(ModernLevelTitle,{level:3,children:[\"Level \",level,\": \",levelInfo.name]}),/*#__PURE__*/_jsx(ModernLevelTheme,{size:\"lg\",children:levelInfo.theme})]}),/*#__PURE__*/_jsxs(ModernConnectionStatus,{isConnected:isConnected,onClick:!isConnected?retryConnection:undefined,children:[isConnected?/*#__PURE__*/_jsx(Wifi,{size:18}):/*#__PURE__*/_jsx(WifiOff,{size:18}),isConnected?'Connected':'Disconnected',!isConnected&&/*#__PURE__*/_jsx(RefreshCw,{size:14})]})]}),/*#__PURE__*/_jsxs(MainContent,{children:[/*#__PURE__*/_jsxs(FlashCardSection,{children:[/*#__PURE__*/_jsxs(ProgressSection,{children:[/*#__PURE__*/_jsx(ProgressTitle,{children:\"Level Progress\"}),/*#__PURE__*/_jsx(ProgressBar,{children:/*#__PURE__*/_jsx(ProgressFill,{style:{width:\"\".concat(progress,\"%\")}})}),/*#__PURE__*/_jsxs(ProgressText,{children:[completedCards.size,\" of \",signs.length,\" signs completed (\",Math.round(progress),\"%)\"]})]}),/*#__PURE__*/_jsx(FlashCard,{sign:currentSign,cardNumber:currentCardIndex+1,totalCards:signs.length,isCorrect:currentCardState==='correct',isIncorrect:currentCardState==='incorrect',isDetecting:isConnected&&!isCurrentCardCompleted,slideDirection:slideDirection,progress:currentCardIndex/signs.length*100}),/*#__PURE__*/_jsxs(Controls,{children:[/*#__PURE__*/_jsxs(ControlButton,{onClick:prevCard,disabled:currentCardIndex===0,children:[/*#__PURE__*/_jsx(ArrowLeft,{size:20}),\"Previous\"]}),isCurrentCardCompleted?/*#__PURE__*/_jsxs(ControlButton,{variant:\"success\",onClick:nextCard,disabled:currentCardIndex===signs.length-1,children:[/*#__PURE__*/_jsx(CheckCircle,{size:20}),\"Next Card\"]}):/*#__PURE__*/_jsxs(ControlButton,{onClick:retryCard,disabled:!isConnected,children:[/*#__PURE__*/_jsx(RotateCcw,{size:20}),\"Retry\"]}),/*#__PURE__*/_jsxs(ControlButton,{onClick:nextCard,disabled:currentCardIndex===signs.length-1,children:[\"Next\",/*#__PURE__*/_jsx(ArrowRight,{size:20})]})]})]}),/*#__PURE__*/_jsxs(CameraSection,{children:[/*#__PURE__*/_jsxs(CameraTitle,{children:[/*#__PURE__*/_jsx(Camera,{size:24}),\"Camera Feed\"]}),/*#__PURE__*/_jsxs(WebcamContainer,{children:[/*#__PURE__*/_jsx(Webcam,{ref:webcamRef,audio:false,width:\"100%\",height:\"auto\",screenshotFormat:\"image/jpeg\",videoConstraints:{width:640,height:480,facingMode:\"user\"}}),recordingStatus&&/*#__PURE__*/_jsx(StatusOverlay,{children:recordingStatus})]}),/*#__PURE__*/_jsxs(PredictionDisplay,{children:[/*#__PURE__*/_jsx(PredictionText,{children:prediction!==null&&prediction!==void 0&&prediction.sign?\"Detected: \".concat(prediction.sign):'Show the sign to get started'}),(prediction===null||prediction===void 0?void 0:prediction.confidence)&&/*#__PURE__*/_jsxs(ConfidenceText,{children:[\"Confidence: \",Math.round(prediction.confidence*100),\"%\"]})]})]})]}),showCompletion&&/*#__PURE__*/_jsx(CompletionModal,{children:/*#__PURE__*/_jsxs(ModalContent,{children:[/*#__PURE__*/_jsx(Trophy,{size:80,style:{color:'#f59e0b',marginBottom:'1rem'}}),/*#__PURE__*/_jsx(ModalTitle,{children:\"\\uD83C\\uDF89 Level Complete!\"}),/*#__PURE__*/_jsxs(ModalText,{children:[\"Congratulations! You've successfully completed Level \",level,\": \",levelInfo.name,\". You've mastered all \",signs.length,\" signs in this level!\"]}),/*#__PURE__*/_jsxs(ModalButtons,{children:[/*#__PURE__*/_jsxs(ControlButton,{onClick:handleLevelComplete,children:[/*#__PURE__*/_jsx(Home,{size:20}),\"Back to Levels\"]}),level<5&&/*#__PURE__*/_jsxs(ControlButton,{variant:\"primary\",onClick:handleNextLevel,children:[/*#__PURE__*/_jsx(Target,{size:20}),\"Next Level\"]})]})]})})]});};export default FlashCardTraining;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "useEffect", "styled", "keyframes", "Webcam", "ArrowLeft", "ArrowRight", "RotateCcw", "Home", "Camera", "Wifi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RefreshCw", "CheckCircle", "Trophy", "Target", "Zap", "<PERSON><PERSON><PERSON>", "Award", "FlashCard", "useSignDetection", "getSignsForLevel", "getLevelInfo", "theme", "Container", "Card", "<PERSON><PERSON>", "Heading", "Text", "Badge", "jsx", "_jsx", "jsxs", "_jsxs", "fadeIn", "_templateObject", "_taggedTemplateLiteral", "celebration", "_templateObject2", "ModernContainer", "div", "_templateObject3", "colors", "gradients", "primary", "spacing", "breakpoints", "md", "sm", "Header", "_templateObject4", "ModernHeader", "_templateObject5", "shadows", "xl", "ModernBackButton", "_templateObject6", "text", "inverse", "lg", "ModernLevelInfo", "_templateObject7", "ModernLevelTitle", "_templateObject8", "ModernLevelTheme", "_templateObject9", "typography", "fontWeight", "medium", "ModernConnectionStatus", "_templateObject0", "props", "isConnected", "success", "error", "MainContent", "_templateObject1", "FlashCardSection", "_templateObject10", "ProgressSection", "_templateObject11", "ProgressTitle", "h3", "_templateObject12", "ProgressBar", "_templateObject13", "ProgressFill", "_templateObject14", "ProgressText", "_templateObject15", "Controls", "_templateObject16", "ControlButton", "button", "_templateObject17", "variant", "CameraSection", "_templateObject18", "CameraTitle", "_templateObject19", "WebcamContainer", "_templateObject20", "StatusOverlay", "_templateObject21", "PredictionDisplay", "_templateObject22", "PredictionText", "_templateObject23", "ConfidenceText", "_templateObject24", "CompletionModal", "_templateObject25", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_templateObject26", "ModalTitle", "h2", "_templateObject27", "ModalText", "p", "_templateObject28", "ModalButtons", "_templateObject29", "FlashCardTraining", "_ref", "level", "onBack", "userProgress", "onProgressUpdate", "webcamRef", "currentCardIndex", "setCurrentCardIndex", "completedCards", "setCompletedCards", "Set", "cardStates", "setCardStates", "slideDirection", "setSlideDirection", "showCompletion", "setShowCompletion", "isCapturing", "setIsCapturing", "levelInfo", "signs", "currentSign", "prediction", "isAIRecording", "recordingStatus", "signMatched", "targetSign", "currentKeypoints", "startRecording", "startAIRecording", "stopRecording", "stopAIRecording", "startFrameCapture", "retryConnection", "setLevel", "progress", "size", "length", "startDetection", "current", "saveTrainingData", "signName", "keypoints", "confidence", "console", "log", "concat", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "sign_name", "timestamp", "Date", "toISOString", "ok", "result", "json", "message", "_prediction$sign", "sign", "toLowerCase", "name", "has", "saveTrainingDataAsync", "saved", "setTimeout", "prev", "_objectSpread", "newCompletedCount", "nextCard", "prevCard", "retryCard", "newSet", "delete", "handleLevelComplete", "handleNextLevel", "children", "style", "color", "textAlign", "padding", "onClick", "isCurrentCardCompleted", "currentCardState", "undefined", "width", "Math", "round", "cardNumber", "totalCards", "isCorrect", "isIncorrect", "isDetecting", "disabled", "ref", "audio", "height", "screenshotFormat", "videoConstraints", "facingMode", "marginBottom"], "sources": ["D:/ASL/ASL-Training/src/components/FlashCardTraining.js"], "sourcesContent": ["import React, { useState, useRef, useCallback, useEffect } from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport Webcam from 'react-webcam';\nimport {\n  ArrowLeft,\n  ArrowRight,\n  RotateCcw,\n  Home,\n  Camera,\n  Wifi,\n  WifiOff,\n  RefreshCw,\n  CheckCircle,\n  Trophy,\n  Target,\n  Zap,\n  Sparkles,\n  Award\n} from 'lucide-react';\nimport Flash<PERSON>ard from './FlashCard';\nimport { useSignDetection } from '../hooks/useSignDetection';\nimport { getSignsForLevel, getLevelInfo } from '../data/signLevels';\nimport { theme } from '../styles/theme';\nimport { Container, Card, Button, Heading, Text, Badge } from './ui/ModernComponents';\n\n// Animations\nconst fadeIn = keyframes`\n  from { opacity: 0; transform: translateY(20px); }\n  to { opacity: 1; transform: translateY(0); }\n`;\n\nconst celebration = keyframes`\n  0%, 100% { transform: scale(1) rotate(0deg); }\n  25% { transform: scale(1.1) rotate(-5deg); }\n  75% { transform: scale(1.1) rotate(5deg); }\n`;\n\n// Modern Styled Components\nconst ModernContainer = styled.div`\n  min-height: 100vh;\n  background: ${theme.colors.gradients.primary};\n  padding: ${theme.spacing[4]};\n  position: relative;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\n      radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),\n      radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);\n    pointer-events: none;\n  }\n\n  > * {\n    position: relative;\n    z-index: 1;\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    padding: ${theme.spacing[3]};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: ${theme.spacing[2]};\n  }\n`;\nconst Header = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${theme.spacing[8]};\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    flex-direction: column;\n    gap: ${theme.spacing[4]};\n  }\n`;\n\nconst ModernHeader = styled(Card)`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${theme.spacing[8]};\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: ${theme.shadows.xl};\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    flex-direction: column;\n    gap: ${theme.spacing[4]};\n    text-align: center;\n    margin-bottom: ${theme.spacing[6]};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    margin-bottom: ${theme.spacing[4]};\n  }\n`;\n\nconst ModernBackButton = styled(Button)`\n  background: rgba(255, 255, 255, 0.15);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  color: ${theme.colors.text.inverse};\n\n  &:hover:not(:disabled) {\n    background: rgba(255, 255, 255, 0.25);\n    transform: translateY(-2px);\n    box-shadow: ${theme.shadows.lg};\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    width: 100%;\n  }\n`;\n\nconst ModernLevelInfo = styled.div`\n  text-align: center;\n  color: ${theme.colors.text.primary};\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    order: -1;\n  }\n`;\n\nconst ModernLevelTitle = styled(Heading)`\n  margin-bottom: ${theme.spacing[1]};\n  background: ${theme.colors.gradients.primary};\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n`;\n\nconst ModernLevelTheme = styled(Text)`\n  opacity: 0.8;\n  font-weight: ${theme.typography.fontWeight.medium};\n`;\n\nconst ModernConnectionStatus = styled(Badge)`\n  background: ${props => props.isConnected\n    ? 'rgba(34, 197, 94, 0.15)'\n    : 'rgba(239, 68, 68, 0.15)'\n  };\n  color: ${props => props.isConnected\n    ? theme.colors.success[700]\n    : theme.colors.error[700]\n  };\n  border: 1px solid ${props => props.isConnected\n    ? theme.colors.success[200]\n    : theme.colors.error[200]\n  };\n  backdrop-filter: blur(10px);\n  cursor: ${props => props.isConnected ? 'default' : 'pointer'};\n\n  &:hover {\n    background: ${props => props.isConnected\n      ? 'rgba(34, 197, 94, 0.2)'\n      : 'rgba(239, 68, 68, 0.2)'\n    };\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    width: 100%;\n    justify-content: center;\n  }\n`;\n\nconst MainContent = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 400px;\n  gap: ${theme.spacing[8]};\n  max-width: 1400px;\n  margin: 0 auto;\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    grid-template-columns: 1fr;\n    gap: ${theme.spacing[6]};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    /* On mobile, show flash card first, then camera */\n    display: flex;\n    flex-direction: column;\n    gap: ${theme.spacing[4]};\n  }\n`;\n\nconst FlashCardSection = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  animation: ${fadeIn} 0.6s ease;\n`;\n\nconst ProgressSection = styled.div`\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20px;\n  padding: 1.5rem;\n  margin-bottom: 2rem;\n  text-align: center;\n`;\n\nconst ProgressTitle = styled.h3`\n  font-size: 1.25rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin-bottom: 1rem;\n`;\n\nconst ProgressBar = styled.div`\n  width: 100%;\n  height: 12px;\n  background: #e2e8f0;\n  border-radius: 6px;\n  overflow: hidden;\n  margin-bottom: 1rem;\n`;\n\nconst ProgressFill = styled.div`\n  height: 100%;\n  background: linear-gradient(90deg, #10b981, #34d399);\n  border-radius: 6px;\n  transition: width 0.5s ease;\n`;\n\nconst ProgressText = styled.div`\n  font-size: 1rem;\n  color: #64748b;\n  font-weight: 600;\n`;\n\nconst Controls = styled.div`\n  display: flex;\n  gap: 1rem;\n  margin-top: 2rem;\n  justify-content: center;\n  \n  @media (max-width: 768px) {\n    gap: 0.75rem;\n    margin-top: 1.5rem;\n  }\n`;\n\nconst ControlButton = styled.button`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 1rem 2rem;\n  border: none;\n  border-radius: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props => {\n    if (props.variant === 'primary') {\n      return `\n        background: linear-gradient(135deg, #3b82f6, #8b5cf6);\n        color: white;\n        &:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);\n        }\n      `;\n    }\n    if (props.variant === 'success') {\n      return `\n        background: linear-gradient(135deg, #10b981, #34d399);\n        color: white;\n        &:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);\n        }\n      `;\n    }\n    return `\n      background: rgba(255, 255, 255, 0.9);\n      color: #64748b;\n      &:hover {\n        background: white;\n        transform: translateY(-2px);\n      }\n    `;\n  }}\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none !important;\n  }\n  \n  @media (max-width: 768px) {\n    padding: 0.75rem 1.5rem;\n    font-size: 0.875rem;\n  }\n`;\n\nconst CameraSection = styled(Card)`\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    order: 1; /* Show after flash card on mobile */\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    order: 2; /* Ensure camera comes after flash card on mobile */\n  }\n`;\n\nconst CameraTitle = styled.h3`\n  font-size: 1.25rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin-bottom: 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n`;\n\nconst WebcamContainer = styled.div`\n  position: relative;\n  border-radius: 16px;\n  overflow: hidden;\n  background: #000;\n  margin-bottom: 1rem;\n`;\n\nconst StatusOverlay = styled.div`\n  position: absolute;\n  top: 1rem;\n  left: 1rem;\n  right: 1rem;\n  background: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: 0.75rem;\n  border-radius: 8px;\n  font-weight: 600;\n  text-align: center;\n`;\n\nconst PredictionDisplay = styled.div`\n  background: #f8fafc;\n  border-radius: 12px;\n  padding: 1rem;\n  text-align: center;\n`;\n\nconst PredictionText = styled.div`\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin-bottom: 0.5rem;\n`;\n\nconst ConfidenceText = styled.div`\n  font-size: 0.875rem;\n  color: #64748b;\n`;\n\nconst CompletionModal = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  animation: ${fadeIn} 0.3s ease;\n`;\n\nconst ModalContent = styled.div`\n  background: white;\n  border-radius: 24px;\n  padding: 3rem;\n  text-align: center;\n  max-width: 500px;\n  margin: 1rem;\n  animation: ${celebration} 0.6s ease;\n`;\n\nconst ModalTitle = styled.h2`\n  font-size: 2.5rem;\n  font-weight: 800;\n  color: #1e293b;\n  margin-bottom: 1rem;\n`;\n\nconst ModalText = styled.p`\n  font-size: 1.125rem;\n  color: #64748b;\n  margin-bottom: 2rem;\n  line-height: 1.6;\n`;\n\nconst ModalButtons = styled.div`\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n`;\n\nconst FlashCardTraining = ({ \n  level, \n  onBack, \n  userProgress = {}, \n  onProgressUpdate \n}) => {\n  const webcamRef = useRef(null);\n  const [currentCardIndex, setCurrentCardIndex] = useState(0);\n  const [completedCards, setCompletedCards] = useState(new Set());\n  const [cardStates, setCardStates] = useState({});\n  const [slideDirection, setSlideDirection] = useState(null);\n  const [showCompletion, setShowCompletion] = useState(false);\n  const [isCapturing, setIsCapturing] = useState(false);\n\n  const levelInfo = getLevelInfo(level);\n  const signs = getSignsForLevel(level);\n  const currentSign = signs[currentCardIndex];\n\n  // Use sign detection hook\n  const {\n    isConnected,\n    prediction,\n    isAIRecording,\n    recordingStatus,\n    signMatched,\n    targetSign,\n    currentKeypoints,\n    startRecording: startAIRecording,\n    stopRecording: stopAIRecording,\n    startFrameCapture,\n    retryConnection,\n    setLevel\n  } = useSignDetection();\n\n  const progress = (completedCards.size / signs.length) * 100;\n\n  // Start detection when connected\n  const startDetection = useCallback(() => {\n    if (!webcamRef.current) return;\n    setIsCapturing(true);\n    startFrameCapture(webcamRef, 100);\n  }, [startFrameCapture]);\n\n  // Save training data when sign is detected correctly\n  const saveTrainingData = useCallback(async (signName, keypoints, confidence) => {\n    try {\n      console.log(`💾 Saving training data for ${signName} with confidence ${confidence}`);\n      \n      const response = await fetch('http://localhost:8000/save-training-data', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          sign_name: signName,\n          keypoints: keypoints,\n          confidence: confidence,\n          timestamp: new Date().toISOString()\n        })\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        console.log(`✅ Training data saved: ${result.message}`);\n        return true;\n      } else {\n        console.error('❌ Failed to save training data');\n        return false;\n      }\n    } catch (error) {\n      console.error('❌ Error saving training data:', error);\n      return false;\n    }\n  }, []);\n\n  // Handle sign detection success with automatic recording and training data saving\n  useEffect(() => {\n    if (signMatched && currentSign && prediction?.sign?.toLowerCase() === currentSign.name.toLowerCase()) {\n      // Only proceed if this card hasn't been completed yet\n      if (!completedCards.has(currentCardIndex)) {\n        console.log(`🎯 Correct sign detected: ${currentSign.name} with confidence ${prediction.confidence}`);\n        \n        // Save training data immediately when sign is detected correctly\n        const saveTrainingDataAsync = async () => {\n          if (currentKeypoints && prediction?.confidence >= 0.5) {\n            const saved = await saveTrainingData(currentSign.name, currentKeypoints, prediction.confidence);\n            if (saved) {\n              console.log(`✅ Training data saved successfully for ${currentSign.name}`);\n            }\n          }\n        };\n        \n        // Save training data\n        saveTrainingDataAsync();\n\n        // Start automatic recording for additional training data\n        if (!isAIRecording && isConnected) {\n          console.log(`🎬 Starting automatic recording for ${currentSign.name}...`);\n          startAIRecording(currentSign.name, true); // Start immediate recording session\n\n          // Stop recording after 3 seconds\n          setTimeout(() => {\n            stopAIRecording();\n            console.log(`✅ Automatic recording completed for: ${currentSign.name}`);\n          }, 3000);\n        }\n\n        // Mark card as completed\n        setCardStates(prev => ({ ...prev, [currentCardIndex]: 'correct' }));\n        setCompletedCards(prev => new Set([...prev, currentCardIndex]));\n\n        // Update progress\n        if (onProgressUpdate) {\n          const newCompletedCount = completedCards.size + 1;\n          onProgressUpdate(level, newCompletedCount, signs.length);\n        }\n\n        // Auto-advance after 2 seconds (allowing time for user to see success)\n        setTimeout(() => {\n          if (currentCardIndex < signs.length - 1) {\n            nextCard();\n          } else {\n            // Level completed\n            setShowCompletion(true);\n            if (onProgressUpdate) {\n              onProgressUpdate(level, signs.length, signs.length);\n            }\n          }\n        }, 2000);\n      }\n    }\n  }, [signMatched, currentSign, prediction, currentCardIndex, signs.length, level, onProgressUpdate, isAIRecording, isConnected, startAIRecording, stopAIRecording, completedCards, saveTrainingData]);\n\n  // Set level when connected\n  useEffect(() => {\n    if (isConnected && level) {\n      setLevel(level);\n    }\n  }, [isConnected, level, setLevel]);\n\n  // Auto-start detection when connected\n  useEffect(() => {\n    if (isConnected && webcamRef.current && !isCapturing) {\n      startDetection();\n    }\n  }, [isConnected, startDetection, isCapturing]);\n\n  const nextCard = useCallback(() => {\n    if (currentCardIndex < signs.length - 1) {\n      setSlideDirection('right');\n      setCurrentCardIndex(prev => prev + 1);\n      setCardStates(prev => ({ ...prev, [currentCardIndex]: null }));\n      setTimeout(() => setSlideDirection(null), 500);\n    }\n  }, [currentCardIndex, signs.length]);\n\n  const prevCard = useCallback(() => {\n    if (currentCardIndex > 0) {\n      setSlideDirection('left');\n      setCurrentCardIndex(prev => prev - 1);\n      setCardStates(prev => ({ ...prev, [currentCardIndex]: null }));\n      setTimeout(() => setSlideDirection(null), 500);\n    }\n  }, [currentCardIndex]);\n\n  const retryCard = useCallback(() => {\n    setCardStates(prev => ({ ...prev, [currentCardIndex]: null }));\n    setCompletedCards(prev => {\n      const newSet = new Set(prev);\n      newSet.delete(currentCardIndex);\n      return newSet;\n    });\n  }, [currentCardIndex]);\n\n  const handleLevelComplete = () => {\n    setShowCompletion(false);\n    onBack();\n  };\n\n  const handleNextLevel = () => {\n    setShowCompletion(false);\n    // This would typically navigate to the next level\n    onBack();\n  };\n\n  if (!levelInfo || !currentSign) {\n    return (\n      <Container>\n        <div style={{ color: 'white', textAlign: 'center', padding: '2rem' }}>\n          <h2>Level not found</h2>\n          <button onClick={onBack}>Go Back</button>\n        </div>\n      </Container>\n    );\n  }\n\n  const isCurrentCardCompleted = completedCards.has(currentCardIndex);\n  const currentCardState = cardStates[currentCardIndex];\n\n  return (\n    <ModernContainer>\n      <ModernHeader size=\"md\">\n        <ModernBackButton variant=\"ghost\" size=\"md\" onClick={onBack}>\n          <ArrowLeft size={20} />\n          Back to Levels\n        </ModernBackButton>\n\n        <ModernLevelInfo>\n          <ModernLevelTitle level={3}>\n            Level {level}: {levelInfo.name}\n          </ModernLevelTitle>\n          <ModernLevelTheme size=\"lg\">\n            {levelInfo.theme}\n          </ModernLevelTheme>\n        </ModernLevelInfo>\n\n        <ModernConnectionStatus\n          isConnected={isConnected}\n          onClick={!isConnected ? retryConnection : undefined}\n        >\n          {isConnected ? <Wifi size={18} /> : <WifiOff size={18} />}\n          {isConnected ? 'Connected' : 'Disconnected'}\n          {!isConnected && <RefreshCw size={14} />}\n        </ModernConnectionStatus>\n      </ModernHeader>\n\n      <MainContent>\n        <FlashCardSection>\n          <ProgressSection>\n            <ProgressTitle>Level Progress</ProgressTitle>\n            <ProgressBar>\n              <ProgressFill style={{ width: `${progress}%` }} />\n            </ProgressBar>\n            <ProgressText>\n              {completedCards.size} of {signs.length} signs completed ({Math.round(progress)}%)\n            </ProgressText>\n          </ProgressSection>\n\n          <FlashCard\n            sign={currentSign}\n            cardNumber={currentCardIndex + 1}\n            totalCards={signs.length}\n            isCorrect={currentCardState === 'correct'}\n            isIncorrect={currentCardState === 'incorrect'}\n            isDetecting={isConnected && !isCurrentCardCompleted}\n            slideDirection={slideDirection}\n            progress={(currentCardIndex / signs.length) * 100}\n          />\n\n          <Controls>\n            <ControlButton\n              onClick={prevCard}\n              disabled={currentCardIndex === 0}\n            >\n              <ArrowLeft size={20} />\n              Previous\n            </ControlButton>\n\n            {isCurrentCardCompleted ? (\n              <ControlButton\n                variant=\"success\"\n                onClick={nextCard}\n                disabled={currentCardIndex === signs.length - 1}\n              >\n                <CheckCircle size={20} />\n                Next Card\n              </ControlButton>\n            ) : (\n              <ControlButton\n                onClick={retryCard}\n                disabled={!isConnected}\n              >\n                <RotateCcw size={20} />\n                Retry\n              </ControlButton>\n            )}\n\n            <ControlButton\n              onClick={nextCard}\n              disabled={currentCardIndex === signs.length - 1}\n            >\n              Next\n              <ArrowRight size={20} />\n            </ControlButton>\n          </Controls>\n        </FlashCardSection>\n\n        <CameraSection>\n          <CameraTitle>\n            <Camera size={24} />\n            Camera Feed\n          </CameraTitle>\n\n          <WebcamContainer>\n            <Webcam\n              ref={webcamRef}\n              audio={false}\n              width=\"100%\"\n              height=\"auto\"\n              screenshotFormat=\"image/jpeg\"\n              videoConstraints={{\n                width: 640,\n                height: 480,\n                facingMode: \"user\"\n              }}\n            />\n\n            {recordingStatus && (\n              <StatusOverlay>\n                {recordingStatus}\n              </StatusOverlay>\n            )}\n          </WebcamContainer>\n\n          <PredictionDisplay>\n            <PredictionText>\n              {prediction?.sign ? `Detected: ${prediction.sign}` : 'Show the sign to get started'}\n            </PredictionText>\n            {prediction?.confidence && (\n              <ConfidenceText>\n                Confidence: {Math.round(prediction.confidence * 100)}%\n              </ConfidenceText>\n            )}\n          </PredictionDisplay>\n        </CameraSection>\n      </MainContent>\n\n      {showCompletion && (\n        <CompletionModal>\n          <ModalContent>\n            <Trophy size={80} style={{ color: '#f59e0b', marginBottom: '1rem' }} />\n            <ModalTitle>🎉 Level Complete!</ModalTitle>\n            <ModalText>\n              Congratulations! You've successfully completed Level {level}: {levelInfo.name}.\n              You've mastered all {signs.length} signs in this level!\n            </ModalText>\n            <ModalButtons>\n              <ControlButton onClick={handleLevelComplete}>\n                <Home size={20} />\n                Back to Levels\n              </ControlButton>\n              {level < 5 && (\n                <ControlButton variant=\"primary\" onClick={handleNextLevel}>\n                  <Target size={20} />\n                  Next Level\n                </ControlButton>\n              )}\n            </ModalButtons>\n          </ModalContent>\n        </CompletionModal>\n      )}\n    </ModernContainer>\n  );\n};\n\nexport default FlashCardTraining;\n"], "mappings": "uwBAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,WAAW,CAAEC,SAAS,KAAQ,OAAO,CACvE,MAAO,CAAAC,MAAM,EAAIC,SAAS,KAAQ,mBAAmB,CACrD,MAAO,CAAAC,MAAM,KAAM,cAAc,CACjC,OACEC,SAAS,CACTC,UAAU,CACVC,SAAS,CACTC,IAAI,CACJC,MAAM,CACNC,IAAI,CACJC,OAAO,CACPC,SAAS,CACTC,WAAW,CACXC,MAAM,CACNC,MAAM,CACNC,GAAG,CACHC,QAAQ,CACRC,KAAK,KACA,cAAc,CACrB,MAAO,CAAAC,SAAS,KAAM,aAAa,CACnC,OAASC,gBAAgB,KAAQ,2BAA2B,CAC5D,OAASC,gBAAgB,CAAEC,YAAY,KAAQ,oBAAoB,CACnE,OAASC,KAAK,KAAQ,iBAAiB,CACvC,OAASC,SAAS,CAAEC,IAAI,CAAEC,MAAM,CAAEC,OAAO,CAAEC,IAAI,CAAEC,KAAK,KAAQ,uBAAuB,CAErF;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,MAAM,CAAG/B,SAAS,CAAAgC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,+GAGvB,CAED,KAAM,CAAAC,WAAW,CAAGlC,SAAS,CAAAmC,gBAAA,GAAAA,gBAAA,CAAAF,sBAAA,2JAI5B,CAED;AACA,KAAM,CAAAG,eAAe,CAAGrC,MAAM,CAACsC,GAAG,CAAAC,gBAAA,GAAAA,gBAAA,CAAAL,sBAAA,srBAElBb,KAAK,CAACmB,MAAM,CAACC,SAAS,CAACC,OAAO,CACjCrB,KAAK,CAACsB,OAAO,CAAC,CAAC,CAAC,CAsBNtB,KAAK,CAACuB,WAAW,CAACC,EAAE,CAC5BxB,KAAK,CAACsB,OAAO,CAAC,CAAC,CAAC,CAGRtB,KAAK,CAACuB,WAAW,CAACE,EAAE,CAC5BzB,KAAK,CAACsB,OAAO,CAAC,CAAC,CAAC,CAE9B,CACD,KAAM,CAAAI,MAAM,CAAG/C,MAAM,CAACsC,GAAG,CAAAU,gBAAA,GAAAA,gBAAA,CAAAd,sBAAA,gMAINb,KAAK,CAACsB,OAAO,CAAC,CAAC,CAAC,CAEZtB,KAAK,CAACuB,WAAW,CAACC,EAAE,CAEhCxB,KAAK,CAACsB,OAAO,CAAC,CAAC,CAAC,CAE1B,CAED,KAAM,CAAAM,YAAY,CAAGjD,MAAM,CAACuB,IAAI,CAAC,CAAA2B,gBAAA,GAAAA,gBAAA,CAAAhB,sBAAA,0bAIdb,KAAK,CAACsB,OAAO,CAAC,CAAC,CAAC,CAInBtB,KAAK,CAAC8B,OAAO,CAACC,EAAE,CAET/B,KAAK,CAACuB,WAAW,CAACC,EAAE,CAEhCxB,KAAK,CAACsB,OAAO,CAAC,CAAC,CAAC,CAENtB,KAAK,CAACsB,OAAO,CAAC,CAAC,CAAC,CAGdtB,KAAK,CAACuB,WAAW,CAACE,EAAE,CACtBzB,KAAK,CAACsB,OAAO,CAAC,CAAC,CAAC,CAEpC,CAED,KAAM,CAAAU,gBAAgB,CAAGrD,MAAM,CAACwB,MAAM,CAAC,CAAA8B,gBAAA,GAAAA,gBAAA,CAAApB,sBAAA,+UAI5Bb,KAAK,CAACmB,MAAM,CAACe,IAAI,CAACC,OAAO,CAKlBnC,KAAK,CAAC8B,OAAO,CAACM,EAAE,CAGXpC,KAAK,CAACuB,WAAW,CAACC,EAAE,CAG1C,CAED,KAAM,CAAAa,eAAe,CAAG1D,MAAM,CAACsC,GAAG,CAAAqB,gBAAA,GAAAA,gBAAA,CAAAzB,sBAAA,oGAEvBb,KAAK,CAACmB,MAAM,CAACe,IAAI,CAACb,OAAO,CAEbrB,KAAK,CAACuB,WAAW,CAACC,EAAE,CAG1C,CAED,KAAM,CAAAe,gBAAgB,CAAG5D,MAAM,CAACyB,OAAO,CAAC,CAAAoC,gBAAA,GAAAA,gBAAA,CAAA3B,sBAAA,0JACrBb,KAAK,CAACsB,OAAO,CAAC,CAAC,CAAC,CACnBtB,KAAK,CAACmB,MAAM,CAACC,SAAS,CAACC,OAAO,CAI7C,CAED,KAAM,CAAAoB,gBAAgB,CAAG9D,MAAM,CAAC0B,IAAI,CAAC,CAAAqC,gBAAA,GAAAA,gBAAA,CAAA7B,sBAAA,gDAEpBb,KAAK,CAAC2C,UAAU,CAACC,UAAU,CAACC,MAAM,CAClD,CAED,KAAM,CAAAC,sBAAsB,CAAGnE,MAAM,CAAC2B,KAAK,CAAC,CAAAyC,gBAAA,GAAAA,gBAAA,CAAAlC,sBAAA,qPAC5BmC,KAAK,EAAIA,KAAK,CAACC,WAAW,CACpC,yBAAyB,CACzB,yBAAyB,CAEpBD,KAAK,EAAIA,KAAK,CAACC,WAAW,CAC/BjD,KAAK,CAACmB,MAAM,CAAC+B,OAAO,CAAC,GAAG,CAAC,CACzBlD,KAAK,CAACmB,MAAM,CAACgC,KAAK,CAAC,GAAG,CAAC,CAEPH,KAAK,EAAIA,KAAK,CAACC,WAAW,CAC1CjD,KAAK,CAACmB,MAAM,CAAC+B,OAAO,CAAC,GAAG,CAAC,CACzBlD,KAAK,CAACmB,MAAM,CAACgC,KAAK,CAAC,GAAG,CAAC,CAGjBH,KAAK,EAAIA,KAAK,CAACC,WAAW,CAAG,SAAS,CAAG,SAAS,CAG5CD,KAAK,EAAIA,KAAK,CAACC,WAAW,CACpC,wBAAwB,CACxB,wBAAwB,CAITjD,KAAK,CAACuB,WAAW,CAACC,EAAE,CAI1C,CAED,KAAM,CAAA4B,WAAW,CAAGzE,MAAM,CAACsC,GAAG,CAAAoC,gBAAA,GAAAA,gBAAA,CAAAxC,sBAAA,wWAGrBb,KAAK,CAACsB,OAAO,CAAC,CAAC,CAAC,CAIFtB,KAAK,CAACuB,WAAW,CAACa,EAAE,CAEhCpC,KAAK,CAACsB,OAAO,CAAC,CAAC,CAAC,CAGJtB,KAAK,CAACuB,WAAW,CAACE,EAAE,CAIhCzB,KAAK,CAACsB,OAAO,CAAC,CAAC,CAAC,CAE1B,CAED,KAAM,CAAAgC,gBAAgB,CAAG3E,MAAM,CAACsC,GAAG,CAAAsC,iBAAA,GAAAA,iBAAA,CAAA1C,sBAAA,4GAIpBF,MAAM,CACpB,CAED,KAAM,CAAA6C,eAAe,CAAG7E,MAAM,CAACsC,GAAG,CAAAwC,iBAAA,GAAAA,iBAAA,CAAA5C,sBAAA,+IAMjC,CAED,KAAM,CAAA6C,aAAa,CAAG/E,MAAM,CAACgF,EAAE,CAAAC,iBAAA,GAAAA,iBAAA,CAAA/C,sBAAA,iGAK9B,CAED,KAAM,CAAAgD,WAAW,CAAGlF,MAAM,CAACsC,GAAG,CAAA6C,iBAAA,GAAAA,iBAAA,CAAAjD,sBAAA,uIAO7B,CAED,KAAM,CAAAkD,YAAY,CAAGpF,MAAM,CAACsC,GAAG,CAAA+C,iBAAA,GAAAA,iBAAA,CAAAnD,sBAAA,2IAK9B,CAED,KAAM,CAAAoD,YAAY,CAAGtF,MAAM,CAACsC,GAAG,CAAAiD,iBAAA,GAAAA,iBAAA,CAAArD,sBAAA,sEAI9B,CAED,KAAM,CAAAsD,QAAQ,CAAGxF,MAAM,CAACsC,GAAG,CAAAmD,iBAAA,GAAAA,iBAAA,CAAAvD,sBAAA,+KAU1B,CAED,KAAM,CAAAwD,aAAa,CAAG1F,MAAM,CAAC2F,MAAM,CAAAC,iBAAA,GAAAA,iBAAA,CAAA1D,sBAAA,6ZAW/BmC,KAAK,EAAI,CACT,GAAIA,KAAK,CAACwB,OAAO,GAAK,SAAS,CAAE,CAC/B,0OAQF,CACA,GAAIxB,KAAK,CAACwB,OAAO,GAAK,SAAS,CAAE,CAC/B,0OAQF,CACA,+KAQF,CAAC,CAYF,CAED,KAAM,CAAAC,aAAa,CAAG9F,MAAM,CAACuB,IAAI,CAAC,CAAAwE,iBAAA,GAAAA,iBAAA,CAAA7D,sBAAA,oUAKXb,KAAK,CAACuB,WAAW,CAACa,EAAE,CAIpBpC,KAAK,CAACuB,WAAW,CAACE,EAAE,CAG1C,CAED,KAAM,CAAAkD,WAAW,CAAGhG,MAAM,CAACgF,EAAE,CAAAiB,iBAAA,GAAAA,iBAAA,CAAA/D,sBAAA,2JAQ5B,CAED,KAAM,CAAAgE,eAAe,CAAGlG,MAAM,CAACsC,GAAG,CAAA6D,iBAAA,GAAAA,iBAAA,CAAAjE,sBAAA,2HAMjC,CAED,KAAM,CAAAkE,aAAa,CAAGpG,MAAM,CAACsC,GAAG,CAAA+D,iBAAA,GAAAA,iBAAA,CAAAnE,sBAAA,0NAW/B,CAED,KAAM,CAAAoE,iBAAiB,CAAGtG,MAAM,CAACsC,GAAG,CAAAiE,iBAAA,GAAAA,iBAAA,CAAArE,sBAAA,mGAKnC,CAED,KAAM,CAAAsE,cAAc,CAAGxG,MAAM,CAACsC,GAAG,CAAAmE,iBAAA,GAAAA,iBAAA,CAAAvE,sBAAA,oGAKhC,CAED,KAAM,CAAAwE,cAAc,CAAG1G,MAAM,CAACsC,GAAG,CAAAqE,iBAAA,GAAAA,iBAAA,CAAAzE,sBAAA,qDAGhC,CAED,KAAM,CAAA0E,eAAe,CAAG5G,MAAM,CAACsC,GAAG,CAAAuE,iBAAA,GAAAA,iBAAA,CAAA3E,sBAAA,wOAWnBF,MAAM,CACpB,CAED,KAAM,CAAA8E,YAAY,CAAG9G,MAAM,CAACsC,GAAG,CAAAyE,iBAAA,GAAAA,iBAAA,CAAA7E,sBAAA,oKAOhBC,WAAW,CACzB,CAED,KAAM,CAAA6E,UAAU,CAAGhH,MAAM,CAACiH,EAAE,CAAAC,iBAAA,GAAAA,iBAAA,CAAAhF,sBAAA,gGAK3B,CAED,KAAM,CAAAiF,SAAS,CAAGnH,MAAM,CAACoH,CAAC,CAAAC,iBAAA,GAAAA,iBAAA,CAAAnF,sBAAA,kGAKzB,CAED,KAAM,CAAAoF,YAAY,CAAGtH,MAAM,CAACsC,GAAG,CAAAiF,iBAAA,GAAAA,iBAAA,CAAArF,sBAAA,sEAI9B,CAED,KAAM,CAAAsF,iBAAiB,CAAGC,IAAA,EAKpB,IALqB,CACzBC,KAAK,CACLC,MAAM,CACNC,YAAY,CAAG,CAAC,CAAC,CACjBC,gBACF,CAAC,CAAAJ,IAAA,CACC,KAAM,CAAAK,SAAS,CAAGjI,MAAM,CAAC,IAAI,CAAC,CAC9B,KAAM,CAACkI,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGpI,QAAQ,CAAC,CAAC,CAAC,CAC3D,KAAM,CAACqI,cAAc,CAAEC,iBAAiB,CAAC,CAAGtI,QAAQ,CAAC,GAAI,CAAAuI,GAAG,CAAC,CAAC,CAAC,CAC/D,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGzI,QAAQ,CAAC,CAAC,CAAC,CAAC,CAChD,KAAM,CAAC0I,cAAc,CAAEC,iBAAiB,CAAC,CAAG3I,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAAC4I,cAAc,CAAEC,iBAAiB,CAAC,CAAG7I,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAAC8I,WAAW,CAAEC,cAAc,CAAC,CAAG/I,QAAQ,CAAC,KAAK,CAAC,CAErD,KAAM,CAAAgJ,SAAS,CAAGxH,YAAY,CAACsG,KAAK,CAAC,CACrC,KAAM,CAAAmB,KAAK,CAAG1H,gBAAgB,CAACuG,KAAK,CAAC,CACrC,KAAM,CAAAoB,WAAW,CAAGD,KAAK,CAACd,gBAAgB,CAAC,CAE3C;AACA,KAAM,CACJzD,WAAW,CACXyE,UAAU,CACVC,aAAa,CACbC,eAAe,CACfC,WAAW,CACXC,UAAU,CACVC,gBAAgB,CAChBC,cAAc,CAAEC,gBAAgB,CAChCC,aAAa,CAAEC,eAAe,CAC9BC,iBAAiB,CACjBC,eAAe,CACfC,QACF,CAAC,CAAGzI,gBAAgB,CAAC,CAAC,CAEtB,KAAM,CAAA0I,QAAQ,CAAI3B,cAAc,CAAC4B,IAAI,CAAGhB,KAAK,CAACiB,MAAM,CAAI,GAAG,CAE3D;AACA,KAAM,CAAAC,cAAc,CAAGjK,WAAW,CAAC,IAAM,CACvC,GAAI,CAACgI,SAAS,CAACkC,OAAO,CAAE,OACxBrB,cAAc,CAAC,IAAI,CAAC,CACpBc,iBAAiB,CAAC3B,SAAS,CAAE,GAAG,CAAC,CACnC,CAAC,CAAE,CAAC2B,iBAAiB,CAAC,CAAC,CAEvB;AACA,KAAM,CAAAQ,gBAAgB,CAAGnK,WAAW,CAAC,MAAOoK,QAAQ,CAAEC,SAAS,CAAEC,UAAU,GAAK,CAC9E,GAAI,CACFC,OAAO,CAACC,GAAG,0CAAAC,MAAA,CAAgCL,QAAQ,sBAAAK,MAAA,CAAoBH,UAAU,CAAE,CAAC,CAEpF,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,0CAA0C,CAAE,CACvEC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnBC,SAAS,CAAEb,QAAQ,CACnBC,SAAS,CAAEA,SAAS,CACpBC,UAAU,CAAEA,UAAU,CACtBY,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CACH,CAAC,CAAC,CAEF,GAAIV,QAAQ,CAACW,EAAE,CAAE,CACf,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAZ,QAAQ,CAACa,IAAI,CAAC,CAAC,CACpChB,OAAO,CAACC,GAAG,gCAAAC,MAAA,CAA2Ba,MAAM,CAACE,OAAO,CAAE,CAAC,CACvD,MAAO,KAAI,CACb,CAAC,IAAM,CACLjB,OAAO,CAAC7F,KAAK,CAAC,gCAAgC,CAAC,CAC/C,MAAO,MAAK,CACd,CACF,CAAE,MAAOA,KAAK,CAAE,CACd6F,OAAO,CAAC7F,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrD,MAAO,MAAK,CACd,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACAzE,SAAS,CAAC,IAAM,KAAAwL,gBAAA,CACd,GAAIrC,WAAW,EAAIJ,WAAW,EAAI,CAAAC,UAAU,SAAVA,UAAU,kBAAAwC,gBAAA,CAAVxC,UAAU,CAAEyC,IAAI,UAAAD,gBAAA,iBAAhBA,gBAAA,CAAkBE,WAAW,CAAC,CAAC,IAAK3C,WAAW,CAAC4C,IAAI,CAACD,WAAW,CAAC,CAAC,CAAE,CACpG;AACA,GAAI,CAACxD,cAAc,CAAC0D,GAAG,CAAC5D,gBAAgB,CAAC,CAAE,CACzCsC,OAAO,CAACC,GAAG,wCAAAC,MAAA,CAA8BzB,WAAW,CAAC4C,IAAI,sBAAAnB,MAAA,CAAoBxB,UAAU,CAACqB,UAAU,CAAE,CAAC,CAErG;AACA,KAAM,CAAAwB,qBAAqB,CAAG,KAAAA,CAAA,GAAY,CACxC,GAAIxC,gBAAgB,EAAI,CAAAL,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEqB,UAAU,GAAI,GAAG,CAAE,CACrD,KAAM,CAAAyB,KAAK,CAAG,KAAM,CAAA5B,gBAAgB,CAACnB,WAAW,CAAC4C,IAAI,CAAEtC,gBAAgB,CAAEL,UAAU,CAACqB,UAAU,CAAC,CAC/F,GAAIyB,KAAK,CAAE,CACTxB,OAAO,CAACC,GAAG,gDAAAC,MAAA,CAA2CzB,WAAW,CAAC4C,IAAI,CAAE,CAAC,CAC3E,CACF,CACF,CAAC,CAED;AACAE,qBAAqB,CAAC,CAAC,CAEvB;AACA,GAAI,CAAC5C,aAAa,EAAI1E,WAAW,CAAE,CACjC+F,OAAO,CAACC,GAAG,kDAAAC,MAAA,CAAwCzB,WAAW,CAAC4C,IAAI,OAAK,CAAC,CACzEpC,gBAAgB,CAACR,WAAW,CAAC4C,IAAI,CAAE,IAAI,CAAC,CAAE;AAE1C;AACAI,UAAU,CAAC,IAAM,CACftC,eAAe,CAAC,CAAC,CACjBa,OAAO,CAACC,GAAG,8CAAAC,MAAA,CAAyCzB,WAAW,CAAC4C,IAAI,CAAE,CAAC,CACzE,CAAC,CAAE,IAAI,CAAC,CACV,CAEA;AACArD,aAAa,CAAC0D,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAAChE,gBAAgB,EAAG,SAAS,EAAG,CAAC,CACnEG,iBAAiB,CAAC6D,IAAI,EAAI,GAAI,CAAA5D,GAAG,CAAC,CAAC,GAAG4D,IAAI,CAAEhE,gBAAgB,CAAC,CAAC,CAAC,CAE/D;AACA,GAAIF,gBAAgB,CAAE,CACpB,KAAM,CAAAoE,iBAAiB,CAAGhE,cAAc,CAAC4B,IAAI,CAAG,CAAC,CACjDhC,gBAAgB,CAACH,KAAK,CAAEuE,iBAAiB,CAAEpD,KAAK,CAACiB,MAAM,CAAC,CAC1D,CAEA;AACAgC,UAAU,CAAC,IAAM,CACf,GAAI/D,gBAAgB,CAAGc,KAAK,CAACiB,MAAM,CAAG,CAAC,CAAE,CACvCoC,QAAQ,CAAC,CAAC,CACZ,CAAC,IAAM,CACL;AACAzD,iBAAiB,CAAC,IAAI,CAAC,CACvB,GAAIZ,gBAAgB,CAAE,CACpBA,gBAAgB,CAACH,KAAK,CAAEmB,KAAK,CAACiB,MAAM,CAAEjB,KAAK,CAACiB,MAAM,CAAC,CACrD,CACF,CACF,CAAC,CAAE,IAAI,CAAC,CACV,CACF,CACF,CAAC,CAAE,CAACZ,WAAW,CAAEJ,WAAW,CAAEC,UAAU,CAAEhB,gBAAgB,CAAEc,KAAK,CAACiB,MAAM,CAAEpC,KAAK,CAAEG,gBAAgB,CAAEmB,aAAa,CAAE1E,WAAW,CAAEgF,gBAAgB,CAAEE,eAAe,CAAEvB,cAAc,CAAEgC,gBAAgB,CAAC,CAAC,CAEpM;AACAlK,SAAS,CAAC,IAAM,CACd,GAAIuE,WAAW,EAAIoD,KAAK,CAAE,CACxBiC,QAAQ,CAACjC,KAAK,CAAC,CACjB,CACF,CAAC,CAAE,CAACpD,WAAW,CAAEoD,KAAK,CAAEiC,QAAQ,CAAC,CAAC,CAElC;AACA5J,SAAS,CAAC,IAAM,CACd,GAAIuE,WAAW,EAAIwD,SAAS,CAACkC,OAAO,EAAI,CAACtB,WAAW,CAAE,CACpDqB,cAAc,CAAC,CAAC,CAClB,CACF,CAAC,CAAE,CAACzF,WAAW,CAAEyF,cAAc,CAAErB,WAAW,CAAC,CAAC,CAE9C,KAAM,CAAAwD,QAAQ,CAAGpM,WAAW,CAAC,IAAM,CACjC,GAAIiI,gBAAgB,CAAGc,KAAK,CAACiB,MAAM,CAAG,CAAC,CAAE,CACvCvB,iBAAiB,CAAC,OAAO,CAAC,CAC1BP,mBAAmB,CAAC+D,IAAI,EAAIA,IAAI,CAAG,CAAC,CAAC,CACrC1D,aAAa,CAAC0D,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAAChE,gBAAgB,EAAG,IAAI,EAAG,CAAC,CAC9D+D,UAAU,CAAC,IAAMvD,iBAAiB,CAAC,IAAI,CAAC,CAAE,GAAG,CAAC,CAChD,CACF,CAAC,CAAE,CAACR,gBAAgB,CAAEc,KAAK,CAACiB,MAAM,CAAC,CAAC,CAEpC,KAAM,CAAAqC,QAAQ,CAAGrM,WAAW,CAAC,IAAM,CACjC,GAAIiI,gBAAgB,CAAG,CAAC,CAAE,CACxBQ,iBAAiB,CAAC,MAAM,CAAC,CACzBP,mBAAmB,CAAC+D,IAAI,EAAIA,IAAI,CAAG,CAAC,CAAC,CACrC1D,aAAa,CAAC0D,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAAChE,gBAAgB,EAAG,IAAI,EAAG,CAAC,CAC9D+D,UAAU,CAAC,IAAMvD,iBAAiB,CAAC,IAAI,CAAC,CAAE,GAAG,CAAC,CAChD,CACF,CAAC,CAAE,CAACR,gBAAgB,CAAC,CAAC,CAEtB,KAAM,CAAAqE,SAAS,CAAGtM,WAAW,CAAC,IAAM,CAClCuI,aAAa,CAAC0D,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAAChE,gBAAgB,EAAG,IAAI,EAAG,CAAC,CAC9DG,iBAAiB,CAAC6D,IAAI,EAAI,CACxB,KAAM,CAAAM,MAAM,CAAG,GAAI,CAAAlE,GAAG,CAAC4D,IAAI,CAAC,CAC5BM,MAAM,CAACC,MAAM,CAACvE,gBAAgB,CAAC,CAC/B,MAAO,CAAAsE,MAAM,CACf,CAAC,CAAC,CACJ,CAAC,CAAE,CAACtE,gBAAgB,CAAC,CAAC,CAEtB,KAAM,CAAAwE,mBAAmB,CAAGA,CAAA,GAAM,CAChC9D,iBAAiB,CAAC,KAAK,CAAC,CACxBd,MAAM,CAAC,CAAC,CACV,CAAC,CAED,KAAM,CAAA6E,eAAe,CAAGA,CAAA,GAAM,CAC5B/D,iBAAiB,CAAC,KAAK,CAAC,CACxB;AACAd,MAAM,CAAC,CAAC,CACV,CAAC,CAED,GAAI,CAACiB,SAAS,EAAI,CAACE,WAAW,CAAE,CAC9B,mBACEjH,IAAA,CAACP,SAAS,EAAAmL,QAAA,cACR1K,KAAA,QAAK2K,KAAK,CAAE,CAAEC,KAAK,CAAE,OAAO,CAAEC,SAAS,CAAE,QAAQ,CAAEC,OAAO,CAAE,MAAO,CAAE,CAAAJ,QAAA,eACnE5K,IAAA,OAAA4K,QAAA,CAAI,iBAAe,CAAI,CAAC,cACxB5K,IAAA,WAAQiL,OAAO,CAAEnF,MAAO,CAAA8E,QAAA,CAAC,SAAO,CAAQ,CAAC,EACtC,CAAC,CACG,CAAC,CAEhB,CAEA,KAAM,CAAAM,sBAAsB,CAAG9E,cAAc,CAAC0D,GAAG,CAAC5D,gBAAgB,CAAC,CACnE,KAAM,CAAAiF,gBAAgB,CAAG5E,UAAU,CAACL,gBAAgB,CAAC,CAErD,mBACEhG,KAAA,CAACM,eAAe,EAAAoK,QAAA,eACd1K,KAAA,CAACkB,YAAY,EAAC4G,IAAI,CAAC,IAAI,CAAA4C,QAAA,eACrB1K,KAAA,CAACsB,gBAAgB,EAACwC,OAAO,CAAC,OAAO,CAACgE,IAAI,CAAC,IAAI,CAACiD,OAAO,CAAEnF,MAAO,CAAA8E,QAAA,eAC1D5K,IAAA,CAAC1B,SAAS,EAAC0J,IAAI,CAAE,EAAG,CAAE,CAAC,iBAEzB,EAAkB,CAAC,cAEnB9H,KAAA,CAAC2B,eAAe,EAAA+I,QAAA,eACd1K,KAAA,CAAC6B,gBAAgB,EAAC8D,KAAK,CAAE,CAAE,CAAA+E,QAAA,EAAC,QACpB,CAAC/E,KAAK,CAAC,IAAE,CAACkB,SAAS,CAAC8C,IAAI,EACd,CAAC,cACnB7J,IAAA,CAACiC,gBAAgB,EAAC+F,IAAI,CAAC,IAAI,CAAA4C,QAAA,CACxB7D,SAAS,CAACvH,KAAK,CACA,CAAC,EACJ,CAAC,cAElBU,KAAA,CAACoC,sBAAsB,EACrBG,WAAW,CAAEA,WAAY,CACzBwI,OAAO,CAAE,CAACxI,WAAW,CAAGoF,eAAe,CAAGuD,SAAU,CAAAR,QAAA,EAEnDnI,WAAW,cAAGzC,IAAA,CAACrB,IAAI,EAACqJ,IAAI,CAAE,EAAG,CAAE,CAAC,cAAGhI,IAAA,CAACpB,OAAO,EAACoJ,IAAI,CAAE,EAAG,CAAE,CAAC,CACxDvF,WAAW,CAAG,WAAW,CAAG,cAAc,CAC1C,CAACA,WAAW,eAAIzC,IAAA,CAACnB,SAAS,EAACmJ,IAAI,CAAE,EAAG,CAAE,CAAC,EAClB,CAAC,EACb,CAAC,cAEf9H,KAAA,CAAC0C,WAAW,EAAAgI,QAAA,eACV1K,KAAA,CAAC4C,gBAAgB,EAAA8H,QAAA,eACf1K,KAAA,CAAC8C,eAAe,EAAA4H,QAAA,eACd5K,IAAA,CAACkD,aAAa,EAAA0H,QAAA,CAAC,gBAAc,CAAe,CAAC,cAC7C5K,IAAA,CAACqD,WAAW,EAAAuH,QAAA,cACV5K,IAAA,CAACuD,YAAY,EAACsH,KAAK,CAAE,CAAEQ,KAAK,IAAA3C,MAAA,CAAKX,QAAQ,KAAI,CAAE,CAAE,CAAC,CACvC,CAAC,cACd7H,KAAA,CAACuD,YAAY,EAAAmH,QAAA,EACVxE,cAAc,CAAC4B,IAAI,CAAC,MAAI,CAAChB,KAAK,CAACiB,MAAM,CAAC,oBAAkB,CAACqD,IAAI,CAACC,KAAK,CAACxD,QAAQ,CAAC,CAAC,IACjF,EAAc,CAAC,EACA,CAAC,cAElB/H,IAAA,CAACZ,SAAS,EACRuK,IAAI,CAAE1C,WAAY,CAClBuE,UAAU,CAAEtF,gBAAgB,CAAG,CAAE,CACjCuF,UAAU,CAAEzE,KAAK,CAACiB,MAAO,CACzByD,SAAS,CAAEP,gBAAgB,GAAK,SAAU,CAC1CQ,WAAW,CAAER,gBAAgB,GAAK,WAAY,CAC9CS,WAAW,CAAEnJ,WAAW,EAAI,CAACyI,sBAAuB,CACpDzE,cAAc,CAAEA,cAAe,CAC/BsB,QAAQ,CAAG7B,gBAAgB,CAAGc,KAAK,CAACiB,MAAM,CAAI,GAAI,CACnD,CAAC,cAEF/H,KAAA,CAACyD,QAAQ,EAAAiH,QAAA,eACP1K,KAAA,CAAC2D,aAAa,EACZoH,OAAO,CAAEX,QAAS,CAClBuB,QAAQ,CAAE3F,gBAAgB,GAAK,CAAE,CAAA0E,QAAA,eAEjC5K,IAAA,CAAC1B,SAAS,EAAC0J,IAAI,CAAE,EAAG,CAAE,CAAC,WAEzB,EAAe,CAAC,CAEfkD,sBAAsB,cACrBhL,KAAA,CAAC2D,aAAa,EACZG,OAAO,CAAC,SAAS,CACjBiH,OAAO,CAAEZ,QAAS,CAClBwB,QAAQ,CAAE3F,gBAAgB,GAAKc,KAAK,CAACiB,MAAM,CAAG,CAAE,CAAA2C,QAAA,eAEhD5K,IAAA,CAAClB,WAAW,EAACkJ,IAAI,CAAE,EAAG,CAAE,CAAC,YAE3B,EAAe,CAAC,cAEhB9H,KAAA,CAAC2D,aAAa,EACZoH,OAAO,CAAEV,SAAU,CACnBsB,QAAQ,CAAE,CAACpJ,WAAY,CAAAmI,QAAA,eAEvB5K,IAAA,CAACxB,SAAS,EAACwJ,IAAI,CAAE,EAAG,CAAE,CAAC,QAEzB,EAAe,CAChB,cAED9H,KAAA,CAAC2D,aAAa,EACZoH,OAAO,CAAEZ,QAAS,CAClBwB,QAAQ,CAAE3F,gBAAgB,GAAKc,KAAK,CAACiB,MAAM,CAAG,CAAE,CAAA2C,QAAA,EACjD,MAEC,cAAA5K,IAAA,CAACzB,UAAU,EAACyJ,IAAI,CAAE,EAAG,CAAE,CAAC,EACX,CAAC,EACR,CAAC,EACK,CAAC,cAEnB9H,KAAA,CAAC+D,aAAa,EAAA2G,QAAA,eACZ1K,KAAA,CAACiE,WAAW,EAAAyG,QAAA,eACV5K,IAAA,CAACtB,MAAM,EAACsJ,IAAI,CAAE,EAAG,CAAE,CAAC,cAEtB,EAAa,CAAC,cAEd9H,KAAA,CAACmE,eAAe,EAAAuG,QAAA,eACd5K,IAAA,CAAC3B,MAAM,EACLyN,GAAG,CAAE7F,SAAU,CACf8F,KAAK,CAAE,KAAM,CACbV,KAAK,CAAC,MAAM,CACZW,MAAM,CAAC,MAAM,CACbC,gBAAgB,CAAC,YAAY,CAC7BC,gBAAgB,CAAE,CAChBb,KAAK,CAAE,GAAG,CACVW,MAAM,CAAE,GAAG,CACXG,UAAU,CAAE,MACd,CAAE,CACH,CAAC,CAED/E,eAAe,eACdpH,IAAA,CAACuE,aAAa,EAAAqG,QAAA,CACXxD,eAAe,CACH,CAChB,EACc,CAAC,cAElBlH,KAAA,CAACuE,iBAAiB,EAAAmG,QAAA,eAChB5K,IAAA,CAAC2E,cAAc,EAAAiG,QAAA,CACZ1D,UAAU,SAAVA,UAAU,WAAVA,UAAU,CAAEyC,IAAI,cAAAjB,MAAA,CAAgBxB,UAAU,CAACyC,IAAI,EAAK,8BAA8B,CACrE,CAAC,CAChB,CAAAzC,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEqB,UAAU,gBACrBrI,KAAA,CAAC2E,cAAc,EAAA+F,QAAA,EAAC,cACF,CAACU,IAAI,CAACC,KAAK,CAACrE,UAAU,CAACqB,UAAU,CAAG,GAAG,CAAC,CAAC,GACvD,EAAgB,CACjB,EACgB,CAAC,EACP,CAAC,EACL,CAAC,CAEb5B,cAAc,eACb3G,IAAA,CAAC+E,eAAe,EAAA6F,QAAA,cACd1K,KAAA,CAAC+E,YAAY,EAAA2F,QAAA,eACX5K,IAAA,CAACjB,MAAM,EAACiJ,IAAI,CAAE,EAAG,CAAC6C,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAS,CAAEsB,YAAY,CAAE,MAAO,CAAE,CAAE,CAAC,cACvEpM,IAAA,CAACmF,UAAU,EAAAyF,QAAA,CAAC,8BAAkB,CAAY,CAAC,cAC3C1K,KAAA,CAACoF,SAAS,EAAAsF,QAAA,EAAC,uDAC4C,CAAC/E,KAAK,CAAC,IAAE,CAACkB,SAAS,CAAC8C,IAAI,CAAC,wBAC1D,CAAC7C,KAAK,CAACiB,MAAM,CAAC,uBACpC,EAAW,CAAC,cACZ/H,KAAA,CAACuF,YAAY,EAAAmF,QAAA,eACX1K,KAAA,CAAC2D,aAAa,EAACoH,OAAO,CAAEP,mBAAoB,CAAAE,QAAA,eAC1C5K,IAAA,CAACvB,IAAI,EAACuJ,IAAI,CAAE,EAAG,CAAE,CAAC,iBAEpB,EAAe,CAAC,CACfnC,KAAK,CAAG,CAAC,eACR3F,KAAA,CAAC2D,aAAa,EAACG,OAAO,CAAC,SAAS,CAACiH,OAAO,CAAEN,eAAgB,CAAAC,QAAA,eACxD5K,IAAA,CAAChB,MAAM,EAACgJ,IAAI,CAAE,EAAG,CAAE,CAAC,aAEtB,EAAe,CAChB,EACW,CAAC,EACH,CAAC,CACA,CAClB,EACc,CAAC,CAEtB,CAAC,CAED,cAAe,CAAArC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}