{"ast": null, "code": "var _jsxFileName = \"D:\\\\ASL\\\\ASL-Training\\\\src\\\\components\\\\FlashCardTraining.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useCallback, useEffect } from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport Webcam from 'react-webcam';\nimport { ArrowLeft, ArrowRight, RotateCcw, Home, Camera, Wifi, WifiOff, RefreshCw, CheckCircle, Trophy, Target, Zap, Sparkles, Award } from 'lucide-react';\nimport FlashCard from './FlashCard';\nimport { useSignDetection } from '../hooks/useSignDetection';\nimport { getSignsForLevel, getLevelInfo } from '../data/signLevels';\nimport { theme } from '../styles/theme';\nimport { Container, Card, Button, Heading, Text, Badge } from './ui/ModernComponents';\n\n// Animations\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst fadeIn = keyframes`\n  from { opacity: 0; transform: translateY(20px); }\n  to { opacity: 1; transform: translateY(0); }\n`;\nconst celebration = keyframes`\n  0%, 100% { transform: scale(1) rotate(0deg); }\n  25% { transform: scale(1.1) rotate(-5deg); }\n  75% { transform: scale(1.1) rotate(5deg); }\n`;\n\n// Modern Styled Components\nconst ModernContainer = styled.div`\n  min-height: 100vh;\n  background: ${theme.colors.gradients.primary};\n  padding: ${theme.spacing[4]};\n  position: relative;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\n      radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),\n      radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);\n    pointer-events: none;\n  }\n\n  > * {\n    position: relative;\n    z-index: 1;\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    padding: ${theme.spacing[3]};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: ${theme.spacing[2]};\n  }\n`;\nconst Header = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${theme.spacing[8]};\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    flex-direction: column;\n    gap: ${theme.spacing[4]};\n  }\n`;\n_c = Header;\nconst ModernHeader = styled(Card)`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${theme.spacing[8]};\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: ${theme.shadows.xl};\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    flex-direction: column;\n    gap: ${theme.spacing[4]};\n    text-align: center;\n    margin-bottom: ${theme.spacing[6]};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    margin-bottom: ${theme.spacing[4]};\n  }\n`;\nconst ModernBackButton = styled(Button)`\n  background: rgba(255, 255, 255, 0.15);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  color: ${theme.colors.text.inverse};\n\n  &:hover:not(:disabled) {\n    background: rgba(255, 255, 255, 0.25);\n    transform: translateY(-2px);\n    box-shadow: ${theme.shadows.lg};\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    width: 100%;\n  }\n`;\nconst ModernLevelInfo = styled.div`\n  text-align: center;\n  color: ${theme.colors.text.primary};\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    order: -1;\n  }\n`;\nconst ModernLevelTitle = styled(Heading)`\n  margin-bottom: ${theme.spacing[1]};\n  background: ${theme.colors.gradients.primary};\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n`;\nconst ModernLevelTheme = styled(Text)`\n  opacity: 0.8;\n  font-weight: ${theme.typography.fontWeight.medium};\n`;\nconst ModernConnectionStatus = styled(Badge)`\n  background: ${props => props.isConnected ? 'rgba(34, 197, 94, 0.15)' : 'rgba(239, 68, 68, 0.15)'};\n  color: ${props => props.isConnected ? theme.colors.success[700] : theme.colors.error[700]};\n  border: 1px solid ${props => props.isConnected ? theme.colors.success[200] : theme.colors.error[200]};\n  backdrop-filter: blur(10px);\n  cursor: ${props => props.isConnected ? 'default' : 'pointer'};\n\n  &:hover {\n    background: ${props => props.isConnected ? 'rgba(34, 197, 94, 0.2)' : 'rgba(239, 68, 68, 0.2)'};\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    width: 100%;\n    justify-content: center;\n  }\n`;\nconst MainContent = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 400px;\n  gap: ${theme.spacing[8]};\n  max-width: 1400px;\n  margin: 0 auto;\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    grid-template-columns: 1fr;\n    gap: ${theme.spacing[6]};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    /* On mobile, show flash card first, then camera */\n    display: flex;\n    flex-direction: column;\n    gap: ${theme.spacing[4]};\n  }\n`;\n_c2 = MainContent;\nconst FlashCardSection = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  animation: ${fadeIn} 0.6s ease;\n`;\n_c3 = FlashCardSection;\nconst ProgressSection = styled.div`\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20px;\n  padding: 1.5rem;\n  margin-bottom: 2rem;\n  text-align: center;\n`;\n_c4 = ProgressSection;\nconst ProgressTitle = styled.h3`\n  font-size: 1.25rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin-bottom: 1rem;\n`;\n_c5 = ProgressTitle;\nconst ProgressBar = styled.div`\n  width: 100%;\n  height: 12px;\n  background: #e2e8f0;\n  border-radius: 6px;\n  overflow: hidden;\n  margin-bottom: 1rem;\n`;\n_c6 = ProgressBar;\nconst ProgressFill = styled.div`\n  height: 100%;\n  background: linear-gradient(90deg, #10b981, #34d399);\n  border-radius: 6px;\n  transition: width 0.5s ease;\n`;\n_c7 = ProgressFill;\nconst ProgressText = styled.div`\n  font-size: 1rem;\n  color: #64748b;\n  font-weight: 600;\n`;\n_c8 = ProgressText;\nconst Controls = styled.div`\n  display: flex;\n  gap: 1rem;\n  margin-top: 2rem;\n  justify-content: center;\n  \n  @media (max-width: 768px) {\n    gap: 0.75rem;\n    margin-top: 1.5rem;\n  }\n`;\n_c9 = Controls;\nconst ControlButton = styled.button`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 1rem 2rem;\n  border: none;\n  border-radius: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props => {\n  if (props.variant === 'primary') {\n    return `\n        background: linear-gradient(135deg, #3b82f6, #8b5cf6);\n        color: white;\n        &:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);\n        }\n      `;\n  }\n  if (props.variant === 'success') {\n    return `\n        background: linear-gradient(135deg, #10b981, #34d399);\n        color: white;\n        &:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);\n        }\n      `;\n  }\n  return `\n      background: rgba(255, 255, 255, 0.9);\n      color: #64748b;\n      &:hover {\n        background: white;\n        transform: translateY(-2px);\n      }\n    `;\n}}\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none !important;\n  }\n  \n  @media (max-width: 768px) {\n    padding: 0.75rem 1.5rem;\n    font-size: 0.875rem;\n  }\n`;\n_c0 = ControlButton;\nconst CameraSection = styled(Card)`\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    order: 1; /* Show after flash card on mobile */\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    order: 2; /* Ensure camera comes after flash card on mobile */\n  }\n`;\n_c1 = CameraSection;\nconst CameraTitle = styled.h3`\n  font-size: 1.25rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin-bottom: 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n`;\n_c10 = CameraTitle;\nconst WebcamContainer = styled.div`\n  position: relative;\n  border-radius: 16px;\n  overflow: hidden;\n  background: #000;\n  margin-bottom: 1rem;\n`;\n_c11 = WebcamContainer;\nconst StatusOverlay = styled.div`\n  position: absolute;\n  top: 1rem;\n  left: 1rem;\n  right: 1rem;\n  background: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: 0.75rem;\n  border-radius: 8px;\n  font-weight: 600;\n  text-align: center;\n`;\n_c12 = StatusOverlay;\nconst PredictionDisplay = styled.div`\n  background: #f8fafc;\n  border-radius: 12px;\n  padding: 1rem;\n  text-align: center;\n`;\n_c13 = PredictionDisplay;\nconst PredictionText = styled.div`\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin-bottom: 0.5rem;\n`;\n_c14 = PredictionText;\nconst ConfidenceText = styled.div`\n  font-size: 0.875rem;\n  color: #64748b;\n`;\n_c15 = ConfidenceText;\nconst CompletionModal = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  animation: ${fadeIn} 0.3s ease;\n`;\n_c16 = CompletionModal;\nconst ModalContent = styled.div`\n  background: white;\n  border-radius: 24px;\n  padding: 3rem;\n  text-align: center;\n  max-width: 500px;\n  margin: 1rem;\n  animation: ${celebration} 0.6s ease;\n`;\n_c17 = ModalContent;\nconst ModalTitle = styled.h2`\n  font-size: 2.5rem;\n  font-weight: 800;\n  color: #1e293b;\n  margin-bottom: 1rem;\n`;\n_c18 = ModalTitle;\nconst ModalText = styled.p`\n  font-size: 1.125rem;\n  color: #64748b;\n  margin-bottom: 2rem;\n  line-height: 1.6;\n`;\n_c19 = ModalText;\nconst ModalButtons = styled.div`\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n`;\n_c20 = ModalButtons;\nconst FlashCardTraining = ({\n  level,\n  onBack,\n  userProgress = {},\n  onProgressUpdate\n}) => {\n  _s();\n  const webcamRef = useRef(null);\n  const [currentCardIndex, setCurrentCardIndex] = useState(0);\n  const [completedCards, setCompletedCards] = useState(new Set());\n  const [cardStates, setCardStates] = useState({});\n  const [slideDirection, setSlideDirection] = useState(null);\n  const [showCompletion, setShowCompletion] = useState(false);\n  const [isCapturing, setIsCapturing] = useState(false);\n  const levelInfo = getLevelInfo(level);\n  const signs = getSignsForLevel(level);\n  const currentSign = signs[currentCardIndex];\n\n  // Use sign detection hook\n  const {\n    isConnected,\n    prediction,\n    isAIRecording,\n    recordingStatus,\n    signMatched,\n    targetSign,\n    startRecording: startAIRecording,\n    stopRecording: stopAIRecording,\n    startFrameCapture,\n    retryConnection,\n    setLevel\n  } = useSignDetection();\n  const progress = completedCards.size / signs.length * 100;\n\n  // Start detection when connected\n  const startDetection = useCallback(() => {\n    if (!webcamRef.current) return;\n    setIsCapturing(true);\n    startFrameCapture(webcamRef, 100);\n  }, [startFrameCapture]);\n\n  // Save training data when sign is detected correctly\n  const saveTrainingData = useCallback(async (signName, keypoints, confidence) => {\n    try {\n      const response = await fetch('http://localhost:8000/save-training-data', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          sign_name: signName,\n          keypoints: keypoints,\n          confidence: confidence,\n          timestamp: new Date().toISOString()\n        })\n      });\n      if (response.ok) {\n        const result = await response.json();\n        console.log(`✅ Training data saved: ${result.message}`);\n      } else {\n        console.error('❌ Failed to save training data');\n      }\n    } catch (error) {\n      console.error('❌ Error saving training data:', error);\n    }\n  }, []);\n\n  // Handle sign detection success with automatic recording\n  useEffect(() => {\n    var _prediction$sign;\n    if (signMatched && currentSign && (prediction === null || prediction === void 0 ? void 0 : (_prediction$sign = prediction.sign) === null || _prediction$sign === void 0 ? void 0 : _prediction$sign.toLowerCase()) === currentSign.name.toLowerCase()) {\n      // Only proceed if this card hasn't been completed yet\n      if (!completedCards.has(currentCardIndex)) {\n        // Start automatic recording for training data\n        if (!isAIRecording && isConnected) {\n          console.log(`🎯 Correct sign detected: ${currentSign.name}. Starting automatic recording...`);\n          startAIRecording(currentSign.name, true); // Start immediate recording session\n\n          // Save training data with current keypoints\n          if (prediction !== null && prediction !== void 0 && prediction.keypoints) {\n            saveTrainingData(currentSign.name, prediction.keypoints, prediction.confidence);\n          }\n\n          // Stop recording after 3 seconds\n          setTimeout(() => {\n            stopAIRecording();\n            console.log(`✅ Automatic recording completed for: ${currentSign.name}`);\n          }, 3000);\n        }\n        setCardStates(prev => ({\n          ...prev,\n          [currentCardIndex]: 'correct'\n        }));\n        setCompletedCards(prev => new Set([...prev, currentCardIndex]));\n\n        // Auto-advance after 2 seconds (allowing time for user to see success)\n        setTimeout(() => {\n          if (currentCardIndex < signs.length - 1) {\n            nextCard();\n          } else {\n            // Level completed\n            setShowCompletion(true);\n            if (onProgressUpdate) {\n              onProgressUpdate(level, signs.length, signs.length);\n            }\n          }\n        }, 2000);\n      }\n    }\n  }, [signMatched, currentSign, prediction, currentCardIndex, signs.length, level, onProgressUpdate, isAIRecording, isConnected, startAIRecording, stopAIRecording, completedCards, saveTrainingData]);\n\n  // Set level when connected\n  useEffect(() => {\n    if (isConnected && level) {\n      setLevel(level);\n    }\n  }, [isConnected, level, setLevel]);\n\n  // Auto-start detection when connected\n  useEffect(() => {\n    if (isConnected && webcamRef.current && !isCapturing) {\n      startDetection();\n    }\n  }, [isConnected, startDetection, isCapturing]);\n  const nextCard = useCallback(() => {\n    if (currentCardIndex < signs.length - 1) {\n      setSlideDirection('right');\n      setCurrentCardIndex(prev => prev + 1);\n      setCardStates(prev => ({\n        ...prev,\n        [currentCardIndex]: null\n      }));\n      setTimeout(() => setSlideDirection(null), 500);\n    }\n  }, [currentCardIndex, signs.length]);\n  const prevCard = useCallback(() => {\n    if (currentCardIndex > 0) {\n      setSlideDirection('left');\n      setCurrentCardIndex(prev => prev - 1);\n      setCardStates(prev => ({\n        ...prev,\n        [currentCardIndex]: null\n      }));\n      setTimeout(() => setSlideDirection(null), 500);\n    }\n  }, [currentCardIndex]);\n  const retryCard = useCallback(() => {\n    setCardStates(prev => ({\n      ...prev,\n      [currentCardIndex]: null\n    }));\n    setCompletedCards(prev => {\n      const newSet = new Set(prev);\n      newSet.delete(currentCardIndex);\n      return newSet;\n    });\n  }, [currentCardIndex]);\n  const handleLevelComplete = () => {\n    setShowCompletion(false);\n    onBack();\n  };\n  const handleNextLevel = () => {\n    setShowCompletion(false);\n    // This would typically navigate to the next level\n    onBack();\n  };\n  if (!levelInfo || !currentSign) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: 'white',\n          textAlign: 'center',\n          padding: '2rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Level not found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onBack,\n          children: \"Go Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 577,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 576,\n      columnNumber: 7\n    }, this);\n  }\n  const isCurrentCardCompleted = completedCards.has(currentCardIndex);\n  const currentCardState = cardStates[currentCardIndex];\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      children: [/*#__PURE__*/_jsxDEV(BackButton, {\n        onClick: onBack,\n        children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 11\n        }, this), \"Back to Levels\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 591,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LevelInfo, {\n        children: [/*#__PURE__*/_jsxDEV(LevelTitle, {\n          children: [\"Level \", level, \": \", levelInfo.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 597,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LevelTheme, {\n          children: levelInfo.theme\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 596,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ConnectionStatus, {\n        isConnected: isConnected,\n        children: [isConnected ? /*#__PURE__*/_jsxDEV(Wifi, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 602,\n          columnNumber: 26\n        }, this) : /*#__PURE__*/_jsxDEV(WifiOff, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 602,\n          columnNumber: 47\n        }, this), isConnected ? 'Connected' : 'Disconnected', !isConnected && /*#__PURE__*/_jsxDEV(RefreshCw, {\n          size: 16,\n          style: {\n            cursor: 'pointer'\n          },\n          onClick: retryConnection\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 601,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 590,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n      children: [/*#__PURE__*/_jsxDEV(FlashCardSection, {\n        children: [/*#__PURE__*/_jsxDEV(ProgressSection, {\n          children: [/*#__PURE__*/_jsxDEV(ProgressTitle, {\n            children: \"Level Progress\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n            children: /*#__PURE__*/_jsxDEV(ProgressFill, {\n              style: {\n                width: `${progress}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 618,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProgressText, {\n            children: [completedCards.size, \" of \", signs.length, \" signs completed (\", Math.round(progress), \"%)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 621,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 616,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FlashCard, {\n          sign: currentSign,\n          cardNumber: currentCardIndex + 1,\n          totalCards: signs.length,\n          isCorrect: currentCardState === 'correct',\n          isIncorrect: currentCardState === 'incorrect',\n          isDetecting: isConnected && !isCurrentCardCompleted,\n          slideDirection: slideDirection,\n          progress: currentCardIndex / signs.length * 100\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 626,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Controls, {\n          children: [/*#__PURE__*/_jsxDEV(ControlButton, {\n            onClick: prevCard,\n            disabled: currentCardIndex === 0,\n            children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 642,\n              columnNumber: 15\n            }, this), \"Previous\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 638,\n            columnNumber: 13\n          }, this), isCurrentCardCompleted ? /*#__PURE__*/_jsxDEV(ControlButton, {\n            variant: \"success\",\n            onClick: nextCard,\n            disabled: currentCardIndex === signs.length - 1,\n            children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 652,\n              columnNumber: 17\n            }, this), \"Next Card\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 647,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(ControlButton, {\n            onClick: retryCard,\n            disabled: !isConnected,\n            children: [/*#__PURE__*/_jsxDEV(RotateCcw, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 660,\n              columnNumber: 17\n            }, this), \"Retry\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ControlButton, {\n            onClick: nextCard,\n            disabled: currentCardIndex === signs.length - 1,\n            children: [\"Next\", /*#__PURE__*/_jsxDEV(ArrowRight, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 670,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 665,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 637,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 615,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CameraSection, {\n        children: [/*#__PURE__*/_jsxDEV(CameraTitle, {\n          children: [/*#__PURE__*/_jsxDEV(Camera, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 677,\n            columnNumber: 13\n          }, this), \"Camera Feed\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 676,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(WebcamContainer, {\n          children: [/*#__PURE__*/_jsxDEV(Webcam, {\n            ref: webcamRef,\n            audio: false,\n            width: \"100%\",\n            height: \"auto\",\n            screenshotFormat: \"image/jpeg\",\n            videoConstraints: {\n              width: 640,\n              height: 480,\n              facingMode: \"user\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 682,\n            columnNumber: 13\n          }, this), recordingStatus && /*#__PURE__*/_jsxDEV(StatusOverlay, {\n            children: recordingStatus\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 696,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 681,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PredictionDisplay, {\n          children: [/*#__PURE__*/_jsxDEV(PredictionText, {\n            children: prediction !== null && prediction !== void 0 && prediction.sign ? `Detected: ${prediction.sign}` : 'Show the sign to get started'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 703,\n            columnNumber: 13\n          }, this), (prediction === null || prediction === void 0 ? void 0 : prediction.confidence) && /*#__PURE__*/_jsxDEV(ConfidenceText, {\n            children: [\"Confidence: \", Math.round(prediction.confidence * 100), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 707,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 702,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 675,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 614,\n      columnNumber: 7\n    }, this), showCompletion && /*#__PURE__*/_jsxDEV(CompletionModal, {\n      children: /*#__PURE__*/_jsxDEV(ModalContent, {\n        children: [/*#__PURE__*/_jsxDEV(Trophy, {\n          size: 80,\n          style: {\n            color: '#f59e0b',\n            marginBottom: '1rem'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 718,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ModalTitle, {\n          children: \"\\uD83C\\uDF89 Level Complete!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 719,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ModalText, {\n          children: [\"Congratulations! You've successfully completed Level \", level, \": \", levelInfo.name, \". You've mastered all \", signs.length, \" signs in this level!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 720,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ModalButtons, {\n          children: [/*#__PURE__*/_jsxDEV(ControlButton, {\n            onClick: handleLevelComplete,\n            children: [/*#__PURE__*/_jsxDEV(Home, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 726,\n              columnNumber: 17\n            }, this), \"Back to Levels\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 15\n          }, this), level < 5 && /*#__PURE__*/_jsxDEV(ControlButton, {\n            variant: \"primary\",\n            onClick: handleNextLevel,\n            children: [/*#__PURE__*/_jsxDEV(Target, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 731,\n              columnNumber: 19\n            }, this), \"Next Level\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 730,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 724,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 717,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 716,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 589,\n    columnNumber: 5\n  }, this);\n};\n_s(FlashCardTraining, \"2gubmJrzLCo422E7P6UflBp1krs=\", false, function () {\n  return [useSignDetection];\n});\n_c21 = FlashCardTraining;\nexport default FlashCardTraining;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21;\n$RefreshReg$(_c, \"Header\");\n$RefreshReg$(_c2, \"MainContent\");\n$RefreshReg$(_c3, \"FlashCardSection\");\n$RefreshReg$(_c4, \"ProgressSection\");\n$RefreshReg$(_c5, \"ProgressTitle\");\n$RefreshReg$(_c6, \"ProgressBar\");\n$RefreshReg$(_c7, \"ProgressFill\");\n$RefreshReg$(_c8, \"ProgressText\");\n$RefreshReg$(_c9, \"Controls\");\n$RefreshReg$(_c0, \"ControlButton\");\n$RefreshReg$(_c1, \"CameraSection\");\n$RefreshReg$(_c10, \"CameraTitle\");\n$RefreshReg$(_c11, \"WebcamContainer\");\n$RefreshReg$(_c12, \"StatusOverlay\");\n$RefreshReg$(_c13, \"PredictionDisplay\");\n$RefreshReg$(_c14, \"PredictionText\");\n$RefreshReg$(_c15, \"ConfidenceText\");\n$RefreshReg$(_c16, \"CompletionModal\");\n$RefreshReg$(_c17, \"ModalContent\");\n$RefreshReg$(_c18, \"ModalTitle\");\n$RefreshReg$(_c19, \"ModalText\");\n$RefreshReg$(_c20, \"ModalButtons\");\n$RefreshReg$(_c21, \"FlashCardTraining\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "useEffect", "styled", "keyframes", "Webcam", "ArrowLeft", "ArrowRight", "RotateCcw", "Home", "Camera", "Wifi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RefreshCw", "CheckCircle", "Trophy", "Target", "Zap", "<PERSON><PERSON><PERSON>", "Award", "FlashCard", "useSignDetection", "getSignsForLevel", "getLevelInfo", "theme", "Container", "Card", "<PERSON><PERSON>", "Heading", "Text", "Badge", "jsxDEV", "_jsxDEV", "fadeIn", "celebration", "ModernContainer", "div", "colors", "gradients", "primary", "spacing", "breakpoints", "md", "sm", "Header", "_c", "ModernHeader", "shadows", "xl", "ModernBackButton", "text", "inverse", "lg", "ModernLevelInfo", "ModernLevelTitle", "ModernLevelTheme", "typography", "fontWeight", "medium", "ModernConnectionStatus", "props", "isConnected", "success", "error", "MainContent", "_c2", "FlashCardSection", "_c3", "ProgressSection", "_c4", "ProgressTitle", "h3", "_c5", "ProgressBar", "_c6", "ProgressFill", "_c7", "ProgressText", "_c8", "Controls", "_c9", "ControlButton", "button", "variant", "_c0", "CameraSection", "_c1", "CameraTitle", "_c10", "WebcamContainer", "_c11", "StatusOverlay", "_c12", "PredictionDisplay", "_c13", "PredictionText", "_c14", "ConfidenceText", "_c15", "CompletionModal", "_c16", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c17", "ModalTitle", "h2", "_c18", "ModalText", "p", "_c19", "ModalButtons", "_c20", "FlashCardTraining", "level", "onBack", "userProgress", "onProgressUpdate", "_s", "webcamRef", "currentCardIndex", "setCurrentCardIndex", "completedCards", "setCompletedCards", "Set", "cardStates", "setCardStates", "slideDirection", "setSlideDirection", "showCompletion", "setShowCompletion", "isCapturing", "setIsCapturing", "levelInfo", "signs", "currentSign", "prediction", "isAIRecording", "recordingStatus", "signMatched", "targetSign", "startRecording", "startAIRecording", "stopRecording", "stopAIRecording", "startFrameCapture", "retryConnection", "setLevel", "progress", "size", "length", "startDetection", "current", "saveTrainingData", "signName", "keypoints", "confidence", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "sign_name", "timestamp", "Date", "toISOString", "ok", "result", "json", "console", "log", "message", "_prediction$sign", "sign", "toLowerCase", "name", "has", "setTimeout", "prev", "nextCard", "prevCard", "retryCard", "newSet", "delete", "handleLevelComplete", "handleNextLevel", "children", "style", "color", "textAlign", "padding", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "isCurrentCardCompleted", "currentCardState", "BackButton", "LevelInfo", "LevelTitle", "LevelTheme", "ConnectionStatus", "cursor", "width", "Math", "round", "cardNumber", "totalCards", "isCorrect", "isIncorrect", "isDetecting", "disabled", "ref", "audio", "height", "screenshotFormat", "videoConstraints", "facingMode", "marginBottom", "_c21", "$RefreshReg$"], "sources": ["D:/ASL/ASL-Training/src/components/FlashCardTraining.js"], "sourcesContent": ["import React, { useState, useRef, useCallback, useEffect } from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport Webcam from 'react-webcam';\nimport {\n  ArrowLeft,\n  ArrowRight,\n  RotateCcw,\n  Home,\n  Camera,\n  Wifi,\n  WifiOff,\n  RefreshCw,\n  CheckCircle,\n  Trophy,\n  Target,\n  Zap,\n  Sparkles,\n  Award\n} from 'lucide-react';\nimport Flash<PERSON>ard from './FlashCard';\nimport { useSignDetection } from '../hooks/useSignDetection';\nimport { getSignsForLevel, getLevelInfo } from '../data/signLevels';\nimport { theme } from '../styles/theme';\nimport { Container, Card, Button, Heading, Text, Badge } from './ui/ModernComponents';\n\n// Animations\nconst fadeIn = keyframes`\n  from { opacity: 0; transform: translateY(20px); }\n  to { opacity: 1; transform: translateY(0); }\n`;\n\nconst celebration = keyframes`\n  0%, 100% { transform: scale(1) rotate(0deg); }\n  25% { transform: scale(1.1) rotate(-5deg); }\n  75% { transform: scale(1.1) rotate(5deg); }\n`;\n\n// Modern Styled Components\nconst ModernContainer = styled.div`\n  min-height: 100vh;\n  background: ${theme.colors.gradients.primary};\n  padding: ${theme.spacing[4]};\n  position: relative;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\n      radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),\n      radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);\n    pointer-events: none;\n  }\n\n  > * {\n    position: relative;\n    z-index: 1;\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    padding: ${theme.spacing[3]};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    padding: ${theme.spacing[2]};\n  }\n`;\nconst Header = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${theme.spacing[8]};\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    flex-direction: column;\n    gap: ${theme.spacing[4]};\n  }\n`;\n\nconst ModernHeader = styled(Card)`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${theme.spacing[8]};\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: ${theme.shadows.xl};\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    flex-direction: column;\n    gap: ${theme.spacing[4]};\n    text-align: center;\n    margin-bottom: ${theme.spacing[6]};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    margin-bottom: ${theme.spacing[4]};\n  }\n`;\n\nconst ModernBackButton = styled(Button)`\n  background: rgba(255, 255, 255, 0.15);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  color: ${theme.colors.text.inverse};\n\n  &:hover:not(:disabled) {\n    background: rgba(255, 255, 255, 0.25);\n    transform: translateY(-2px);\n    box-shadow: ${theme.shadows.lg};\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    width: 100%;\n  }\n`;\n\nconst ModernLevelInfo = styled.div`\n  text-align: center;\n  color: ${theme.colors.text.primary};\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    order: -1;\n  }\n`;\n\nconst ModernLevelTitle = styled(Heading)`\n  margin-bottom: ${theme.spacing[1]};\n  background: ${theme.colors.gradients.primary};\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n`;\n\nconst ModernLevelTheme = styled(Text)`\n  opacity: 0.8;\n  font-weight: ${theme.typography.fontWeight.medium};\n`;\n\nconst ModernConnectionStatus = styled(Badge)`\n  background: ${props => props.isConnected\n    ? 'rgba(34, 197, 94, 0.15)'\n    : 'rgba(239, 68, 68, 0.15)'\n  };\n  color: ${props => props.isConnected\n    ? theme.colors.success[700]\n    : theme.colors.error[700]\n  };\n  border: 1px solid ${props => props.isConnected\n    ? theme.colors.success[200]\n    : theme.colors.error[200]\n  };\n  backdrop-filter: blur(10px);\n  cursor: ${props => props.isConnected ? 'default' : 'pointer'};\n\n  &:hover {\n    background: ${props => props.isConnected\n      ? 'rgba(34, 197, 94, 0.2)'\n      : 'rgba(239, 68, 68, 0.2)'\n    };\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    width: 100%;\n    justify-content: center;\n  }\n`;\n\nconst MainContent = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 400px;\n  gap: ${theme.spacing[8]};\n  max-width: 1400px;\n  margin: 0 auto;\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    grid-template-columns: 1fr;\n    gap: ${theme.spacing[6]};\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    /* On mobile, show flash card first, then camera */\n    display: flex;\n    flex-direction: column;\n    gap: ${theme.spacing[4]};\n  }\n`;\n\nconst FlashCardSection = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  animation: ${fadeIn} 0.6s ease;\n`;\n\nconst ProgressSection = styled.div`\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20px;\n  padding: 1.5rem;\n  margin-bottom: 2rem;\n  text-align: center;\n`;\n\nconst ProgressTitle = styled.h3`\n  font-size: 1.25rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin-bottom: 1rem;\n`;\n\nconst ProgressBar = styled.div`\n  width: 100%;\n  height: 12px;\n  background: #e2e8f0;\n  border-radius: 6px;\n  overflow: hidden;\n  margin-bottom: 1rem;\n`;\n\nconst ProgressFill = styled.div`\n  height: 100%;\n  background: linear-gradient(90deg, #10b981, #34d399);\n  border-radius: 6px;\n  transition: width 0.5s ease;\n`;\n\nconst ProgressText = styled.div`\n  font-size: 1rem;\n  color: #64748b;\n  font-weight: 600;\n`;\n\nconst Controls = styled.div`\n  display: flex;\n  gap: 1rem;\n  margin-top: 2rem;\n  justify-content: center;\n  \n  @media (max-width: 768px) {\n    gap: 0.75rem;\n    margin-top: 1.5rem;\n  }\n`;\n\nconst ControlButton = styled.button`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 1rem 2rem;\n  border: none;\n  border-radius: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props => {\n    if (props.variant === 'primary') {\n      return `\n        background: linear-gradient(135deg, #3b82f6, #8b5cf6);\n        color: white;\n        &:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);\n        }\n      `;\n    }\n    if (props.variant === 'success') {\n      return `\n        background: linear-gradient(135deg, #10b981, #34d399);\n        color: white;\n        &:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);\n        }\n      `;\n    }\n    return `\n      background: rgba(255, 255, 255, 0.9);\n      color: #64748b;\n      &:hover {\n        background: white;\n        transform: translateY(-2px);\n      }\n    `;\n  }}\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none !important;\n  }\n  \n  @media (max-width: 768px) {\n    padding: 0.75rem 1.5rem;\n    font-size: 0.875rem;\n  }\n`;\n\nconst CameraSection = styled(Card)`\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n\n  @media (max-width: ${theme.breakpoints.lg}) {\n    order: 1; /* Show after flash card on mobile */\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    order: 2; /* Ensure camera comes after flash card on mobile */\n  }\n`;\n\nconst CameraTitle = styled.h3`\n  font-size: 1.25rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin-bottom: 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n`;\n\nconst WebcamContainer = styled.div`\n  position: relative;\n  border-radius: 16px;\n  overflow: hidden;\n  background: #000;\n  margin-bottom: 1rem;\n`;\n\nconst StatusOverlay = styled.div`\n  position: absolute;\n  top: 1rem;\n  left: 1rem;\n  right: 1rem;\n  background: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: 0.75rem;\n  border-radius: 8px;\n  font-weight: 600;\n  text-align: center;\n`;\n\nconst PredictionDisplay = styled.div`\n  background: #f8fafc;\n  border-radius: 12px;\n  padding: 1rem;\n  text-align: center;\n`;\n\nconst PredictionText = styled.div`\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin-bottom: 0.5rem;\n`;\n\nconst ConfidenceText = styled.div`\n  font-size: 0.875rem;\n  color: #64748b;\n`;\n\nconst CompletionModal = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  animation: ${fadeIn} 0.3s ease;\n`;\n\nconst ModalContent = styled.div`\n  background: white;\n  border-radius: 24px;\n  padding: 3rem;\n  text-align: center;\n  max-width: 500px;\n  margin: 1rem;\n  animation: ${celebration} 0.6s ease;\n`;\n\nconst ModalTitle = styled.h2`\n  font-size: 2.5rem;\n  font-weight: 800;\n  color: #1e293b;\n  margin-bottom: 1rem;\n`;\n\nconst ModalText = styled.p`\n  font-size: 1.125rem;\n  color: #64748b;\n  margin-bottom: 2rem;\n  line-height: 1.6;\n`;\n\nconst ModalButtons = styled.div`\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n`;\n\nconst FlashCardTraining = ({ \n  level, \n  onBack, \n  userProgress = {}, \n  onProgressUpdate \n}) => {\n  const webcamRef = useRef(null);\n  const [currentCardIndex, setCurrentCardIndex] = useState(0);\n  const [completedCards, setCompletedCards] = useState(new Set());\n  const [cardStates, setCardStates] = useState({});\n  const [slideDirection, setSlideDirection] = useState(null);\n  const [showCompletion, setShowCompletion] = useState(false);\n  const [isCapturing, setIsCapturing] = useState(false);\n\n  const levelInfo = getLevelInfo(level);\n  const signs = getSignsForLevel(level);\n  const currentSign = signs[currentCardIndex];\n\n  // Use sign detection hook\n  const {\n    isConnected,\n    prediction,\n    isAIRecording,\n    recordingStatus,\n    signMatched,\n    targetSign,\n    startRecording: startAIRecording,\n    stopRecording: stopAIRecording,\n    startFrameCapture,\n    retryConnection,\n    setLevel\n  } = useSignDetection();\n\n  const progress = (completedCards.size / signs.length) * 100;\n\n  // Start detection when connected\n  const startDetection = useCallback(() => {\n    if (!webcamRef.current) return;\n    setIsCapturing(true);\n    startFrameCapture(webcamRef, 100);\n  }, [startFrameCapture]);\n\n  // Save training data when sign is detected correctly\n  const saveTrainingData = useCallback(async (signName, keypoints, confidence) => {\n    try {\n      const response = await fetch('http://localhost:8000/save-training-data', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          sign_name: signName,\n          keypoints: keypoints,\n          confidence: confidence,\n          timestamp: new Date().toISOString()\n        })\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        console.log(`✅ Training data saved: ${result.message}`);\n      } else {\n        console.error('❌ Failed to save training data');\n      }\n    } catch (error) {\n      console.error('❌ Error saving training data:', error);\n    }\n  }, []);\n\n  // Handle sign detection success with automatic recording\n  useEffect(() => {\n    if (signMatched && currentSign && prediction?.sign?.toLowerCase() === currentSign.name.toLowerCase()) {\n      // Only proceed if this card hasn't been completed yet\n      if (!completedCards.has(currentCardIndex)) {\n        // Start automatic recording for training data\n        if (!isAIRecording && isConnected) {\n          console.log(`🎯 Correct sign detected: ${currentSign.name}. Starting automatic recording...`);\n          startAIRecording(currentSign.name, true); // Start immediate recording session\n\n          // Save training data with current keypoints\n          if (prediction?.keypoints) {\n            saveTrainingData(currentSign.name, prediction.keypoints, prediction.confidence);\n          }\n\n          // Stop recording after 3 seconds\n          setTimeout(() => {\n            stopAIRecording();\n            console.log(`✅ Automatic recording completed for: ${currentSign.name}`);\n          }, 3000);\n        }\n\n        setCardStates(prev => ({ ...prev, [currentCardIndex]: 'correct' }));\n        setCompletedCards(prev => new Set([...prev, currentCardIndex]));\n\n        // Auto-advance after 2 seconds (allowing time for user to see success)\n        setTimeout(() => {\n          if (currentCardIndex < signs.length - 1) {\n            nextCard();\n          } else {\n            // Level completed\n            setShowCompletion(true);\n            if (onProgressUpdate) {\n              onProgressUpdate(level, signs.length, signs.length);\n            }\n          }\n        }, 2000);\n      }\n    }\n  }, [signMatched, currentSign, prediction, currentCardIndex, signs.length, level, onProgressUpdate, isAIRecording, isConnected, startAIRecording, stopAIRecording, completedCards, saveTrainingData]);\n\n  // Set level when connected\n  useEffect(() => {\n    if (isConnected && level) {\n      setLevel(level);\n    }\n  }, [isConnected, level, setLevel]);\n\n  // Auto-start detection when connected\n  useEffect(() => {\n    if (isConnected && webcamRef.current && !isCapturing) {\n      startDetection();\n    }\n  }, [isConnected, startDetection, isCapturing]);\n\n  const nextCard = useCallback(() => {\n    if (currentCardIndex < signs.length - 1) {\n      setSlideDirection('right');\n      setCurrentCardIndex(prev => prev + 1);\n      setCardStates(prev => ({ ...prev, [currentCardIndex]: null }));\n      setTimeout(() => setSlideDirection(null), 500);\n    }\n  }, [currentCardIndex, signs.length]);\n\n  const prevCard = useCallback(() => {\n    if (currentCardIndex > 0) {\n      setSlideDirection('left');\n      setCurrentCardIndex(prev => prev - 1);\n      setCardStates(prev => ({ ...prev, [currentCardIndex]: null }));\n      setTimeout(() => setSlideDirection(null), 500);\n    }\n  }, [currentCardIndex]);\n\n  const retryCard = useCallback(() => {\n    setCardStates(prev => ({ ...prev, [currentCardIndex]: null }));\n    setCompletedCards(prev => {\n      const newSet = new Set(prev);\n      newSet.delete(currentCardIndex);\n      return newSet;\n    });\n  }, [currentCardIndex]);\n\n  const handleLevelComplete = () => {\n    setShowCompletion(false);\n    onBack();\n  };\n\n  const handleNextLevel = () => {\n    setShowCompletion(false);\n    // This would typically navigate to the next level\n    onBack();\n  };\n\n  if (!levelInfo || !currentSign) {\n    return (\n      <Container>\n        <div style={{ color: 'white', textAlign: 'center', padding: '2rem' }}>\n          <h2>Level not found</h2>\n          <button onClick={onBack}>Go Back</button>\n        </div>\n      </Container>\n    );\n  }\n\n  const isCurrentCardCompleted = completedCards.has(currentCardIndex);\n  const currentCardState = cardStates[currentCardIndex];\n\n  return (\n    <Container>\n      <Header>\n        <BackButton onClick={onBack}>\n          <ArrowLeft size={20} />\n          Back to Levels\n        </BackButton>\n\n        <LevelInfo>\n          <LevelTitle>Level {level}: {levelInfo.name}</LevelTitle>\n          <LevelTheme>{levelInfo.theme}</LevelTheme>\n        </LevelInfo>\n\n        <ConnectionStatus isConnected={isConnected}>\n          {isConnected ? <Wifi size={20} /> : <WifiOff size={20} />}\n          {isConnected ? 'Connected' : 'Disconnected'}\n          {!isConnected && (\n            <RefreshCw\n              size={16}\n              style={{ cursor: 'pointer' }}\n              onClick={retryConnection}\n            />\n          )}\n        </ConnectionStatus>\n      </Header>\n\n      <MainContent>\n        <FlashCardSection>\n          <ProgressSection>\n            <ProgressTitle>Level Progress</ProgressTitle>\n            <ProgressBar>\n              <ProgressFill style={{ width: `${progress}%` }} />\n            </ProgressBar>\n            <ProgressText>\n              {completedCards.size} of {signs.length} signs completed ({Math.round(progress)}%)\n            </ProgressText>\n          </ProgressSection>\n\n          <FlashCard\n            sign={currentSign}\n            cardNumber={currentCardIndex + 1}\n            totalCards={signs.length}\n            isCorrect={currentCardState === 'correct'}\n            isIncorrect={currentCardState === 'incorrect'}\n            isDetecting={isConnected && !isCurrentCardCompleted}\n            slideDirection={slideDirection}\n            progress={(currentCardIndex / signs.length) * 100}\n          />\n\n          <Controls>\n            <ControlButton\n              onClick={prevCard}\n              disabled={currentCardIndex === 0}\n            >\n              <ArrowLeft size={20} />\n              Previous\n            </ControlButton>\n\n            {isCurrentCardCompleted ? (\n              <ControlButton\n                variant=\"success\"\n                onClick={nextCard}\n                disabled={currentCardIndex === signs.length - 1}\n              >\n                <CheckCircle size={20} />\n                Next Card\n              </ControlButton>\n            ) : (\n              <ControlButton\n                onClick={retryCard}\n                disabled={!isConnected}\n              >\n                <RotateCcw size={20} />\n                Retry\n              </ControlButton>\n            )}\n\n            <ControlButton\n              onClick={nextCard}\n              disabled={currentCardIndex === signs.length - 1}\n            >\n              Next\n              <ArrowRight size={20} />\n            </ControlButton>\n          </Controls>\n        </FlashCardSection>\n\n        <CameraSection>\n          <CameraTitle>\n            <Camera size={24} />\n            Camera Feed\n          </CameraTitle>\n\n          <WebcamContainer>\n            <Webcam\n              ref={webcamRef}\n              audio={false}\n              width=\"100%\"\n              height=\"auto\"\n              screenshotFormat=\"image/jpeg\"\n              videoConstraints={{\n                width: 640,\n                height: 480,\n                facingMode: \"user\"\n              }}\n            />\n\n            {recordingStatus && (\n              <StatusOverlay>\n                {recordingStatus}\n              </StatusOverlay>\n            )}\n          </WebcamContainer>\n\n          <PredictionDisplay>\n            <PredictionText>\n              {prediction?.sign ? `Detected: ${prediction.sign}` : 'Show the sign to get started'}\n            </PredictionText>\n            {prediction?.confidence && (\n              <ConfidenceText>\n                Confidence: {Math.round(prediction.confidence * 100)}%\n              </ConfidenceText>\n            )}\n          </PredictionDisplay>\n        </CameraSection>\n      </MainContent>\n\n      {showCompletion && (\n        <CompletionModal>\n          <ModalContent>\n            <Trophy size={80} style={{ color: '#f59e0b', marginBottom: '1rem' }} />\n            <ModalTitle>🎉 Level Complete!</ModalTitle>\n            <ModalText>\n              Congratulations! You've successfully completed Level {level}: {levelInfo.name}.\n              You've mastered all {signs.length} signs in this level!\n            </ModalText>\n            <ModalButtons>\n              <ControlButton onClick={handleLevelComplete}>\n                <Home size={20} />\n                Back to Levels\n              </ControlButton>\n              {level < 5 && (\n                <ControlButton variant=\"primary\" onClick={handleNextLevel}>\n                  <Target size={20} />\n                  Next Level\n                </ControlButton>\n              )}\n            </ModalButtons>\n          </ModalContent>\n        </CompletionModal>\n      )}\n    </Container>\n  );\n};\n\nexport default FlashCardTraining;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AACvE,OAAOC,MAAM,IAAIC,SAAS,QAAQ,mBAAmB;AACrD,OAAOC,MAAM,MAAM,cAAc;AACjC,SACEC,SAAS,EACTC,UAAU,EACVC,SAAS,EACTC,IAAI,EACJC,MAAM,EACNC,IAAI,EACJC,OAAO,EACPC,SAAS,EACTC,WAAW,EACXC,MAAM,EACNC,MAAM,EACNC,GAAG,EACHC,QAAQ,EACRC,KAAK,QACA,cAAc;AACrB,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,oBAAoB;AACnE,SAASC,KAAK,QAAQ,iBAAiB;AACvC,SAASC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAEC,KAAK,QAAQ,uBAAuB;;AAErF;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,MAAM,GAAG7B,SAAS;AACxB;AACA;AACA,CAAC;AAED,MAAM8B,WAAW,GAAG9B,SAAS;AAC7B;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAM+B,eAAe,GAAGhC,MAAM,CAACiC,GAAG;AAClC;AACA,gBAAgBZ,KAAK,CAACa,MAAM,CAACC,SAAS,CAACC,OAAO;AAC9C,aAAaf,KAAK,CAACgB,OAAO,CAAC,CAAC,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBhB,KAAK,CAACiB,WAAW,CAACC,EAAE;AAC3C,eAAelB,KAAK,CAACgB,OAAO,CAAC,CAAC,CAAC;AAC/B;AACA;AACA,uBAAuBhB,KAAK,CAACiB,WAAW,CAACE,EAAE;AAC3C,eAAenB,KAAK,CAACgB,OAAO,CAAC,CAAC,CAAC;AAC/B;AACA,CAAC;AACD,MAAMI,MAAM,GAAGzC,MAAM,CAACiC,GAAG;AACzB;AACA;AACA;AACA,mBAAmBZ,KAAK,CAACgB,OAAO,CAAC,CAAC,CAAC;AACnC;AACA,uBAAuBhB,KAAK,CAACiB,WAAW,CAACC,EAAE;AAC3C;AACA,WAAWlB,KAAK,CAACgB,OAAO,CAAC,CAAC,CAAC;AAC3B;AACA,CAAC;AAACK,EAAA,GAVID,MAAM;AAYZ,MAAME,YAAY,GAAG3C,MAAM,CAACuB,IAAI,CAAC;AACjC;AACA;AACA;AACA,mBAAmBF,KAAK,CAACgB,OAAO,CAAC,CAAC,CAAC;AACnC;AACA;AACA;AACA,gBAAgBhB,KAAK,CAACuB,OAAO,CAACC,EAAE;AAChC;AACA,uBAAuBxB,KAAK,CAACiB,WAAW,CAACC,EAAE;AAC3C;AACA,WAAWlB,KAAK,CAACgB,OAAO,CAAC,CAAC,CAAC;AAC3B;AACA,qBAAqBhB,KAAK,CAACgB,OAAO,CAAC,CAAC,CAAC;AACrC;AACA;AACA,uBAAuBhB,KAAK,CAACiB,WAAW,CAACE,EAAE;AAC3C,qBAAqBnB,KAAK,CAACgB,OAAO,CAAC,CAAC,CAAC;AACrC;AACA,CAAC;AAED,MAAMS,gBAAgB,GAAG9C,MAAM,CAACwB,MAAM,CAAC;AACvC;AACA;AACA;AACA,WAAWH,KAAK,CAACa,MAAM,CAACa,IAAI,CAACC,OAAO;AACpC;AACA;AACA;AACA;AACA,kBAAkB3B,KAAK,CAACuB,OAAO,CAACK,EAAE;AAClC;AACA;AACA,uBAAuB5B,KAAK,CAACiB,WAAW,CAACC,EAAE;AAC3C;AACA;AACA,CAAC;AAED,MAAMW,eAAe,GAAGlD,MAAM,CAACiC,GAAG;AAClC;AACA,WAAWZ,KAAK,CAACa,MAAM,CAACa,IAAI,CAACX,OAAO;AACpC;AACA,uBAAuBf,KAAK,CAACiB,WAAW,CAACC,EAAE;AAC3C;AACA;AACA,CAAC;AAED,MAAMY,gBAAgB,GAAGnD,MAAM,CAACyB,OAAO,CAAC;AACxC,mBAAmBJ,KAAK,CAACgB,OAAO,CAAC,CAAC,CAAC;AACnC,gBAAgBhB,KAAK,CAACa,MAAM,CAACC,SAAS,CAACC,OAAO;AAC9C;AACA;AACA;AACA,CAAC;AAED,MAAMgB,gBAAgB,GAAGpD,MAAM,CAAC0B,IAAI,CAAC;AACrC;AACA,iBAAiBL,KAAK,CAACgC,UAAU,CAACC,UAAU,CAACC,MAAM;AACnD,CAAC;AAED,MAAMC,sBAAsB,GAAGxD,MAAM,CAAC2B,KAAK,CAAC;AAC5C,gBAAgB8B,KAAK,IAAIA,KAAK,CAACC,WAAW,GACpC,yBAAyB,GACzB,yBAAyB;AAC/B,WACWD,KAAK,IAAIA,KAAK,CAACC,WAAW,GAC/BrC,KAAK,CAACa,MAAM,CAACyB,OAAO,CAAC,GAAG,CAAC,GACzBtC,KAAK,CAACa,MAAM,CAAC0B,KAAK,CAAC,GAAG,CAAC;AAC7B,sBACsBH,KAAK,IAAIA,KAAK,CAACC,WAAW,GAC1CrC,KAAK,CAACa,MAAM,CAACyB,OAAO,CAAC,GAAG,CAAC,GACzBtC,KAAK,CAACa,MAAM,CAAC0B,KAAK,CAAC,GAAG,CAAC;AAC7B;AACA,YACYH,KAAK,IAAIA,KAAK,CAACC,WAAW,GAAG,SAAS,GAAG,SAAS;AAC9D;AACA;AACA,kBAAkBD,KAAK,IAAIA,KAAK,CAACC,WAAW,GACpC,wBAAwB,GACxB,wBAAwB;AAChC;AACA;AACA,uBACuBrC,KAAK,CAACiB,WAAW,CAACC,EAAE;AAC3C;AACA;AACA;AACA,CAAC;AAED,MAAMsB,WAAW,GAAG7D,MAAM,CAACiC,GAAG;AAC9B;AACA;AACA,SAASZ,KAAK,CAACgB,OAAO,CAAC,CAAC,CAAC;AACzB;AACA;AACA;AACA,uBAAuBhB,KAAK,CAACiB,WAAW,CAACW,EAAE;AAC3C;AACA,WAAW5B,KAAK,CAACgB,OAAO,CAAC,CAAC,CAAC;AAC3B;AACA;AACA,uBAAuBhB,KAAK,CAACiB,WAAW,CAACE,EAAE;AAC3C;AACA;AACA;AACA,WAAWnB,KAAK,CAACgB,OAAO,CAAC,CAAC,CAAC;AAC3B;AACA,CAAC;AAACyB,GAAA,GAlBID,WAAW;AAoBjB,MAAME,gBAAgB,GAAG/D,MAAM,CAACiC,GAAG;AACnC;AACA;AACA;AACA,eAAeH,MAAM;AACrB,CAAC;AAACkC,GAAA,GALID,gBAAgB;AAOtB,MAAME,eAAe,GAAGjE,MAAM,CAACiC,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACiC,GAAA,GANID,eAAe;AAQrB,MAAME,aAAa,GAAGnE,MAAM,CAACoE,EAAE;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,aAAa;AAOnB,MAAMG,WAAW,GAAGtE,MAAM,CAACiC,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsC,GAAA,GAPID,WAAW;AASjB,MAAME,YAAY,GAAGxE,MAAM,CAACiC,GAAG;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACwC,GAAA,GALID,YAAY;AAOlB,MAAME,YAAY,GAAG1E,MAAM,CAACiC,GAAG;AAC/B;AACA;AACA;AACA,CAAC;AAAC0C,GAAA,GAJID,YAAY;AAMlB,MAAME,QAAQ,GAAG5E,MAAM,CAACiC,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC4C,GAAA,GAVID,QAAQ;AAYd,MAAME,aAAa,GAAG9E,MAAM,CAAC+E,MAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAItB,KAAK,IAAI;EACT,IAAIA,KAAK,CAACuB,OAAO,KAAK,SAAS,EAAE;IAC/B,OAAO;AACb;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;EACH;EACA,IAAIvB,KAAK,CAACuB,OAAO,KAAK,SAAS,EAAE;IAC/B,OAAO;AACb;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;EACH;EACA,OAAO;AACX;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACH,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GApDIH,aAAa;AAsDnB,MAAMI,aAAa,GAAGlF,MAAM,CAACuB,IAAI,CAAC;AAClC;AACA;AACA;AACA;AACA,uBAAuBF,KAAK,CAACiB,WAAW,CAACW,EAAE;AAC3C;AACA;AACA;AACA,uBAAuB5B,KAAK,CAACiB,WAAW,CAACE,EAAE;AAC3C;AACA;AACA,CAAC;AAAC2C,GAAA,GAZID,aAAa;AAcnB,MAAME,WAAW,GAAGpF,MAAM,CAACoE,EAAE;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACiB,IAAA,GARID,WAAW;AAUjB,MAAME,eAAe,GAAGtF,MAAM,CAACiC,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsD,IAAA,GANID,eAAe;AAQrB,MAAME,aAAa,GAAGxF,MAAM,CAACiC,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACwD,IAAA,GAXID,aAAa;AAanB,MAAME,iBAAiB,GAAG1F,MAAM,CAACiC,GAAG;AACpC;AACA;AACA;AACA;AACA,CAAC;AAAC0D,IAAA,GALID,iBAAiB;AAOvB,MAAME,cAAc,GAAG5F,MAAM,CAACiC,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAAC4D,IAAA,GALID,cAAc;AAOpB,MAAME,cAAc,GAAG9F,MAAM,CAACiC,GAAG;AACjC;AACA;AACA,CAAC;AAAC8D,IAAA,GAHID,cAAc;AAKpB,MAAME,eAAe,GAAGhG,MAAM,CAACiC,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeH,MAAM;AACrB,CAAC;AAACmE,IAAA,GAZID,eAAe;AAcrB,MAAME,YAAY,GAAGlG,MAAM,CAACiC,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,eAAeF,WAAW;AAC1B,CAAC;AAACoE,IAAA,GARID,YAAY;AAUlB,MAAME,UAAU,GAAGpG,MAAM,CAACqG,EAAE;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GALIF,UAAU;AAOhB,MAAMG,SAAS,GAAGvG,MAAM,CAACwG,CAAC;AAC1B;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GALIF,SAAS;AAOf,MAAMG,YAAY,GAAG1G,MAAM,CAACiC,GAAG;AAC/B;AACA;AACA;AACA,CAAC;AAAC0E,IAAA,GAJID,YAAY;AAMlB,MAAME,iBAAiB,GAAGA,CAAC;EACzBC,KAAK;EACLC,MAAM;EACNC,YAAY,GAAG,CAAC,CAAC;EACjBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,SAAS,GAAGrH,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM,CAACsH,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxH,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACyH,cAAc,EAAEC,iBAAiB,CAAC,GAAG1H,QAAQ,CAAC,IAAI2H,GAAG,CAAC,CAAC,CAAC;EAC/D,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG7H,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC8H,cAAc,EAAEC,iBAAiB,CAAC,GAAG/H,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACgI,cAAc,EAAEC,iBAAiB,CAAC,GAAGjI,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACkI,WAAW,EAAEC,cAAc,CAAC,GAAGnI,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMoI,SAAS,GAAG5G,YAAY,CAACyF,KAAK,CAAC;EACrC,MAAMoB,KAAK,GAAG9G,gBAAgB,CAAC0F,KAAK,CAAC;EACrC,MAAMqB,WAAW,GAAGD,KAAK,CAACd,gBAAgB,CAAC;;EAE3C;EACA,MAAM;IACJzD,WAAW;IACXyE,UAAU;IACVC,aAAa;IACbC,eAAe;IACfC,WAAW;IACXC,UAAU;IACVC,cAAc,EAAEC,gBAAgB;IAChCC,aAAa,EAAEC,eAAe;IAC9BC,iBAAiB;IACjBC,eAAe;IACfC;EACF,CAAC,GAAG5H,gBAAgB,CAAC,CAAC;EAEtB,MAAM6H,QAAQ,GAAI1B,cAAc,CAAC2B,IAAI,GAAGf,KAAK,CAACgB,MAAM,GAAI,GAAG;;EAE3D;EACA,MAAMC,cAAc,GAAGpJ,WAAW,CAAC,MAAM;IACvC,IAAI,CAACoH,SAAS,CAACiC,OAAO,EAAE;IACxBpB,cAAc,CAAC,IAAI,CAAC;IACpBa,iBAAiB,CAAC1B,SAAS,EAAE,GAAG,CAAC;EACnC,CAAC,EAAE,CAAC0B,iBAAiB,CAAC,CAAC;;EAEvB;EACA,MAAMQ,gBAAgB,GAAGtJ,WAAW,CAAC,OAAOuJ,QAAQ,EAAEC,SAAS,EAAEC,UAAU,KAAK;IAC9E,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,0CAA0C,EAAE;QACvEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,SAAS,EAAEV,QAAQ;UACnBC,SAAS,EAAEA,SAAS;UACpBC,UAAU,EAAEA,UAAU;UACtBS,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACpC,CAAC;MACH,CAAC,CAAC;MAEF,IAAIV,QAAQ,CAACW,EAAE,EAAE;QACf,MAAMC,MAAM,GAAG,MAAMZ,QAAQ,CAACa,IAAI,CAAC,CAAC;QACpCC,OAAO,CAACC,GAAG,CAAC,0BAA0BH,MAAM,CAACI,OAAO,EAAE,CAAC;MACzD,CAAC,MAAM;QACLF,OAAO,CAAC1G,KAAK,CAAC,gCAAgC,CAAC;MACjD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd0G,OAAO,CAAC1G,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7D,SAAS,CAAC,MAAM;IAAA,IAAA0K,gBAAA;IACd,IAAInC,WAAW,IAAIJ,WAAW,IAAI,CAAAC,UAAU,aAAVA,UAAU,wBAAAsC,gBAAA,GAAVtC,UAAU,CAAEuC,IAAI,cAAAD,gBAAA,uBAAhBA,gBAAA,CAAkBE,WAAW,CAAC,CAAC,MAAKzC,WAAW,CAAC0C,IAAI,CAACD,WAAW,CAAC,CAAC,EAAE;MACpG;MACA,IAAI,CAACtD,cAAc,CAACwD,GAAG,CAAC1D,gBAAgB,CAAC,EAAE;QACzC;QACA,IAAI,CAACiB,aAAa,IAAI1E,WAAW,EAAE;UACjC4G,OAAO,CAACC,GAAG,CAAC,6BAA6BrC,WAAW,CAAC0C,IAAI,mCAAmC,CAAC;UAC7FnC,gBAAgB,CAACP,WAAW,CAAC0C,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;;UAE1C;UACA,IAAIzC,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEmB,SAAS,EAAE;YACzBF,gBAAgB,CAAClB,WAAW,CAAC0C,IAAI,EAAEzC,UAAU,CAACmB,SAAS,EAAEnB,UAAU,CAACoB,UAAU,CAAC;UACjF;;UAEA;UACAuB,UAAU,CAAC,MAAM;YACfnC,eAAe,CAAC,CAAC;YACjB2B,OAAO,CAACC,GAAG,CAAC,wCAAwCrC,WAAW,CAAC0C,IAAI,EAAE,CAAC;UACzE,CAAC,EAAE,IAAI,CAAC;QACV;QAEAnD,aAAa,CAACsD,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAAC5D,gBAAgB,GAAG;QAAU,CAAC,CAAC,CAAC;QACnEG,iBAAiB,CAACyD,IAAI,IAAI,IAAIxD,GAAG,CAAC,CAAC,GAAGwD,IAAI,EAAE5D,gBAAgB,CAAC,CAAC,CAAC;;QAE/D;QACA2D,UAAU,CAAC,MAAM;UACf,IAAI3D,gBAAgB,GAAGc,KAAK,CAACgB,MAAM,GAAG,CAAC,EAAE;YACvC+B,QAAQ,CAAC,CAAC;UACZ,CAAC,MAAM;YACL;YACAnD,iBAAiB,CAAC,IAAI,CAAC;YACvB,IAAIb,gBAAgB,EAAE;cACpBA,gBAAgB,CAACH,KAAK,EAAEoB,KAAK,CAACgB,MAAM,EAAEhB,KAAK,CAACgB,MAAM,CAAC;YACrD;UACF;QACF,CAAC,EAAE,IAAI,CAAC;MACV;IACF;EACF,CAAC,EAAE,CAACX,WAAW,EAAEJ,WAAW,EAAEC,UAAU,EAAEhB,gBAAgB,EAAEc,KAAK,CAACgB,MAAM,EAAEpC,KAAK,EAAEG,gBAAgB,EAAEoB,aAAa,EAAE1E,WAAW,EAAE+E,gBAAgB,EAAEE,eAAe,EAAEtB,cAAc,EAAE+B,gBAAgB,CAAC,CAAC;;EAEpM;EACArJ,SAAS,CAAC,MAAM;IACd,IAAI2D,WAAW,IAAImD,KAAK,EAAE;MACxBiC,QAAQ,CAACjC,KAAK,CAAC;IACjB;EACF,CAAC,EAAE,CAACnD,WAAW,EAAEmD,KAAK,EAAEiC,QAAQ,CAAC,CAAC;;EAElC;EACA/I,SAAS,CAAC,MAAM;IACd,IAAI2D,WAAW,IAAIwD,SAAS,CAACiC,OAAO,IAAI,CAACrB,WAAW,EAAE;MACpDoB,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACxF,WAAW,EAAEwF,cAAc,EAAEpB,WAAW,CAAC,CAAC;EAE9C,MAAMkD,QAAQ,GAAGlL,WAAW,CAAC,MAAM;IACjC,IAAIqH,gBAAgB,GAAGc,KAAK,CAACgB,MAAM,GAAG,CAAC,EAAE;MACvCtB,iBAAiB,CAAC,OAAO,CAAC;MAC1BP,mBAAmB,CAAC2D,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACrCtD,aAAa,CAACsD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAAC5D,gBAAgB,GAAG;MAAK,CAAC,CAAC,CAAC;MAC9D2D,UAAU,CAAC,MAAMnD,iBAAiB,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;IAChD;EACF,CAAC,EAAE,CAACR,gBAAgB,EAAEc,KAAK,CAACgB,MAAM,CAAC,CAAC;EAEpC,MAAMgC,QAAQ,GAAGnL,WAAW,CAAC,MAAM;IACjC,IAAIqH,gBAAgB,GAAG,CAAC,EAAE;MACxBQ,iBAAiB,CAAC,MAAM,CAAC;MACzBP,mBAAmB,CAAC2D,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACrCtD,aAAa,CAACsD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAAC5D,gBAAgB,GAAG;MAAK,CAAC,CAAC,CAAC;MAC9D2D,UAAU,CAAC,MAAMnD,iBAAiB,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;IAChD;EACF,CAAC,EAAE,CAACR,gBAAgB,CAAC,CAAC;EAEtB,MAAM+D,SAAS,GAAGpL,WAAW,CAAC,MAAM;IAClC2H,aAAa,CAACsD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAAC5D,gBAAgB,GAAG;IAAK,CAAC,CAAC,CAAC;IAC9DG,iBAAiB,CAACyD,IAAI,IAAI;MACxB,MAAMI,MAAM,GAAG,IAAI5D,GAAG,CAACwD,IAAI,CAAC;MAC5BI,MAAM,CAACC,MAAM,CAACjE,gBAAgB,CAAC;MAC/B,OAAOgE,MAAM;IACf,CAAC,CAAC;EACJ,CAAC,EAAE,CAAChE,gBAAgB,CAAC,CAAC;EAEtB,MAAMkE,mBAAmB,GAAGA,CAAA,KAAM;IAChCxD,iBAAiB,CAAC,KAAK,CAAC;IACxBf,MAAM,CAAC,CAAC;EACV,CAAC;EAED,MAAMwE,eAAe,GAAGA,CAAA,KAAM;IAC5BzD,iBAAiB,CAAC,KAAK,CAAC;IACxB;IACAf,MAAM,CAAC,CAAC;EACV,CAAC;EAED,IAAI,CAACkB,SAAS,IAAI,CAACE,WAAW,EAAE;IAC9B,oBACErG,OAAA,CAACP,SAAS;MAAAiK,QAAA,eACR1J,OAAA;QAAK2J,KAAK,EAAE;UAAEC,KAAK,EAAE,OAAO;UAAEC,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBACnE1J,OAAA;UAAA0J,QAAA,EAAI;QAAe;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBlK,OAAA;UAAQmK,OAAO,EAAElF,MAAO;UAAAyE,QAAA,EAAC;QAAO;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEhB;EAEA,MAAME,sBAAsB,GAAG5E,cAAc,CAACwD,GAAG,CAAC1D,gBAAgB,CAAC;EACnE,MAAM+E,gBAAgB,GAAG1E,UAAU,CAACL,gBAAgB,CAAC;EAErD,oBACEtF,OAAA,CAACP,SAAS;IAAAiK,QAAA,gBACR1J,OAAA,CAACY,MAAM;MAAA8I,QAAA,gBACL1J,OAAA,CAACsK,UAAU;QAACH,OAAO,EAAElF,MAAO;QAAAyE,QAAA,gBAC1B1J,OAAA,CAAC1B,SAAS;UAAC6I,IAAI,EAAE;QAAG;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kBAEzB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEblK,OAAA,CAACuK,SAAS;QAAAb,QAAA,gBACR1J,OAAA,CAACwK,UAAU;UAAAd,QAAA,GAAC,QAAM,EAAC1E,KAAK,EAAC,IAAE,EAACmB,SAAS,CAAC4C,IAAI;QAAA;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACxDlK,OAAA,CAACyK,UAAU;UAAAf,QAAA,EAAEvD,SAAS,CAAC3G;QAAK;UAAAuK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eAEZlK,OAAA,CAAC0K,gBAAgB;QAAC7I,WAAW,EAAEA,WAAY;QAAA6H,QAAA,GACxC7H,WAAW,gBAAG7B,OAAA,CAACrB,IAAI;UAACwI,IAAI,EAAE;QAAG;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGlK,OAAA,CAACpB,OAAO;UAACuI,IAAI,EAAE;QAAG;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACxDrI,WAAW,GAAG,WAAW,GAAG,cAAc,EAC1C,CAACA,WAAW,iBACX7B,OAAA,CAACnB,SAAS;UACRsI,IAAI,EAAE,EAAG;UACTwC,KAAK,EAAE;YAAEgB,MAAM,EAAE;UAAU,CAAE;UAC7BR,OAAO,EAAEnD;QAAgB;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACe,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC,eAETlK,OAAA,CAACgC,WAAW;MAAA0H,QAAA,gBACV1J,OAAA,CAACkC,gBAAgB;QAAAwH,QAAA,gBACf1J,OAAA,CAACoC,eAAe;UAAAsH,QAAA,gBACd1J,OAAA,CAACsC,aAAa;YAAAoH,QAAA,EAAC;UAAc;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,eAC7ClK,OAAA,CAACyC,WAAW;YAAAiH,QAAA,eACV1J,OAAA,CAAC2C,YAAY;cAACgH,KAAK,EAAE;gBAAEiB,KAAK,EAAE,GAAG1D,QAAQ;cAAI;YAAE;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACdlK,OAAA,CAAC6C,YAAY;YAAA6G,QAAA,GACVlE,cAAc,CAAC2B,IAAI,EAAC,MAAI,EAACf,KAAK,CAACgB,MAAM,EAAC,oBAAkB,EAACyD,IAAI,CAACC,KAAK,CAAC5D,QAAQ,CAAC,EAAC,IACjF;UAAA;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAElBlK,OAAA,CAACZ,SAAS;UACRyJ,IAAI,EAAExC,WAAY;UAClB0E,UAAU,EAAEzF,gBAAgB,GAAG,CAAE;UACjC0F,UAAU,EAAE5E,KAAK,CAACgB,MAAO;UACzB6D,SAAS,EAAEZ,gBAAgB,KAAK,SAAU;UAC1Ca,WAAW,EAAEb,gBAAgB,KAAK,WAAY;UAC9Cc,WAAW,EAAEtJ,WAAW,IAAI,CAACuI,sBAAuB;UACpDvE,cAAc,EAAEA,cAAe;UAC/BqB,QAAQ,EAAG5B,gBAAgB,GAAGc,KAAK,CAACgB,MAAM,GAAI;QAAI;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eAEFlK,OAAA,CAAC+C,QAAQ;UAAA2G,QAAA,gBACP1J,OAAA,CAACiD,aAAa;YACZkH,OAAO,EAAEf,QAAS;YAClBgC,QAAQ,EAAE9F,gBAAgB,KAAK,CAAE;YAAAoE,QAAA,gBAEjC1J,OAAA,CAAC1B,SAAS;cAAC6I,IAAI,EAAE;YAAG;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAEzB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,EAEfE,sBAAsB,gBACrBpK,OAAA,CAACiD,aAAa;YACZE,OAAO,EAAC,SAAS;YACjBgH,OAAO,EAAEhB,QAAS;YAClBiC,QAAQ,EAAE9F,gBAAgB,KAAKc,KAAK,CAACgB,MAAM,GAAG,CAAE;YAAAsC,QAAA,gBAEhD1J,OAAA,CAAClB,WAAW;cAACqI,IAAI,EAAE;YAAG;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAE3B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,gBAEhBlK,OAAA,CAACiD,aAAa;YACZkH,OAAO,EAAEd,SAAU;YACnB+B,QAAQ,EAAE,CAACvJ,WAAY;YAAA6H,QAAA,gBAEvB1J,OAAA,CAACxB,SAAS;cAAC2I,IAAI,EAAE;YAAG;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,SAEzB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAChB,eAEDlK,OAAA,CAACiD,aAAa;YACZkH,OAAO,EAAEhB,QAAS;YAClBiC,QAAQ,EAAE9F,gBAAgB,KAAKc,KAAK,CAACgB,MAAM,GAAG,CAAE;YAAAsC,QAAA,GACjD,MAEC,eAAA1J,OAAA,CAACzB,UAAU;cAAC4I,IAAI,EAAE;YAAG;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAEnBlK,OAAA,CAACqD,aAAa;QAAAqG,QAAA,gBACZ1J,OAAA,CAACuD,WAAW;UAAAmG,QAAA,gBACV1J,OAAA,CAACtB,MAAM;YAACyI,IAAI,EAAE;UAAG;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEtB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAEdlK,OAAA,CAACyD,eAAe;UAAAiG,QAAA,gBACd1J,OAAA,CAAC3B,MAAM;YACLgN,GAAG,EAAEhG,SAAU;YACfiG,KAAK,EAAE,KAAM;YACbV,KAAK,EAAC,MAAM;YACZW,MAAM,EAAC,MAAM;YACbC,gBAAgB,EAAC,YAAY;YAC7BC,gBAAgB,EAAE;cAChBb,KAAK,EAAE,GAAG;cACVW,MAAM,EAAE,GAAG;cACXG,UAAU,EAAE;YACd;UAAE;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAED1D,eAAe,iBACdxG,OAAA,CAAC2D,aAAa;YAAA+F,QAAA,EACXlD;UAAe;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAChB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC,eAElBlK,OAAA,CAAC6D,iBAAiB;UAAA6F,QAAA,gBAChB1J,OAAA,CAAC+D,cAAc;YAAA2F,QAAA,EACZpD,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEuC,IAAI,GAAG,aAAavC,UAAU,CAACuC,IAAI,EAAE,GAAG;UAA8B;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,EAChB,CAAA5D,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEoB,UAAU,kBACrB1H,OAAA,CAACiE,cAAc;YAAAyF,QAAA,GAAC,cACF,EAACmB,IAAI,CAACC,KAAK,CAACxE,UAAU,CAACoB,UAAU,GAAG,GAAG,CAAC,EAAC,GACvD;UAAA;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAgB,CACjB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACgB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAEbnE,cAAc,iBACb/F,OAAA,CAACmE,eAAe;MAAAuF,QAAA,eACd1J,OAAA,CAACqE,YAAY;QAAAqF,QAAA,gBACX1J,OAAA,CAACjB,MAAM;UAACoI,IAAI,EAAE,EAAG;UAACwC,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAE+B,YAAY,EAAE;UAAO;QAAE;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvElK,OAAA,CAACuE,UAAU;UAAAmF,QAAA,EAAC;QAAkB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC3ClK,OAAA,CAAC0E,SAAS;UAAAgF,QAAA,GAAC,uDAC4C,EAAC1E,KAAK,EAAC,IAAE,EAACmB,SAAS,CAAC4C,IAAI,EAAC,wBAC1D,EAAC3C,KAAK,CAACgB,MAAM,EAAC,uBACpC;QAAA;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACZlK,OAAA,CAAC6E,YAAY;UAAA6E,QAAA,gBACX1J,OAAA,CAACiD,aAAa;YAACkH,OAAO,EAAEX,mBAAoB;YAAAE,QAAA,gBAC1C1J,OAAA,CAACvB,IAAI;cAAC0I,IAAI,EAAE;YAAG;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kBAEpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,EACflF,KAAK,GAAG,CAAC,iBACRhF,OAAA,CAACiD,aAAa;YAACE,OAAO,EAAC,SAAS;YAACgH,OAAO,EAAEV,eAAgB;YAAAC,QAAA,gBACxD1J,OAAA,CAAChB,MAAM;cAACmI,IAAI,EAAE;YAAG;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAEtB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAChB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAClB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEhB,CAAC;AAAC9E,EAAA,CAzUIL,iBAAiB;EAAA,QA+BjB1F,gBAAgB;AAAA;AAAAuM,IAAA,GA/BhB7G,iBAAiB;AA2UvB,eAAeA,iBAAiB;AAAC,IAAAlE,EAAA,EAAAoB,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAA8G,IAAA;AAAAC,YAAA,CAAAhL,EAAA;AAAAgL,YAAA,CAAA5J,GAAA;AAAA4J,YAAA,CAAA1J,GAAA;AAAA0J,YAAA,CAAAxJ,GAAA;AAAAwJ,YAAA,CAAArJ,GAAA;AAAAqJ,YAAA,CAAAnJ,GAAA;AAAAmJ,YAAA,CAAAjJ,GAAA;AAAAiJ,YAAA,CAAA/I,GAAA;AAAA+I,YAAA,CAAA7I,GAAA;AAAA6I,YAAA,CAAAzI,GAAA;AAAAyI,YAAA,CAAAvI,GAAA;AAAAuI,YAAA,CAAArI,IAAA;AAAAqI,YAAA,CAAAnI,IAAA;AAAAmI,YAAA,CAAAjI,IAAA;AAAAiI,YAAA,CAAA/H,IAAA;AAAA+H,YAAA,CAAA7H,IAAA;AAAA6H,YAAA,CAAA3H,IAAA;AAAA2H,YAAA,CAAAzH,IAAA;AAAAyH,YAAA,CAAAvH,IAAA;AAAAuH,YAAA,CAAApH,IAAA;AAAAoH,YAAA,CAAAjH,IAAA;AAAAiH,YAAA,CAAA/G,IAAA;AAAA+G,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}