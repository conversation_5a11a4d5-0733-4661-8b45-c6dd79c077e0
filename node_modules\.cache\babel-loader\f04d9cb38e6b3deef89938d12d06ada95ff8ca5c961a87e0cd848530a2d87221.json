{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 21c0-2.5 2-2.5 2-5\",\n  key: \"1sicvv\"\n}], [\"path\", {\n  d: \"M16 21c0-2.5 2-2.5 2-5\",\n  key: \"1o3eny\"\n}], [\"path\", {\n  d: \"m19 8-.8 3a1.25 1.25 0 0 1-1.2 1H7a1.25 1.25 0 0 1-1.2-1L5 8\",\n  key: \"1bvca4\"\n}], [\"path\", {\n  d: \"M21 3a1 1 0 0 1 1 1v2a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V4a1 1 0 0 1 1-1z\",\n  key: \"x3qr1j\"\n}], [\"path\", {\n  d: \"M6 21c0-2.5 2-2.5 2-5\",\n  key: \"i3w1gp\"\n}]];\nconst AlarmSmoke = createLucideIcon(\"alarm-smoke\", __iconNode);\nexport { __iconNode, AlarmSmoke as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "AlarmSmoke", "createLucideIcon"], "sources": ["D:\\ASL\\ASL-Training\\node_modules\\lucide-react\\src\\icons\\alarm-smoke.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M11 21c0-2.5 2-2.5 2-5', key: '1sicvv' }],\n  ['path', { d: 'M16 21c0-2.5 2-2.5 2-5', key: '1o3eny' }],\n  ['path', { d: 'm19 8-.8 3a1.25 1.25 0 0 1-1.2 1H7a1.25 1.25 0 0 1-1.2-1L5 8', key: '1bvca4' }],\n  [\n    'path',\n    { d: 'M21 3a1 1 0 0 1 1 1v2a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V4a1 1 0 0 1 1-1z', key: 'x3qr1j' },\n  ],\n  ['path', { d: 'M6 21c0-2.5 2-2.5 2-5', key: 'i3w1gp' }],\n];\n\n/**\n * @component @name AlarmSmoke\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEgMjFjMC0yLjUgMi0yLjUgMi01IiAvPgogIDxwYXRoIGQ9Ik0xNiAyMWMwLTIuNSAyLTIuNSAyLTUiIC8+CiAgPHBhdGggZD0ibTE5IDgtLjggM2ExLjI1IDEuMjUgMCAwIDEtMS4yIDFIN2ExLjI1IDEuMjUgMCAwIDEtMS4yLTFMNSA4IiAvPgogIDxwYXRoIGQ9Ik0yMSAzYTEgMSAwIDAgMSAxIDF2MmEyIDIgMCAwIDEtMiAySDRhMiAyIDAgMCAxLTItMlY0YTEgMSAwIDAgMSAxLTF6IiAvPgogIDxwYXRoIGQ9Ik02IDIxYzAtMi41IDItMi41IDItNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/alarm-smoke\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst AlarmSmoke = createLucideIcon('alarm-smoke', __iconNode);\n\nexport default AlarmSmoke;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,8DAAgE;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7F,CACE,QACA;EAAED,CAAA,EAAG,sEAAwE;EAAAC,GAAA,EAAK;AAAS,EAC7F,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,uBAAyB;EAAAC,GAAA,EAAK;AAAU,GACxD;AAaM,MAAAC,UAAA,GAAaC,gBAAiB,gBAAeJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}