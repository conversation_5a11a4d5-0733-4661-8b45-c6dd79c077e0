{"ast": null, "code": "var _jsxFileName = \"D:\\\\ASL\\\\ASL-Training\\\\src\\\\components\\\\TrainingPage.js\",\n  _s = $RefreshSig$();\nimport { useState, useRef, useCallback, useEffect } from 'react';\nimport styled from 'styled-components';\nimport Webcam from 'react-webcam';\nimport { Brain, Camera, ArrowLeft, Play, Square, Download, Eye, Target, Wifi, WifiOff, RefreshCw } from 'lucide-react';\nimport { useSignDetection } from '../hooks/useSignDetection';\nimport config from '../config';\nimport signLanguageData from '../data/signLanguageData';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TrainingContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow-x: hidden;\n\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n`;\n_c = TrainingContainer;\nconst Navigation = styled.nav`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: var(--bg-glass);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-neural);\n  padding: var(--space-4) 0;\n  transition: var(--transition-normal);\n`;\n_c2 = Navigation;\nconst NavContainer = styled.div`\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  @media (max-width: 768px) {\n    padding: 0 var(--space-4);\n  }\n`;\n_c3 = NavContainer;\nconst Logo = styled.div`\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n    gap: var(--space-2);\n  }\n`;\n_c4 = Logo;\nconst LogoIcon = styled.div`\n  width: 40px;\n  height: 40px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    width: 36px;\n    height: 36px;\n  }\n`;\n_c5 = LogoIcon;\nconst BackButton = styled.button`\n  background: var(--bg-glass);\n  color: var(--text-secondary);\n  border: 1px solid var(--border-neural);\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-xl);\n  font-size: 0.9rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  backdrop-filter: blur(10px);\n\n  &:hover {\n    background: var(--primary-50);\n    color: var(--primary-600);\n    border-color: var(--primary-300);\n    transform: translateY(-1px);\n    box-shadow: var(--shadow-lg);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-2) var(--space-4);\n    font-size: 0.85rem;\n  }\n`;\n_c6 = BackButton;\nconst PageTitle = styled.h1`\n  font-family: var(--font-primary);\n  font-size: 2.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  text-align: center;\n  margin-bottom: var(--space-4);\n  letter-spacing: -0.02em;\n\n  @media (max-width: 768px) {\n    font-size: 2rem;\n  }\n`;\n_c7 = PageTitle;\nconst PageSubtitle = styled.p`\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  text-align: center;\n  margin-bottom: var(--space-16);\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n\n  @media (max-width: 768px) {\n    margin-bottom: var(--space-12);\n    font-size: 1rem;\n  }\n`;\n_c8 = PageSubtitle;\nconst StatusBadge = styled.div`\n  display: inline-flex;\n  align-items: center;\n  gap: var(--space-2);\n  background: var(--bg-glass);\n  border: 1px solid var(--border-neural);\n  border-radius: var(--radius-full);\n  padding: var(--space-2) var(--space-4);\n  margin-bottom: var(--space-8);\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: var(--text-accent);\n  backdrop-filter: blur(10px);\n  box-shadow: var(--shadow-glow);\n\n  @media (max-width: 768px) {\n    font-size: 0.8rem;\n    padding: var(--space-2) var(--space-3);\n  }\n`;\n_c9 = StatusBadge;\nconst MainContent = styled.main`\n  padding: var(--space-20) var(--space-4) var(--space-16);\n  max-width: 1200px;\n  margin: 0 auto;\n\n  @media (max-width: 768px) {\n    padding: var(--space-12) var(--space-3) var(--space-8);\n    max-width: 100%;\n  }\n`;\n_c0 = MainContent;\nconst TrainingGrid = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--space-8);\n  max-width: 1200px;\n  margin: 0 auto var(--space-12);\n\n  @media (max-width: 1024px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-4);\n  }\n\n  @media (max-width: 768px) {\n    gap: var(--space-3);\n    margin: 0 auto var(--space-6);\n    grid-template-areas: \n      \"sign\"\n      \"camera\";\n  }\n`;\n_c1 = TrainingGrid;\nconst CameraSection = styled.div`\n  background: var(--bg-glass);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-10);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-lg);\n  transition: var(--transition-normal);\n  position: relative;\n  overflow: hidden;\n\n  @media (max-width: 768px) {\n    padding: var(--space-6);\n    border-radius: var(--radius-xl);\n    grid-area: camera;\n  }\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 4px;\n    background: var(--bg-neural);\n    transform: scaleX(0);\n    transition: var(--transition-normal);\n  }\n\n  &:hover {\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n    border-color: var(--primary-300);\n\n    &::before {\n      transform: scaleX(1);\n    }\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-8);\n  }\n`;\n_c10 = CameraSection;\nconst SectionTitle = styled.h2`\n  font-family: var(--font-primary);\n  font-size: 1.25rem;\n  margin-bottom: var(--space-6);\n  color: var(--text-primary);\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    margin-bottom: var(--space-4);\n  }\n`;\n_c11 = SectionTitle;\nconst SectionIcon = styled.div`\n  width: 36px;\n  height: 36px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    width: 32px;\n    height: 32px;\n  }\n`;\n_c12 = SectionIcon;\nconst WebcamContainer = styled.div`\n  position: relative;\n  border-radius: var(--radius-2xl);\n  overflow: hidden;\n  background: var(--neural-100);\n  aspect-ratio: 4/3;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 3px solid var(--border-neural);\n  margin-bottom: var(--space-6);\n  box-shadow: var(--shadow-lg);\n\n  @media (max-width: 768px) {\n    aspect-ratio: 3/4;\n    margin-bottom: var(--space-4);\n    border-radius: var(--radius-xl);\n    border-width: 2px;\n  }\n`;\n_c13 = WebcamContainer;\nconst StyledWebcam = styled(Webcam)`\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n`;\n_c14 = StyledWebcam;\nconst RecordingOverlay = styled.div`\n  position: absolute;\n  top: var(--space-4);\n  right: var(--space-4);\n  background: ${props => props.isRecording ? 'var(--error-500)' : 'var(--neural-600)'};\n  color: white;\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-full);\n  font-size: 0.9rem;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  box-shadow: var(--shadow-lg);\n  animation: ${props => props.isRecording ? 'pulse 1.5s infinite' : 'none'};\n\n  @keyframes pulse {\n    0%, 100% { opacity: 1; transform: scale(1); }\n    50% { opacity: 0.8; transform: scale(1.05); }\n  }\n`;\n_c15 = RecordingOverlay;\nconst SignSection = styled.div`\n  background: var(--bg-primary);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-8);\n  border: 1px solid var(--border-light);\n  box-shadow: var(--shadow-lg);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  transition: all 0.3s ease;\n\n  &:hover {\n    box-shadow: var(--shadow-xl);\n    border-color: var(--primary-200);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-6);\n    grid-area: sign;\n  }\n`;\n_c16 = SignSection;\nconst SignSelector = styled.select`\n  width: 100%;\n  padding: var(--space-3) var(--space-4);\n  border: 2px solid var(--border-light);\n  border-radius: var(--radius-lg);\n  background: var(--bg-primary);\n  color: var(--text-primary);\n  font-size: 1rem;\n  font-weight: 500;\n  margin-bottom: var(--space-4);\n  cursor: pointer;\n  transition: var(--transition-normal);\n\n  &:focus {\n    outline: none;\n    border-color: var(--primary-500);\n    box-shadow: 0 0 0 3px var(--primary-100);\n  }\n\n  &:hover {\n    border-color: var(--primary-300);\n  }\n\n  option {\n    padding: var(--space-2);\n    background: var(--bg-primary);\n    color: var(--text-primary);\n  }\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    padding: var(--space-4);\n    margin-bottom: var(--space-3);\n  }\n`;\n_c17 = SignSelector;\nconst SignDisplay = styled.div`\n  width: 300px;\n  height: 300px;\n  background: var(--primary-50);\n  border-radius: var(--radius-2xl);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: var(--space-6);\n  border: 2px solid var(--primary-200);\n  transition: all 0.3s ease;\n  overflow: hidden;\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: var(--radius-xl);\n  }\n\n  &:hover {\n    transform: scale(1.02);\n    border-color: var(--primary-300);\n  }\n\n  @media (max-width: 768px) {\n    width: 250px;\n    height: 250px;\n  }\n`;\n_c18 = SignDisplay;\nconst SignName = styled.h3`\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  margin-bottom: var(--space-3);\n  color: var(--text-primary);\n  font-weight: 700;\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n  }\n`;\n_c19 = SignName;\nconst SignDescription = styled.p`\n  text-align: center;\n  line-height: 1.6;\n  color: var(--text-secondary);\n  font-size: 0.9rem;\n  font-weight: 400;\n  max-width: 280px;\n`;\n_c20 = SignDescription;\nconst TopControlsSection = styled.div`\n  display: flex;\n  justify-content: center;\n  gap: var(--space-4);\n  margin-bottom: var(--space-8);\n  padding: var(--space-4);\n  background: var(--bg-glass);\n  border-radius: var(--radius-xl);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(10px);\n  box-shadow: var(--shadow-lg);\n\n  @media (max-width: 768px) {\n    flex-direction: column;\n    align-items: center;\n    gap: var(--space-3);\n    margin-bottom: var(--space-6);\n    padding: var(--space-3);\n  }\n`;\n_c21 = TopControlsSection;\nconst RecordingStatus = styled.div`\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  padding: var(--space-2) var(--space-3);\n  border-radius: var(--radius-lg);\n  font-size: 0.875rem;\n  font-weight: 500;\n  background: ${props => props.isRecording ? 'var(--error-50)' : 'var(--success-50)'};\n  color: ${props => props.isRecording ? 'var(--error-700)' : 'var(--success-700)'};\n  border: 1px solid ${props => props.isRecording ? 'var(--error-200)' : 'var(--success-200)'};\n  white-space: nowrap;\n\n  @media (max-width: 768px) {\n    font-size: 0.8rem;\n    padding: var(--space-1) var(--space-2);\n  }\n`;\n_c22 = RecordingStatus;\nconst ControlButton = styled.button`\n  background: ${props => props.variant === 'primary' ? 'var(--primary-600)' : props.variant === 'retry' ? 'var(--warning-500)' : 'var(--bg-primary)'};\n  border: ${props => props.variant === 'primary' || props.variant === 'retry' ? 'none' : '1px solid var(--border-medium)'};\n  color: ${props => props.variant === 'primary' || props.variant === 'retry' ? 'white' : 'var(--text-primary)'};\n  padding: ${props => props.compact ? 'var(--space-2) var(--space-4)' : 'var(--space-3) var(--space-6)'};\n  border-radius: var(--radius-lg);\n  cursor: pointer;\n  font-size: ${props => props.compact ? '0.8rem' : '0.9rem'};\n  font-weight: 600;\n  transition: all 0.2s ease;\n  min-width: ${props => props.compact ? '120px' : '160px'};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--space-2);\n\n  @media (max-width: 768px) {\n    padding: ${props => props.compact ? 'var(--space-3) var(--space-5)' : 'var(--space-4) var(--space-8)'};\n    font-size: ${props => props.compact ? '0.9rem' : '1rem'};\n    min-width: ${props => props.compact ? '140px' : '180px'};\n    border-radius: var(--radius-xl);\n  }\n  box-shadow: ${props => props.variant === 'primary' || props.variant === 'retry' ? 'var(--shadow-lg)' : 'var(--shadow-sm)'};\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: ${props => props.variant === 'primary' || props.variant === 'retry' ? 'var(--shadow-xl)' : 'var(--shadow-md)'};\n    background: ${props => props.variant === 'primary' ? 'var(--primary-700)' : props.variant === 'retry' ? 'var(--warning-600)' : 'var(--gray-50)'};\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none;\n  }\n\n  @media (max-width: 768px) {\n    width: 100%;\n    max-width: ${props => props.compact ? '200px' : '280px'};\n  }\n`;\n_c23 = ControlButton;\nconst StatusMessage = styled.div`\n  text-align: center;\n  margin-top: var(--space-6);\n  padding: var(--space-4) var(--space-6);\n  border-radius: var(--radius-lg);\n  background: ${props => props.type === 'success' ? 'var(--success-500)' : props.type === 'error' ? 'var(--error-500)' : 'var(--primary-600)'};\n  color: white;\n  font-weight: 500;\n  font-size: 0.875rem;\n  max-width: 400px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n_c24 = StatusMessage;\nconst RecordingsSection = styled.div`\n  margin-top: var(--space-16);\n  background: var(--bg-secondary);\n  padding: var(--space-12) var(--space-4);\n  border-radius: var(--radius-2xl);\n  max-width: 1200px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n_c25 = RecordingsSection;\nconst RecordingsTitle = styled.h3`\n  font-family: var(--font-primary);\n  color: var(--text-primary);\n  margin-bottom: var(--space-8);\n  font-size: 1.5rem;\n  font-weight: 600;\n  text-align: center;\n`;\n_c26 = RecordingsTitle;\nconst RecordingsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: var(--space-6);\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-4);\n  }\n`;\n_c27 = RecordingsGrid;\nconst RecordingCard = styled.div`\n  background: var(--bg-primary);\n  padding: var(--space-6);\n  border-radius: var(--radius-xl);\n  border: 1px solid var(--border-light);\n  box-shadow: var(--shadow-sm);\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: translateY(-2px);\n    border-color: var(--primary-200);\n    box-shadow: var(--shadow-lg);\n  }\n`;\n_c28 = RecordingCard;\nconst RecordingTitle = styled.p`\n  margin: 0 0 var(--space-2) 0;\n  color: var(--text-primary);\n  font-weight: 600;\n  font-size: 1rem;\n  font-family: var(--font-primary);\n`;\n_c29 = RecordingTitle;\nconst RecordingTime = styled.p`\n  margin: 0 0 var(--space-4) 0;\n  font-size: 0.8rem;\n  color: var(--text-tertiary);\n`;\n_c30 = RecordingTime;\nconst DownloadButton = styled.button`\n  background: var(--primary-600);\n  border: none;\n  border-radius: var(--radius-lg);\n  padding: var(--space-2) var(--space-4);\n  color: white;\n  cursor: pointer;\n  font-size: 0.8rem;\n  font-weight: 500;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  margin: 0 auto;\n\n  &:hover {\n    background: var(--primary-700);\n    transform: translateY(-1px);\n  }\n`;\n_c31 = DownloadButton;\nconst PredictionDisplay = styled.div`\n  background: var(--bg-glass);\n  border: 2px solid ${props => {\n  if (props.matched) return 'var(--success-400)';\n  if (props.isStale) return 'var(--warning-300)';\n  return 'var(--border-light)';\n}};\n  border-radius: var(--radius-xl);\n  padding: var(--space-4);\n  margin-bottom: var(--space-4);\n  text-align: center;\n  transition: var(--transition-normal);\n  backdrop-filter: blur(10px);\n  opacity: ${props => props.isStale ? 0.7 : 1};\n  min-height: 80px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n\n  ${props => props.matched && `\n    background: var(--success-50);\n    box-shadow: 0 0 20px var(--success-200);\n    animation: pulse 1s ease-in-out;\n  `}\n\n  ${props => props.isStale && `\n    background: var(--warning-50);\n  `}\n\n  @keyframes pulse {\n    0%, 100% { transform: scale(1); }\n    50% { transform: scale(1.02); }\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-3);\n    margin-bottom: var(--space-3);\n    min-height: 70px;\n  }\n`;\n_c32 = PredictionDisplay;\nconst PredictionText = styled.div`\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: ${props => {\n  if (props.matched) return 'var(--success-700)';\n  if (props.isStale) return 'var(--warning-700)';\n  return 'var(--text-primary)';\n}};\n  margin-bottom: var(--space-2);\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n  }\n`;\n_c33 = PredictionText;\nconst ConfidenceBar = styled.div`\n  width: 100%;\n  height: 8px;\n  background: var(--bg-secondary);\n  border-radius: var(--radius-full);\n  overflow: hidden;\n  margin-top: var(--space-2);\n`;\n_c34 = ConfidenceBar;\nconst ConfidenceFill = styled.div`\n  height: 100%;\n  background: ${props => {\n  if (props.confidence > 0.8) return 'var(--success-500)';\n  if (props.confidence > 0.6) return 'var(--warning-500)';\n  return 'var(--error-500)';\n}};\n  width: ${props => props.confidence * 100}%;\n  transition: width 0.3s ease;\n`;\n_c35 = ConfidenceFill;\nconst ConnectionStatus = styled.div`\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  padding: var(--space-2) var(--space-3);\n  border-radius: var(--radius-lg);\n  font-size: 0.875rem;\n  font-weight: 500;\n  background: ${props => props.connected ? 'var(--success-50)' : 'var(--error-50)'};\n  color: ${props => props.connected ? 'var(--success-700)' : 'var(--error-700)'};\n  border: 1px solid ${props => props.connected ? 'var(--success-200)' : 'var(--error-200)'};\n`;\n_c36 = ConnectionStatus;\nconst TrainingPage = ({\n  onBackToHome\n}) => {\n  _s();\n  const [currentSign, setCurrentSign] = useState('hello');\n  const [status, setStatus] = useState('');\n  const [isCapturing, setIsCapturing] = useState(false);\n  const [lastRecordingStatus, setLastRecordingStatus] = useState('');\n  // eslint-disable-next-line no-unused-vars\n  const [recordedVideos, setRecordedVideos] = useState([]);\n  const [imgError, setImgError] = useState(false);\n  const webcamRef = useRef(null);\n  const autoRecordTimeoutRef = useRef(null);\n  const matchCountRef = useRef(0);\n\n  // Use sign detection hook\n  const {\n    isConnected,\n    prediction,\n    isAIRecording,\n    recordingStatus,\n    signMatched,\n    targetSign,\n    startRecording: startAIRecording,\n    stopRecording: stopAIRecording,\n    startFrameCapture,\n    retryConnection\n  } = useSignDetection();\n  const handleSignChange = useCallback(event => {\n    setCurrentSign(event.target.value);\n    setImgError(false);\n  }, []);\n  const startDetection = useCallback(() => {\n    if (!webcamRef.current) {\n      setStatus('Camera not available');\n      return;\n    }\n    setIsCapturing(true);\n    startFrameCapture(webcamRef, 100); // Send frame every 100ms\n    setStatus('AI detection started');\n  }, [startFrameCapture]);\n  const startManualRecording = useCallback(() => {\n    if (!isConnected) {\n      setStatus('AI backend not connected');\n      return;\n    }\n    if (!webcamRef.current) {\n      setStatus('Camera not available');\n      return;\n    }\n    if (isAIRecording) {\n      setStatus('Already recording...');\n      return;\n    }\n\n    // Start manual 3-second recording with selected sign name\n    const selectedSignName = signLanguageData[currentSign].name;\n    setStatus(`🎬 Starting 3-second recording for \"${selectedSignName}\"...`);\n    setLastRecordingStatus(`🎬 Recording \"${selectedSignName}\"...`);\n    startAIRecording(selectedSignName, true); // Pass true to immediately start recording session\n\n    // Auto-stop after 3 seconds\n    autoRecordTimeoutRef.current = setTimeout(() => {\n      stopAIRecording();\n      setStatus(`✅ Recording complete! \"${selectedSignName}\" saved to recordings folder with landmark data`);\n      setLastRecordingStatus(`✅ Recording saved: \"${selectedSignName}\" (3 seconds)`);\n    }, 3000);\n\n    // Also start frame capture if not already started\n    if (!isCapturing) {\n      startDetection();\n    }\n  }, [currentSign, isConnected, isCapturing, startDetection, isAIRecording, startAIRecording, stopAIRecording]);\n  const stopManualRecording = useCallback(() => {\n    // Stop current recording\n    if (isAIRecording) {\n      stopAIRecording();\n      setLastRecordingStatus(`✅ Recording saved: \"${signLanguageData[currentSign].name}\" (Manual stop)`);\n    }\n    matchCountRef.current = 0;\n    if (autoRecordTimeoutRef.current) {\n      clearTimeout(autoRecordTimeoutRef.current);\n    }\n    setStatus('Manual recording stopped');\n  }, [stopAIRecording, isAIRecording, currentSign]);\n  const downloadRecording = video => {\n    const a = document.createElement('a');\n    a.href = video.url;\n    a.download = `sign_${video.sign}_${video.timestamp}.webm`;\n    a.click();\n  };\n\n  // Auto-start detection when connected\n  useEffect(() => {\n    if (isConnected && webcamRef.current && !isCapturing) {\n      startDetection();\n    }\n  }, [isConnected, startDetection, isCapturing]);\n\n  // Update last recording status when recordingStatus changes\n  useEffect(() => {\n    if (recordingStatus && recordingStatus.includes('saved')) {\n      setLastRecordingStatus(recordingStatus);\n    }\n  }, [recordingStatus]);\n\n  // Always-on auto-recording logic - records when confidence >= 50%\n  useEffect(() => {\n    if (!prediction || !isConnected) {\n      matchCountRef.current = 0;\n      return;\n    }\n    const predictedSign = prediction.sign.toLowerCase();\n    const targetSignLower = signLanguageData[currentSign].name.toLowerCase();\n    const confidence = prediction.confidence;\n\n    // Auto-record when sign matches with >= 50% confidence\n    if (predictedSign === targetSignLower && confidence >= 0.5) {\n      matchCountRef.current += 1;\n\n      // Start recording after 2 consecutive matches to avoid false positives\n      if (matchCountRef.current >= 2 && !isAIRecording) {\n        setStatus(`🎬 Auto-recording \"${signLanguageData[currentSign].name}\"... (${Math.round(confidence * 100)}% confidence)`);\n        startAIRecording(signLanguageData[currentSign].name, false); // Auto-recording doesn't start session immediately\n\n        // Auto-stop recording after 3 seconds\n        autoRecordTimeoutRef.current = setTimeout(() => {\n          stopAIRecording();\n          setStatus(`✅ Auto-recording complete! \"${signLanguageData[currentSign].name}\" saved to recordings folder with landmark data`);\n          setLastRecordingStatus(`✅ Auto-recording saved: \"${signLanguageData[currentSign].name}\" (3 seconds)`);\n          matchCountRef.current = 0;\n        }, 3000);\n      }\n    } else {\n      // Reset match count if sign doesn't match or confidence is too low\n      matchCountRef.current = 0;\n    }\n    return () => {\n      if (autoRecordTimeoutRef.current) {\n        clearTimeout(autoRecordTimeoutRef.current);\n      }\n    };\n  }, [prediction, currentSign, isAIRecording, startAIRecording, stopAIRecording, isConnected]);\n  return /*#__PURE__*/_jsxDEV(TrainingContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Navigation, {\n      children: /*#__PURE__*/_jsxDEV(NavContainer, {\n        children: [/*#__PURE__*/_jsxDEV(Logo, {\n          children: [/*#__PURE__*/_jsxDEV(LogoIcon, {\n            children: /*#__PURE__*/_jsxDEV(Brain, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 904,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 903,\n            columnNumber: 13\n          }, this), \"ASL Neural\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 902,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(BackButton, {\n          onClick: onBackToHome,\n          children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 909,\n            columnNumber: 13\n          }, this), \"Back to Home\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 908,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 901,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 900,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginBottom: 'var(--space-12)'\n        },\n        children: /*#__PURE__*/_jsxDEV(StatusBadge, {\n          children: [/*#__PURE__*/_jsxDEV(Eye, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 918,\n            columnNumber: 13\n          }, this), \"Neural Vision Active\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 917,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 916,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PageTitle, {\n        children: \"AI Training Session\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 923,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PageSubtitle, {\n        children: \"Experience real-time neural network analysis as our AI learns from your sign language practice\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 924,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TopControlsSection, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 'var(--space-4)',\n            flexWrap: 'wrap',\n            justifyContent: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ControlButton, {\n            variant: \"primary\",\n            compact: true,\n            onClick: isAIRecording ? stopManualRecording : startManualRecording,\n            children: isAIRecording ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Square, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 937,\n                columnNumber: 19\n              }, this), \"Stop Recording\"]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Play, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 942,\n                columnNumber: 19\n              }, this), \"Record 3s Video\"]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 930,\n            columnNumber: 13\n          }, this), lastRecordingStatus && /*#__PURE__*/_jsxDEV(RecordingStatus, {\n            isRecording: isAIRecording,\n            children: lastRecordingStatus\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 949,\n            columnNumber: 15\n          }, this), !isConnected && /*#__PURE__*/_jsxDEV(ControlButton, {\n            variant: \"retry\",\n            compact: true,\n            onClick: retryConnection,\n            children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 960,\n              columnNumber: 17\n            }, this), \"Retry Connection\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 955,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 929,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 928,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TrainingGrid, {\n        children: [/*#__PURE__*/_jsxDEV(SignSection, {\n          children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n            children: [/*#__PURE__*/_jsxDEV(SectionIcon, {\n              children: /*#__PURE__*/_jsxDEV(Target, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 971,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 970,\n              columnNumber: 15\n            }, this), \"Select a Sign\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 969,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SignSelector, {\n            value: currentSign,\n            onChange: handleSignChange,\n            disabled: isAIRecording,\n            children: Object.keys(signLanguageData).map(signKey => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: signKey,\n              children: signLanguageData[signKey].name\n            }, signKey, false, {\n              fileName: _jsxFileName,\n              lineNumber: 981,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 975,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SignDisplay, {\n            children: !imgError ? /*#__PURE__*/_jsxDEV(\"img\", {\n              src: signLanguageData[currentSign].gif,\n              alt: signLanguageData[currentSign].name,\n              onError: () => setImgError(true),\n              style: {\n                display: imgError ? 'none' : 'block'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 988,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                fontSize: '3rem',\n                width: '100%',\n                height: '100%',\n                alignItems: 'center',\n                justifyContent: 'center'\n              },\n              children: \"\\uD83D\\uDCF7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 995,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 986,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SignName, {\n            children: signLanguageData[currentSign].name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1000,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SignDescription, {\n            children: signLanguageData[currentSign].description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1001,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 968,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CameraSection, {\n          children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n            children: [/*#__PURE__*/_jsxDEV(SectionIcon, {\n              children: /*#__PURE__*/_jsxDEV(Camera, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1009,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1008,\n              columnNumber: 15\n            }, this), \"Neural Vision Feed\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1007,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ConnectionStatus, {\n            connected: isConnected,\n            children: [isConnected ? /*#__PURE__*/_jsxDEV(Wifi, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1015,\n              columnNumber: 30\n            }, this) : /*#__PURE__*/_jsxDEV(WifiOff, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1015,\n              columnNumber: 51\n            }, this), isConnected ? 'AI Connected' : 'AI Disconnected']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1014,\n            columnNumber: 13\n          }, this), prediction && /*#__PURE__*/_jsxDEV(PredictionDisplay, {\n            matched: signMatched,\n            isStale: prediction.isStale,\n            children: [/*#__PURE__*/_jsxDEV(PredictionText, {\n              matched: signMatched,\n              isStale: prediction.isStale,\n              children: [\"Detected: \", prediction.sign, prediction.isStale && ' (previous)']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1021,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ConfidenceBar, {\n              children: /*#__PURE__*/_jsxDEV(ConfidenceFill, {\n                confidence: prediction.confidence\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1026,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1025,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                marginTop: '8px',\n                color: 'var(--text-secondary)'\n              },\n              children: [\"Confidence: \", Math.round(prediction.confidence * 100), \"%\", signMatched && targetSign && /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: 'var(--success-600)',\n                  marginLeft: '8px'\n                },\n                children: \"\\u2713 Match! Recording...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1031,\n                columnNumber: 21\n              }, this), !isAIRecording && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: 'var(--primary-600)',\n                  marginTop: '4px'\n                },\n                children: [\"\\uD83C\\uDFAF Auto-recording active: Perform \\\"\", signLanguageData[currentSign].name, \"\\\" sign (\\u226550% confidence)\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1038,\n                  columnNumber: 23\n                }, this), \"\\uD83D\\uDCA1 Or click \\\"Record 3 Seconds\\\" for manual recording\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1036,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1028,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1020,\n            columnNumber: 15\n          }, this), !prediction && /*#__PURE__*/_jsxDEV(PredictionDisplay, {\n            children: [/*#__PURE__*/_jsxDEV(PredictionText, {\n              children: [\"\\uD83C\\uDFAF Ready to detect \\\"\", signLanguageData[currentSign].name, \"\\\"\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1048,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                color: 'var(--text-secondary)'\n              },\n              children: \"Auto-recording is active. Perform the sign with \\u226550% confidence to trigger recording.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1051,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1047,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(WebcamContainer, {\n            children: [/*#__PURE__*/_jsxDEV(StyledWebcam, {\n              ref: webcamRef,\n              audio: false,\n              screenshotFormat: \"image/jpeg\",\n              videoConstraints: {\n                width: 640,\n                height: 480,\n                facingMode: \"user\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1057,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(RecordingOverlay, {\n              isRecording: isAIRecording,\n              children: isAIRecording ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '8px',\n                    height: '8px',\n                    borderRadius: '50%',\n                    backgroundColor: 'white',\n                    marginRight: '4px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1070,\n                  columnNumber: 21\n                }, this), \"Recording\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Eye, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1081,\n                  columnNumber: 21\n                }, this), \"Ready\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1067,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1056,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1006,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 967,\n        columnNumber: 9\n      }, this), (status || recordingStatus) && /*#__PURE__*/_jsxDEV(StatusMessage, {\n        type: (status || recordingStatus).includes('error') ? 'error' : (status || recordingStatus).includes('success') ? 'success' : 'info',\n        children: recordingStatus || status\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1091,\n        columnNumber: 11\n      }, this), recordedVideos.length > 0 && /*#__PURE__*/_jsxDEV(RecordingsSection, {\n        children: [/*#__PURE__*/_jsxDEV(RecordingsTitle, {\n          children: \"Your Practice Recordings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1098,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(RecordingsGrid, {\n          children: recordedVideos.map(video => /*#__PURE__*/_jsxDEV(RecordingCard, {\n            children: [/*#__PURE__*/_jsxDEV(RecordingTitle, {\n              children: video.sign\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1102,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(RecordingTime, {\n              children: new Date(video.timestamp).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1103,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(DownloadButton, {\n              onClick: () => downloadRecording(video),\n              children: [/*#__PURE__*/_jsxDEV(Download, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1107,\n                columnNumber: 21\n              }, this), \"Download\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1106,\n              columnNumber: 19\n            }, this)]\n          }, video.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1101,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1099,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1097,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 915,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 899,\n    columnNumber: 5\n  }, this);\n};\n_s(TrainingPage, \"vJYzeVOKzYbbUXBIWr5fuFbPT+Y=\", false, function () {\n  return [useSignDetection];\n});\n_c37 = TrainingPage;\nexport default TrainingPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32, _c33, _c34, _c35, _c36, _c37;\n$RefreshReg$(_c, \"TrainingContainer\");\n$RefreshReg$(_c2, \"Navigation\");\n$RefreshReg$(_c3, \"NavContainer\");\n$RefreshReg$(_c4, \"Logo\");\n$RefreshReg$(_c5, \"LogoIcon\");\n$RefreshReg$(_c6, \"BackButton\");\n$RefreshReg$(_c7, \"PageTitle\");\n$RefreshReg$(_c8, \"PageSubtitle\");\n$RefreshReg$(_c9, \"StatusBadge\");\n$RefreshReg$(_c0, \"MainContent\");\n$RefreshReg$(_c1, \"TrainingGrid\");\n$RefreshReg$(_c10, \"CameraSection\");\n$RefreshReg$(_c11, \"SectionTitle\");\n$RefreshReg$(_c12, \"SectionIcon\");\n$RefreshReg$(_c13, \"WebcamContainer\");\n$RefreshReg$(_c14, \"StyledWebcam\");\n$RefreshReg$(_c15, \"RecordingOverlay\");\n$RefreshReg$(_c16, \"SignSection\");\n$RefreshReg$(_c17, \"SignSelector\");\n$RefreshReg$(_c18, \"SignDisplay\");\n$RefreshReg$(_c19, \"SignName\");\n$RefreshReg$(_c20, \"SignDescription\");\n$RefreshReg$(_c21, \"TopControlsSection\");\n$RefreshReg$(_c22, \"RecordingStatus\");\n$RefreshReg$(_c23, \"ControlButton\");\n$RefreshReg$(_c24, \"StatusMessage\");\n$RefreshReg$(_c25, \"RecordingsSection\");\n$RefreshReg$(_c26, \"RecordingsTitle\");\n$RefreshReg$(_c27, \"RecordingsGrid\");\n$RefreshReg$(_c28, \"RecordingCard\");\n$RefreshReg$(_c29, \"RecordingTitle\");\n$RefreshReg$(_c30, \"RecordingTime\");\n$RefreshReg$(_c31, \"DownloadButton\");\n$RefreshReg$(_c32, \"PredictionDisplay\");\n$RefreshReg$(_c33, \"PredictionText\");\n$RefreshReg$(_c34, \"ConfidenceBar\");\n$RefreshReg$(_c35, \"ConfidenceFill\");\n$RefreshReg$(_c36, \"ConnectionStatus\");\n$RefreshReg$(_c37, \"TrainingPage\");", "map": {"version": 3, "names": ["useState", "useRef", "useCallback", "useEffect", "styled", "Webcam", "Brain", "Camera", "ArrowLeft", "Play", "Square", "Download", "Eye", "Target", "Wifi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RefreshCw", "useSignDetection", "config", "signLanguageData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TrainingContainer", "div", "_c", "Navigation", "nav", "_c2", "NavContainer", "_c3", "Logo", "_c4", "LogoIcon", "_c5", "BackButton", "button", "_c6", "Page<PERSON><PERSON>le", "h1", "_c7", "PageSubtitle", "p", "_c8", "StatusBadge", "_c9", "MainContent", "main", "_c0", "TrainingGrid", "_c1", "CameraSection", "_c10", "SectionTitle", "h2", "_c11", "SectionIcon", "_c12", "WebcamContainer", "_c13", "StyledWebcam", "_c14", "RecordingOverlay", "props", "isRecording", "_c15", "SignSection", "_c16", "SignSelector", "select", "_c17", "SignDisplay", "_c18", "SignName", "h3", "_c19", "SignDescription", "_c20", "TopControlsSection", "_c21", "RecordingStatus", "_c22", "ControlButton", "variant", "compact", "_c23", "StatusMessage", "type", "_c24", "RecordingsSection", "_c25", "RecordingsTitle", "_c26", "RecordingsGrid", "_c27", "RecordingCard", "_c28", "RecordingTitle", "_c29", "RecordingTime", "_c30", "DownloadButton", "_c31", "PredictionDisplay", "matched", "isStale", "_c32", "PredictionText", "_c33", "ConfidenceBar", "_c34", "ConfidenceFill", "confidence", "_c35", "ConnectionStatus", "connected", "_c36", "TrainingPage", "onBackToHome", "_s", "currentSign", "setCurrentSign", "status", "setStatus", "isCapturing", "setIsCapturing", "lastRecordingStatus", "setLastRecordingStatus", "recordedVideos", "setRecordedVideos", "imgError", "setImgError", "webcamRef", "autoRecordTimeoutRef", "matchCountRef", "isConnected", "prediction", "isAIRecording", "recordingStatus", "signMatched", "targetSign", "startRecording", "startAIRecording", "stopRecording", "stopAIRecording", "startFrameCapture", "retryConnection", "handleSignChange", "event", "target", "value", "startDetection", "current", "startManualRecording", "selectedSignName", "name", "setTimeout", "stopManualRecording", "clearTimeout", "downloadRecording", "video", "a", "document", "createElement", "href", "url", "download", "sign", "timestamp", "click", "includes", "predictedSign", "toLowerCase", "targetSignLower", "Math", "round", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "style", "textAlign", "marginBottom", "display", "alignItems", "gap", "flexWrap", "justifyContent", "onChange", "disabled", "Object", "keys", "map", "sign<PERSON><PERSON>", "src", "gif", "alt", "onError", "fontSize", "width", "height", "description", "marginTop", "color", "marginLeft", "ref", "audio", "screenshotFormat", "videoConstraints", "facingMode", "borderRadius", "backgroundColor", "marginRight", "length", "Date", "toLocaleString", "id", "_c37", "$RefreshReg$"], "sources": ["D:/ASL/ASL-Training/src/components/TrainingPage.js"], "sourcesContent": ["import { useState, useRef, useCallback, useEffect } from 'react';\nimport styled from 'styled-components';\nimport Webcam from 'react-webcam';\nimport {\n  Brain,\n  Camera,\n  ArrowLeft,\n  Play,\n  Square,\n  Download,\n  Eye,\n  Target,\n  Wifi,\n  WifiOff,\n  RefreshCw\n} from 'lucide-react';\nimport { useSignDetection } from '../hooks/useSignDetection';\nimport config from '../config';\nimport signLanguageData from '../data/signLanguageData';\n\nconst TrainingContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow-x: hidden;\n\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n`;\n\nconst Navigation = styled.nav`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: var(--bg-glass);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-neural);\n  padding: var(--space-4) 0;\n  transition: var(--transition-normal);\n`;\n\nconst NavContainer = styled.div`\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  @media (max-width: 768px) {\n    padding: 0 var(--space-4);\n  }\n`;\n\nconst Logo = styled.div`\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n    gap: var(--space-2);\n  }\n`;\n\nconst LogoIcon = styled.div`\n  width: 40px;\n  height: 40px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    width: 36px;\n    height: 36px;\n  }\n`;\n\nconst BackButton = styled.button`\n  background: var(--bg-glass);\n  color: var(--text-secondary);\n  border: 1px solid var(--border-neural);\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-xl);\n  font-size: 0.9rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  backdrop-filter: blur(10px);\n\n  &:hover {\n    background: var(--primary-50);\n    color: var(--primary-600);\n    border-color: var(--primary-300);\n    transform: translateY(-1px);\n    box-shadow: var(--shadow-lg);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-2) var(--space-4);\n    font-size: 0.85rem;\n  }\n`;\n\nconst PageTitle = styled.h1`\n  font-family: var(--font-primary);\n  font-size: 2.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  text-align: center;\n  margin-bottom: var(--space-4);\n  letter-spacing: -0.02em;\n\n  @media (max-width: 768px) {\n    font-size: 2rem;\n  }\n`;\n\nconst PageSubtitle = styled.p`\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  text-align: center;\n  margin-bottom: var(--space-16);\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n\n  @media (max-width: 768px) {\n    margin-bottom: var(--space-12);\n    font-size: 1rem;\n  }\n`;\n\nconst StatusBadge = styled.div`\n  display: inline-flex;\n  align-items: center;\n  gap: var(--space-2);\n  background: var(--bg-glass);\n  border: 1px solid var(--border-neural);\n  border-radius: var(--radius-full);\n  padding: var(--space-2) var(--space-4);\n  margin-bottom: var(--space-8);\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: var(--text-accent);\n  backdrop-filter: blur(10px);\n  box-shadow: var(--shadow-glow);\n\n  @media (max-width: 768px) {\n    font-size: 0.8rem;\n    padding: var(--space-2) var(--space-3);\n  }\n`;\n\nconst MainContent = styled.main`\n  padding: var(--space-20) var(--space-4) var(--space-16);\n  max-width: 1200px;\n  margin: 0 auto;\n\n  @media (max-width: 768px) {\n    padding: var(--space-12) var(--space-3) var(--space-8);\n    max-width: 100%;\n  }\n`;\n\nconst TrainingGrid = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--space-8);\n  max-width: 1200px;\n  margin: 0 auto var(--space-12);\n\n  @media (max-width: 1024px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-4);\n  }\n\n  @media (max-width: 768px) {\n    gap: var(--space-3);\n    margin: 0 auto var(--space-6);\n    grid-template-areas: \n      \"sign\"\n      \"camera\";\n  }\n`;\n\nconst CameraSection = styled.div`\n  background: var(--bg-glass);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-10);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-lg);\n  transition: var(--transition-normal);\n  position: relative;\n  overflow: hidden;\n\n  @media (max-width: 768px) {\n    padding: var(--space-6);\n    border-radius: var(--radius-xl);\n    grid-area: camera;\n  }\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 4px;\n    background: var(--bg-neural);\n    transform: scaleX(0);\n    transition: var(--transition-normal);\n  }\n\n  &:hover {\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n    border-color: var(--primary-300);\n\n    &::before {\n      transform: scaleX(1);\n    }\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-8);\n  }\n`;\n\nconst SectionTitle = styled.h2`\n  font-family: var(--font-primary);\n  font-size: 1.25rem;\n  margin-bottom: var(--space-6);\n  color: var(--text-primary);\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    margin-bottom: var(--space-4);\n  }\n`;\n\nconst SectionIcon = styled.div`\n  width: 36px;\n  height: 36px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    width: 32px;\n    height: 32px;\n  }\n`;\n\nconst WebcamContainer = styled.div`\n  position: relative;\n  border-radius: var(--radius-2xl);\n  overflow: hidden;\n  background: var(--neural-100);\n  aspect-ratio: 4/3;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 3px solid var(--border-neural);\n  margin-bottom: var(--space-6);\n  box-shadow: var(--shadow-lg);\n\n  @media (max-width: 768px) {\n    aspect-ratio: 3/4;\n    margin-bottom: var(--space-4);\n    border-radius: var(--radius-xl);\n    border-width: 2px;\n  }\n`;\n\nconst StyledWebcam = styled(Webcam)`\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n`;\n\nconst RecordingOverlay = styled.div`\n  position: absolute;\n  top: var(--space-4);\n  right: var(--space-4);\n  background: ${props => props.isRecording ?\n    'var(--error-500)' :\n    'var(--neural-600)'\n  };\n  color: white;\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-full);\n  font-size: 0.9rem;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  box-shadow: var(--shadow-lg);\n  animation: ${props => props.isRecording ? 'pulse 1.5s infinite' : 'none'};\n\n  @keyframes pulse {\n    0%, 100% { opacity: 1; transform: scale(1); }\n    50% { opacity: 0.8; transform: scale(1.05); }\n  }\n`;\n\nconst SignSection = styled.div`\n  background: var(--bg-primary);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-8);\n  border: 1px solid var(--border-light);\n  box-shadow: var(--shadow-lg);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  transition: all 0.3s ease;\n\n  &:hover {\n    box-shadow: var(--shadow-xl);\n    border-color: var(--primary-200);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-6);\n    grid-area: sign;\n  }\n`;\n\nconst SignSelector = styled.select`\n  width: 100%;\n  padding: var(--space-3) var(--space-4);\n  border: 2px solid var(--border-light);\n  border-radius: var(--radius-lg);\n  background: var(--bg-primary);\n  color: var(--text-primary);\n  font-size: 1rem;\n  font-weight: 500;\n  margin-bottom: var(--space-4);\n  cursor: pointer;\n  transition: var(--transition-normal);\n\n  &:focus {\n    outline: none;\n    border-color: var(--primary-500);\n    box-shadow: 0 0 0 3px var(--primary-100);\n  }\n\n  &:hover {\n    border-color: var(--primary-300);\n  }\n\n  option {\n    padding: var(--space-2);\n    background: var(--bg-primary);\n    color: var(--text-primary);\n  }\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    padding: var(--space-4);\n    margin-bottom: var(--space-3);\n  }\n`;\n\nconst SignDisplay = styled.div`\n  width: 300px;\n  height: 300px;\n  background: var(--primary-50);\n  border-radius: var(--radius-2xl);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: var(--space-6);\n  border: 2px solid var(--primary-200);\n  transition: all 0.3s ease;\n  overflow: hidden;\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: var(--radius-xl);\n  }\n\n  &:hover {\n    transform: scale(1.02);\n    border-color: var(--primary-300);\n  }\n\n  @media (max-width: 768px) {\n    width: 250px;\n    height: 250px;\n  }\n`;\n\nconst SignName = styled.h3`\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  margin-bottom: var(--space-3);\n  color: var(--text-primary);\n  font-weight: 700;\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n  }\n`;\n\nconst SignDescription = styled.p`\n  text-align: center;\n  line-height: 1.6;\n  color: var(--text-secondary);\n  font-size: 0.9rem;\n  font-weight: 400;\n  max-width: 280px;\n`;\n\nconst TopControlsSection = styled.div`\n  display: flex;\n  justify-content: center;\n  gap: var(--space-4);\n  margin-bottom: var(--space-8);\n  padding: var(--space-4);\n  background: var(--bg-glass);\n  border-radius: var(--radius-xl);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(10px);\n  box-shadow: var(--shadow-lg);\n\n  @media (max-width: 768px) {\n    flex-direction: column;\n    align-items: center;\n    gap: var(--space-3);\n    margin-bottom: var(--space-6);\n    padding: var(--space-3);\n  }\n`;\n\nconst RecordingStatus = styled.div`\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  padding: var(--space-2) var(--space-3);\n  border-radius: var(--radius-lg);\n  font-size: 0.875rem;\n  font-weight: 500;\n  background: ${props => props.isRecording ? 'var(--error-50)' : 'var(--success-50)'};\n  color: ${props => props.isRecording ? 'var(--error-700)' : 'var(--success-700)'};\n  border: 1px solid ${props => props.isRecording ? 'var(--error-200)' : 'var(--success-200)'};\n  white-space: nowrap;\n\n  @media (max-width: 768px) {\n    font-size: 0.8rem;\n    padding: var(--space-1) var(--space-2);\n  }\n`;\n\nconst ControlButton = styled.button`\n  background: ${props => props.variant === 'primary'\n    ? 'var(--primary-600)'\n    : props.variant === 'retry'\n    ? 'var(--warning-500)'\n    : 'var(--bg-primary)'};\n  border: ${props => props.variant === 'primary' || props.variant === 'retry'\n    ? 'none'\n    : '1px solid var(--border-medium)'};\n  color: ${props => props.variant === 'primary' || props.variant === 'retry'\n    ? 'white'\n    : 'var(--text-primary)'};\n  padding: ${props => props.compact\n    ? 'var(--space-2) var(--space-4)'\n    : 'var(--space-3) var(--space-6)'};\n  border-radius: var(--radius-lg);\n  cursor: pointer;\n  font-size: ${props => props.compact ? '0.8rem' : '0.9rem'};\n  font-weight: 600;\n  transition: all 0.2s ease;\n  min-width: ${props => props.compact ? '120px' : '160px'};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--space-2);\n\n  @media (max-width: 768px) {\n    padding: ${props => props.compact\n      ? 'var(--space-3) var(--space-5)'\n      : 'var(--space-4) var(--space-8)'};\n    font-size: ${props => props.compact ? '0.9rem' : '1rem'};\n    min-width: ${props => props.compact ? '140px' : '180px'};\n    border-radius: var(--radius-xl);\n  }\n  box-shadow: ${props => props.variant === 'primary' || props.variant === 'retry'\n    ? 'var(--shadow-lg)'\n    : 'var(--shadow-sm)'};\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: ${props => props.variant === 'primary' || props.variant === 'retry'\n      ? 'var(--shadow-xl)'\n      : 'var(--shadow-md)'};\n    background: ${props => props.variant === 'primary'\n      ? 'var(--primary-700)'\n      : props.variant === 'retry'\n      ? 'var(--warning-600)'\n      : 'var(--gray-50)'};\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none;\n  }\n\n  @media (max-width: 768px) {\n    width: 100%;\n    max-width: ${props => props.compact ? '200px' : '280px'};\n  }\n`;\n\nconst StatusMessage = styled.div`\n  text-align: center;\n  margin-top: var(--space-6);\n  padding: var(--space-4) var(--space-6);\n  border-radius: var(--radius-lg);\n  background: ${props =>\n    props.type === 'success' ? 'var(--success-500)' :\n    props.type === 'error' ? 'var(--error-500)' :\n    'var(--primary-600)'\n  };\n  color: white;\n  font-weight: 500;\n  font-size: 0.875rem;\n  max-width: 400px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n\nconst RecordingsSection = styled.div`\n  margin-top: var(--space-16);\n  background: var(--bg-secondary);\n  padding: var(--space-12) var(--space-4);\n  border-radius: var(--radius-2xl);\n  max-width: 1200px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n\nconst RecordingsTitle = styled.h3`\n  font-family: var(--font-primary);\n  color: var(--text-primary);\n  margin-bottom: var(--space-8);\n  font-size: 1.5rem;\n  font-weight: 600;\n  text-align: center;\n`;\n\nconst RecordingsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: var(--space-6);\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-4);\n  }\n`;\n\nconst RecordingCard = styled.div`\n  background: var(--bg-primary);\n  padding: var(--space-6);\n  border-radius: var(--radius-xl);\n  border: 1px solid var(--border-light);\n  box-shadow: var(--shadow-sm);\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: translateY(-2px);\n    border-color: var(--primary-200);\n    box-shadow: var(--shadow-lg);\n  }\n`;\n\nconst RecordingTitle = styled.p`\n  margin: 0 0 var(--space-2) 0;\n  color: var(--text-primary);\n  font-weight: 600;\n  font-size: 1rem;\n  font-family: var(--font-primary);\n`;\n\nconst RecordingTime = styled.p`\n  margin: 0 0 var(--space-4) 0;\n  font-size: 0.8rem;\n  color: var(--text-tertiary);\n`;\n\nconst DownloadButton = styled.button`\n  background: var(--primary-600);\n  border: none;\n  border-radius: var(--radius-lg);\n  padding: var(--space-2) var(--space-4);\n  color: white;\n  cursor: pointer;\n  font-size: 0.8rem;\n  font-weight: 500;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  margin: 0 auto;\n\n  &:hover {\n    background: var(--primary-700);\n    transform: translateY(-1px);\n  }\n`;\n\nconst PredictionDisplay = styled.div`\n  background: var(--bg-glass);\n  border: 2px solid ${props => {\n    if (props.matched) return 'var(--success-400)';\n    if (props.isStale) return 'var(--warning-300)';\n    return 'var(--border-light)';\n  }};\n  border-radius: var(--radius-xl);\n  padding: var(--space-4);\n  margin-bottom: var(--space-4);\n  text-align: center;\n  transition: var(--transition-normal);\n  backdrop-filter: blur(10px);\n  opacity: ${props => props.isStale ? 0.7 : 1};\n  min-height: 80px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n\n  ${props => props.matched && `\n    background: var(--success-50);\n    box-shadow: 0 0 20px var(--success-200);\n    animation: pulse 1s ease-in-out;\n  `}\n\n  ${props => props.isStale && `\n    background: var(--warning-50);\n  `}\n\n  @keyframes pulse {\n    0%, 100% { transform: scale(1); }\n    50% { transform: scale(1.02); }\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-3);\n    margin-bottom: var(--space-3);\n    min-height: 70px;\n  }\n`;\n\nconst PredictionText = styled.div`\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: ${props => {\n    if (props.matched) return 'var(--success-700)';\n    if (props.isStale) return 'var(--warning-700)';\n    return 'var(--text-primary)';\n  }};\n  margin-bottom: var(--space-2);\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n  }\n`;\n\nconst ConfidenceBar = styled.div`\n  width: 100%;\n  height: 8px;\n  background: var(--bg-secondary);\n  border-radius: var(--radius-full);\n  overflow: hidden;\n  margin-top: var(--space-2);\n`;\n\nconst ConfidenceFill = styled.div`\n  height: 100%;\n  background: ${props => {\n    if (props.confidence > 0.8) return 'var(--success-500)';\n    if (props.confidence > 0.6) return 'var(--warning-500)';\n    return 'var(--error-500)';\n  }};\n  width: ${props => (props.confidence * 100)}%;\n  transition: width 0.3s ease;\n`;\n\nconst ConnectionStatus = styled.div`\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  padding: var(--space-2) var(--space-3);\n  border-radius: var(--radius-lg);\n  font-size: 0.875rem;\n  font-weight: 500;\n  background: ${props => props.connected ? 'var(--success-50)' : 'var(--error-50)'};\n  color: ${props => props.connected ? 'var(--success-700)' : 'var(--error-700)'};\n  border: 1px solid ${props => props.connected ? 'var(--success-200)' : 'var(--error-200)'};\n`;\n\nconst TrainingPage = ({ onBackToHome }) => {\n  const [currentSign, setCurrentSign] = useState('hello');\n  const [status, setStatus] = useState('');\n  const [isCapturing, setIsCapturing] = useState(false);\n  const [lastRecordingStatus, setLastRecordingStatus] = useState('');\n  // eslint-disable-next-line no-unused-vars\n  const [recordedVideos, setRecordedVideos] = useState([]);\n  const [imgError, setImgError] = useState(false);\n\n  const webcamRef = useRef(null);\n  const autoRecordTimeoutRef = useRef(null);\n  const matchCountRef = useRef(0);\n\n  // Use sign detection hook\n  const {\n    isConnected,\n    prediction,\n    isAIRecording,\n    recordingStatus,\n    signMatched,\n    targetSign,\n    startRecording: startAIRecording,\n    stopRecording: stopAIRecording,\n    startFrameCapture,\n    retryConnection\n  } = useSignDetection();\n\n  const handleSignChange = useCallback((event) => {\n    setCurrentSign(event.target.value);\n    setImgError(false);\n  }, []);\n\n  const startDetection = useCallback(() => {\n    if (!webcamRef.current) {\n      setStatus('Camera not available');\n      return;\n    }\n\n    setIsCapturing(true);\n    startFrameCapture(webcamRef, 100); // Send frame every 100ms\n    setStatus('AI detection started');\n  }, [startFrameCapture]);\n\n  const startManualRecording = useCallback(() => {\n    if (!isConnected) {\n      setStatus('AI backend not connected');\n      return;\n    }\n\n    if (!webcamRef.current) {\n      setStatus('Camera not available');\n      return;\n    }\n\n    if (isAIRecording) {\n      setStatus('Already recording...');\n      return;\n    }\n\n    // Start manual 3-second recording with selected sign name\n    const selectedSignName = signLanguageData[currentSign].name;\n    setStatus(`🎬 Starting 3-second recording for \"${selectedSignName}\"...`);\n    setLastRecordingStatus(`🎬 Recording \"${selectedSignName}\"...`);\n    startAIRecording(selectedSignName, true); // Pass true to immediately start recording session\n\n    // Auto-stop after 3 seconds\n    autoRecordTimeoutRef.current = setTimeout(() => {\n      stopAIRecording();\n      setStatus(`✅ Recording complete! \"${selectedSignName}\" saved to recordings folder with landmark data`);\n      setLastRecordingStatus(`✅ Recording saved: \"${selectedSignName}\" (3 seconds)`);\n    }, 3000);\n\n    // Also start frame capture if not already started\n    if (!isCapturing) {\n      startDetection();\n    }\n  }, [currentSign, isConnected, isCapturing, startDetection, isAIRecording, startAIRecording, stopAIRecording]);\n\n  const stopManualRecording = useCallback(() => {\n    // Stop current recording\n    if (isAIRecording) {\n      stopAIRecording();\n      setLastRecordingStatus(`✅ Recording saved: \"${signLanguageData[currentSign].name}\" (Manual stop)`);\n    }\n    matchCountRef.current = 0;\n    if (autoRecordTimeoutRef.current) {\n      clearTimeout(autoRecordTimeoutRef.current);\n    }\n    setStatus('Manual recording stopped');\n  }, [stopAIRecording, isAIRecording, currentSign]);\n\n  const downloadRecording = (video) => {\n    const a = document.createElement('a');\n    a.href = video.url;\n    a.download = `sign_${video.sign}_${video.timestamp}.webm`;\n    a.click();\n  };\n\n  // Auto-start detection when connected\n  useEffect(() => {\n    if (isConnected && webcamRef.current && !isCapturing) {\n      startDetection();\n    }\n  }, [isConnected, startDetection, isCapturing]);\n\n  // Update last recording status when recordingStatus changes\n  useEffect(() => {\n    if (recordingStatus && recordingStatus.includes('saved')) {\n      setLastRecordingStatus(recordingStatus);\n    }\n  }, [recordingStatus]);\n\n  // Always-on auto-recording logic - records when confidence >= 50%\n  useEffect(() => {\n    if (!prediction || !isConnected) {\n      matchCountRef.current = 0;\n      return;\n    }\n\n    const predictedSign = prediction.sign.toLowerCase();\n    const targetSignLower = signLanguageData[currentSign].name.toLowerCase();\n    const confidence = prediction.confidence;\n\n    // Auto-record when sign matches with >= 50% confidence\n    if (predictedSign === targetSignLower && confidence >= 0.5) {\n      matchCountRef.current += 1;\n\n      // Start recording after 2 consecutive matches to avoid false positives\n      if (matchCountRef.current >= 2 && !isAIRecording) {\n        setStatus(`🎬 Auto-recording \"${signLanguageData[currentSign].name}\"... (${Math.round(confidence * 100)}% confidence)`);\n        startAIRecording(signLanguageData[currentSign].name, false); // Auto-recording doesn't start session immediately\n\n        // Auto-stop recording after 3 seconds\n        autoRecordTimeoutRef.current = setTimeout(() => {\n          stopAIRecording();\n          setStatus(`✅ Auto-recording complete! \"${signLanguageData[currentSign].name}\" saved to recordings folder with landmark data`);\n          setLastRecordingStatus(`✅ Auto-recording saved: \"${signLanguageData[currentSign].name}\" (3 seconds)`);\n          matchCountRef.current = 0;\n        }, 3000);\n      }\n    } else {\n      // Reset match count if sign doesn't match or confidence is too low\n      matchCountRef.current = 0;\n    }\n\n    return () => {\n      if (autoRecordTimeoutRef.current) {\n        clearTimeout(autoRecordTimeoutRef.current);\n      }\n    };\n  }, [prediction, currentSign, isAIRecording, startAIRecording, stopAIRecording, isConnected]);\n\n  return (\n    <TrainingContainer>\n      <Navigation>\n        <NavContainer>\n          <Logo>\n            <LogoIcon>\n              <Brain size={24} />\n            </LogoIcon>\n            ASL Neural\n          </Logo>\n          <BackButton onClick={onBackToHome}>\n            <ArrowLeft size={18} />\n            Back to Home\n          </BackButton>\n        </NavContainer>\n      </Navigation>\n\n      <MainContent>\n        <div style={{ textAlign: 'center', marginBottom: 'var(--space-12)' }}>\n          <StatusBadge>\n            <Eye size={16} />\n            Neural Vision Active\n          </StatusBadge>\n        </div>\n\n        <PageTitle>AI Training Session</PageTitle>\n        <PageSubtitle>\n          Experience real-time neural network analysis as our AI learns from your sign language practice\n        </PageSubtitle>\n\n        <TopControlsSection>\n          <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-4)', flexWrap: 'wrap', justifyContent: 'center' }}>\n            <ControlButton\n              variant=\"primary\"\n              compact\n              onClick={isAIRecording ? stopManualRecording : startManualRecording}\n            >\n              {isAIRecording ? (\n                <>\n                  <Square size={16} />\n                  Stop Recording\n                </>\n              ) : (\n                <>\n                  <Play size={16} />\n                  Record 3s Video\n                </>\n              )}\n            </ControlButton>\n\n            {lastRecordingStatus && (\n              <RecordingStatus isRecording={isAIRecording}>\n                {lastRecordingStatus}\n              </RecordingStatus>\n            )}\n\n            {!isConnected && (\n              <ControlButton\n                variant=\"retry\"\n                compact\n                onClick={retryConnection}\n              >\n                <RefreshCw size={16} />\n                Retry Connection\n              </ControlButton>\n            )}\n          </div>\n        </TopControlsSection>\n\n        <TrainingGrid>\n          <SignSection>\n            <SectionTitle>\n              <SectionIcon>\n                <Target size={24} />\n              </SectionIcon>\n              Select a Sign\n            </SectionTitle>\n            <SignSelector\n              value={currentSign}\n              onChange={handleSignChange}\n              disabled={isAIRecording}\n            >\n              {Object.keys(signLanguageData).map(signKey => (\n                <option key={signKey} value={signKey}>\n                  {signLanguageData[signKey].name}\n                </option>\n              ))}\n            </SignSelector>\n            <SignDisplay>\n              {!imgError ? (\n                <img\n                  src={signLanguageData[currentSign].gif}\n                  alt={signLanguageData[currentSign].name}\n                  onError={() => setImgError(true)}\n                  style={{ display: imgError ? 'none' : 'block' }}\n                />\n              ) : (\n                <div style={{display: 'flex', fontSize: '3rem', width: '100%', height: '100%', alignItems: 'center', justifyContent: 'center'}}>\n                  📷\n                </div>\n              )}\n            </SignDisplay>\n            <SignName>{signLanguageData[currentSign].name}</SignName>\n            <SignDescription>\n              {signLanguageData[currentSign].description}\n            </SignDescription>\n          </SignSection>\n\n          <CameraSection>\n            <SectionTitle>\n              <SectionIcon>\n                <Camera size={24} />\n              </SectionIcon>\n              Neural Vision Feed\n            </SectionTitle>\n\n            <ConnectionStatus connected={isConnected}>\n              {isConnected ? <Wifi size={16} /> : <WifiOff size={16} />}\n              {isConnected ? 'AI Connected' : 'AI Disconnected'}\n            </ConnectionStatus>\n\n            {prediction && (\n              <PredictionDisplay matched={signMatched} isStale={prediction.isStale}>\n                <PredictionText matched={signMatched} isStale={prediction.isStale}>\n                  Detected: {prediction.sign}\n                  {prediction.isStale && ' (previous)'}\n                </PredictionText>\n                <ConfidenceBar>\n                  <ConfidenceFill confidence={prediction.confidence} />\n                </ConfidenceBar>\n                <div style={{ fontSize: '0.875rem', marginTop: '8px', color: 'var(--text-secondary)' }}>\n                  Confidence: {Math.round(prediction.confidence * 100)}%\n                  {signMatched && targetSign && (\n                    <span style={{ color: 'var(--success-600)', marginLeft: '8px' }}>\n                      ✓ Match! Recording...\n                    </span>\n                  )}\n                  {!isAIRecording && (\n                    <div style={{ color: 'var(--primary-600)', marginTop: '4px' }}>\n                      🎯 Auto-recording active: Perform \"{signLanguageData[currentSign].name}\" sign (≥50% confidence)\n                      <br />\n                      💡 Or click \"Record 3 Seconds\" for manual recording\n                    </div>\n                  )}\n                </div>\n              </PredictionDisplay>\n            )}\n\n            {!prediction && (\n              <PredictionDisplay>\n                <PredictionText>\n                  🎯 Ready to detect \"{signLanguageData[currentSign].name}\"\n                </PredictionText>\n                <div style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>\n                  Auto-recording is active. Perform the sign with ≥50% confidence to trigger recording.\n                </div>\n              </PredictionDisplay>\n            )}\n            <WebcamContainer>\n              <StyledWebcam\n                ref={webcamRef}\n                audio={false}\n                screenshotFormat=\"image/jpeg\"\n                videoConstraints={{\n                  width: 640,\n                  height: 480,\n                  facingMode: \"user\"\n                }}\n              />\n              <RecordingOverlay isRecording={isAIRecording}>\n                {isAIRecording ? (\n                  <>\n                    <div style={{\n                      width: '8px',\n                      height: '8px',\n                      borderRadius: '50%',\n                      backgroundColor: 'white',\n                      marginRight: '4px'\n                    }} />\n                    Recording\n                  </>\n                ) : (\n                  <>\n                    <Eye size={16} />\n                    Ready\n                  </>\n                )}\n              </RecordingOverlay>\n            </WebcamContainer>\n          </CameraSection>\n        </TrainingGrid>\n\n        {(status || recordingStatus) && (\n          <StatusMessage type={(status || recordingStatus).includes('error') ? 'error' : (status || recordingStatus).includes('success') ? 'success' : 'info'}>\n            {recordingStatus || status}\n          </StatusMessage>\n        )}\n\n        {recordedVideos.length > 0 && (\n          <RecordingsSection>\n            <RecordingsTitle>Your Practice Recordings</RecordingsTitle>\n            <RecordingsGrid>\n              {recordedVideos.map((video) => (\n                <RecordingCard key={video.id}>\n                  <RecordingTitle>{video.sign}</RecordingTitle>\n                  <RecordingTime>\n                    {new Date(video.timestamp).toLocaleString()}\n                  </RecordingTime>\n                  <DownloadButton onClick={() => downloadRecording(video)}>\n                    <Download size={16} />\n                    Download\n                  </DownloadButton>\n                </RecordingCard>\n              ))}\n            </RecordingsGrid>\n          </RecordingsSection>\n        )}\n      </MainContent>\n    </TrainingContainer>\n  );\n};\n\nexport default TrainingPage; "], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AAChE,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,cAAc;AACjC,SACEC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,IAAI,EACJC,MAAM,EACNC,QAAQ,EACRC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,OAAO,EACPC,SAAS,QACJ,cAAc;AACrB,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,gBAAgB,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExD,MAAMC,iBAAiB,GAAGpB,MAAM,CAACqB,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAnBIF,iBAAiB;AAqBvB,MAAMG,UAAU,GAAGvB,MAAM,CAACwB,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAXIF,UAAU;AAahB,MAAMG,YAAY,GAAG1B,MAAM,CAACqB,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACM,GAAA,GAXID,YAAY;AAalB,MAAME,IAAI,GAAG5B,MAAM,CAACqB,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GAhBID,IAAI;AAkBV,MAAME,QAAQ,GAAG9B,MAAM,CAACqB,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GAfID,QAAQ;AAiBd,MAAME,UAAU,GAAGhC,MAAM,CAACiC,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GA3BIF,UAAU;AA6BhB,MAAMG,SAAS,GAAGnC,MAAM,CAACoC,EAAE;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAfIF,SAAS;AAiBf,MAAMG,YAAY,GAAGtC,MAAM,CAACuC,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAdIF,YAAY;AAgBlB,MAAMG,WAAW,GAAGzC,MAAM,CAACqB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACqB,GAAA,GAnBID,WAAW;AAqBjB,MAAME,WAAW,GAAG3C,MAAM,CAAC4C,IAAI;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GATIF,WAAW;AAWjB,MAAMG,YAAY,GAAG9C,MAAM,CAACqB,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC0B,GAAA,GAnBID,YAAY;AAqBlB,MAAME,aAAa,GAAGhD,MAAM,CAACqB,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC4B,IAAA,GAzCID,aAAa;AA2CnB,MAAME,YAAY,GAAGlD,MAAM,CAACmD,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAdIF,YAAY;AAgBlB,MAAMG,WAAW,GAAGrD,MAAM,CAACqB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACiC,IAAA,GAfID,WAAW;AAiBjB,MAAME,eAAe,GAAGvD,MAAM,CAACqB,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmC,IAAA,GAnBID,eAAe;AAqBrB,MAAME,YAAY,GAAGzD,MAAM,CAACC,MAAM,CAAC;AACnC;AACA;AACA;AACA,CAAC;AAACyD,IAAA,GAJID,YAAY;AAMlB,MAAME,gBAAgB,GAAG3D,MAAM,CAACqB,GAAG;AACnC;AACA;AACA;AACA,gBAAgBuC,KAAK,IAAIA,KAAK,CAACC,WAAW,GACtC,kBAAkB,GAClB,mBAAmB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eACeD,KAAK,IAAIA,KAAK,CAACC,WAAW,GAAG,qBAAqB,GAAG,MAAM;AAC1E;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAvBIH,gBAAgB;AAyBtB,MAAMI,WAAW,GAAG/D,MAAM,CAACqB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC2C,IAAA,GArBID,WAAW;AAuBjB,MAAME,YAAY,GAAGjE,MAAM,CAACkE,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAlCIF,YAAY;AAoClB,MAAMG,WAAW,GAAGpE,MAAM,CAACqB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgD,IAAA,GA7BID,WAAW;AA+BjB,MAAME,QAAQ,GAAGtE,MAAM,CAACuE,EAAE;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAVIF,QAAQ;AAYd,MAAMG,eAAe,GAAGzE,MAAM,CAACuC,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmC,IAAA,GAPID,eAAe;AASrB,MAAME,kBAAkB,GAAG3E,MAAM,CAACqB,GAAG;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuD,IAAA,GAnBID,kBAAkB;AAqBxB,MAAME,eAAe,GAAG7E,MAAM,CAACqB,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBuC,KAAK,IAAIA,KAAK,CAACC,WAAW,GAAG,iBAAiB,GAAG,mBAAmB;AACpF,WAAWD,KAAK,IAAIA,KAAK,CAACC,WAAW,GAAG,kBAAkB,GAAG,oBAAoB;AACjF,sBAAsBD,KAAK,IAAIA,KAAK,CAACC,WAAW,GAAG,kBAAkB,GAAG,oBAAoB;AAC5F;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACiB,IAAA,GAjBID,eAAe;AAmBrB,MAAME,aAAa,GAAG/E,MAAM,CAACiC,MAAM;AACnC,gBAAgB2B,KAAK,IAAIA,KAAK,CAACoB,OAAO,KAAK,SAAS,GAC9C,oBAAoB,GACpBpB,KAAK,CAACoB,OAAO,KAAK,OAAO,GACzB,oBAAoB,GACpB,mBAAmB;AACzB,YAAYpB,KAAK,IAAIA,KAAK,CAACoB,OAAO,KAAK,SAAS,IAAIpB,KAAK,CAACoB,OAAO,KAAK,OAAO,GACvE,MAAM,GACN,gCAAgC;AACtC,WAAWpB,KAAK,IAAIA,KAAK,CAACoB,OAAO,KAAK,SAAS,IAAIpB,KAAK,CAACoB,OAAO,KAAK,OAAO,GACtE,OAAO,GACP,qBAAqB;AAC3B,aAAapB,KAAK,IAAIA,KAAK,CAACqB,OAAO,GAC7B,+BAA+B,GAC/B,+BAA+B;AACrC;AACA;AACA,eAAerB,KAAK,IAAIA,KAAK,CAACqB,OAAO,GAAG,QAAQ,GAAG,QAAQ;AAC3D;AACA;AACA,eAAerB,KAAK,IAAIA,KAAK,CAACqB,OAAO,GAAG,OAAO,GAAG,OAAO;AACzD;AACA;AACA;AACA;AACA;AACA;AACA,eAAerB,KAAK,IAAIA,KAAK,CAACqB,OAAO,GAC7B,+BAA+B,GAC/B,+BAA+B;AACvC,iBAAiBrB,KAAK,IAAIA,KAAK,CAACqB,OAAO,GAAG,QAAQ,GAAG,MAAM;AAC3D,iBAAiBrB,KAAK,IAAIA,KAAK,CAACqB,OAAO,GAAG,OAAO,GAAG,OAAO;AAC3D;AACA;AACA,gBAAgBrB,KAAK,IAAIA,KAAK,CAACoB,OAAO,KAAK,SAAS,IAAIpB,KAAK,CAACoB,OAAO,KAAK,OAAO,GAC3E,kBAAkB,GAClB,kBAAkB;AACxB;AACA;AACA;AACA,kBAAkBpB,KAAK,IAAIA,KAAK,CAACoB,OAAO,KAAK,SAAS,IAAIpB,KAAK,CAACoB,OAAO,KAAK,OAAO,GAC3E,kBAAkB,GAClB,kBAAkB;AAC1B,kBAAkBpB,KAAK,IAAIA,KAAK,CAACoB,OAAO,KAAK,SAAS,GAC9C,oBAAoB,GACpBpB,KAAK,CAACoB,OAAO,KAAK,OAAO,GACzB,oBAAoB,GACpB,gBAAgB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiBpB,KAAK,IAAIA,KAAK,CAACqB,OAAO,GAAG,OAAO,GAAG,OAAO;AAC3D;AACA,CAAC;AAACC,IAAA,GA5DIH,aAAa;AA8DnB,MAAMI,aAAa,GAAGnF,MAAM,CAACqB,GAAG;AAChC;AACA;AACA;AACA;AACA,gBAAgBuC,KAAK,IACjBA,KAAK,CAACwB,IAAI,KAAK,SAAS,GAAG,oBAAoB,GAC/CxB,KAAK,CAACwB,IAAI,KAAK,OAAO,GAAG,kBAAkB,GAC3C,oBAAoB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,CACC;AAACC,IAAA,GAhBIF,aAAa;AAkBnB,MAAMG,iBAAiB,GAAGtF,MAAM,CAACqB,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkE,IAAA,GARID,iBAAiB;AAUvB,MAAME,eAAe,GAAGxF,MAAM,CAACuE,EAAE;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkB,IAAA,GAPID,eAAe;AASrB,MAAME,cAAc,GAAG1F,MAAM,CAACqB,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsE,IAAA,GATID,cAAc;AAWpB,MAAME,aAAa,GAAG5F,MAAM,CAACqB,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACwE,IAAA,GAbID,aAAa;AAenB,MAAME,cAAc,GAAG9F,MAAM,CAACuC,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACwD,IAAA,GANID,cAAc;AAQpB,MAAME,aAAa,GAAGhG,MAAM,CAACuC,CAAC;AAC9B;AACA;AACA;AACA,CAAC;AAAC0D,IAAA,GAJID,aAAa;AAMnB,MAAME,cAAc,GAAGlG,MAAM,CAACiC,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkE,IAAA,GAnBID,cAAc;AAqBpB,MAAME,iBAAiB,GAAGpG,MAAM,CAACqB,GAAG;AACpC;AACA,sBAAsBuC,KAAK,IAAI;EAC3B,IAAIA,KAAK,CAACyC,OAAO,EAAE,OAAO,oBAAoB;EAC9C,IAAIzC,KAAK,CAAC0C,OAAO,EAAE,OAAO,oBAAoB;EAC9C,OAAO,qBAAqB;AAC9B,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA,aAAa1C,KAAK,IAAIA,KAAK,CAAC0C,OAAO,GAAG,GAAG,GAAG,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA,IAAI1C,KAAK,IAAIA,KAAK,CAACyC,OAAO,IAAI;AAC9B;AACA;AACA;AACA,GAAG;AACH;AACA,IAAIzC,KAAK,IAAIA,KAAK,CAAC0C,OAAO,IAAI;AAC9B;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAvCIH,iBAAiB;AAyCvB,MAAMI,cAAc,GAAGxG,MAAM,CAACqB,GAAG;AACjC;AACA;AACA,WAAWuC,KAAK,IAAI;EAChB,IAAIA,KAAK,CAACyC,OAAO,EAAE,OAAO,oBAAoB;EAC9C,IAAIzC,KAAK,CAAC0C,OAAO,EAAE,OAAO,oBAAoB;EAC9C,OAAO,qBAAqB;AAC9B,CAAC;AACH;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,IAAA,GAbID,cAAc;AAepB,MAAME,aAAa,GAAG1G,MAAM,CAACqB,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsF,IAAA,GAPID,aAAa;AASnB,MAAME,cAAc,GAAG5G,MAAM,CAACqB,GAAG;AACjC;AACA,gBAAgBuC,KAAK,IAAI;EACrB,IAAIA,KAAK,CAACiD,UAAU,GAAG,GAAG,EAAE,OAAO,oBAAoB;EACvD,IAAIjD,KAAK,CAACiD,UAAU,GAAG,GAAG,EAAE,OAAO,oBAAoB;EACvD,OAAO,kBAAkB;AAC3B,CAAC;AACH,WAAWjD,KAAK,IAAKA,KAAK,CAACiD,UAAU,GAAG,GAAI;AAC5C;AACA,CAAC;AAACC,IAAA,GATIF,cAAc;AAWpB,MAAMG,gBAAgB,GAAG/G,MAAM,CAACqB,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBuC,KAAK,IAAIA,KAAK,CAACoD,SAAS,GAAG,mBAAmB,GAAG,iBAAiB;AAClF,WAAWpD,KAAK,IAAIA,KAAK,CAACoD,SAAS,GAAG,oBAAoB,GAAG,kBAAkB;AAC/E,sBAAsBpD,KAAK,IAAIA,KAAK,CAACoD,SAAS,GAAG,oBAAoB,GAAG,kBAAkB;AAC1F,CAAC;AAACC,IAAA,GAXIF,gBAAgB;AAatB,MAAMG,YAAY,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1H,QAAQ,CAAC,OAAO,CAAC;EACvD,MAAM,CAAC2H,MAAM,EAAEC,SAAS,CAAC,GAAG5H,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC6H,WAAW,EAAEC,cAAc,CAAC,GAAG9H,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+H,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhI,QAAQ,CAAC,EAAE,CAAC;EAClE;EACA,MAAM,CAACiI,cAAc,EAAEC,iBAAiB,CAAC,GAAGlI,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACmI,QAAQ,EAAEC,WAAW,CAAC,GAAGpI,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAMqI,SAAS,GAAGpI,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMqI,oBAAoB,GAAGrI,MAAM,CAAC,IAAI,CAAC;EACzC,MAAMsI,aAAa,GAAGtI,MAAM,CAAC,CAAC,CAAC;;EAE/B;EACA,MAAM;IACJuI,WAAW;IACXC,UAAU;IACVC,aAAa;IACbC,eAAe;IACfC,WAAW;IACXC,UAAU;IACVC,cAAc,EAAEC,gBAAgB;IAChCC,aAAa,EAAEC,eAAe;IAC9BC,iBAAiB;IACjBC;EACF,CAAC,GAAGlI,gBAAgB,CAAC,CAAC;EAEtB,MAAMmI,gBAAgB,GAAGlJ,WAAW,CAAEmJ,KAAK,IAAK;IAC9C3B,cAAc,CAAC2B,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;IAClCnB,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMoB,cAAc,GAAGtJ,WAAW,CAAC,MAAM;IACvC,IAAI,CAACmI,SAAS,CAACoB,OAAO,EAAE;MACtB7B,SAAS,CAAC,sBAAsB,CAAC;MACjC;IACF;IAEAE,cAAc,CAAC,IAAI,CAAC;IACpBoB,iBAAiB,CAACb,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC;IACnCT,SAAS,CAAC,sBAAsB,CAAC;EACnC,CAAC,EAAE,CAACsB,iBAAiB,CAAC,CAAC;EAEvB,MAAMQ,oBAAoB,GAAGxJ,WAAW,CAAC,MAAM;IAC7C,IAAI,CAACsI,WAAW,EAAE;MAChBZ,SAAS,CAAC,0BAA0B,CAAC;MACrC;IACF;IAEA,IAAI,CAACS,SAAS,CAACoB,OAAO,EAAE;MACtB7B,SAAS,CAAC,sBAAsB,CAAC;MACjC;IACF;IAEA,IAAIc,aAAa,EAAE;MACjBd,SAAS,CAAC,sBAAsB,CAAC;MACjC;IACF;;IAEA;IACA,MAAM+B,gBAAgB,GAAGxI,gBAAgB,CAACsG,WAAW,CAAC,CAACmC,IAAI;IAC3DhC,SAAS,CAAC,uCAAuC+B,gBAAgB,MAAM,CAAC;IACxE3B,sBAAsB,CAAC,iBAAiB2B,gBAAgB,MAAM,CAAC;IAC/DZ,gBAAgB,CAACY,gBAAgB,EAAE,IAAI,CAAC,CAAC,CAAC;;IAE1C;IACArB,oBAAoB,CAACmB,OAAO,GAAGI,UAAU,CAAC,MAAM;MAC9CZ,eAAe,CAAC,CAAC;MACjBrB,SAAS,CAAC,0BAA0B+B,gBAAgB,iDAAiD,CAAC;MACtG3B,sBAAsB,CAAC,uBAAuB2B,gBAAgB,eAAe,CAAC;IAChF,CAAC,EAAE,IAAI,CAAC;;IAER;IACA,IAAI,CAAC9B,WAAW,EAAE;MAChB2B,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAC/B,WAAW,EAAEe,WAAW,EAAEX,WAAW,EAAE2B,cAAc,EAAEd,aAAa,EAAEK,gBAAgB,EAAEE,eAAe,CAAC,CAAC;EAE7G,MAAMa,mBAAmB,GAAG5J,WAAW,CAAC,MAAM;IAC5C;IACA,IAAIwI,aAAa,EAAE;MACjBO,eAAe,CAAC,CAAC;MACjBjB,sBAAsB,CAAC,uBAAuB7G,gBAAgB,CAACsG,WAAW,CAAC,CAACmC,IAAI,iBAAiB,CAAC;IACpG;IACArB,aAAa,CAACkB,OAAO,GAAG,CAAC;IACzB,IAAInB,oBAAoB,CAACmB,OAAO,EAAE;MAChCM,YAAY,CAACzB,oBAAoB,CAACmB,OAAO,CAAC;IAC5C;IACA7B,SAAS,CAAC,0BAA0B,CAAC;EACvC,CAAC,EAAE,CAACqB,eAAe,EAAEP,aAAa,EAAEjB,WAAW,CAAC,CAAC;EAEjD,MAAMuC,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMC,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGJ,KAAK,CAACK,GAAG;IAClBJ,CAAC,CAACK,QAAQ,GAAG,QAAQN,KAAK,CAACO,IAAI,IAAIP,KAAK,CAACQ,SAAS,OAAO;IACzDP,CAAC,CAACQ,KAAK,CAAC,CAAC;EACX,CAAC;;EAED;EACAvK,SAAS,CAAC,MAAM;IACd,IAAIqI,WAAW,IAAIH,SAAS,CAACoB,OAAO,IAAI,CAAC5B,WAAW,EAAE;MACpD2B,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAChB,WAAW,EAAEgB,cAAc,EAAE3B,WAAW,CAAC,CAAC;;EAE9C;EACA1H,SAAS,CAAC,MAAM;IACd,IAAIwI,eAAe,IAAIA,eAAe,CAACgC,QAAQ,CAAC,OAAO,CAAC,EAAE;MACxD3C,sBAAsB,CAACW,eAAe,CAAC;IACzC;EACF,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;;EAErB;EACAxI,SAAS,CAAC,MAAM;IACd,IAAI,CAACsI,UAAU,IAAI,CAACD,WAAW,EAAE;MAC/BD,aAAa,CAACkB,OAAO,GAAG,CAAC;MACzB;IACF;IAEA,MAAMmB,aAAa,GAAGnC,UAAU,CAAC+B,IAAI,CAACK,WAAW,CAAC,CAAC;IACnD,MAAMC,eAAe,GAAG3J,gBAAgB,CAACsG,WAAW,CAAC,CAACmC,IAAI,CAACiB,WAAW,CAAC,CAAC;IACxE,MAAM5D,UAAU,GAAGwB,UAAU,CAACxB,UAAU;;IAExC;IACA,IAAI2D,aAAa,KAAKE,eAAe,IAAI7D,UAAU,IAAI,GAAG,EAAE;MAC1DsB,aAAa,CAACkB,OAAO,IAAI,CAAC;;MAE1B;MACA,IAAIlB,aAAa,CAACkB,OAAO,IAAI,CAAC,IAAI,CAACf,aAAa,EAAE;QAChDd,SAAS,CAAC,sBAAsBzG,gBAAgB,CAACsG,WAAW,CAAC,CAACmC,IAAI,SAASmB,IAAI,CAACC,KAAK,CAAC/D,UAAU,GAAG,GAAG,CAAC,eAAe,CAAC;QACvH8B,gBAAgB,CAAC5H,gBAAgB,CAACsG,WAAW,CAAC,CAACmC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;;QAE7D;QACAtB,oBAAoB,CAACmB,OAAO,GAAGI,UAAU,CAAC,MAAM;UAC9CZ,eAAe,CAAC,CAAC;UACjBrB,SAAS,CAAC,+BAA+BzG,gBAAgB,CAACsG,WAAW,CAAC,CAACmC,IAAI,iDAAiD,CAAC;UAC7H5B,sBAAsB,CAAC,4BAA4B7G,gBAAgB,CAACsG,WAAW,CAAC,CAACmC,IAAI,eAAe,CAAC;UACrGrB,aAAa,CAACkB,OAAO,GAAG,CAAC;QAC3B,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,MAAM;MACL;MACAlB,aAAa,CAACkB,OAAO,GAAG,CAAC;IAC3B;IAEA,OAAO,MAAM;MACX,IAAInB,oBAAoB,CAACmB,OAAO,EAAE;QAChCM,YAAY,CAACzB,oBAAoB,CAACmB,OAAO,CAAC;MAC5C;IACF,CAAC;EACH,CAAC,EAAE,CAAChB,UAAU,EAAEhB,WAAW,EAAEiB,aAAa,EAAEK,gBAAgB,EAAEE,eAAe,EAAET,WAAW,CAAC,CAAC;EAE5F,oBACEnH,OAAA,CAACG,iBAAiB;IAAAyJ,QAAA,gBAChB5J,OAAA,CAACM,UAAU;MAAAsJ,QAAA,eACT5J,OAAA,CAACS,YAAY;QAAAmJ,QAAA,gBACX5J,OAAA,CAACW,IAAI;UAAAiJ,QAAA,gBACH5J,OAAA,CAACa,QAAQ;YAAA+I,QAAA,eACP5J,OAAA,CAACf,KAAK;cAAC4K,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,cAEb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPjK,OAAA,CAACe,UAAU;UAACmJ,OAAO,EAAEhE,YAAa;UAAA0D,QAAA,gBAChC5J,OAAA,CAACb,SAAS;YAAC0K,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEzB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEbjK,OAAA,CAAC0B,WAAW;MAAAkI,QAAA,gBACV5J,OAAA;QAAKmK,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAkB,CAAE;QAAAT,QAAA,eACnE5J,OAAA,CAACwB,WAAW;UAAAoI,QAAA,gBACV5J,OAAA,CAACT,GAAG;YAACsK,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,wBAEnB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eAENjK,OAAA,CAACkB,SAAS;QAAA0I,QAAA,EAAC;MAAmB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eAC1CjK,OAAA,CAACqB,YAAY;QAAAuI,QAAA,EAAC;MAEd;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eAEfjK,OAAA,CAAC0D,kBAAkB;QAAAkG,QAAA,eACjB5J,OAAA;UAAKmK,KAAK,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE,gBAAgB;YAAEC,QAAQ,EAAE,MAAM;YAAEC,cAAc,EAAE;UAAS,CAAE;UAAAd,QAAA,gBACvH5J,OAAA,CAAC8D,aAAa;YACZC,OAAO,EAAC,SAAS;YACjBC,OAAO;YACPkG,OAAO,EAAE7C,aAAa,GAAGoB,mBAAmB,GAAGJ,oBAAqB;YAAAuB,QAAA,EAEnEvC,aAAa,gBACZrH,OAAA,CAAAE,SAAA;cAAA0J,QAAA,gBACE5J,OAAA,CAACX,MAAM;gBAACwK,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,kBAEtB;YAAA,eAAE,CAAC,gBAEHjK,OAAA,CAAAE,SAAA;cAAA0J,QAAA,gBACE5J,OAAA,CAACZ,IAAI;gBAACyK,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAEpB;YAAA,eAAE;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACY,CAAC,EAEfvD,mBAAmB,iBAClB1G,OAAA,CAAC4D,eAAe;YAAChB,WAAW,EAAEyE,aAAc;YAAAuC,QAAA,EACzClD;UAAmB;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAClB,EAEA,CAAC9C,WAAW,iBACXnH,OAAA,CAAC8D,aAAa;YACZC,OAAO,EAAC,OAAO;YACfC,OAAO;YACPkG,OAAO,EAAEpC,eAAgB;YAAA8B,QAAA,gBAEzB5J,OAAA,CAACL,SAAS;cAACkK,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,oBAEzB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAChB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAErBjK,OAAA,CAAC6B,YAAY;QAAA+H,QAAA,gBACX5J,OAAA,CAAC8C,WAAW;UAAA8G,QAAA,gBACV5J,OAAA,CAACiC,YAAY;YAAA2H,QAAA,gBACX5J,OAAA,CAACoC,WAAW;cAAAwH,QAAA,eACV5J,OAAA,CAACR,MAAM;gBAACqK,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,iBAEhB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACfjK,OAAA,CAACgD,YAAY;YACXkF,KAAK,EAAE9B,WAAY;YACnBuE,QAAQ,EAAE5C,gBAAiB;YAC3B6C,QAAQ,EAAEvD,aAAc;YAAAuC,QAAA,EAEvBiB,MAAM,CAACC,IAAI,CAAChL,gBAAgB,CAAC,CAACiL,GAAG,CAACC,OAAO,iBACxChL,OAAA;cAAsBkI,KAAK,EAAE8C,OAAQ;cAAApB,QAAA,EAClC9J,gBAAgB,CAACkL,OAAO,CAAC,CAACzC;YAAI,GADpByC,OAAO;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEZ,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,eACfjK,OAAA,CAACmD,WAAW;YAAAyG,QAAA,EACT,CAAC9C,QAAQ,gBACR9G,OAAA;cACEiL,GAAG,EAAEnL,gBAAgB,CAACsG,WAAW,CAAC,CAAC8E,GAAI;cACvCC,GAAG,EAAErL,gBAAgB,CAACsG,WAAW,CAAC,CAACmC,IAAK;cACxC6C,OAAO,EAAEA,CAAA,KAAMrE,WAAW,CAAC,IAAI,CAAE;cACjCoD,KAAK,EAAE;gBAAEG,OAAO,EAAExD,QAAQ,GAAG,MAAM,GAAG;cAAQ;YAAE;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,gBAEFjK,OAAA;cAAKmK,KAAK,EAAE;gBAACG,OAAO,EAAE,MAAM;gBAAEe,QAAQ,EAAE,MAAM;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,MAAM,EAAE,MAAM;gBAAEhB,UAAU,EAAE,QAAQ;gBAAEG,cAAc,EAAE;cAAQ,CAAE;cAAAd,QAAA,EAAC;YAEhI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,eACdjK,OAAA,CAACqD,QAAQ;YAAAuG,QAAA,EAAE9J,gBAAgB,CAACsG,WAAW,CAAC,CAACmC;UAAI;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACzDjK,OAAA,CAACwD,eAAe;YAAAoG,QAAA,EACb9J,gBAAgB,CAACsG,WAAW,CAAC,CAACoF;UAAW;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAEdjK,OAAA,CAAC+B,aAAa;UAAA6H,QAAA,gBACZ5J,OAAA,CAACiC,YAAY;YAAA2H,QAAA,gBACX5J,OAAA,CAACoC,WAAW;cAAAwH,QAAA,eACV5J,OAAA,CAACd,MAAM;gBAAC2K,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,sBAEhB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eAEfjK,OAAA,CAAC8F,gBAAgB;YAACC,SAAS,EAAEoB,WAAY;YAAAyC,QAAA,GACtCzC,WAAW,gBAAGnH,OAAA,CAACP,IAAI;cAACoK,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGjK,OAAA,CAACN,OAAO;cAACmK,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACxD9C,WAAW,GAAG,cAAc,GAAG,iBAAiB;UAAA;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,EAElB7C,UAAU,iBACTpH,OAAA,CAACmF,iBAAiB;YAACC,OAAO,EAAEmC,WAAY;YAAClC,OAAO,EAAE+B,UAAU,CAAC/B,OAAQ;YAAAuE,QAAA,gBACnE5J,OAAA,CAACuF,cAAc;cAACH,OAAO,EAAEmC,WAAY;cAAClC,OAAO,EAAE+B,UAAU,CAAC/B,OAAQ;cAAAuE,QAAA,GAAC,YACvD,EAACxC,UAAU,CAAC+B,IAAI,EACzB/B,UAAU,CAAC/B,OAAO,IAAI,aAAa;YAAA;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACjBjK,OAAA,CAACyF,aAAa;cAAAmE,QAAA,eACZ5J,OAAA,CAAC2F,cAAc;gBAACC,UAAU,EAAEwB,UAAU,CAACxB;cAAW;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eAChBjK,OAAA;cAAKmK,KAAK,EAAE;gBAAEkB,QAAQ,EAAE,UAAU;gBAAEI,SAAS,EAAE,KAAK;gBAAEC,KAAK,EAAE;cAAwB,CAAE;cAAA9B,QAAA,GAAC,cAC1E,EAACF,IAAI,CAACC,KAAK,CAACvC,UAAU,CAACxB,UAAU,GAAG,GAAG,CAAC,EAAC,GACrD,EAAC2B,WAAW,IAAIC,UAAU,iBACxBxH,OAAA;gBAAMmK,KAAK,EAAE;kBAAEuB,KAAK,EAAE,oBAAoB;kBAAEC,UAAU,EAAE;gBAAM,CAAE;gBAAA/B,QAAA,EAAC;cAEjE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP,EACA,CAAC5C,aAAa,iBACbrH,OAAA;gBAAKmK,KAAK,EAAE;kBAAEuB,KAAK,EAAE,oBAAoB;kBAAED,SAAS,EAAE;gBAAM,CAAE;gBAAA7B,QAAA,GAAC,gDAC1B,EAAC9J,gBAAgB,CAACsG,WAAW,CAAC,CAACmC,IAAI,EAAC,gCACvE,eAAAvI,OAAA;kBAAA8J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,mEAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACW,CACpB,EAEA,CAAC7C,UAAU,iBACVpH,OAAA,CAACmF,iBAAiB;YAAAyE,QAAA,gBAChB5J,OAAA,CAACuF,cAAc;cAAAqE,QAAA,GAAC,iCACM,EAAC9J,gBAAgB,CAACsG,WAAW,CAAC,CAACmC,IAAI,EAAC,IAC1D;YAAA;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAgB,CAAC,eACjBjK,OAAA;cAAKmK,KAAK,EAAE;gBAAEkB,QAAQ,EAAE,UAAU;gBAAEK,KAAK,EAAE;cAAwB,CAAE;cAAA9B,QAAA,EAAC;YAEtE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACW,CACpB,eACDjK,OAAA,CAACsC,eAAe;YAAAsH,QAAA,gBACd5J,OAAA,CAACwC,YAAY;cACXoJ,GAAG,EAAE5E,SAAU;cACf6E,KAAK,EAAE,KAAM;cACbC,gBAAgB,EAAC,YAAY;cAC7BC,gBAAgB,EAAE;gBAChBT,KAAK,EAAE,GAAG;gBACVC,MAAM,EAAE,GAAG;gBACXS,UAAU,EAAE;cACd;YAAE;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFjK,OAAA,CAAC0C,gBAAgB;cAACE,WAAW,EAAEyE,aAAc;cAAAuC,QAAA,EAC1CvC,aAAa,gBACZrH,OAAA,CAAAE,SAAA;gBAAA0J,QAAA,gBACE5J,OAAA;kBAAKmK,KAAK,EAAE;oBACVmB,KAAK,EAAE,KAAK;oBACZC,MAAM,EAAE,KAAK;oBACbU,YAAY,EAAE,KAAK;oBACnBC,eAAe,EAAE,OAAO;oBACxBC,WAAW,EAAE;kBACf;gBAAE;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,aAEP;cAAA,eAAE,CAAC,gBAEHjK,OAAA,CAAAE,SAAA;gBAAA0J,QAAA,gBACE5J,OAAA,CAACT,GAAG;kBAACsK,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,SAEnB;cAAA,eAAE;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EAEd,CAAC3D,MAAM,IAAIgB,eAAe,kBACzBtH,OAAA,CAACkE,aAAa;QAACC,IAAI,EAAE,CAACmC,MAAM,IAAIgB,eAAe,EAAEgC,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG,CAAChD,MAAM,IAAIgB,eAAe,EAAEgC,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,MAAO;QAAAM,QAAA,EACjJtC,eAAe,IAAIhB;MAAM;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAChB,EAEArD,cAAc,CAACwF,MAAM,GAAG,CAAC,iBACxBpM,OAAA,CAACqE,iBAAiB;QAAAuF,QAAA,gBAChB5J,OAAA,CAACuE,eAAe;UAAAqF,QAAA,EAAC;QAAwB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAiB,CAAC,eAC3DjK,OAAA,CAACyE,cAAc;UAAAmF,QAAA,EACZhD,cAAc,CAACmE,GAAG,CAAEnC,KAAK,iBACxB5I,OAAA,CAAC2E,aAAa;YAAAiF,QAAA,gBACZ5J,OAAA,CAAC6E,cAAc;cAAA+E,QAAA,EAAEhB,KAAK,CAACO;YAAI;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiB,CAAC,eAC7CjK,OAAA,CAAC+E,aAAa;cAAA6E,QAAA,EACX,IAAIyC,IAAI,CAACzD,KAAK,CAACQ,SAAS,CAAC,CAACkD,cAAc,CAAC;YAAC;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eAChBjK,OAAA,CAACiF,cAAc;cAACiF,OAAO,EAAEA,CAAA,KAAMvB,iBAAiB,CAACC,KAAK,CAAE;cAAAgB,QAAA,gBACtD5J,OAAA,CAACV,QAAQ;gBAACuK,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAExB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAgB,CAAC;UAAA,GARCrB,KAAK,CAAC2D,EAAE;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASb,CAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACpB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAExB,CAAC;AAAC9D,EAAA,CApXIF,YAAY;EAAA,QAyBZrG,gBAAgB;AAAA;AAAA4M,IAAA,GAzBhBvG,YAAY;AAsXlB,eAAeA,YAAY;AAAC,IAAA5F,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAwG,IAAA;AAAAC,YAAA,CAAApM,EAAA;AAAAoM,YAAA,CAAAjM,GAAA;AAAAiM,YAAA,CAAA/L,GAAA;AAAA+L,YAAA,CAAA7L,GAAA;AAAA6L,YAAA,CAAA3L,GAAA;AAAA2L,YAAA,CAAAxL,GAAA;AAAAwL,YAAA,CAAArL,GAAA;AAAAqL,YAAA,CAAAlL,GAAA;AAAAkL,YAAA,CAAAhL,GAAA;AAAAgL,YAAA,CAAA7K,GAAA;AAAA6K,YAAA,CAAA3K,GAAA;AAAA2K,YAAA,CAAAzK,IAAA;AAAAyK,YAAA,CAAAtK,IAAA;AAAAsK,YAAA,CAAApK,IAAA;AAAAoK,YAAA,CAAAlK,IAAA;AAAAkK,YAAA,CAAAhK,IAAA;AAAAgK,YAAA,CAAA5J,IAAA;AAAA4J,YAAA,CAAA1J,IAAA;AAAA0J,YAAA,CAAAvJ,IAAA;AAAAuJ,YAAA,CAAArJ,IAAA;AAAAqJ,YAAA,CAAAlJ,IAAA;AAAAkJ,YAAA,CAAAhJ,IAAA;AAAAgJ,YAAA,CAAA9I,IAAA;AAAA8I,YAAA,CAAA5I,IAAA;AAAA4I,YAAA,CAAAxI,IAAA;AAAAwI,YAAA,CAAArI,IAAA;AAAAqI,YAAA,CAAAnI,IAAA;AAAAmI,YAAA,CAAAjI,IAAA;AAAAiI,YAAA,CAAA/H,IAAA;AAAA+H,YAAA,CAAA7H,IAAA;AAAA6H,YAAA,CAAA3H,IAAA;AAAA2H,YAAA,CAAAzH,IAAA;AAAAyH,YAAA,CAAAvH,IAAA;AAAAuH,YAAA,CAAAnH,IAAA;AAAAmH,YAAA,CAAAjH,IAAA;AAAAiH,YAAA,CAAA/G,IAAA;AAAA+G,YAAA,CAAA5G,IAAA;AAAA4G,YAAA,CAAAzG,IAAA;AAAAyG,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}