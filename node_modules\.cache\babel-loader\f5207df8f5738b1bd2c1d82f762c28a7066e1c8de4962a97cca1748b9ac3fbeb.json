{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m16 22-1-4\",\n  key: \"1ow2iv\"\n}], [\"path\", {\n  d: \"M19 13.99a1 1 0 0 0 1-1V12a2 2 0 0 0-2-2h-3a1 1 0 0 1-1-1V4a2 2 0 0 0-4 0v5a1 1 0 0 1-1 1H6a2 2 0 0 0-2 2v.99a1 1 0 0 0 1 1\",\n  key: \"iw8jdu\"\n}], [\"path\", {\n  d: \"M5 14h14l1.973 6.767A1 1 0 0 1 20 22H4a1 1 0 0 1-.973-1.233z\",\n  key: \"1soew8\"\n}], [\"path\", {\n  d: \"m8 22 1-4\",\n  key: \"s3unb\"\n}]];\nconst BrushCleaning = createLucideIcon(\"brush-cleaning\", __iconNode);\nexport { __iconNode, BrushCleaning as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "BrushCleaning", "createLucideIcon"], "sources": ["D:\\ASL\\training-frontend\\node_modules\\lucide-react\\src\\icons\\brush-cleaning.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm16 22-1-4', key: '1ow2iv' }],\n  [\n    'path',\n    {\n      d: 'M19 13.99a1 1 0 0 0 1-1V12a2 2 0 0 0-2-2h-3a1 1 0 0 1-1-1V4a2 2 0 0 0-4 0v5a1 1 0 0 1-1 1H6a2 2 0 0 0-2 2v.99a1 1 0 0 0 1 1',\n      key: 'iw8jdu',\n    },\n  ],\n  ['path', { d: 'M5 14h14l1.973 6.767A1 1 0 0 1 20 22H4a1 1 0 0 1-.973-1.233z', key: '1soew8' }],\n  ['path', { d: 'm8 22 1-4', key: 's3unb' }],\n];\n\n/**\n * @component @name BrushCleaning\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTYgMjItMS00IiAvPgogIDxwYXRoIGQ9Ik0xOSAxMy45OWExIDEgMCAwIDAgMS0xVjEyYTIgMiAwIDAgMC0yLTJoLTNhMSAxIDAgMCAxLTEtMVY0YTIgMiAwIDAgMC00IDB2NWExIDEgMCAwIDEtMSAxSDZhMiAyIDAgMCAwLTIgMnYuOTlhMSAxIDAgMCAwIDEgMSIgLz4KICA8cGF0aCBkPSJNNSAxNGgxNGwxLjk3MyA2Ljc2N0ExIDEgMCAwIDEgMjAgMjJINGExIDEgMCAwIDEtLjk3My0xLjIzM3oiIC8+CiAgPHBhdGggZD0ibTggMjIgMS00IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/brush-cleaning\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BrushCleaning = createLucideIcon('brush-cleaning', __iconNode);\n\nexport default BrushCleaning;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,8DAAgE;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7F,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAS,GAC3C;AAaM,MAAAC,aAAA,GAAgBC,gBAAiB,mBAAkBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}