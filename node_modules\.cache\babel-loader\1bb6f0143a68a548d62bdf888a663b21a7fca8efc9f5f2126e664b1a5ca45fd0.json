{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m20.9 18.55-8-15.98a1 1 0 0 0-1.8 0l-8 15.98\",\n  key: \"53pte7\"\n}], [\"ellipse\", {\n  cx: \"12\",\n  cy: \"19\",\n  rx: \"9\",\n  ry: \"3\",\n  key: \"1ji25f\"\n}]];\nconst Cone = createLucideIcon(\"cone\", __iconNode);\nexport { __iconNode, Cone as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "rx", "ry", "Cone", "createLucideIcon"], "sources": ["D:\\ASL\\ASL-Training\\node_modules\\lucide-react\\src\\icons\\cone.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm20.9 18.55-8-15.98a1 1 0 0 0-1.8 0l-8 15.98', key: '53pte7' }],\n  ['ellipse', { cx: '12', cy: '19', rx: '9', ry: '3', key: '1ji25f' }],\n];\n\n/**\n * @component @name Cone\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjAuOSAxOC41NS04LTE1Ljk4YTEgMSAwIDAgMC0xLjggMGwtOCAxNS45OCIgLz4KICA8ZWxsaXBzZSBjeD0iMTIiIGN5PSIxOSIgcng9IjkiIHJ5PSIzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/cone\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Cone = createLucideIcon('cone', __iconNode);\n\nexport default Cone;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,8CAAgD;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,WAAW;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAU,GACrE;AAaM,MAAAK,IAAA,GAAOC,gBAAiB,SAAQR,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}