{"ast": null, "code": "import _taggedTemplateLiteral from\"D:/ASL/training-frontend/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6,_templateObject7,_templateObject8,_templateObject9,_templateObject0,_templateObject1,_templateObject10,_templateObject11,_templateObject12,_templateObject13,_templateObject14,_templateObject15,_templateObject16,_templateObject17,_templateObject18,_templateObject19,_templateObject20,_templateObject21,_templateObject22,_templateObject23,_templateObject24,_templateObject25,_templateObject26,_templateObject27;import React from'react';import styled from'styled-components';import{Brain,Camera,Users,Shield,Smartphone,Target,Play,BookOpen,Zap,Cpu,Eye,Award,TrendingUp,Globe}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const HomeContainer=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  min-height: 100vh;\\n  background: var(--bg-primary);\\n  position: relative;\\n  overflow: hidden;\\n\\n  &::before {\\n    content: '';\\n    position: fixed;\\n    top: 0;\\n    left: 0;\\n    right: 0;\\n    bottom: 0;\\n    background:\\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%),\\n      radial-gradient(circle at 40% 60%, rgba(16, 185, 129, 0.02) 0%, transparent 50%);\\n    pointer-events: none;\\n    z-index: 0;\\n  }\\n\"])));const Navigation=styled.nav(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 50;\\n  background: var(--bg-glass);\\n  backdrop-filter: blur(20px);\\n  border-bottom: 1px solid var(--border-neural);\\n  padding: var(--space-4) 0;\\n  transition: var(--transition-normal);\\n\\n  @media (max-width: 768px) {\\n    padding: var(--space-3) 0;\\n  }\\n\"])));const NavContainer=styled.div(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  padding: 0 var(--space-6);\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n\\n  @media (max-width: 768px) {\\n    padding: 0 var(--space-4);\\n  }\\n\"])));const Logo=styled.div(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  font-family: var(--font-primary);\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  background: var(--text-gradient);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n  display: flex;\\n  align-items: center;\\n  gap: var(--space-3);\\n\\n  @media (max-width: 768px) {\\n    font-size: 1.25rem;\\n    gap: var(--space-2);\\n  }\\n\"])));const LogoIcon=styled.div(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  width: 40px;\\n  height: 40px;\\n  background: var(--bg-neural);\\n  border-radius: var(--radius-lg);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  box-shadow: var(--shadow-neural);\\n\\n  @media (max-width: 768px) {\\n    width: 36px;\\n    height: 36px;\\n  }\\n\"])));const NavLinks=styled.div(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  display: flex;\\n  align-items: center;\\n  gap: var(--space-8);\\n\\n  @media (max-width: 768px) {\\n    gap: var(--space-4);\\n  }\\n\"])));const NavLink=styled.a(_templateObject7||(_templateObject7=_taggedTemplateLiteral([\"\\n  color: var(--text-secondary);\\n  text-decoration: none;\\n  font-weight: 500;\\n  font-size: 0.9rem;\\n  transition: var(--transition-fast);\\n  position: relative;\\n\\n  &:hover {\\n    color: var(--text-accent);\\n  }\\n\\n  &::after {\\n    content: '';\\n    position: absolute;\\n    bottom: -4px;\\n    left: 0;\\n    width: 0;\\n    height: 2px;\\n    background: var(--bg-neural);\\n    transition: var(--transition-normal);\\n  }\\n\\n  &:hover::after {\\n    width: 100%;\\n  }\\n\\n  @media (max-width: 768px) {\\n    font-size: 0.85rem;\\n  }\\n\"])));const HeroSection=styled.section(_templateObject8||(_templateObject8=_taggedTemplateLiteral([\"\\n  min-height: 100vh;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: var(--space-24) var(--space-6) var(--space-20);\\n  text-align: center;\\n  position: relative;\\n  z-index: 1;\\n\\n  @media (max-width: 768px) {\\n    padding: var(--space-20) var(--space-4) var(--space-16);\\n    min-height: calc(100vh - 80px);\\n  }\\n\"])));const HeroContent=styled.div(_templateObject9||(_templateObject9=_taggedTemplateLiteral([\"\\n  max-width: 900px;\\n  width: 100%;\\n  position: relative;\\n  z-index: 2;\\n\"])));const HeroBadge=styled.div(_templateObject0||(_templateObject0=_taggedTemplateLiteral([\"\\n  display: inline-flex;\\n  align-items: center;\\n  gap: var(--space-2);\\n  background: var(--bg-glass);\\n  border: 1px solid var(--border-neural);\\n  border-radius: var(--radius-full);\\n  padding: var(--space-2) var(--space-4);\\n  margin-bottom: var(--space-6);\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: var(--text-accent);\\n  backdrop-filter: blur(10px);\\n  box-shadow: var(--shadow-glow);\\n\\n  @media (max-width: 768px) {\\n    font-size: 0.8rem;\\n    padding: var(--space-2) var(--space-3);\\n  }\\n\"])));const HeroTitle=styled.h1(_templateObject1||(_templateObject1=_taggedTemplateLiteral([\"\\n  font-family: var(--font-primary);\\n  font-size: 3.5rem;\\n  font-weight: 800;\\n  background: var(--text-gradient);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n  margin-bottom: var(--space-6);\\n  line-height: 1.1;\\n  letter-spacing: -0.02em;\\n\\n  @media (max-width: 768px) {\\n    font-size: 2.5rem;\\n    margin-bottom: var(--space-4);\\n  }\\n\\n  @media (max-width: 480px) {\\n    font-size: 2rem;\\n  }\\n\"])));const HeroSubtitle=styled.p(_templateObject10||(_templateObject10=_taggedTemplateLiteral([\"\\n  font-size: 1.25rem;\\n  font-weight: 400;\\n  color: var(--text-secondary);\\n  margin-bottom: var(--space-8);\\n  line-height: 1.6;\\n  max-width: 700px;\\n  margin-left: auto;\\n  margin-right: auto;\\n\\n  @media (max-width: 768px) {\\n    font-size: 1.125rem;\\n    margin-bottom: var(--space-6);\\n  }\\n\\n  @media (max-width: 480px) {\\n    font-size: 1rem;\\n  }\\n\"])));const HeroDescription=styled.p(_templateObject11||(_templateObject11=_taggedTemplateLiteral([\"\\n  font-size: 1rem;\\n  line-height: 1.7;\\n  color: var(--text-tertiary);\\n  margin-bottom: var(--space-10);\\n  max-width: 600px;\\n  margin-left: auto;\\n  margin-right: auto;\\n\\n  @media (max-width: 768px) {\\n    font-size: 0.95rem;\\n    margin-bottom: var(--space-8);\\n    line-height: 1.6;\\n  }\\n\"])));const HeroActions=styled.div(_templateObject12||(_templateObject12=_taggedTemplateLiteral([\"\\n  display: flex;\\n  gap: var(--space-5);\\n  justify-content: center;\\n  align-items: center;\\n  flex-wrap: wrap;\\n  margin-bottom: var(--space-16);\\n\\n  @media (max-width: 768px) {\\n    gap: var(--space-4);\\n    margin-bottom: var(--space-12);\\n    flex-direction: column;\\n  }\\n\"])));const PrimaryButton=styled.button(_templateObject13||(_templateObject13=_taggedTemplateLiteral([\"\\n  background: var(--bg-neural);\\n  color: white;\\n  border: none;\\n  padding: var(--space-4) var(--space-10);\\n  border-radius: var(--radius-xl);\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: var(--transition-normal);\\n  display: flex;\\n  align-items: center;\\n  gap: var(--space-3);\\n  box-shadow: var(--shadow-neural);\\n  position: relative;\\n  overflow: hidden;\\n\\n  &::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n    transition: var(--transition-slow);\\n  }\\n\\n  &:hover {\\n    transform: translateY(-3px);\\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\\n\\n    &::before {\\n      left: 100%;\\n    }\\n  }\\n\\n  &:active {\\n    transform: translateY(-1px);\\n  }\\n\\n  @media (max-width: 768px) {\\n    padding: var(--space-4) var(--space-8);\\n    font-size: 1rem;\\n    width: 100%;\\n    max-width: 300px;\\n  }\\n\"])));const SecondaryButton=styled.button(_templateObject14||(_templateObject14=_taggedTemplateLiteral([\"\\n  background: var(--bg-glass);\\n  color: var(--text-primary);\\n  border: 2px solid var(--border-neural);\\n  padding: var(--space-4) var(--space-8);\\n  border-radius: var(--radius-xl);\\n  font-size: 1.125rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: var(--transition-normal);\\n  display: flex;\\n  align-items: center;\\n  gap: var(--space-3);\\n  backdrop-filter: blur(10px);\\n\\n  &:hover {\\n    border-color: var(--primary-600);\\n    color: var(--primary-600);\\n    background: var(--primary-50);\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-lg);\\n  }\\n\\n  @media (max-width: 768px) {\\n    padding: var(--space-4) var(--space-6);\\n    font-size: 1rem;\\n    width: 100%;\\n    max-width: 300px;\\n  }\\n\"])));const FeaturesSection=styled.section(_templateObject15||(_templateObject15=_taggedTemplateLiteral([\"\\n  padding: var(--space-24) var(--space-6);\\n  background: var(--bg-tertiary);\\n  position: relative;\\n\\n  &::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: 0;\\n    right: 0;\\n    bottom: 0;\\n    background:\\n      radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),\\n      radial-gradient(circle at 70% 70%, rgba(147, 51, 234, 0.05) 0%, transparent 50%);\\n    pointer-events: none;\\n  }\\n\\n  @media (max-width: 768px) {\\n    padding: var(--space-20) var(--space-4);\\n  }\\n\"])));const FeaturesContainer=styled.div(_templateObject16||(_templateObject16=_taggedTemplateLiteral([\"\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  text-align: center;\\n  position: relative;\\n  z-index: 1;\\n\"])));const SectionTitle=styled.h2(_templateObject17||(_templateObject17=_taggedTemplateLiteral([\"\\n  font-family: var(--font-primary);\\n  font-size: 2.75rem;\\n  font-weight: 700;\\n  background: var(--text-gradient);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n  margin-bottom: var(--space-6);\\n  letter-spacing: -0.02em;\\n\\n  @media (max-width: 768px) {\\n    font-size: 2rem;\\n  }\\n\"])));const SectionSubtitle=styled.p(_templateObject18||(_templateObject18=_taggedTemplateLiteral([\"\\n  font-size: 1.125rem;\\n  color: var(--text-secondary);\\n  margin-bottom: var(--space-16);\\n  max-width: 700px;\\n  margin-left: auto;\\n  margin-right: auto;\\n  line-height: 1.6;\\n\\n  @media (max-width: 768px) {\\n    font-size: 1rem;\\n    margin-bottom: var(--space-12);\\n  }\\n\"])));const FeatureGrid=styled.div(_templateObject19||(_templateObject19=_taggedTemplateLiteral([\"\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));\\n  gap: var(--space-8);\\n  margin-bottom: var(--space-20);\\n\\n  @media (max-width: 768px) {\\n    grid-template-columns: 1fr;\\n    gap: var(--space-6);\\n  }\\n\"])));const FeatureCard=styled.div(_templateObject20||(_templateObject20=_taggedTemplateLiteral([\"\\n  background: var(--bg-glass);\\n  border-radius: var(--radius-2xl);\\n  padding: var(--space-10);\\n  border: 1px solid var(--border-neural);\\n  transition: var(--transition-normal);\\n  text-align: left;\\n  position: relative;\\n  overflow: hidden;\\n  backdrop-filter: blur(20px);\\n  box-shadow: var(--shadow-lg);\\n\\n  &::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: 0;\\n    right: 0;\\n    height: 4px;\\n    background: var(--bg-neural);\\n    transform: scaleX(0);\\n    transition: var(--transition-normal);\\n  }\\n\\n  &:hover {\\n    transform: translateY(-6px);\\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\\n    border-color: var(--primary-300);\\n\\n    &::before {\\n      transform: scaleX(1);\\n    }\\n  }\\n\\n  @media (max-width: 768px) {\\n    padding: var(--space-8);\\n    text-align: center;\\n  }\\n\"])));const FeatureIconWrapper=styled.div(_templateObject21||(_templateObject21=_taggedTemplateLiteral([\"\\n  width: 64px;\\n  height: 64px;\\n  background: var(--bg-neural);\\n  border-radius: var(--radius-xl);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: var(--space-6);\\n  box-shadow: var(--shadow-neural);\\n\\n  @media (max-width: 768px) {\\n    margin: 0 auto var(--space-6);\\n  }\\n\"])));const FeatureTitle=styled.h3(_templateObject22||(_templateObject22=_taggedTemplateLiteral([\"\\n  font-size: 1.5rem;\\n  margin-bottom: var(--space-4);\\n  color: var(--text-primary);\\n  font-weight: 700;\\n  font-family: var(--font-primary);\\n\\n  @media (max-width: 768px) {\\n    text-align: center;\\n  }\\n\"])));const FeatureDescription=styled.p(_templateObject23||(_templateObject23=_taggedTemplateLiteral([\"\\n  font-size: 1rem;\\n  color: var(--text-secondary);\\n  line-height: 1.7;\\n  font-weight: 400;\\n\\n  @media (max-width: 768px) {\\n    text-align: center;\\n  }\\n\"])));const HeroStats=styled.div(_templateObject24||(_templateObject24=_taggedTemplateLiteral([\"\\n  display: flex;\\n  justify-content: center;\\n  gap: var(--space-8);\\n  margin-top: var(--space-8);\\n\\n  @media (max-width: 768px) {\\n    gap: var(--space-6);\\n    flex-wrap: wrap;\\n  }\\n\"])));const StatItem=styled.div(_templateObject25||(_templateObject25=_taggedTemplateLiteral([\"\\n  text-align: center;\\n\"])));const StatNumber=styled.div(_templateObject26||(_templateObject26=_taggedTemplateLiteral([\"\\n  font-size: 2rem;\\n  font-weight: 700;\\n  color: var(--primary-600);\\n  font-family: var(--font-primary);\\n\\n  @media (max-width: 768px) {\\n    font-size: 1.5rem;\\n  }\\n\"])));const StatLabel=styled.div(_templateObject27||(_templateObject27=_taggedTemplateLiteral([\"\\n  font-size: 0.875rem;\\n  color: var(--text-tertiary);\\n  font-weight: 500;\\n  margin-top: var(--space-1);\\n\"])));const HomePage=_ref=>{let{onStartTraining,onNavigateToAbout,onNavigateToContact}=_ref;return/*#__PURE__*/_jsxs(HomeContainer,{children:[/*#__PURE__*/_jsx(Navigation,{children:/*#__PURE__*/_jsxs(NavContainer,{children:[/*#__PURE__*/_jsxs(Logo,{children:[/*#__PURE__*/_jsx(LogoIcon,{children:/*#__PURE__*/_jsx(Brain,{size:24})}),\"ASL Neural\"]}),/*#__PURE__*/_jsxs(NavLinks,{children:[/*#__PURE__*/_jsx(NavLink,{href:\"#features\",children:\"Features\"}),/*#__PURE__*/_jsx(NavLink,{onClick:onNavigateToAbout,style:{cursor:'pointer'},children:\"About\"}),/*#__PURE__*/_jsx(NavLink,{onClick:onNavigateToContact,style:{cursor:'pointer'},children:\"Contact\"})]})]})}),/*#__PURE__*/_jsx(HeroSection,{children:/*#__PURE__*/_jsxs(HeroContent,{children:[/*#__PURE__*/_jsxs(HeroBadge,{children:[/*#__PURE__*/_jsx(Zap,{size:16}),\"AI-Powered Learning Platform\"]}),/*#__PURE__*/_jsx(HeroTitle,{children:\"Master Sign Language with Neural Intelligence\"}),/*#__PURE__*/_jsx(HeroSubtitle,{children:\"Revolutionary AI platform that transforms sign language learning through real-time computer vision and adaptive neural networks\"}),/*#__PURE__*/_jsx(HeroDescription,{children:\"Experience the future of accessibility education. Our advanced machine learning algorithms provide instant feedback while contributing to breakthrough AI research for the deaf and hard-of-hearing community.\"}),/*#__PURE__*/_jsxs(HeroActions,{children:[/*#__PURE__*/_jsxs(PrimaryButton,{onClick:onStartTraining,children:[/*#__PURE__*/_jsx(Play,{size:20}),\"Start Neural Training\"]}),/*#__PURE__*/_jsxs(SecondaryButton,{onClick:onNavigateToAbout,children:[/*#__PURE__*/_jsx(BookOpen,{size:20}),\"Explore Technology\"]})]}),/*#__PURE__*/_jsxs(HeroStats,{children:[/*#__PURE__*/_jsxs(StatItem,{children:[/*#__PURE__*/_jsx(StatNumber,{children:\"25K+\"}),/*#__PURE__*/_jsx(StatLabel,{children:\"Neural Sessions\"})]}),/*#__PURE__*/_jsxs(StatItem,{children:[/*#__PURE__*/_jsx(StatNumber,{children:\"150+\"}),/*#__PURE__*/_jsx(StatLabel,{children:\"Sign Patterns\"})]}),/*#__PURE__*/_jsxs(StatItem,{children:[/*#__PURE__*/_jsx(StatNumber,{children:\"98.7%\"}),/*#__PURE__*/_jsx(StatLabel,{children:\"AI Accuracy\"})]})]})]})}),/*#__PURE__*/_jsx(FeaturesSection,{id:\"features\",children:/*#__PURE__*/_jsxs(FeaturesContainer,{children:[/*#__PURE__*/_jsx(SectionTitle,{children:\"Neural Network Capabilities\"}),/*#__PURE__*/_jsx(SectionSubtitle,{children:\"Discover how our advanced AI technology revolutionizes sign language learning through cutting-edge computer vision and machine learning\"}),/*#__PURE__*/_jsxs(FeatureGrid,{children:[/*#__PURE__*/_jsxs(FeatureCard,{children:[/*#__PURE__*/_jsx(FeatureIconWrapper,{children:/*#__PURE__*/_jsx(Camera,{size:28,color:\"white\"})}),/*#__PURE__*/_jsx(FeatureTitle,{children:\"Real-time Computer Vision\"}),/*#__PURE__*/_jsx(FeatureDescription,{children:\"Advanced neural networks analyze your hand movements in real-time, providing instant feedback with 98.7% accuracy using state-of-the-art pose estimation algorithms\"})]}),/*#__PURE__*/_jsxs(FeatureCard,{children:[/*#__PURE__*/_jsx(FeatureIconWrapper,{children:/*#__PURE__*/_jsx(Cpu,{size:28,color:\"white\"})}),/*#__PURE__*/_jsx(FeatureTitle,{children:\"Adaptive AI Learning\"}),/*#__PURE__*/_jsx(FeatureDescription,{children:\"Our deep learning models continuously adapt to your learning style, creating personalized training paths that optimize skill acquisition and retention rates\"})]}),/*#__PURE__*/_jsxs(FeatureCard,{children:[/*#__PURE__*/_jsx(FeatureIconWrapper,{children:/*#__PURE__*/_jsx(Globe,{size:28,color:\"white\"})}),/*#__PURE__*/_jsx(FeatureTitle,{children:\"Global Impact Network\"}),/*#__PURE__*/_jsx(FeatureDescription,{children:\"Join a worldwide community contributing to breakthrough AI research that advances accessibility technology for millions in the deaf and hard-of-hearing community\"})]}),/*#__PURE__*/_jsxs(FeatureCard,{children:[/*#__PURE__*/_jsx(FeatureIconWrapper,{children:/*#__PURE__*/_jsx(Smartphone,{size:28,color:\"white\"})}),/*#__PURE__*/_jsx(FeatureTitle,{children:\"Cross-Platform Intelligence\"}),/*#__PURE__*/_jsx(FeatureDescription,{children:\"Seamless AI-powered experience across all devices with cloud-synchronized progress and edge computing for lightning-fast response times\"})]}),/*#__PURE__*/_jsxs(FeatureCard,{children:[/*#__PURE__*/_jsx(FeatureIconWrapper,{children:/*#__PURE__*/_jsx(Target,{size:28,color:\"white\"})}),/*#__PURE__*/_jsx(FeatureTitle,{children:\"Precision Learning Analytics\"}),/*#__PURE__*/_jsx(FeatureDescription,{children:\"Advanced analytics track micro-movements and learning patterns, providing data-driven insights to accelerate your mastery of sign language\"})]}),/*#__PURE__*/_jsxs(FeatureCard,{children:[/*#__PURE__*/_jsx(FeatureIconWrapper,{children:/*#__PURE__*/_jsx(Shield,{size:28,color:\"white\"})}),/*#__PURE__*/_jsx(FeatureTitle,{children:\"Privacy-First Architecture\"}),/*#__PURE__*/_jsx(FeatureDescription,{children:\"Enterprise-grade security with local processing ensures your data remains private while contributing anonymized insights to advance AI research\"})]})]})]})})]});};export default HomePage;", "map": {"version": 3, "names": ["React", "styled", "Brain", "Camera", "Users", "Shield", "Smartphone", "Target", "Play", "BookOpen", "Zap", "Cpu", "Eye", "Award", "TrendingUp", "Globe", "jsx", "_jsx", "jsxs", "_jsxs", "HomeContainer", "div", "_templateObject", "_taggedTemplateLiteral", "Navigation", "nav", "_templateObject2", "NavContainer", "_templateObject3", "Logo", "_templateObject4", "LogoIcon", "_templateObject5", "NavLinks", "_templateObject6", "NavLink", "a", "_templateObject7", "HeroSection", "section", "_templateObject8", "Hero<PERSON><PERSON><PERSON>", "_templateObject9", "HeroBadge", "_templateObject0", "<PERSON><PERSON><PERSON><PERSON>", "h1", "_templateObject1", "HeroSubtitle", "p", "_templateObject10", "HeroDescription", "_templateObject11", "HeroActions", "_templateObject12", "PrimaryButton", "button", "_templateObject13", "SecondaryButton", "_templateObject14", "FeaturesSection", "_templateObject15", "FeaturesContainer", "_templateObject16", "SectionTitle", "h2", "_templateObject17", "SectionSubtitle", "_templateObject18", "FeatureGrid", "_templateObject19", "FeatureCard", "_templateObject20", "FeatureIconWrapper", "_templateObject21", "FeatureTitle", "h3", "_templateObject22", "FeatureDescription", "_templateObject23", "HeroStats", "_templateObject24", "StatItem", "_templateObject25", "StatNumber", "_templateObject26", "StatLabel", "_templateObject27", "HomePage", "_ref", "onStartTraining", "onNavigateToAbout", "onNavigateToContact", "children", "size", "href", "onClick", "style", "cursor", "id", "color"], "sources": ["D:/ASL/training-frontend/src/components/HomePage.js"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport {\n  Brain,\n  Camera,\n  Users,\n  Shield,\n  Smartphone,\n  Target,\n  Play,\n  BookOpen,\n  Zap,\n  Cpu,\n  Eye,\n  Award,\n  TrendingUp,\n  Globe\n} from 'lucide-react';\n\nconst HomeContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 40% 60%, rgba(16, 185, 129, 0.02) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n`;\n\nconst Navigation = styled.nav`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: var(--bg-glass);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-neural);\n  padding: var(--space-4) 0;\n  transition: var(--transition-normal);\n\n  @media (max-width: 768px) {\n    padding: var(--space-3) 0;\n  }\n`;\n\nconst NavContainer = styled.div`\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  @media (max-width: 768px) {\n    padding: 0 var(--space-4);\n  }\n`;\n\nconst Logo = styled.div`\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n    gap: var(--space-2);\n  }\n`;\n\nconst LogoIcon = styled.div`\n  width: 40px;\n  height: 40px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    width: 36px;\n    height: 36px;\n  }\n`;\n\nconst NavLinks = styled.div`\n  display: flex;\n  align-items: center;\n  gap: var(--space-8);\n\n  @media (max-width: 768px) {\n    gap: var(--space-4);\n  }\n`;\n\nconst NavLink = styled.a`\n  color: var(--text-secondary);\n  text-decoration: none;\n  font-weight: 500;\n  font-size: 0.9rem;\n  transition: var(--transition-fast);\n  position: relative;\n\n  &:hover {\n    color: var(--text-accent);\n  }\n\n  &::after {\n    content: '';\n    position: absolute;\n    bottom: -4px;\n    left: 0;\n    width: 0;\n    height: 2px;\n    background: var(--bg-neural);\n    transition: var(--transition-normal);\n  }\n\n  &:hover::after {\n    width: 100%;\n  }\n\n  @media (max-width: 768px) {\n    font-size: 0.85rem;\n  }\n`;\n\nconst HeroSection = styled.section`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: var(--space-24) var(--space-6) var(--space-20);\n  text-align: center;\n  position: relative;\n  z-index: 1;\n\n  @media (max-width: 768px) {\n    padding: var(--space-20) var(--space-4) var(--space-16);\n    min-height: calc(100vh - 80px);\n  }\n`;\n\nconst HeroContent = styled.div`\n  max-width: 900px;\n  width: 100%;\n  position: relative;\n  z-index: 2;\n`;\n\nconst HeroBadge = styled.div`\n  display: inline-flex;\n  align-items: center;\n  gap: var(--space-2);\n  background: var(--bg-glass);\n  border: 1px solid var(--border-neural);\n  border-radius: var(--radius-full);\n  padding: var(--space-2) var(--space-4);\n  margin-bottom: var(--space-6);\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: var(--text-accent);\n  backdrop-filter: blur(10px);\n  box-shadow: var(--shadow-glow);\n\n  @media (max-width: 768px) {\n    font-size: 0.8rem;\n    padding: var(--space-2) var(--space-3);\n  }\n`;\n\nconst HeroTitle = styled.h1`\n  font-family: var(--font-primary);\n  font-size: 3.5rem;\n  font-weight: 800;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin-bottom: var(--space-6);\n  line-height: 1.1;\n  letter-spacing: -0.02em;\n\n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n    margin-bottom: var(--space-4);\n  }\n\n  @media (max-width: 480px) {\n    font-size: 2rem;\n  }\n`;\n\nconst HeroSubtitle = styled.p`\n  font-size: 1.25rem;\n  font-weight: 400;\n  color: var(--text-secondary);\n  margin-bottom: var(--space-8);\n  line-height: 1.6;\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    margin-bottom: var(--space-6);\n  }\n\n  @media (max-width: 480px) {\n    font-size: 1rem;\n  }\n`;\n\nconst HeroDescription = styled.p`\n  font-size: 1rem;\n  line-height: 1.7;\n  color: var(--text-tertiary);\n  margin-bottom: var(--space-10);\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n\n  @media (max-width: 768px) {\n    font-size: 0.95rem;\n    margin-bottom: var(--space-8);\n    line-height: 1.6;\n  }\n`;\n\nconst HeroActions = styled.div`\n  display: flex;\n  gap: var(--space-5);\n  justify-content: center;\n  align-items: center;\n  flex-wrap: wrap;\n  margin-bottom: var(--space-16);\n\n  @media (max-width: 768px) {\n    gap: var(--space-4);\n    margin-bottom: var(--space-12);\n    flex-direction: column;\n  }\n`;\n\nconst PrimaryButton = styled.button`\n  background: var(--bg-neural);\n  color: white;\n  border: none;\n  padding: var(--space-4) var(--space-10);\n  border-radius: var(--radius-xl);\n  font-size: 1.125rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  box-shadow: var(--shadow-neural);\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: -100%;\n    width: 100%;\n    height: 100%;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n    transition: var(--transition-slow);\n  }\n\n  &:hover {\n    transform: translateY(-3px);\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n\n    &::before {\n      left: 100%;\n    }\n  }\n\n  &:active {\n    transform: translateY(-1px);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-4) var(--space-8);\n    font-size: 1rem;\n    width: 100%;\n    max-width: 300px;\n  }\n`;\n\nconst SecondaryButton = styled.button`\n  background: var(--bg-glass);\n  color: var(--text-primary);\n  border: 2px solid var(--border-neural);\n  padding: var(--space-4) var(--space-8);\n  border-radius: var(--radius-xl);\n  font-size: 1.125rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  backdrop-filter: blur(10px);\n\n  &:hover {\n    border-color: var(--primary-600);\n    color: var(--primary-600);\n    background: var(--primary-50);\n    transform: translateY(-2px);\n    box-shadow: var(--shadow-lg);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-4) var(--space-6);\n    font-size: 1rem;\n    width: 100%;\n    max-width: 300px;\n  }\n`;\n\nconst FeaturesSection = styled.section`\n  padding: var(--space-24) var(--space-6);\n  background: var(--bg-tertiary);\n  position: relative;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),\n      radial-gradient(circle at 70% 70%, rgba(147, 51, 234, 0.05) 0%, transparent 50%);\n    pointer-events: none;\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-20) var(--space-4);\n  }\n`;\n\nconst FeaturesContainer = styled.div`\n  max-width: 1400px;\n  margin: 0 auto;\n  text-align: center;\n  position: relative;\n  z-index: 1;\n`;\n\nconst SectionTitle = styled.h2`\n  font-family: var(--font-primary);\n  font-size: 2.75rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin-bottom: var(--space-6);\n  letter-spacing: -0.02em;\n\n  @media (max-width: 768px) {\n    font-size: 2rem;\n  }\n`;\n\nconst SectionSubtitle = styled.p`\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  margin-bottom: var(--space-16);\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n\n  @media (max-width: 768px) {\n    font-size: 1rem;\n    margin-bottom: var(--space-12);\n  }\n`;\n\nconst FeatureGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));\n  gap: var(--space-8);\n  margin-bottom: var(--space-20);\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-6);\n  }\n`;\n\nconst FeatureCard = styled.div`\n  background: var(--bg-glass);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-10);\n  border: 1px solid var(--border-neural);\n  transition: var(--transition-normal);\n  text-align: left;\n  position: relative;\n  overflow: hidden;\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-lg);\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 4px;\n    background: var(--bg-neural);\n    transform: scaleX(0);\n    transition: var(--transition-normal);\n  }\n\n  &:hover {\n    transform: translateY(-6px);\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n    border-color: var(--primary-300);\n\n    &::before {\n      transform: scaleX(1);\n    }\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-8);\n    text-align: center;\n  }\n`;\n\nconst FeatureIconWrapper = styled.div`\n  width: 64px;\n  height: 64px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-xl);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: var(--space-6);\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    margin: 0 auto var(--space-6);\n  }\n`;\n\nconst FeatureTitle = styled.h3`\n  font-size: 1.5rem;\n  margin-bottom: var(--space-4);\n  color: var(--text-primary);\n  font-weight: 700;\n  font-family: var(--font-primary);\n\n  @media (max-width: 768px) {\n    text-align: center;\n  }\n`;\n\nconst FeatureDescription = styled.p`\n  font-size: 1rem;\n  color: var(--text-secondary);\n  line-height: 1.7;\n  font-weight: 400;\n\n  @media (max-width: 768px) {\n    text-align: center;\n  }\n`;\n\nconst HeroStats = styled.div`\n  display: flex;\n  justify-content: center;\n  gap: var(--space-8);\n  margin-top: var(--space-8);\n\n  @media (max-width: 768px) {\n    gap: var(--space-6);\n    flex-wrap: wrap;\n  }\n`;\n\nconst StatItem = styled.div`\n  text-align: center;\n`;\n\nconst StatNumber = styled.div`\n  font-size: 2rem;\n  font-weight: 700;\n  color: var(--primary-600);\n  font-family: var(--font-primary);\n\n  @media (max-width: 768px) {\n    font-size: 1.5rem;\n  }\n`;\n\nconst StatLabel = styled.div`\n  font-size: 0.875rem;\n  color: var(--text-tertiary);\n  font-weight: 500;\n  margin-top: var(--space-1);\n`;\n\nconst HomePage = ({ onStartTraining, onNavigateToAbout, onNavigateToContact }) => {\n  return (\n    <HomeContainer>\n      <Navigation>\n        <NavContainer>\n          <Logo>\n            <LogoIcon>\n              <Brain size={24} />\n            </LogoIcon>\n            ASL Neural\n          </Logo>\n          <NavLinks>\n            <NavLink href=\"#features\">Features</NavLink>\n            <NavLink onClick={onNavigateToAbout} style={{ cursor: 'pointer' }}>About</NavLink>\n            <NavLink onClick={onNavigateToContact} style={{ cursor: 'pointer' }}>Contact</NavLink>\n          </NavLinks>\n        </NavContainer>\n      </Navigation>\n\n      <HeroSection>\n        <HeroContent>\n          <HeroBadge>\n            <Zap size={16} />\n            AI-Powered Learning Platform\n          </HeroBadge>\n\n          <HeroTitle>Master Sign Language with Neural Intelligence</HeroTitle>\n          <HeroSubtitle>\n            Revolutionary AI platform that transforms sign language learning through\n            real-time computer vision and adaptive neural networks\n          </HeroSubtitle>\n          <HeroDescription>\n            Experience the future of accessibility education. Our advanced machine learning\n            algorithms provide instant feedback while contributing to breakthrough AI research\n            for the deaf and hard-of-hearing community.\n          </HeroDescription>\n\n          <HeroActions>\n            <PrimaryButton onClick={onStartTraining}>\n              <Play size={20} />\n              Start Neural Training\n            </PrimaryButton>\n            <SecondaryButton onClick={onNavigateToAbout}>\n              <BookOpen size={20} />\n              Explore Technology\n            </SecondaryButton>\n          </HeroActions>\n\n          <HeroStats>\n            <StatItem>\n              <StatNumber>25K+</StatNumber>\n              <StatLabel>Neural Sessions</StatLabel>\n            </StatItem>\n            <StatItem>\n              <StatNumber>150+</StatNumber>\n              <StatLabel>Sign Patterns</StatLabel>\n            </StatItem>\n            <StatItem>\n              <StatNumber>98.7%</StatNumber>\n              <StatLabel>AI Accuracy</StatLabel>\n            </StatItem>\n          </HeroStats>\n        </HeroContent>\n      </HeroSection>\n\n      <FeaturesSection id=\"features\">\n        <FeaturesContainer>\n          <SectionTitle>Neural Network Capabilities</SectionTitle>\n          <SectionSubtitle>\n            Discover how our advanced AI technology revolutionizes sign language learning\n            through cutting-edge computer vision and machine learning\n          </SectionSubtitle>\n\n          <FeatureGrid>\n            <FeatureCard>\n              <FeatureIconWrapper>\n                <Camera size={28} color=\"white\" />\n              </FeatureIconWrapper>\n              <FeatureTitle>Real-time Computer Vision</FeatureTitle>\n              <FeatureDescription>\n                Advanced neural networks analyze your hand movements in real-time, providing\n                instant feedback with 98.7% accuracy using state-of-the-art pose estimation algorithms\n              </FeatureDescription>\n            </FeatureCard>\n\n            <FeatureCard>\n              <FeatureIconWrapper>\n                <Cpu size={28} color=\"white\" />\n              </FeatureIconWrapper>\n              <FeatureTitle>Adaptive AI Learning</FeatureTitle>\n              <FeatureDescription>\n                Our deep learning models continuously adapt to your learning style, creating\n                personalized training paths that optimize skill acquisition and retention rates\n              </FeatureDescription>\n            </FeatureCard>\n\n            <FeatureCard>\n              <FeatureIconWrapper>\n                <Globe size={28} color=\"white\" />\n              </FeatureIconWrapper>\n              <FeatureTitle>Global Impact Network</FeatureTitle>\n              <FeatureDescription>\n                Join a worldwide community contributing to breakthrough AI research that advances\n                accessibility technology for millions in the deaf and hard-of-hearing community\n              </FeatureDescription>\n            </FeatureCard>\n\n            <FeatureCard>\n              <FeatureIconWrapper>\n                <Smartphone size={28} color=\"white\" />\n              </FeatureIconWrapper>\n              <FeatureTitle>Cross-Platform Intelligence</FeatureTitle>\n              <FeatureDescription>\n                Seamless AI-powered experience across all devices with cloud-synchronized progress\n                and edge computing for lightning-fast response times\n              </FeatureDescription>\n            </FeatureCard>\n\n            <FeatureCard>\n              <FeatureIconWrapper>\n                <Target size={28} color=\"white\" />\n              </FeatureIconWrapper>\n              <FeatureTitle>Precision Learning Analytics</FeatureTitle>\n              <FeatureDescription>\n                Advanced analytics track micro-movements and learning patterns, providing\n                data-driven insights to accelerate your mastery of sign language\n              </FeatureDescription>\n            </FeatureCard>\n\n            <FeatureCard>\n              <FeatureIconWrapper>\n                <Shield size={28} color=\"white\" />\n              </FeatureIconWrapper>\n              <FeatureTitle>Privacy-First Architecture</FeatureTitle>\n              <FeatureDescription>\n                Enterprise-grade security with local processing ensures your data remains private\n                while contributing anonymized insights to advance AI research\n              </FeatureDescription>\n            </FeatureCard>\n          </FeatureGrid>\n        </FeaturesContainer>\n      </FeaturesSection>\n    </HomeContainer>\n  );\n};\n\nexport default HomePage; "], "mappings": "goBAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,OACEC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,MAAM,CACNC,UAAU,CACVC,MAAM,CACNC,IAAI,CACJC,QAAQ,CACRC,GAAG,CACHC,GAAG,CACHC,GAAG,CACHC,KAAK,CACLC,UAAU,CACVC,KAAK,KACA,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEtB,KAAM,CAAAC,aAAa,CAAGnB,MAAM,CAACoB,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,8iBAoB/B,CAED,KAAM,CAAAC,UAAU,CAAGvB,MAAM,CAACwB,GAAG,CAAAC,gBAAA,GAAAA,gBAAA,CAAAH,sBAAA,iVAe5B,CAED,KAAM,CAAAI,YAAY,CAAG1B,MAAM,CAACoB,GAAG,CAAAO,gBAAA,GAAAA,gBAAA,CAAAL,sBAAA,oOAW9B,CAED,KAAM,CAAAM,IAAI,CAAG5B,MAAM,CAACoB,GAAG,CAAAS,gBAAA,GAAAA,gBAAA,CAAAP,sBAAA,+XAgBtB,CAED,KAAM,CAAAQ,QAAQ,CAAG9B,MAAM,CAACoB,GAAG,CAAAW,gBAAA,GAAAA,gBAAA,CAAAT,sBAAA,uTAe1B,CAED,KAAM,CAAAU,QAAQ,CAAGhC,MAAM,CAACoB,GAAG,CAAAa,gBAAA,GAAAA,gBAAA,CAAAX,sBAAA,4IAQ1B,CAED,KAAM,CAAAY,OAAO,CAAGlC,MAAM,CAACmC,CAAC,CAAAC,gBAAA,GAAAA,gBAAA,CAAAd,sBAAA,8hBA8BvB,CAED,KAAM,CAAAe,WAAW,CAAGrC,MAAM,CAACsC,OAAO,CAAAC,gBAAA,GAAAA,gBAAA,CAAAjB,sBAAA,wWAcjC,CAED,KAAM,CAAAkB,WAAW,CAAGxC,MAAM,CAACoB,GAAG,CAAAqB,gBAAA,GAAAA,gBAAA,CAAAnB,sBAAA,qFAK7B,CAED,KAAM,CAAAoB,SAAS,CAAG1C,MAAM,CAACoB,GAAG,CAAAuB,gBAAA,GAAAA,gBAAA,CAAArB,sBAAA,0gBAmB3B,CAED,KAAM,CAAAsB,SAAS,CAAG5C,MAAM,CAAC6C,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAAxB,sBAAA,qdAoB1B,CAED,KAAM,CAAAyB,YAAY,CAAG/C,MAAM,CAACgD,CAAC,CAAAC,iBAAA,GAAAA,iBAAA,CAAA3B,sBAAA,gXAkB5B,CAED,KAAM,CAAA4B,eAAe,CAAGlD,MAAM,CAACgD,CAAC,CAAAG,iBAAA,GAAAA,iBAAA,CAAA7B,sBAAA,kTAc/B,CAED,KAAM,CAAA8B,WAAW,CAAGpD,MAAM,CAACoB,GAAG,CAAAiC,iBAAA,GAAAA,iBAAA,CAAA/B,sBAAA,iSAa7B,CAED,KAAM,CAAAgC,aAAa,CAAGtD,MAAM,CAACuD,MAAM,CAAAC,iBAAA,GAAAA,iBAAA,CAAAlC,sBAAA,wgCA+ClC,CAED,KAAM,CAAAmC,eAAe,CAAGzD,MAAM,CAACuD,MAAM,CAAAG,iBAAA,GAAAA,iBAAA,CAAApC,sBAAA,quBA6BpC,CAED,KAAM,CAAAqC,eAAe,CAAG3D,MAAM,CAACsC,OAAO,CAAAsB,iBAAA,GAAAA,iBAAA,CAAAtC,sBAAA,6hBAqBrC,CAED,KAAM,CAAAuC,iBAAiB,CAAG7D,MAAM,CAACoB,GAAG,CAAA0C,iBAAA,GAAAA,iBAAA,CAAAxC,sBAAA,gHAMnC,CAED,KAAM,CAAAyC,YAAY,CAAG/D,MAAM,CAACgE,EAAE,CAAAC,iBAAA,GAAAA,iBAAA,CAAA3C,sBAAA,+VAc7B,CAED,KAAM,CAAA4C,eAAe,CAAGlE,MAAM,CAACgD,CAAC,CAAAmB,iBAAA,GAAAA,iBAAA,CAAA7C,sBAAA,8RAa/B,CAED,KAAM,CAAA8C,WAAW,CAAGpE,MAAM,CAACoB,GAAG,CAAAiD,iBAAA,GAAAA,iBAAA,CAAA/C,sBAAA,wPAU7B,CAED,KAAM,CAAAgD,WAAW,CAAGtE,MAAM,CAACoB,GAAG,CAAAmD,iBAAA,GAAAA,iBAAA,CAAAjD,sBAAA,g1BAsC7B,CAED,KAAM,CAAAkD,kBAAkB,CAAGxE,MAAM,CAACoB,GAAG,CAAAqD,iBAAA,GAAAA,iBAAA,CAAAnD,sBAAA,uUAcpC,CAED,KAAM,CAAAoD,YAAY,CAAG1E,MAAM,CAAC2E,EAAE,CAAAC,iBAAA,GAAAA,iBAAA,CAAAtD,sBAAA,0NAU7B,CAED,KAAM,CAAAuD,kBAAkB,CAAG7E,MAAM,CAACgD,CAAC,CAAA8B,iBAAA,GAAAA,iBAAA,CAAAxD,sBAAA,wKASlC,CAED,KAAM,CAAAyD,SAAS,CAAG/E,MAAM,CAACoB,GAAG,CAAA4D,iBAAA,GAAAA,iBAAA,CAAA1D,sBAAA,qMAU3B,CAED,KAAM,CAAA2D,QAAQ,CAAGjF,MAAM,CAACoB,GAAG,CAAA8D,iBAAA,GAAAA,iBAAA,CAAA5D,sBAAA,iCAE1B,CAED,KAAM,CAAA6D,UAAU,CAAGnF,MAAM,CAACoB,GAAG,CAAAgE,iBAAA,GAAAA,iBAAA,CAAA9D,sBAAA,oLAS5B,CAED,KAAM,CAAA+D,SAAS,CAAGrF,MAAM,CAACoB,GAAG,CAAAkE,iBAAA,GAAAA,iBAAA,CAAAhE,sBAAA,sHAK3B,CAED,KAAM,CAAAiE,QAAQ,CAAGC,IAAA,EAAiE,IAAhE,CAAEC,eAAe,CAAEC,iBAAiB,CAAEC,mBAAoB,CAAC,CAAAH,IAAA,CAC3E,mBACEtE,KAAA,CAACC,aAAa,EAAAyE,QAAA,eACZ5E,IAAA,CAACO,UAAU,EAAAqE,QAAA,cACT1E,KAAA,CAACQ,YAAY,EAAAkE,QAAA,eACX1E,KAAA,CAACU,IAAI,EAAAgE,QAAA,eACH5E,IAAA,CAACc,QAAQ,EAAA8D,QAAA,cACP5E,IAAA,CAACf,KAAK,EAAC4F,IAAI,CAAE,EAAG,CAAE,CAAC,CACX,CAAC,aAEb,EAAM,CAAC,cACP3E,KAAA,CAACc,QAAQ,EAAA4D,QAAA,eACP5E,IAAA,CAACkB,OAAO,EAAC4D,IAAI,CAAC,WAAW,CAAAF,QAAA,CAAC,UAAQ,CAAS,CAAC,cAC5C5E,IAAA,CAACkB,OAAO,EAAC6D,OAAO,CAAEL,iBAAkB,CAACM,KAAK,CAAE,CAAEC,MAAM,CAAE,SAAU,CAAE,CAAAL,QAAA,CAAC,OAAK,CAAS,CAAC,cAClF5E,IAAA,CAACkB,OAAO,EAAC6D,OAAO,CAAEJ,mBAAoB,CAACK,KAAK,CAAE,CAAEC,MAAM,CAAE,SAAU,CAAE,CAAAL,QAAA,CAAC,SAAO,CAAS,CAAC,EAC9E,CAAC,EACC,CAAC,CACL,CAAC,cAEb5E,IAAA,CAACqB,WAAW,EAAAuD,QAAA,cACV1E,KAAA,CAACsB,WAAW,EAAAoD,QAAA,eACV1E,KAAA,CAACwB,SAAS,EAAAkD,QAAA,eACR5E,IAAA,CAACP,GAAG,EAACoF,IAAI,CAAE,EAAG,CAAE,CAAC,+BAEnB,EAAW,CAAC,cAEZ7E,IAAA,CAAC4B,SAAS,EAAAgD,QAAA,CAAC,+CAA6C,CAAW,CAAC,cACpE5E,IAAA,CAAC+B,YAAY,EAAA6C,QAAA,CAAC,iIAGd,CAAc,CAAC,cACf5E,IAAA,CAACkC,eAAe,EAAA0C,QAAA,CAAC,gNAIjB,CAAiB,CAAC,cAElB1E,KAAA,CAACkC,WAAW,EAAAwC,QAAA,eACV1E,KAAA,CAACoC,aAAa,EAACyC,OAAO,CAAEN,eAAgB,CAAAG,QAAA,eACtC5E,IAAA,CAACT,IAAI,EAACsF,IAAI,CAAE,EAAG,CAAE,CAAC,wBAEpB,EAAe,CAAC,cAChB3E,KAAA,CAACuC,eAAe,EAACsC,OAAO,CAAEL,iBAAkB,CAAAE,QAAA,eAC1C5E,IAAA,CAACR,QAAQ,EAACqF,IAAI,CAAE,EAAG,CAAE,CAAC,qBAExB,EAAiB,CAAC,EACP,CAAC,cAEd3E,KAAA,CAAC6D,SAAS,EAAAa,QAAA,eACR1E,KAAA,CAAC+D,QAAQ,EAAAW,QAAA,eACP5E,IAAA,CAACmE,UAAU,EAAAS,QAAA,CAAC,MAAI,CAAY,CAAC,cAC7B5E,IAAA,CAACqE,SAAS,EAAAO,QAAA,CAAC,iBAAe,CAAW,CAAC,EAC9B,CAAC,cACX1E,KAAA,CAAC+D,QAAQ,EAAAW,QAAA,eACP5E,IAAA,CAACmE,UAAU,EAAAS,QAAA,CAAC,MAAI,CAAY,CAAC,cAC7B5E,IAAA,CAACqE,SAAS,EAAAO,QAAA,CAAC,eAAa,CAAW,CAAC,EAC5B,CAAC,cACX1E,KAAA,CAAC+D,QAAQ,EAAAW,QAAA,eACP5E,IAAA,CAACmE,UAAU,EAAAS,QAAA,CAAC,OAAK,CAAY,CAAC,cAC9B5E,IAAA,CAACqE,SAAS,EAAAO,QAAA,CAAC,aAAW,CAAW,CAAC,EAC1B,CAAC,EACF,CAAC,EACD,CAAC,CACH,CAAC,cAEd5E,IAAA,CAAC2C,eAAe,EAACuC,EAAE,CAAC,UAAU,CAAAN,QAAA,cAC5B1E,KAAA,CAAC2C,iBAAiB,EAAA+B,QAAA,eAChB5E,IAAA,CAAC+C,YAAY,EAAA6B,QAAA,CAAC,6BAA2B,CAAc,CAAC,cACxD5E,IAAA,CAACkD,eAAe,EAAA0B,QAAA,CAAC,yIAGjB,CAAiB,CAAC,cAElB1E,KAAA,CAACkD,WAAW,EAAAwB,QAAA,eACV1E,KAAA,CAACoD,WAAW,EAAAsB,QAAA,eACV5E,IAAA,CAACwD,kBAAkB,EAAAoB,QAAA,cACjB5E,IAAA,CAACd,MAAM,EAAC2F,IAAI,CAAE,EAAG,CAACM,KAAK,CAAC,OAAO,CAAE,CAAC,CAChB,CAAC,cACrBnF,IAAA,CAAC0D,YAAY,EAAAkB,QAAA,CAAC,2BAAyB,CAAc,CAAC,cACtD5E,IAAA,CAAC6D,kBAAkB,EAAAe,QAAA,CAAC,qKAGpB,CAAoB,CAAC,EACV,CAAC,cAEd1E,KAAA,CAACoD,WAAW,EAAAsB,QAAA,eACV5E,IAAA,CAACwD,kBAAkB,EAAAoB,QAAA,cACjB5E,IAAA,CAACN,GAAG,EAACmF,IAAI,CAAE,EAAG,CAACM,KAAK,CAAC,OAAO,CAAE,CAAC,CACb,CAAC,cACrBnF,IAAA,CAAC0D,YAAY,EAAAkB,QAAA,CAAC,sBAAoB,CAAc,CAAC,cACjD5E,IAAA,CAAC6D,kBAAkB,EAAAe,QAAA,CAAC,8JAGpB,CAAoB,CAAC,EACV,CAAC,cAEd1E,KAAA,CAACoD,WAAW,EAAAsB,QAAA,eACV5E,IAAA,CAACwD,kBAAkB,EAAAoB,QAAA,cACjB5E,IAAA,CAACF,KAAK,EAAC+E,IAAI,CAAE,EAAG,CAACM,KAAK,CAAC,OAAO,CAAE,CAAC,CACf,CAAC,cACrBnF,IAAA,CAAC0D,YAAY,EAAAkB,QAAA,CAAC,uBAAqB,CAAc,CAAC,cAClD5E,IAAA,CAAC6D,kBAAkB,EAAAe,QAAA,CAAC,mKAGpB,CAAoB,CAAC,EACV,CAAC,cAEd1E,KAAA,CAACoD,WAAW,EAAAsB,QAAA,eACV5E,IAAA,CAACwD,kBAAkB,EAAAoB,QAAA,cACjB5E,IAAA,CAACX,UAAU,EAACwF,IAAI,CAAE,EAAG,CAACM,KAAK,CAAC,OAAO,CAAE,CAAC,CACpB,CAAC,cACrBnF,IAAA,CAAC0D,YAAY,EAAAkB,QAAA,CAAC,6BAA2B,CAAc,CAAC,cACxD5E,IAAA,CAAC6D,kBAAkB,EAAAe,QAAA,CAAC,yIAGpB,CAAoB,CAAC,EACV,CAAC,cAEd1E,KAAA,CAACoD,WAAW,EAAAsB,QAAA,eACV5E,IAAA,CAACwD,kBAAkB,EAAAoB,QAAA,cACjB5E,IAAA,CAACV,MAAM,EAACuF,IAAI,CAAE,EAAG,CAACM,KAAK,CAAC,OAAO,CAAE,CAAC,CAChB,CAAC,cACrBnF,IAAA,CAAC0D,YAAY,EAAAkB,QAAA,CAAC,8BAA4B,CAAc,CAAC,cACzD5E,IAAA,CAAC6D,kBAAkB,EAAAe,QAAA,CAAC,4IAGpB,CAAoB,CAAC,EACV,CAAC,cAEd1E,KAAA,CAACoD,WAAW,EAAAsB,QAAA,eACV5E,IAAA,CAACwD,kBAAkB,EAAAoB,QAAA,cACjB5E,IAAA,CAACZ,MAAM,EAACyF,IAAI,CAAE,EAAG,CAACM,KAAK,CAAC,OAAO,CAAE,CAAC,CAChB,CAAC,cACrBnF,IAAA,CAAC0D,YAAY,EAAAkB,QAAA,CAAC,4BAA0B,CAAc,CAAC,cACvD5E,IAAA,CAAC6D,kBAAkB,EAAAe,QAAA,CAAC,iJAGpB,CAAoB,CAAC,EACV,CAAC,EACH,CAAC,EACG,CAAC,CACL,CAAC,EACL,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAL,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}