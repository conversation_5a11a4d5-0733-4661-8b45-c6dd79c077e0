{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 20h-1a2 2 0 0 1-2-2 2 2 0 0 1-2 2H6\",\n  key: \"1528k5\"\n}], [\"path\", {\n  d: \"M13 8h7a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-7\",\n  key: \"13ksps\"\n}], [\"path\", {\n  d: \"M5 16H4a2 2 0 0 1-2-2v-4a2 2 0 0 1 2-2h1\",\n  key: \"1n9rhb\"\n}], [\"path\", {\n  d: \"M6 4h1a2 2 0 0 1 2 2 2 2 0 0 1 2-2h1\",\n  key: \"1mj8rg\"\n}], [\"path\", {\n  d: \"M9 6v12\",\n  key: \"velyjx\"\n}]];\nconst TextCursorInput = createLucideIcon(\"text-cursor-input\", __iconNode);\nexport { __iconNode, TextCursorInput as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "TextCursorInput", "createLucideIcon"], "sources": ["D:\\ASL\\training-frontend\\node_modules\\lucide-react\\src\\icons\\text-cursor-input.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 20h-1a2 2 0 0 1-2-2 2 2 0 0 1-2 2H6', key: '1528k5' }],\n  ['path', { d: 'M13 8h7a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-7', key: '13ksps' }],\n  ['path', { d: 'M5 16H4a2 2 0 0 1-2-2v-4a2 2 0 0 1 2-2h1', key: '1n9rhb' }],\n  ['path', { d: 'M6 4h1a2 2 0 0 1 2 2 2 2 0 0 1 2-2h1', key: '1mj8rg' }],\n  ['path', { d: 'M9 6v12', key: 'velyjx' }],\n];\n\n/**\n * @component @name TextCursorInput\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMjBoLTFhMiAyIDAgMCAxLTItMiAyIDIgMCAwIDEtMiAySDYiIC8+CiAgPHBhdGggZD0iTTEzIDhoN2EyIDIgMCAwIDEgMiAydjRhMiAyIDAgMCAxLTIgMmgtNyIgLz4KICA8cGF0aCBkPSJNNSAxNkg0YTIgMiAwIDAgMS0yLTJ2LTRhMiAyIDAgMCAxIDItMmgxIiAvPgogIDxwYXRoIGQ9Ik02IDRoMWEyIDIgMCAwIDEgMiAyIDIgMiAwIDAgMSAyLTJoMSIgLz4KICA8cGF0aCBkPSJNOSA2djEyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/text-cursor-input\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TextCursorInput = createLucideIcon('text-cursor-input', __iconNode);\n\nexport default TextCursorInput;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,yCAA2C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxE,CAAC,MAAQ;EAAED,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,MAAQ;EAAED,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,MAAQ;EAAED,CAAA,EAAG,sCAAwC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrE,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAU,GAC1C;AAaM,MAAAC,eAAA,GAAkBC,gBAAiB,sBAAqBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}