{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z\",\n  key: \"i9b6wo\"\n}], [\"line\", {\n  x1: \"4\",\n  x2: \"4\",\n  y1: \"22\",\n  y2: \"15\",\n  key: \"1cm3nv\"\n}]];\nconst Flag = createLucideIcon(\"flag\", __iconNode);\nexport { __iconNode, Flag as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "x1", "x2", "y1", "y2", "Flag", "createLucideIcon"], "sources": ["D:\\ASL\\training-frontend\\node_modules\\lucide-react\\src\\icons\\flag.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z', key: 'i9b6wo' }],\n  ['line', { x1: '4', x2: '4', y1: '22', y2: '15', key: '1cm3nv' }],\n];\n\n/**\n * @component @name Flag\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAxNXMxLTEgNC0xIDUgMiA4IDIgNC0xIDQtMVYzcy0xIDEtNCAxLTUtMi04LTItNCAxLTQgMXoiIC8+CiAgPGxpbmUgeDE9IjQiIHgyPSI0IiB5MT0iMjIiIHkyPSIxNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/flag\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Flag = createLucideIcon('flag', __iconNode);\n\nexport default Flag;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,2DAA6D;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1F,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAU,GAClE;AAaM,MAAAK,IAAA,GAAOC,gBAAiB,SAAQR,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}