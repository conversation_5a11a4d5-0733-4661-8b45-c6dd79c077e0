{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"5\",\n  cy: \"6\",\n  r: \"3\",\n  key: \"1qnov2\"\n}], [\"path\", {\n  d: \"M5 9v12\",\n  key: \"ih889a\"\n}], [\"circle\", {\n  cx: \"19\",\n  cy: \"18\",\n  r: \"3\",\n  key: \"1qljk2\"\n}], [\"path\", {\n  d: \"m15 9-3-3 3-3\",\n  key: \"1lwv8l\"\n}], [\"path\", {\n  d: \"M12 6h5a2 2 0 0 1 2 2v7\",\n  key: \"1yj91y\"\n}]];\nconst GitPullRequestArrow = createLucideIcon(\"git-pull-request-arrow\", __iconNode);\nexport { __iconNode, GitPullRequestArrow as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "d", "GitPullRequestArrow", "createLucideIcon"], "sources": ["D:\\ASL\\training-frontend\\node_modules\\lucide-react\\src\\icons\\git-pull-request-arrow.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '5', cy: '6', r: '3', key: '1qnov2' }],\n  ['path', { d: 'M5 9v12', key: 'ih889a' }],\n  ['circle', { cx: '19', cy: '18', r: '3', key: '1qljk2' }],\n  ['path', { d: 'm15 9-3-3 3-3', key: '1lwv8l' }],\n  ['path', { d: 'M12 6h5a2 2 0 0 1 2 2v7', key: '1yj91y' }],\n];\n\n/**\n * @component @name GitPullRequestArrow\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSI1IiBjeT0iNiIgcj0iMyIgLz4KICA8cGF0aCBkPSJNNSA5djEyIiAvPgogIDxjaXJjbGUgY3g9IjE5IiBjeT0iMTgiIHI9IjMiIC8+CiAgPHBhdGggZD0ibTE1IDktMy0zIDMtMyIgLz4KICA8cGF0aCBkPSJNMTIgNmg1YTIgMiAwIDAgMSAyIDJ2NyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/git-pull-request-arrow\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst GitPullRequestArrow = createLucideIcon('git-pull-request-arrow', __iconNode);\n\nexport default GitPullRequestArrow;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAKC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAU;EAAEH,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,eAAiB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,yBAA2B;EAAAD,GAAA,EAAK;AAAU,GAC1D;AAaM,MAAAE,mBAAA,GAAsBC,gBAAiB,2BAA0BP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}