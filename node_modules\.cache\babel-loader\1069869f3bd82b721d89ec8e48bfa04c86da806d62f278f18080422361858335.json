{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useRef, useCallback, useEffect } from 'react';\nimport config from '../config';\nexport const useSignDetection = () => {\n  _s();\n  const [isConnected, setIsConnected] = useState(false);\n  const [prediction, setPrediction] = useState(null);\n  const [lastPrediction, setLastPrediction] = useState(null);\n  const [isRecording, setIsRecording] = useState(false);\n  const [recordingStatus, setRecordingStatus] = useState('');\n  const [processedFrame, setProcessedFrame] = useState(null);\n  const [signMatched, setSignMatched] = useState(false);\n  const [targetSign, setTargetSign] = useState('');\n  const [predictionHistory, setPredictionHistory] = useState([]);\n  const [currentKeypoints, setCurrentKeypoints] = useState(null);\n  const wsRef = useRef(null);\n  const reconnectTimeoutRef = useRef(null);\n  const frameIntervalRef = useRef(null);\n  const connect = useCallback(() => {\n    try {\n      wsRef.current = new WebSocket(config.WEBSOCKET_URL);\n      wsRef.current.onopen = () => {\n        console.log('Connected to sign detection backend');\n        setIsConnected(true);\n        setRecordingStatus('');\n      };\n      wsRef.current.onmessage = event => {\n        try {\n          const data = JSON.parse(event.data);\n          console.log('Received from backend:', data.type, data.prediction);\n          switch (data.type) {\n            case 'frame_processed':\n              // Always update processed frame\n              setProcessedFrame(data.processed_frame);\n              setSignMatched(data.sign_matched || false);\n              setTargetSign(data.target_sign || '');\n\n              // Update prediction - keep last prediction if new one is null\n              if (data.prediction) {\n                setPrediction(data.prediction);\n                setLastPrediction(data.prediction);\n\n                // Add to prediction history (keep last 5)\n                setPredictionHistory(prev => {\n                  const newHistory = [data.prediction, ...prev.slice(0, 4)];\n                  return newHistory;\n                });\n                console.log(`Detected: ${data.prediction.sign} (${Math.round(data.prediction.confidence * 100)}%)`);\n\n                // Store keypoints if available\n                if (data.keypoints) {\n                  setCurrentKeypoints(data.keypoints);\n                }\n\n                // Show recording session status\n                if (data.should_start_session) {\n                  setRecordingStatus(`🎬 Started recording session for ${data.target_sign} (3 seconds)`);\n                } else if (data.is_in_session && data.sign_matched) {\n                  setRecordingStatus(`📹 Recording session active for ${data.target_sign}...`);\n                }\n              } else if (lastPrediction) {\n                // Keep showing last prediction with reduced opacity\n                setPrediction({\n                  ...lastPrediction,\n                  confidence: lastPrediction.confidence * 0.7,\n                  isStale: true\n                });\n              }\n              break;\n            case 'recording_started':\n              setIsRecording(true);\n              setRecordingStatus(`Ready to record: ${data.target_sign} (will auto-record for 3s when detected)`);\n              break;\n            case 'recording_stopped':\n              setIsRecording(false);\n              if (data.result) {\n                const reason = data.reason === 'auto_stop_session_complete' ? 'Session completed' : 'Manual stop';\n                setRecordingStatus(`✅ Recording saved: ${data.result.frame_count} frames (${reason})`);\n              } else {\n                setRecordingStatus('Recording stopped');\n              }\n              break;\n            default:\n              console.log('Unknown message type:', data.type);\n          }\n        } catch (error) {\n          console.error('Error parsing WebSocket message:', error);\n        }\n      };\n      wsRef.current.onclose = () => {\n        console.log('Disconnected from sign detection backend');\n        setIsConnected(false);\n        setRecordingStatus('Connection lost - Click retry to reconnect');\n\n        // Attempt to reconnect after 3 seconds\n        reconnectTimeoutRef.current = setTimeout(() => {\n          console.log('Attempting to reconnect...');\n          connect();\n        }, 3000);\n      };\n      wsRef.current.onerror = error => {\n        console.error('WebSocket error:', error);\n        setRecordingStatus('Connection error');\n      };\n    } catch (error) {\n      console.error('Error connecting to WebSocket:', error);\n      setRecordingStatus('Failed to connect to backend');\n    }\n  }, []);\n  const disconnect = useCallback(() => {\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n    }\n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n    }\n    if (wsRef.current) {\n      wsRef.current.close();\n      wsRef.current = null;\n    }\n    setIsConnected(false);\n  }, []);\n  const sendFrame = useCallback(frameData => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'frame',\n        frame: frameData\n      };\n      wsRef.current.send(JSON.stringify(message));\n    }\n  }, []);\n  const setLevel = useCallback(level => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'set_level',\n        level: level\n      };\n      wsRef.current.send(JSON.stringify(message));\n      console.log(`Setting detection level to ${level}`);\n    }\n  }, []);\n  const startRecording = useCallback((targetSignName, startSession = false) => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'start_recording',\n        target_sign: targetSignName,\n        start_session: startSession // New parameter to immediately start recording session\n      };\n      wsRef.current.send(JSON.stringify(message));\n      setTargetSign(targetSignName);\n    }\n  }, []);\n  const stopRecording = useCallback(() => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'stop_recording'\n      };\n      wsRef.current.send(JSON.stringify(message));\n    }\n  }, []);\n  const startFrameCapture = useCallback((webcamRef, interval = 100) => {\n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n    }\n    frameIntervalRef.current = setInterval(() => {\n      if (webcamRef.current && isConnected) {\n        const imageSrc = webcamRef.current.getScreenshot();\n        if (imageSrc) {\n          console.log('Sending frame to backend');\n          sendFrame(imageSrc);\n        }\n      }\n    }, interval);\n  }, [isConnected, sendFrame]);\n  const stopFrameCapture = useCallback(() => {\n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n      frameIntervalRef.current = null;\n    }\n  }, []);\n\n  // Auto-connect on mount\n  useEffect(() => {\n    connect();\n    return () => {\n      disconnect();\n    };\n  }, [connect, disconnect]);\n  const retryConnection = useCallback(() => {\n    setRecordingStatus('Attempting to reconnect...');\n    disconnect();\n    setTimeout(() => {\n      connect();\n    }, 1000);\n  }, [connect, disconnect]);\n  return {\n    isConnected,\n    prediction,\n    lastPrediction,\n    predictionHistory,\n    isRecording,\n    recordingStatus,\n    processedFrame,\n    signMatched,\n    targetSign,\n    currentKeypoints,\n    connect,\n    disconnect,\n    retryConnection,\n    sendFrame,\n    startRecording,\n    stopRecording,\n    startFrameCapture,\n    stopFrameCapture,\n    setLevel\n  };\n};\n_s(useSignDetection, \"FbQdu+2/vWkjGJmrA0zCGzXrdWE=\");", "map": {"version": 3, "names": ["useState", "useRef", "useCallback", "useEffect", "config", "useSignDetection", "_s", "isConnected", "setIsConnected", "prediction", "setPrediction", "lastPrediction", "setLastPrediction", "isRecording", "setIsRecording", "recordingStatus", "setRecordingStatus", "processedFrame", "setProcessedFrame", "signMatched", "setSignMatched", "targetSign", "setTargetSign", "predictionHistory", "setPredictionHistory", "currentKeypoints", "setCurrentKeypoints", "wsRef", "reconnectTimeoutRef", "frameIntervalRef", "connect", "current", "WebSocket", "WEBSOCKET_URL", "onopen", "console", "log", "onmessage", "event", "data", "JSON", "parse", "type", "processed_frame", "sign_matched", "target_sign", "prev", "newHistory", "slice", "sign", "Math", "round", "confidence", "keypoints", "should_start_session", "is_in_session", "isStale", "result", "reason", "frame_count", "error", "onclose", "setTimeout", "onerror", "disconnect", "clearTimeout", "clearInterval", "close", "sendFrame", "frameData", "readyState", "OPEN", "message", "frame", "send", "stringify", "setLevel", "level", "startRecording", "targetSignName", "startSession", "start_session", "stopRecording", "startFrameCapture", "webcamRef", "interval", "setInterval", "imageSrc", "getScreenshot", "stopFrameCapture", "retryConnection"], "sources": ["D:/ASL/ASL-Training/src/hooks/useSignDetection.js"], "sourcesContent": ["import { useState, useRef, useCallback, useEffect } from 'react';\nimport config from '../config';\n\nexport const useSignDetection = () => {\n  const [isConnected, setIsConnected] = useState(false);\n  const [prediction, setPrediction] = useState(null);\n  const [lastPrediction, setLastPrediction] = useState(null);\n  const [isRecording, setIsRecording] = useState(false);\n  const [recordingStatus, setRecordingStatus] = useState('');\n  const [processedFrame, setProcessedFrame] = useState(null);\n  const [signMatched, setSignMatched] = useState(false);\n  const [targetSign, setTargetSign] = useState('');\n  const [predictionHistory, setPredictionHistory] = useState([]);\n  const [currentKeypoints, setCurrentKeypoints] = useState(null);\n  \n  const wsRef = useRef(null);\n  const reconnectTimeoutRef = useRef(null);\n  const frameIntervalRef = useRef(null);\n  \n  const connect = useCallback(() => {\n    try {\n      wsRef.current = new WebSocket(config.WEBSOCKET_URL);\n      \n      wsRef.current.onopen = () => {\n        console.log('Connected to sign detection backend');\n        setIsConnected(true);\n        setRecordingStatus('');\n      };\n      \n      wsRef.current.onmessage = (event) => {\n        try {\n          const data = JSON.parse(event.data);\n          console.log('Received from backend:', data.type, data.prediction);\n\n          switch (data.type) {\n            case 'frame_processed':\n              // Always update processed frame\n              setProcessedFrame(data.processed_frame);\n              setSignMatched(data.sign_matched || false);\n              setTargetSign(data.target_sign || '');\n\n              // Update prediction - keep last prediction if new one is null\n              if (data.prediction) {\n                setPrediction(data.prediction);\n                setLastPrediction(data.prediction);\n\n                // Add to prediction history (keep last 5)\n                setPredictionHistory(prev => {\n                  const newHistory = [data.prediction, ...prev.slice(0, 4)];\n                  return newHistory;\n                });\n\n                console.log(`Detected: ${data.prediction.sign} (${Math.round(data.prediction.confidence * 100)}%)`);\n                \n                // Store keypoints if available\n                if (data.keypoints) {\n                  setCurrentKeypoints(data.keypoints);\n                }\n                \n                // Show recording session status\n                if (data.should_start_session) {\n                  setRecordingStatus(`🎬 Started recording session for ${data.target_sign} (3 seconds)`);\n                } else if (data.is_in_session && data.sign_matched) {\n                  setRecordingStatus(`📹 Recording session active for ${data.target_sign}...`);\n                }\n              } else if (lastPrediction) {\n                // Keep showing last prediction with reduced opacity\n                setPrediction({\n                  ...lastPrediction,\n                  confidence: lastPrediction.confidence * 0.7,\n                  isStale: true\n                });\n              }\n              break;\n              \n            case 'recording_started':\n              setIsRecording(true);\n              setRecordingStatus(`Ready to record: ${data.target_sign} (will auto-record for 3s when detected)`);\n              break;\n              \n            case 'recording_stopped':\n              setIsRecording(false);\n              if (data.result) {\n                const reason = data.reason === 'auto_stop_session_complete' ? 'Session completed' : 'Manual stop';\n                setRecordingStatus(`✅ Recording saved: ${data.result.frame_count} frames (${reason})`);\n              } else {\n                setRecordingStatus('Recording stopped');\n              }\n              break;\n              \n            default:\n              console.log('Unknown message type:', data.type);\n          }\n        } catch (error) {\n          console.error('Error parsing WebSocket message:', error);\n        }\n      };\n      \n      wsRef.current.onclose = () => {\n        console.log('Disconnected from sign detection backend');\n        setIsConnected(false);\n        setRecordingStatus('Connection lost - Click retry to reconnect');\n\n        // Attempt to reconnect after 3 seconds\n        reconnectTimeoutRef.current = setTimeout(() => {\n          console.log('Attempting to reconnect...');\n          connect();\n        }, 3000);\n      };\n      \n      wsRef.current.onerror = (error) => {\n        console.error('WebSocket error:', error);\n        setRecordingStatus('Connection error');\n      };\n      \n    } catch (error) {\n      console.error('Error connecting to WebSocket:', error);\n      setRecordingStatus('Failed to connect to backend');\n    }\n  }, []);\n  \n  const disconnect = useCallback(() => {\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n    }\n    \n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n    }\n    \n    if (wsRef.current) {\n      wsRef.current.close();\n      wsRef.current = null;\n    }\n    \n    setIsConnected(false);\n  }, []);\n  \n  const sendFrame = useCallback((frameData) => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'frame',\n        frame: frameData\n      };\n      wsRef.current.send(JSON.stringify(message));\n    }\n  }, []);\n\n  const setLevel = useCallback((level) => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'set_level',\n        level: level\n      };\n      wsRef.current.send(JSON.stringify(message));\n      console.log(`Setting detection level to ${level}`);\n    }\n  }, []);\n  \n  const startRecording = useCallback((targetSignName, startSession = false) => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'start_recording',\n        target_sign: targetSignName,\n        start_session: startSession  // New parameter to immediately start recording session\n      };\n      wsRef.current.send(JSON.stringify(message));\n      setTargetSign(targetSignName);\n    }\n  }, []);\n  \n  const stopRecording = useCallback(() => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'stop_recording'\n      };\n      wsRef.current.send(JSON.stringify(message));\n    }\n  }, []);\n  \n  const startFrameCapture = useCallback((webcamRef, interval = 100) => {\n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n    }\n\n    frameIntervalRef.current = setInterval(() => {\n      if (webcamRef.current && isConnected) {\n        const imageSrc = webcamRef.current.getScreenshot();\n        if (imageSrc) {\n          console.log('Sending frame to backend');\n          sendFrame(imageSrc);\n        }\n      }\n    }, interval);\n  }, [isConnected, sendFrame]);\n  \n  const stopFrameCapture = useCallback(() => {\n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n      frameIntervalRef.current = null;\n    }\n  }, []);\n  \n  // Auto-connect on mount\n  useEffect(() => {\n    connect();\n    \n    return () => {\n      disconnect();\n    };\n  }, [connect, disconnect]);\n  \n  const retryConnection = useCallback(() => {\n    setRecordingStatus('Attempting to reconnect...');\n    disconnect();\n    setTimeout(() => {\n      connect();\n    }, 1000);\n  }, [connect, disconnect]);\n\n  return {\n    isConnected,\n    prediction,\n    lastPrediction,\n    predictionHistory,\n    isRecording,\n    recordingStatus,\n    processedFrame,\n    signMatched,\n    targetSign,\n    currentKeypoints,\n    connect,\n    disconnect,\n    retryConnection,\n    sendFrame,\n    startRecording,\n    stopRecording,\n    startFrameCapture,\n    stopFrameCapture,\n    setLevel\n  };\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AAChE,OAAOC,MAAM,MAAM,WAAW;AAE9B,OAAO,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACW,cAAc,EAAEC,iBAAiB,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAE9D,MAAM2B,KAAK,GAAG1B,MAAM,CAAC,IAAI,CAAC;EAC1B,MAAM2B,mBAAmB,GAAG3B,MAAM,CAAC,IAAI,CAAC;EACxC,MAAM4B,gBAAgB,GAAG5B,MAAM,CAAC,IAAI,CAAC;EAErC,MAAM6B,OAAO,GAAG5B,WAAW,CAAC,MAAM;IAChC,IAAI;MACFyB,KAAK,CAACI,OAAO,GAAG,IAAIC,SAAS,CAAC5B,MAAM,CAAC6B,aAAa,CAAC;MAEnDN,KAAK,CAACI,OAAO,CAACG,MAAM,GAAG,MAAM;QAC3BC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;QAClD5B,cAAc,CAAC,IAAI,CAAC;QACpBQ,kBAAkB,CAAC,EAAE,CAAC;MACxB,CAAC;MAEDW,KAAK,CAACI,OAAO,CAACM,SAAS,GAAIC,KAAK,IAAK;QACnC,IAAI;UACF,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC;UACnCJ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEG,IAAI,CAACG,IAAI,EAAEH,IAAI,CAAC9B,UAAU,CAAC;UAEjE,QAAQ8B,IAAI,CAACG,IAAI;YACf,KAAK,iBAAiB;cACpB;cACAxB,iBAAiB,CAACqB,IAAI,CAACI,eAAe,CAAC;cACvCvB,cAAc,CAACmB,IAAI,CAACK,YAAY,IAAI,KAAK,CAAC;cAC1CtB,aAAa,CAACiB,IAAI,CAACM,WAAW,IAAI,EAAE,CAAC;;cAErC;cACA,IAAIN,IAAI,CAAC9B,UAAU,EAAE;gBACnBC,aAAa,CAAC6B,IAAI,CAAC9B,UAAU,CAAC;gBAC9BG,iBAAiB,CAAC2B,IAAI,CAAC9B,UAAU,CAAC;;gBAElC;gBACAe,oBAAoB,CAACsB,IAAI,IAAI;kBAC3B,MAAMC,UAAU,GAAG,CAACR,IAAI,CAAC9B,UAAU,EAAE,GAAGqC,IAAI,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;kBACzD,OAAOD,UAAU;gBACnB,CAAC,CAAC;gBAEFZ,OAAO,CAACC,GAAG,CAAC,aAAaG,IAAI,CAAC9B,UAAU,CAACwC,IAAI,KAAKC,IAAI,CAACC,KAAK,CAACZ,IAAI,CAAC9B,UAAU,CAAC2C,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC;;gBAEnG;gBACA,IAAIb,IAAI,CAACc,SAAS,EAAE;kBAClB3B,mBAAmB,CAACa,IAAI,CAACc,SAAS,CAAC;gBACrC;;gBAEA;gBACA,IAAId,IAAI,CAACe,oBAAoB,EAAE;kBAC7BtC,kBAAkB,CAAC,oCAAoCuB,IAAI,CAACM,WAAW,cAAc,CAAC;gBACxF,CAAC,MAAM,IAAIN,IAAI,CAACgB,aAAa,IAAIhB,IAAI,CAACK,YAAY,EAAE;kBAClD5B,kBAAkB,CAAC,mCAAmCuB,IAAI,CAACM,WAAW,KAAK,CAAC;gBAC9E;cACF,CAAC,MAAM,IAAIlC,cAAc,EAAE;gBACzB;gBACAD,aAAa,CAAC;kBACZ,GAAGC,cAAc;kBACjByC,UAAU,EAAEzC,cAAc,CAACyC,UAAU,GAAG,GAAG;kBAC3CI,OAAO,EAAE;gBACX,CAAC,CAAC;cACJ;cACA;YAEF,KAAK,mBAAmB;cACtB1C,cAAc,CAAC,IAAI,CAAC;cACpBE,kBAAkB,CAAC,oBAAoBuB,IAAI,CAACM,WAAW,0CAA0C,CAAC;cAClG;YAEF,KAAK,mBAAmB;cACtB/B,cAAc,CAAC,KAAK,CAAC;cACrB,IAAIyB,IAAI,CAACkB,MAAM,EAAE;gBACf,MAAMC,MAAM,GAAGnB,IAAI,CAACmB,MAAM,KAAK,4BAA4B,GAAG,mBAAmB,GAAG,aAAa;gBACjG1C,kBAAkB,CAAC,sBAAsBuB,IAAI,CAACkB,MAAM,CAACE,WAAW,YAAYD,MAAM,GAAG,CAAC;cACxF,CAAC,MAAM;gBACL1C,kBAAkB,CAAC,mBAAmB,CAAC;cACzC;cACA;YAEF;cACEmB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEG,IAAI,CAACG,IAAI,CAAC;UACnD;QACF,CAAC,CAAC,OAAOkB,KAAK,EAAE;UACdzB,OAAO,CAACyB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QAC1D;MACF,CAAC;MAEDjC,KAAK,CAACI,OAAO,CAAC8B,OAAO,GAAG,MAAM;QAC5B1B,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD5B,cAAc,CAAC,KAAK,CAAC;QACrBQ,kBAAkB,CAAC,4CAA4C,CAAC;;QAEhE;QACAY,mBAAmB,CAACG,OAAO,GAAG+B,UAAU,CAAC,MAAM;UAC7C3B,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;UACzCN,OAAO,CAAC,CAAC;QACX,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MAEDH,KAAK,CAACI,OAAO,CAACgC,OAAO,GAAIH,KAAK,IAAK;QACjCzB,OAAO,CAACyB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;QACxC5C,kBAAkB,CAAC,kBAAkB,CAAC;MACxC,CAAC;IAEH,CAAC,CAAC,OAAO4C,KAAK,EAAE;MACdzB,OAAO,CAACyB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD5C,kBAAkB,CAAC,8BAA8B,CAAC;IACpD;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMgD,UAAU,GAAG9D,WAAW,CAAC,MAAM;IACnC,IAAI0B,mBAAmB,CAACG,OAAO,EAAE;MAC/BkC,YAAY,CAACrC,mBAAmB,CAACG,OAAO,CAAC;IAC3C;IAEA,IAAIF,gBAAgB,CAACE,OAAO,EAAE;MAC5BmC,aAAa,CAACrC,gBAAgB,CAACE,OAAO,CAAC;IACzC;IAEA,IAAIJ,KAAK,CAACI,OAAO,EAAE;MACjBJ,KAAK,CAACI,OAAO,CAACoC,KAAK,CAAC,CAAC;MACrBxC,KAAK,CAACI,OAAO,GAAG,IAAI;IACtB;IAEAvB,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM4D,SAAS,GAAGlE,WAAW,CAAEmE,SAAS,IAAK;IAC3C,IAAI1C,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACI,OAAO,CAACuC,UAAU,KAAKtC,SAAS,CAACuC,IAAI,EAAE;MAChE,MAAMC,OAAO,GAAG;QACd9B,IAAI,EAAE,OAAO;QACb+B,KAAK,EAAEJ;MACT,CAAC;MACD1C,KAAK,CAACI,OAAO,CAAC2C,IAAI,CAAClC,IAAI,CAACmC,SAAS,CAACH,OAAO,CAAC,CAAC;IAC7C;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,QAAQ,GAAG1E,WAAW,CAAE2E,KAAK,IAAK;IACtC,IAAIlD,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACI,OAAO,CAACuC,UAAU,KAAKtC,SAAS,CAACuC,IAAI,EAAE;MAChE,MAAMC,OAAO,GAAG;QACd9B,IAAI,EAAE,WAAW;QACjBmC,KAAK,EAAEA;MACT,CAAC;MACDlD,KAAK,CAACI,OAAO,CAAC2C,IAAI,CAAClC,IAAI,CAACmC,SAAS,CAACH,OAAO,CAAC,CAAC;MAC3CrC,OAAO,CAACC,GAAG,CAAC,8BAA8ByC,KAAK,EAAE,CAAC;IACpD;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,cAAc,GAAG5E,WAAW,CAAC,CAAC6E,cAAc,EAAEC,YAAY,GAAG,KAAK,KAAK;IAC3E,IAAIrD,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACI,OAAO,CAACuC,UAAU,KAAKtC,SAAS,CAACuC,IAAI,EAAE;MAChE,MAAMC,OAAO,GAAG;QACd9B,IAAI,EAAE,iBAAiB;QACvBG,WAAW,EAAEkC,cAAc;QAC3BE,aAAa,EAAED,YAAY,CAAE;MAC/B,CAAC;MACDrD,KAAK,CAACI,OAAO,CAAC2C,IAAI,CAAClC,IAAI,CAACmC,SAAS,CAACH,OAAO,CAAC,CAAC;MAC3ClD,aAAa,CAACyD,cAAc,CAAC;IAC/B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,aAAa,GAAGhF,WAAW,CAAC,MAAM;IACtC,IAAIyB,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACI,OAAO,CAACuC,UAAU,KAAKtC,SAAS,CAACuC,IAAI,EAAE;MAChE,MAAMC,OAAO,GAAG;QACd9B,IAAI,EAAE;MACR,CAAC;MACDf,KAAK,CAACI,OAAO,CAAC2C,IAAI,CAAClC,IAAI,CAACmC,SAAS,CAACH,OAAO,CAAC,CAAC;IAC7C;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,iBAAiB,GAAGjF,WAAW,CAAC,CAACkF,SAAS,EAAEC,QAAQ,GAAG,GAAG,KAAK;IACnE,IAAIxD,gBAAgB,CAACE,OAAO,EAAE;MAC5BmC,aAAa,CAACrC,gBAAgB,CAACE,OAAO,CAAC;IACzC;IAEAF,gBAAgB,CAACE,OAAO,GAAGuD,WAAW,CAAC,MAAM;MAC3C,IAAIF,SAAS,CAACrD,OAAO,IAAIxB,WAAW,EAAE;QACpC,MAAMgF,QAAQ,GAAGH,SAAS,CAACrD,OAAO,CAACyD,aAAa,CAAC,CAAC;QAClD,IAAID,QAAQ,EAAE;UACZpD,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;UACvCgC,SAAS,CAACmB,QAAQ,CAAC;QACrB;MACF;IACF,CAAC,EAAEF,QAAQ,CAAC;EACd,CAAC,EAAE,CAAC9E,WAAW,EAAE6D,SAAS,CAAC,CAAC;EAE5B,MAAMqB,gBAAgB,GAAGvF,WAAW,CAAC,MAAM;IACzC,IAAI2B,gBAAgB,CAACE,OAAO,EAAE;MAC5BmC,aAAa,CAACrC,gBAAgB,CAACE,OAAO,CAAC;MACvCF,gBAAgB,CAACE,OAAO,GAAG,IAAI;IACjC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA5B,SAAS,CAAC,MAAM;IACd2B,OAAO,CAAC,CAAC;IAET,OAAO,MAAM;MACXkC,UAAU,CAAC,CAAC;IACd,CAAC;EACH,CAAC,EAAE,CAAClC,OAAO,EAAEkC,UAAU,CAAC,CAAC;EAEzB,MAAM0B,eAAe,GAAGxF,WAAW,CAAC,MAAM;IACxCc,kBAAkB,CAAC,4BAA4B,CAAC;IAChDgD,UAAU,CAAC,CAAC;IACZF,UAAU,CAAC,MAAM;MACfhC,OAAO,CAAC,CAAC;IACX,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,CAACA,OAAO,EAAEkC,UAAU,CAAC,CAAC;EAEzB,OAAO;IACLzD,WAAW;IACXE,UAAU;IACVE,cAAc;IACdY,iBAAiB;IACjBV,WAAW;IACXE,eAAe;IACfE,cAAc;IACdE,WAAW;IACXE,UAAU;IACVI,gBAAgB;IAChBK,OAAO;IACPkC,UAAU;IACV0B,eAAe;IACftB,SAAS;IACTU,cAAc;IACdI,aAAa;IACbC,iBAAiB;IACjBM,gBAAgB;IAChBb;EACF,CAAC;AACH,CAAC;AAACtE,EAAA,CA9OWD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}