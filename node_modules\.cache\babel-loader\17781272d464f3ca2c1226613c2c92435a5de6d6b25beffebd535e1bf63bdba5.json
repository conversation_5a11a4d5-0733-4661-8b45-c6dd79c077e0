{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"5\",\n  cy: \"6\",\n  r: \"3\",\n  key: \"1qnov2\"\n}], [\"path\", {\n  d: \"M12 6h5a2 2 0 0 1 2 2v7\",\n  key: \"1yj91y\"\n}], [\"path\", {\n  d: \"m15 9-3-3 3-3\",\n  key: \"1lwv8l\"\n}], [\"circle\", {\n  cx: \"19\",\n  cy: \"18\",\n  r: \"3\",\n  key: \"1qljk2\"\n}], [\"path\", {\n  d: \"M12 18H7a2 2 0 0 1-2-2V9\",\n  key: \"16sdep\"\n}], [\"path\", {\n  d: \"m9 15 3 3-3 3\",\n  key: \"1m3kbl\"\n}]];\nconst GitCompareArrows = createLucideIcon(\"git-compare-arrows\", __iconNode);\nexport { __iconNode, GitCompareArrows as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "d", "GitCompareArrows", "createLucideIcon"], "sources": ["D:\\ASL\\training-frontend\\node_modules\\lucide-react\\src\\icons\\git-compare-arrows.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '5', cy: '6', r: '3', key: '1qnov2' }],\n  ['path', { d: 'M12 6h5a2 2 0 0 1 2 2v7', key: '1yj91y' }],\n  ['path', { d: 'm15 9-3-3 3-3', key: '1lwv8l' }],\n  ['circle', { cx: '19', cy: '18', r: '3', key: '1qljk2' }],\n  ['path', { d: 'M12 18H7a2 2 0 0 1-2-2V9', key: '16sdep' }],\n  ['path', { d: 'm9 15 3 3-3 3', key: '1m3kbl' }],\n];\n\n/**\n * @component @name GitCompareArrows\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSI1IiBjeT0iNiIgcj0iMyIgLz4KICA8cGF0aCBkPSJNMTIgNmg1YTIgMiAwIDAgMSAyIDJ2NyIgLz4KICA8cGF0aCBkPSJtMTUgOS0zLTMgMy0zIiAvPgogIDxjaXJjbGUgY3g9IjE5IiBjeT0iMTgiIHI9IjMiIC8+CiAgPHBhdGggZD0iTTEyIDE4SDdhMiAyIDAgMCAxLTItMlY5IiAvPgogIDxwYXRoIGQ9Im05IDE1IDMgMy0zIDMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/git-compare-arrows\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst GitCompareArrows = createLucideIcon('git-compare-arrows', __iconNode);\n\nexport default GitCompareArrows;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAKC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAEC,CAAA,EAAG,yBAA2B;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,eAAiB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,QAAU;EAAEH,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,0BAA4B;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAEC,CAAA,EAAG,eAAiB;EAAAD,GAAA,EAAK;AAAU,GAChD;AAaM,MAAAE,gBAAA,GAAmBC,gBAAiB,uBAAsBP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}