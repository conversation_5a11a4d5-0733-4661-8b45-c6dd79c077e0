import { useState, useRef, useCallback, useEffect } from 'react';
import styled from 'styled-components';
import Webcam from 'react-webcam';
import {
  Brain,
  Camera,
  ArrowLeft,
  Play,
  Square,
  Download,
  Eye,
  Target,
  Wifi,
  WifiOff,
  RefreshCw
} from 'lucide-react';
import { useSignDetection } from '../hooks/useSignDetection';
import LevelSelector from './LevelSelector';
import FlashCardTraining from './FlashCardTraining';
import config from '../config';

const TrainingContainer = styled.div`
  min-height: 100vh;
  background: var(--bg-primary);
  position: relative;
  overflow-x: hidden;

  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
  }
`;

const Navigation = styled.nav`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-neural);
  padding: var(--space-4) 0;
  transition: var(--transition-normal);
`;

const NavContainer = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;

  @media (max-width: 768px) {
    padding: 0 var(--space-4);
  }
`;

const Logo = styled.div`
  font-family: var(--font-primary);
  font-size: 1.5rem;
  font-weight: 700;
  background: var(--text-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: flex;
  align-items: center;
  gap: var(--space-3);

  @media (max-width: 768px) {
    font-size: 1.25rem;
    gap: var(--space-2);
  }
`;

const LogoIcon = styled.div`
  width: 40px;
  height: 40px;
  background: var(--bg-neural);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-neural);

  @media (max-width: 768px) {
    width: 36px;
    height: 36px;
  }
`;

const BackButton = styled.button`
  background: rgba(59, 130, 246, 0.15);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.3);
  padding: var(--space-3) var(--space-5);
  border-radius: var(--radius-xl);
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  backdrop-filter: blur(10px);

  &:hover {
    background: rgba(59, 130, 246, 0.25);
    color: #2563eb;
    border-color: rgba(59, 130, 246, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
  }

  @media (max-width: 768px) {
    padding: var(--space-2) var(--space-4);
    font-size: 0.85rem;
  }
`;

const PageTitle = styled.h1`
  font-family: var(--font-primary);
  font-size: 2.5rem;
  font-weight: 700;
  background: var(--text-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
  margin-bottom: var(--space-4);
  letter-spacing: -0.02em;

  @media (max-width: 768px) {
    font-size: 2rem;
  }
`;

const PageSubtitle = styled.p`
  font-size: 1.125rem;
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: var(--space-16);
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;

  @media (max-width: 768px) {
    margin-bottom: var(--space-12);
    font-size: 1rem;
  }
`;

const StatusBadge = styled.div`
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  background: var(--bg-glass);
  border: 1px solid var(--border-neural);
  border-radius: var(--radius-full);
  padding: var(--space-2) var(--space-4);
  margin-bottom: var(--space-8);
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-accent);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-glow);

  @media (max-width: 768px) {
    font-size: 0.8rem;
    padding: var(--space-2) var(--space-3);
  }
`;

const MainContent = styled.main`
  padding: var(--space-20) var(--space-4) var(--space-16);
  max-width: 1200px;
  margin: 0 auto;

  @media (max-width: 768px) {
    padding: var(--space-12) var(--space-3) var(--space-8);
    max-width: 100%;
  }
`;

const TrainingGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-8);
  max-width: 1200px;
  margin: 0 auto var(--space-12);

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  @media (max-width: 768px) {
    gap: var(--space-3);
    margin: 0 auto var(--space-6);
    grid-template-areas: 
      "sign"
      "camera";
  }
`;

const CameraSection = styled.div`
  background: var(--bg-glass);
  border-radius: var(--radius-2xl);
  padding: var(--space-10);
  border: 1px solid var(--border-neural);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;

  @media (max-width: 768px) {
    padding: var(--space-6);
    border-radius: var(--radius-xl);
    grid-area: camera;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--bg-neural);
    transform: scaleX(0);
    transition: var(--transition-normal);
  }

  &:hover {
    box-shadow: var(--shadow-xl), var(--shadow-glow);
    border-color: var(--primary-300);

    &::before {
      transform: scaleX(1);
    }
  }

  @media (max-width: 768px) {
    padding: var(--space-8);
  }
`;

const SectionTitle = styled.h2`
  font-family: var(--font-primary);
  font-size: 1.25rem;
  margin-bottom: var(--space-6);
  color: var(--text-primary);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--space-3);

  @media (max-width: 768px) {
    font-size: 1.125rem;
    margin-bottom: var(--space-4);
  }
`;

const SectionIcon = styled.div`
  width: 36px;
  height: 36px;
  background: var(--bg-neural);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-neural);

  @media (max-width: 768px) {
    width: 32px;
    height: 32px;
  }
`;

const WebcamContainer = styled.div`
  position: relative;
  border-radius: var(--radius-2xl);
  overflow: hidden;
  background: var(--neural-100);
  aspect-ratio: 4/3;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid var(--border-neural);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-lg);

  @media (max-width: 768px) {
    aspect-ratio: 3/4;
    margin-bottom: var(--space-4);
    border-radius: var(--radius-xl);
    border-width: 2px;
  }
`;

const StyledWebcam = styled(Webcam)`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const RecordingOverlay = styled.div`
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  background: ${props => props.isRecording ?
    'var(--error-500)' :
    'var(--neural-600)'
  };
  color: white;
  padding: var(--space-3) var(--space-5);
  border-radius: var(--radius-full);
  font-size: 0.9rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  box-shadow: var(--shadow-lg);
  animation: ${props => props.isRecording ? 'pulse 1.5s infinite' : 'none'};

  @keyframes pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.05); }
  }
`;

const SignSection = styled.div`
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-200);
  }

  @media (max-width: 768px) {
    padding: var(--space-6);
    grid-area: sign;
  }
`;

const SignSelector = styled.select`
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: var(--space-4);
  cursor: pointer;
  transition: var(--transition-normal);

  &:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px var(--primary-100);
  }

  &:hover {
    border-color: var(--primary-300);
  }

  option {
    padding: var(--space-2);
    background: var(--bg-primary);
    color: var(--text-primary);
  }

  @media (max-width: 768px) {
    font-size: 1.125rem;
    padding: var(--space-4);
    margin-bottom: var(--space-3);
  }
`;

const SignDisplay = styled.div`
  width: 300px;
  height: 300px;
  background: var(--primary-50);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-6);
  border: 2px solid var(--primary-200);
  transition: all 0.3s ease;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--radius-xl);
  }

  &:hover {
    transform: scale(1.02);
    border-color: var(--primary-300);
  }

  @media (max-width: 768px) {
    width: 250px;
    height: 250px;
  }
`;

const SignName = styled.h3`
  font-family: var(--font-primary);
  font-size: 1.5rem;
  margin-bottom: var(--space-3);
  color: var(--text-primary);
  font-weight: 700;

  @media (max-width: 768px) {
    font-size: 1.25rem;
  }
`;

const SignDescription = styled.p`
  text-align: center;
  line-height: 1.6;
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-weight: 400;
  max-width: 280px;
`;

const TopControlsSection = styled.div`
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  margin-bottom: var(--space-8);
  padding: var(--space-4);
  background: var(--bg-glass);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-neural);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-lg);

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-6);
    padding: var(--space-3);
  }
`;

const RecordingStatus = styled.div`
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 500;
  background: ${props => props.isRecording ? 'var(--error-50)' : 'var(--success-50)'};
  color: ${props => props.isRecording ? 'var(--error-700)' : 'var(--success-700)'};
  border: 1px solid ${props => props.isRecording ? 'var(--error-200)' : 'var(--success-200)'};
  white-space: nowrap;

  @media (max-width: 768px) {
    font-size: 0.8rem;
    padding: var(--space-1) var(--space-2);
  }
`;

const ControlButton = styled.button`
  background: ${props => props.variant === 'primary'
    ? 'var(--primary-600)'
    : props.variant === 'retry'
    ? 'var(--warning-500)'
    : 'var(--bg-primary)'};
  border: ${props => props.variant === 'primary' || props.variant === 'retry'
    ? 'none'
    : '1px solid var(--border-medium)'};
  color: ${props => props.variant === 'primary' || props.variant === 'retry'
    ? 'white'
    : 'var(--text-primary)'};
  padding: ${props => props.compact
    ? 'var(--space-2) var(--space-4)'
    : 'var(--space-3) var(--space-6)'};
  border-radius: var(--radius-lg);
  cursor: pointer;
  font-size: ${props => props.compact ? '0.8rem' : '0.9rem'};
  font-weight: 600;
  transition: all 0.2s ease;
  min-width: ${props => props.compact ? '120px' : '160px'};
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);

  @media (max-width: 768px) {
    padding: ${props => props.compact
      ? 'var(--space-3) var(--space-5)'
      : 'var(--space-4) var(--space-8)'};
    font-size: ${props => props.compact ? '0.9rem' : '1rem'};
    min-width: ${props => props.compact ? '140px' : '180px'};
    border-radius: var(--radius-xl);
  }
  box-shadow: ${props => props.variant === 'primary' || props.variant === 'retry'
    ? 'var(--shadow-lg)'
    : 'var(--shadow-sm)'};

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.variant === 'primary' || props.variant === 'retry'
      ? 'var(--shadow-xl)'
      : 'var(--shadow-md)'};
    background: ${props => props.variant === 'primary'
      ? 'var(--primary-700)'
      : props.variant === 'retry'
      ? 'var(--warning-600)'
      : 'var(--gray-50)'};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  @media (max-width: 768px) {
    width: 100%;
    max-width: ${props => props.compact ? '200px' : '280px'};
  }
`;

const StatusMessage = styled.div`
  text-align: center;
  margin-top: var(--space-6);
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-lg);
  background: ${props =>
    props.type === 'success' ? 'var(--success-500)' :
    props.type === 'error' ? 'var(--error-500)' :
    'var(--primary-600)'
  };
  color: white;
  font-weight: 500;
  font-size: 0.875rem;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
`;

const RecordingsSection = styled.div`
  margin-top: var(--space-16);
  background: var(--bg-secondary);
  padding: var(--space-12) var(--space-4);
  border-radius: var(--radius-2xl);
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
`;

const RecordingsTitle = styled.h3`
  font-family: var(--font-primary);
  color: var(--text-primary);
  margin-bottom: var(--space-8);
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
`;

const RecordingsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
`;

const RecordingCard = styled.div`
  background: var(--bg-primary);
  padding: var(--space-6);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    border-color: var(--primary-200);
    box-shadow: var(--shadow-lg);
  }
`;

const RecordingTitle = styled.p`
  margin: 0 0 var(--space-2) 0;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1rem;
  font-family: var(--font-primary);
`;

const RecordingTime = styled.p`
  margin: 0 0 var(--space-4) 0;
  font-size: 0.8rem;
  color: var(--text-tertiary);
`;

const DownloadButton = styled.button`
  background: var(--primary-600);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-2) var(--space-4);
  color: white;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin: 0 auto;

  &:hover {
    background: var(--primary-700);
    transform: translateY(-1px);
  }
`;

const PredictionDisplay = styled.div`
  background: var(--bg-glass);
  border: 2px solid ${props => {
    if (props.matched) return 'var(--success-400)';
    if (props.isStale) return 'var(--warning-300)';
    return 'var(--border-light)';
  }};
  border-radius: var(--radius-xl);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
  text-align: center;
  transition: var(--transition-normal);
  backdrop-filter: blur(10px);
  opacity: ${props => props.isStale ? 0.7 : 1};
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;

  ${props => props.matched && `
    background: var(--success-50);
    box-shadow: 0 0 20px var(--success-200);
    animation: pulse 1s ease-in-out;
  `}

  ${props => props.isStale && `
    background: var(--warning-50);
  `}

  @keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
  }

  @media (max-width: 768px) {
    padding: var(--space-3);
    margin-bottom: var(--space-3);
    min-height: 70px;
  }
`;

const PredictionText = styled.div`
  font-size: 1.25rem;
  font-weight: 600;
  color: ${props => {
    if (props.matched) return 'var(--success-700)';
    if (props.isStale) return 'var(--warning-700)';
    return 'var(--text-primary)';
  }};
  margin-bottom: var(--space-2);

  @media (max-width: 768px) {
    font-size: 1.125rem;
  }
`;

const ConfidenceBar = styled.div`
  width: 100%;
  height: 8px;
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-top: var(--space-2);
`;

const ConfidenceFill = styled.div`
  height: 100%;
  background: ${props => {
    if (props.confidence > 0.8) return 'var(--success-500)';
    if (props.confidence > 0.6) return 'var(--warning-500)';
    return 'var(--error-500)';
  }};
  width: ${props => (props.confidence * 100)}%;
  transition: width 0.3s ease;
`;

const ConnectionStatus = styled.div`
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 500;
  background: ${props => props.connected ? 'var(--success-50)' : 'var(--error-50)'};
  color: ${props => props.connected ? 'var(--success-700)' : 'var(--error-700)'};
  border: 1px solid ${props => props.connected ? 'var(--success-200)' : 'var(--error-200)'};
`;

// Sign language data with GIFs (100 signs, model-predictable)
const signLanguageData = {
  "TV": { name: "TV", gif: "https://lifeprint.com/asl101/gifs/t/tv.gif", description: "Sign for TV." },
  "after": { name: "After", gif: "https://lifeprint.com/asl101/gifs/a/after-over-across.gif", description: "Sign for after." },
  "airplane": { name: "Airplane", gif: "https://www.lifeprint.com/asl101/gifs/a/airplane-flying.gif", description: "Sign for airplane." },
  "all": { name: "All", gif: "https://lifeprint.com/asl101/gifs/a/all-whole.gif", description: "Sign for all." },
  "alligator": { name: "Alligator", gif: "https://lifeprint.com/asl101/gifs/a/alligator.gif", description: "Sign for alligator." },
  "animal": { name: "Animal", gif: "https://www.lifeprint.com/asl101/gifs-animated/animal.gif", description: "Sign for animal." },
  "another": { name: "Another", gif: "https://lifeprint.com/asl101/gifs-animated/another.gif", description: "Sign for another." },
  "any": { name: "Any", gif: "https://lifeprint.com/asl101/gifs/a/any.gif", description: "Sign for any." },
  "apple": { name: "Apple", gif: "https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif", description: "Sign for apple." },
  "arm": { name: "Arm", gif: "https://th.bing.com/th/id/OIP.KkJLqfbp6XEHiIe-jOUb2AHaEc?w=251&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for arm." },
  "aunt": { name: "Aunt", gif: "https://th.bing.com/th/id/OIP.Yz5UUZdNTrVWXf72we_N6wHaHa?rs=1&pid=ImgDetMain", description: "Sign for aunt." },
  "awake": { name: "Awake", gif: "https://th.bing.com/th/id/OIP.XcgdjGKBo8LynmiAw-tDCQHaE-?w=235&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for awake." },
  "backyard": { name: "Backyard", gif: "https://lifeprint.com/asl101/gifs/b/backyard.gif", description: "Sign for backyard." },
  "bad": { name: "Bad", gif: "https://media.giphy.com/media/v1.Y2lkPTc5MGI3NjExeThwMm9rZGVkejE2N2RhMWp0NTY3Z2pwNHBqZXFlOTF3ZGd0cG1zZSZlcD12MV9naWZzX3NlYXJjaCZjdD1n/l0MYL34CVEqLK27yU/giphy.gif", description: "Sign for bad." },
  "balloon": { name: "Balloon", gif: "https://media.giphy.com/media/26FL9yfajyobRXJde/giphy.gif", description: "Sign for balloon." },
  "bath": { name: "Bath", gif: "https://media.giphy.com/media/l0MYPjjoeJbZVPmNO/giphy.gif", description: "Sign for bath." },
  "because": { name: "Because", gif: "https://lifeprint.com/asl101/gifs-animated/because.gif", description: "Sign for because." },
  "bed": { name: "Bed", gif: "https://lifeprint.com/asl101/gifs/b/bed-1.gif", description: "Sign for bed." },
  "bedroom": { name: "Bedroom", gif: "https://lifeprint.com/asl101/gifs/b/bedroom.gif", description: "Sign for bedroom." },
  "bee": { name: "Bee", gif: "https://lifeprint.com/asl101/gifs/b/bee.gif", description: "Sign for bee." },
  "before": { name: "Before", gif: "https://th.bing.com/th/id/OIP.0EvzUY4jH2cDCa4nNcRw4wHaE-?w=267&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for before." },
  "beside": { name: "Beside", gif: "https://lifeprint.com/asl101/gifs/b/beside.gif", description: "Sign for beside." },
  "better": { name: "Better", gif: "https://lifeprint.com/asl101/gifs/b/better.gif", description: "Sign for better." },
  "bird": { name: "Bird", gif: "https://lifeprint.com/asl101/gifs/b/bird.gif", description: "Sign for bird." },
  "black": { name: "Black", gif: "https://th.bing.com/th/id/R.********************************?rik=52tGw7%2fGcx2HtwMm9rZGVkejE2N2RhMWp0NTY3Z2pwNHBqZXFlOTF3ZGd0cG1zZSZlcD12MV9naWZzX3NlYXJjaCZjdD1n/l0MYL34CVEqLK27yU/giphy.gif", description: "Sign for black." },
  "blow": { name: "Blow", gif: "https://th.bing.com/th/id/OIP.rJg-otMBtvfj1T1HkSKugwHaEc?w=304&h=182&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for blow." },
  "blue": { name: "Blue", gif: "https://lifeprint.com/asl101/gifs/b/blue-1.gif", description: "Sign for blue." },
  "boat": { name: "Boat", gif: "https://lifeprint.com/asl101/gifs/b/boat-ship.gif", description: "Sign for boat." },
  "book": { name: "Book", gif: "https://media.giphy.com/media/l0MYL43dl4pQEn3uE/giphy.gif", description: "Sign for book." },
  "boy": { name: "Boy", gif: "https://lifeprint.com/asl101/gifs/b/boy.gif", description: "Sign for boy." },
  "brother": { name: "Brother", gif: "https://lifeprint.com/asl101/gifs/b/brother.gif", description: "Sign for brother." },
  "brown": { name: "Brown", gif: "https://lifeprint.com/asl101/gifs/b/brown.gif", description: "Sign for brown." },
  "bug": { name: "Bug", gif: "https://lifeprint.com/asl101/gifs/b/bug.gif", description: "Sign for bug." },
  "bye": { name: "Bye", gif: "https://c.tenor.com/vME77PObDN8AAAAC/asl-bye-asl-goodbye.gif", description: "Sign for bye." },
  "callonphone": { name: "Call on phone", gif: "https://www.lifeprint.com/asl101/gifs/c/call-hearing.gif", description: "Sign for call on phone." },
  "can": { name: "Can", gif: "https://lifeprint.com/asl101/gifs/c/can.gif", description: "Sign for can." },
  "car": { name: "Car", gif: "https://th.bing.com/th/id/OIP.wxw32OaIdqFt8f_ucHVoRgHaEH?w=308&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for car." },
  "carrot": { name: "Carrot", gif: "https://media.giphy.com/media/l0HlDdvqxs1jsRtiU/giphy.gif", description: "Sign for carrot." },
  "cat": { name: "Cat", gif: "https://lifeprint.com/asl101/gifs-animated/cat-02.gif", description: "Sign for cat." },
  "cereal": { name: "Cereal", gif: "https://th.bing.com/th/id/R.********************************?rik=wPMg%2fK1dYTfR%2bw&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fcereal.gif&ehk=RpDS3wWZM4eryawaxA1wAvWwM0EM%2fdGgJkWY2ce1KFs%3d&risl=&pid=ImgRaw&r=0", description: "Sign for cereal." },
  "chair": { name: "Chair", gif: "https://th.bing.com/th/id/OIP.5kr1MkVLnuN2Z9Jkw-0QpAHaE-?w=237&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for chair." },
  "cheek": { name: "Cheek", gif: "https://lifeprint.com/asl101/gifs/c/cheek.gif", description: "Sign for cheek." },
  "child": { name: "Child", gif: "https://lifeprint.com/asl101/gifs/c/child.gif", description: "Sign for child." },
  "chin": { name: "Chin", gif: "https://lifeprint.com/asl101/gifs/c/chin.gif", description: "Sign for chin." },
  "chocolate": { name: "Chocolate", gif: "https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fi.pinimg.com%2foriginals%2f9f%2fa2%2fb5%2f9fa2b5064a72b5e46202d20848f1bf21.gif&ehk=izvOlFp25%2fx5NVTCmqVz0UOnZNOWy%2fAJJtzAhkZ8nTg%3d", description: "Sign for chocolate." },
  "clean": { name: "Clean", gif: "https://media.giphy.com/media/3o7TKoturrdpf5Muwo/giphy.gif", description: "Sign for clean." },
  "close": { name: "Close", gif: "https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia2.giphy.com%2fmedia%2fl4JyZuXNGxS3Yydeo%2fgiphy.gif%3fcid%3d790b7611318eb5b864ad67b3cecb35b9d81240a50d251bb0%26rid%3dgiphy.gif%26ct%3dg&ehk=A6wfp3Afm3rFCPLWSjgQd6JVjmRSBNBlk9vd0jVNgJc%3d", description: "Sign for close." },
  "closet": { name: "Closet", gif: "https://lifeprint.com/asl101/gifs/c/closet.gif", description: "Sign for closet." },
  "cloud": { name: "Cloud", gif: "https://th.bing.com/th/id/OIP.hMO89bV2zwVcIVIa7FOT5QHaEc?rs=1&pid=ImgDetMain", description: "Sign for cloud." },
  "clown": { name: "Clown", gif: "https://th.bing.com/th/id/R.********************************?rik=OPrV3%2b1Zkelr2A&pid=ImgRaw&r=0", description: "Sign for clown." },
  "cow": { name: "Cow", gif: "https://lifeprint.com/asl101/gifs/c/cow.gif", description: "Sign for cow." },
  "cowboy": { name: "Cowboy", gif: "https://lifeprint.com/asl101/gifs/c/cowboy.gif", description: "Sign for cowboy." },
  "cry": { name: "Cry", gif: "https://www.lifeprint.com/asl101/gifs/c/cry-tears.gif", description: "Sign for cry." },
  "cut": { name: "Cut", gif: "https://th.bing.com/th/id/OIP.ZtKu3hlJ6pduArqfgEcyUgHaE-?w=248&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for cut." },
  "cute": { name: "Cute", gif: "https://lifeprint.com/asl101/gifs/c/cute-sugar.gif", description: "Sign for cute." },
  "dad": { name: "Dad", gif: "https://media.giphy.com/media/l0MYQcLKwtl5v6H1S/giphy.gif", description: "Sign for dad." },
  "dance": { name: "Dance", gif: "https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia.giphy.com%2fmedia%2f3o7TKMspYQjQTbOz2U%2fgiphy.gif&ehk=h%2bdBHCxuoOT89ovSy5uTk6MCL9acaBEV6ld9lrVDRF4%3d", description: "Sign for dance." },
  "dirty": { name: "Dirty", gif: "https://th.bing.com/th/id/OIP.wRA7r1OPPUuEoLL4Hds9jAHaHa?rs=1&pid=ImgDetMain", description: "Sign for dirty." },
  "dog": { name: "Dog", gif: "https://th.bing.com/th/id/R.********************************?rik=uVshGsOHXgldoQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fd%2fdog1.gif&ehk=Xnfrvg0xwy1u%2fae9vWdKYMT25%2bF6qoteW2FThCbVrOA%3d&risl=&pid=ImgRaw&r=0", description: "Sign for dog." },
  "doll": { name: "Doll", gif: "https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fwww.lifeprint.com%2fasl101%2fgifs-animated%2fdoll.gif&ehk=hPI0Fzzl9CGOrgQYS2Z53a5YdYgjxYFeOIGghGAEZYU%3d", description: "Sign for doll." },
  "donkey": { name: "Donkey", gif: "https://www.lifeprint.com/asl101/gifs/d/donkey-1h.gif", description: "Sign for donkey." },
  "down": { name: "Down", gif: "https://th.bing.com/th/id/OIP.CZlW6IpZUdgspxVpW6PZ8QHaE-?w=250&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for down." },
  "drawer": { name: "Drawer", gif: "https://th.bing.com/th/id/OIP.8yooqOFFixqki7j28PVpYQHaE-?w=234&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for drawer." },
  "drink": { name: "Drink", gif: "https://www.lifeprint.com/asl101/gifs/d/drink-c.gif", description: "Sign for drink." },
  "drop": { name: "Drop", gif: "https://th.bing.com/th/id/OIP.XQJn0tOccOUmG8OZHz8X9gHaE-?w=247&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for drop." },
  "dry": { name: "Dry", gif: "https://th.bing.com/th/id/OIP.A0oQgM0IGtwZjfz1Caj-AgHaE-?w=268&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for dry." },
  "dryer": { name: "Dryer", gif: "https://lifeprint.com/asl101/gifs/d/dryer.gif", description: "Sign for dryer." },
  "duck": { name: "Duck", gif: "https://th.bing.com/th/id/R.********************************?rik=ZetjiJ3WOhOXrQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fd%2fduck.gif&ehk=STeui62x5lieai0VcyeZkX2t8rILR%2f8GR5F3x2xJ5tw%3d&risl=&pid=ImgRaw&r=0", description: "Sign for duck." },
  "ear": { name: "Ear", gif: "https://lifeprint.com/asl101/signjpegs/e/ears.h3.jpg", description: "Sign for ear." },
  "elephant": { name: "Elephant", gif: "https://lifeprint.com/asl101/gifs-animated/elephant.gif", description: "Sign for elephant." },
  "empty": { name: "Empty", gif: "https://lifeprint.com/images-signs/empty.gif", description: "Sign for empty." },
  "every": { name: "Every", gif: "https://lifeprint.com/asl101/gifs-animated/every.gif", description: "Sign for every." },
  "eye": { name: "Eye", gif: "https://lifeprint.com/asl101/gifs/e/eyes.gif", description: "Sign for eye." },
  "face": { name: "Face", gif: "https://lifeprint.com/asl101/gifs/f/face.gif", description: "Sign for face." },
  "fall": { name: "Fall", gif: "https://lifeprint.com/asl101/gifs/f/fall.gif", description: "Sign for fall." },
  "farm": { name: "Farm", gif: "https://th.bing.com/th/id/R.********************************?rik=IO%2brRd7xNmCQBQ&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2ffarm.gif&ehk=aOO01Vk8fbE84nLfNNnOVL3kUdyWJtLaTEcwePgbP9A%3d&risl=&pid=ImgRaw&r=0", description: "Sign for farm." },
  "fast": { name: "Fast", gif: "https://th.bing.com/th/id/OIP.YX_BqT1FjGm8HeM4k4WFAgAAAA?rs=1&pid=ImgDetMain", description: "Sign for fast." },
  "feet": { name: "Feet", gif: "https://th.bing.com/th/id/OIP.RaYFj5lvSS6NeIna8NtmZQHaE-?w=247&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for feet." },
  "find": { name: "Find", gif: "https://www.lifeprint.com/asl101/gifs/f/find-pick.gif", description: "Sign for find." },
  "fine": { name: "Fine", gif: "https://th.bing.com/th/id/R.********************************?rik=Qpm%2bw3fHTAWj1A&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2ff%2ffine.gif&ehk=mGMZf4l%2bLZMq4atRomNJSvrSjYgFe%2bRVCm1dYLh5J3I%3d&risl=&pid=ImgRaw&r=0", description: "Sign for fine." },
  "finger": { name: "Finger", gif: "https://lifeprint.com/asl101/gifs/f/finger.gif", description: "Sign for finger." },
  "finish": { name: "Finish", gif: "https://th.bing.com/th/id/R.********************************?rik=34j4pW2f3E5TtQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2ff%2ffinish.gif&ehk=xNk24Jbe3t0moSmcmUftmZzCRgHIxsarq3W9E7kGmPM%3d&risl=&pid=ImgRaw&r=0", description: "Sign for finish." },
  "fireman": { name: "Fireman", gif: "https://lifeprint.com/asl101/gifs/f/fireman-c2.gif", description: "Sign for fireman." },
  "first": { name: "First", gif: "https://lifeprint.com/asl101/gifs/f/first.gif", description: "Sign for first." },
  "fish": { name: "Fish", gif: "https://th.bing.com/th/id/OIP.Lzhd7lIIa-V4H3faS1d3mQHaHa?rs=1&pid=ImgDetMain", description: "Sign for fish." },
  "flag": { name: "Flag", gif: "https://th.bing.com/th/id/OIP.3LqQWEnK4TG0lohgQ3G5uAHaE-?w=263&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for flag." },
  "flower": { name: "Flower", gif: "https://media.giphy.com/media/3o7TKGkqPpLUdFiFPy/giphy.gif", description: "Sign for flower." },
  "food": { name: "Food", gif: "https://i.pinimg.com/originals/cc/bb/0c/ccbb0c143db0b51e9947a5966db42fd8.gif", description: "Sign for food." },
  "for": { name: "For", gif: "https://lifeprint.com/asl101/gifs/f/for.gif", description: "Sign for for." },
  "frenchfries": { name: "French Fries", gif: "https://www.lifeprint.com/asl101/gifs/f/french-fries.gif", description: "Sign for french fries." },
  "frog": { name: "Frog", gif: "https://media.giphy.com/media/l0HlKl64lIvTjZ7QA/giphy.gif", description: "Sign for frog." },
  "garbage": { name: "Garbage", gif: "https://th.bing.com/th/id/R.********************************?rik=78iU%2fDx85Ut9fA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fg%2fgarbage.gif&ehk=lafY%2f1y5WEEfr04p6Uq4waDP9iV7bJB5r2k3RYGOhWY%3d&risl=&pid=ImgRaw&r=0", description: "Sign for garbage." },
  "gift": { name: "Gift", gif: "https://www.babysignlanguage.com/signs/gift.gif", description: "Sign for gift." },
  "giraffe": { name: "Giraffe", gif: "https://www.lifeprint.com/asl101/gifs/g/giraffe.gif", description: "Sign for giraffe." },
  "girl": { name: "Girl", gif: "https://th.bing.com/th/id/R.********************************?rik=yDsGUPEaDyeSlA&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fgirl.gif&ehk=zdVxVSayRBDn67vVCpMhUH6UmzUQE8vaY7%2bv8jedvs8%3d&risl=&pid=ImgRaw&r=0", description: "Sign for girl." },
  "give": { name: "Give", gif: "https://www.lifeprint.com/asl101/gifs/g/give-x-two-handed.gif", description: "Sign for give." },
  "glasswindow": { name: "Glass Window", gif: "https://lifeprint.com/asl101/gifs/g/glass.gif", description: "Sign for glass window." },
  "go": { name: "Go", gif: "https://media.giphy.com/media/l3vRdVMMN9VsW5a0w/giphy.gif", description: "Sign for go." },
  "goose": { name: "Goose", gif: "https://www.babysignlanguage.com/signs/goose.gif", description: "Sign for goose." },
  "grandma": { name: "Grandma", gif: "https://www.lifeprint.com/asl101/gifs/g/grandma.gif", description: "Sign for grandma." },
  "grandpa": { name: "Grandpa", gif: "https://th.bing.com/th/id/OIP.yyLPc-rWg0PMNbrwjeQQngHaE-?w=238&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for grandpa." },
  "grass": { name: "Grass", gif: "https://th.bing.com/th/id/R.********************************?rik=uGZNVzt6tISwHA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs-animated%2fgrass.gif&ehk=VKQd9cvBrglo47EhogWYL9rOiZZsEJ7Yqt%2bgJ8N99yQ%3d&risl=&pid=ImgRaw&r=0", description: "Sign for grass." },
  "green": { name: "Green", gif: "https://i.pinimg.com/originals/cb/7f/75/cb7f757ffb79cb3d1309c9ad785e83a1.gif", description: "Sign for green." },
  "gum": { name: "Gum", gif: "https://lifeprint.com/asl101/gifs/g/gum.gif", description: "Sign for gum." },
  "hair": { name: "Hair", gif: "https://www.lifeprint.com/asl101/gifs/h/hair-g-version.gif", description: "Sign for hair." },
  "happy": { name: "Happy", gif: "https://media0.giphy.com/media/3o7TKFpahYpUp4g0N2/giphy.gif?cid=790b7611bca188a92e2e759876756ef62ba95b7cdd337c77&rid=giphy.gif&ct=g", description: "Sign for happy." },
  "hat": { name: "Hat", gif: "https://th.bing.com/th/id/OIP.QyFdqn-0ZqUwNfE6jbzKWAHaE-?w=258&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for hat." },
  "hate": { name: "Hate", gif: "https://media.giphy.com/media/l0MYPiNw8l2LAPJXW/giphy.gif", description: "Sign for hate." },
  "have": { name: "Have", gif: "https://th.bing.com/th/id/R.********************************?rik=q5Ei%2b7oJb7Uzyw&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhave.gif&ehk=H9yIaJxFVejkfHpkhTUipBRv9CW63KBFy6QW5cdbkKw%3d&risl=&pid=ImgRaw&r=0", description: "Sign for have." },
  "haveto": { name: "Have to", gif: "https://lifeprint.com/asl101/gifs/h/have-to.gif", description: "Sign for have to." },
  "head": { name: "Head", gif: "https://th.bing.com/th/id/R.********************************?rik=OcbJdRbpEFsWXQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fsignjpegs%2fh%2fhead-1.jpg&ehk=RPBV45fSrLDEWYiZvRuZs2c1JNrL4WzdqLSNMFIF3Rs%3d&risl=&pid=ImgRaw&r=0", description: "Sign for head." },
  "hear": { name: "Hear", gif: "https://www.lifeprint.com/asl101/signjpegs/h/hear.h4.jpg", description: "Sign for hear." },
  "helicopter": { name: "Helicopter", gif: "https://th.bing.com/th/id/R.********************************?rik=5uhWxBaByliWA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhelicopter.gif&ehk=mwAyT82RBoeYDe7yaHA1jL3%2f30dUksltmv4dF7YGf%2bU%3d&risl=&pid=ImgRaw&r=0", description: "Sign for helicopter." },
  "hello": { name: "Hello", gif: "https://media.giphy.com/media/3o7TKNKOfKlIhbD3gY/giphy.gif", description: "Sign for hello." },
  "hen": { name: "Hen", gif: "https://media0.giphy.com/media/26hisADhtILiu1J3W/giphy.gif?cid=790b76112d512b94e1647afb111c8d77f92ae31f37864f2&rid=giphy.gif&ct=g", description: "Sign for hen." },
  "hesheit": { name: "He/She/It", gif: "https://lifeprint.com/asl101/gifs/h/he-she-it.gif", description: "Sign for he/she/it." },
  "hide": { name: "Hide", gif: "https://lifeprint.com/asl101/gifs/h/hide.gif", description: "Sign for hide." },
  "high": { name: "High", gif: "https://lifeprint.com/asl101/gifs/h/high.gif", description: "Sign for high." },
  "home": { name: "Home", gif: "https://th.bing.com/th/id/R.********************************?rik=%2bnBd%2foQjxnoPfg&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhome-2.gif&ehk=7yD%2f%2fh6JN1Y4D4BOrUjgKW4Jccy2Y4GVYLf%2fzyk%2b5YY%3d&risl=&pid=ImgRaw&r=0", description: "Sign for home." },
  "horse": { name: "Horse", gif: "https://media.giphy.com/media/l0HlM5HffraiQaHUk/giphy.gif", description: "Sign for horse." },
  "hot": { name: "Hot", gif: "https://media.giphy.com/media/3o6Zt99k5aDok347bG/giphy.gif", description: "Sign for hot." },
  "hungry": { name: "Hungry", gif: "https://media.giphy.com/media/l3vR0xkdFEz4tnfTq/giphy.gif", description: "Sign for hungry." },
  "icecream": { name: "Ice Cream", gif: "https://media.giphy.com/media/3o7TKp6yVibVMhBSLu/giphy.gif", description: "Sign for ice cream." },
  "if": { name: "If", gif: "https://lifeprint.com/asl101/gifs/i/if.gif", description: "Sign for if." },
  "into": { name: "Into", gif: "https://lifeprint.com/asl101/gifs/i/into.gif", description: "Sign for into." },
  "jacket": { name: "Jacket", gif: "https://www.lifeprint.com/asl101/gifs/c/coat.gif", description: "Sign for jacket." },
  "jeans": { name: "Jeans", gif: "https://lifeprint.com/asl101/gifs/j/jeans.gif", description: "Sign for jeans." },
  "jump": { name: "Jump", gif: "https://lifeprint.com/asl101/gifs-animated/jump.gif", description: "Sign for jump." },
  "kiss": { name: "Kiss", gif: "https://i.gifer.com/PxGY.gif", description: "Sign for kiss." },
  "kitty": { name: "Kitty", gif: "https://lifeprint.com/asl101/gifs-animated/cat-02.gif", description: "Sign for kitty." },
  "lamp": { name: "Lamp", gif: "https://lifeprint.com/asl101/gifs/l/lamp.gif", description: "Sign for lamp." },
  "later": { name: "Later", gif: "https://media3.giphy.com/media/l0MYHTyMzMRcikIxi/giphy.gif?cid=790b761128cd39f9baa06dbeb4e099d13e3516763d5f0952&rid=giphy.gif&ct=g", description: "Sign for later." },
  "like": { name: "Like", gif: "https://lifeprint.com/asl101/gifs/l/like.gif", description: "Sign for like." },
  "lion": { name: "Lion", gif: "https://th.bing.com/th/id/OIP.8sDkvbXdMKVmCNlDV79WpAHaE-?w=247&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for lion." },
  "lips": { name: "Lips", gif: "https://lifeprint.com/asl101/gifs/l/lips.gif", description: "Sign for lips." },
  "listen": { name: "Listen", gif: "https://th.bing.com/th/id/OIP.VjsXAad6abRwkCla83kbZQHaEc?w=284&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for listen." },
  "look": { name: "Look", gif: "https://th.bing.com/th/id/R.********************************?rik=pYhzip7LqNs7qw&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fl%2flook-at-1.gif&ehk=rFJ7dBrMGFDK0nHLzrOPAzROVE7yqyDEcb%2btLqKqYOI%3d&risl=&pid=ImgRaw&r=0", description: "Sign for look." },
  "loud": { name: "Loud", gif: "https://lifeprint.com/asl101/gifs-animated/loud.gif", description: "Sign for loud." },
  "mad": { name: "Mad", gif: "https://lifeprint.com/asl101/gifs/m/mad.gif", description: "Sign for mad." },
  "make": { name: "Make", gif: "https://th.bing.com/th/id/OIP.CPz7T2bH107Tu-DBnHvatAHaEc?w=313&h=188&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for make." },
  "man": { name: "Man", gif: "https://lifeprint.com/asl101/gifs/m/man.gif", description: "Sign for man." },
  "many": { name: "Many", gif: "https://lifeprint.com/asl101/gifs/m/many.gif", description: "Sign for many." },
  "milk": { name: "Milk", gif: "https://lifeprint.com/asl101/gifs/m/milk.gif", description: "Sign for milk." },
  "minemy": { name: "Mine/My", gif: "https://th.bing.com/th/id/OIP.VBkNZsR_pK7KUoCNiWYMdgHaEc?w=288&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for mine/my." },
  "mitten": { name: "Mitten", gif: "https://lifeprint.com/asl101/gifs-animated/mittens.gif", description: "Sign for mitten." },
  "mom": { name: "Mom", gif: "https://lifeprint.com/asl101/gifs/m/mom.gif", description: "Sign for mom." },
  "moon": { name: "Moon", gif: "https://th.bing.com/th/id/R.********************************?rik=XbVhBJtkANrG9g&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fm%2fmoon.gif&ehk=YSDvFeUSTa9X1BEJhDjdnLC4c7zWn8z7Hj%2fMkkLUyFE%3d&risl=&pid=ImgRaw&r=0", description: "Sign for moon." },
  "morning": { name: "Morning", gif: "https://media0.giphy.com/media/3o6ZtrcJ9GCXGGw0ww/source.gif", description: "Sign for morning." },
  "mouse": { name: "Mouse", gif: "https://lifeprint.com/asl101/gifs/m/mouse.gif", description: "Sign for mouse." },
  "mouth": { name: "Mouth", gif: "https://lifeprint.com/asl101/gifs-animated/mouth.gif", description: "Sign for mouth." },
  "nap": { name: "Nap", gif: "https://lifeprint.com/asl101/gifs/n/nap.gif", description: "Sign for nap." },
  "napkin": { name: "Napkin", gif: "https://lifeprint.com/asl101/gifs/n/napkin.gif", description: "Sign for napkin." },
  "night": { name: "Night", gif: "https://lifeprint.com/asl101/gifs/n/night.gif", description: "Sign for night." },
  "no": { name: "No", gif: "https://lifeprint.com/asl101/gifs/n/no-2-movement.gif", description: "Sign for no." },
  "noisy": { name: "Noisy", gif: "https://lifeprint.com/asl101/gifs/n/noisy.gif", description: "Sign for noisy." },
  "nose": { name: "Nose", gif: "https://lifeprint.com/asl101/signjpegs/n/nose.h1.jpg", description: "Sign for nose." },
  "not": { name: "Not", gif: "https://th.bing.com/th/id/R.********************************?rik=6%2bbZ2jRA%2famQ4Q&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fn%2fnot-negative.gif&ehk=%2bppuO9P0%2fpdzrrdNO4FXpxdIGs8jgY%2fj%2b1ZCwdbDWO4%3d&risl=&pid=ImgRaw&r=0", description: "Sign for not." },
  "now": { name: "Now", gif: "https://lifeprint.com/asl101/gifs/n/now.gif", description: "Sign for now." },
  "nuts": { name: "Nuts", gif: "https://th.bing.com/th/id/OIP.wRnQjn9j2vfFfzAnRR205QHaE-?w=276&h=185&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for nuts." },
  "old": { name: "Old", gif: "https://lifeprint.com/asl101/gifs/o/old.gif", description: "Sign for old." },
  "on": { name: "On", gif: "https://lifeprint.com/asl101/gifs/o/on-onto.gif", description: "Sign for on." },
  "open": { name: "Open", gif: "https://th.bing.com/th/id/OIP.BeMiGXQFuYk_6ZrgG3iqzQHaE-?w=264&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for open." },
  "orange": { name: "Orange", gif: "https://lifeprint.com/asl101/gifs/o/orange.gif", description: "Sign for orange." },
  "outside": { name: "Outside", gif: "https://lifeprint.com/asl101/gifs/o/outside.gif", description: "Sign for outside." },
  "owie": { name: "Owie", gif: "https://lifeprint.com/asl101/gifs/o/owie.gif", description: "Sign for owie." },
  "owl": { name: "Owl", gif: "https://lifeprint.com/asl101/gifs/o/owl.gif", description: "Sign for owl." },
  "pajamas": { name: "Pajamas", gif: "https://lifeprint.com/asl101/gifs/p/pajamas.gif", description: "Sign for pajamas." },
  "pen": { name: "Pen", gif: "https://lifeprint.com/asl101/gifs/p/pen.gif", description: "Sign for pen." },
  "pencil": { name: "Pencil", gif: "https://lifeprint.com/asl101/gifs/p/pencil-2.gif", description: "Sign for pencil." },
  "penny": { name: "Penny", gif: "https://lifeprint.com/asl101/gifs/p/penny.gif", description: "Sign for penny." },
  "person": { name: "Person", gif: "https://lifeprint.com/asl101/gifs/p/person.gif", description: "Sign for person." },
  "pig": { name: "Pig", gif: "https://lifeprint.com/asl101/gifs/p/pig.gif", description: "Sign for pig." },
  "pizza": { name: "Pizza", gif: "https://lifeprint.com/asl101/gifs/p/pizza.gif", description: "Sign for pizza." },
  "please": { name: "Please", gif: "https://lifeprint.com/asl101/gifs-animated/pleasecloseup.gif", description: "Sign for please." },
  "police": { name: "Police", gif: "https://th.bing.com/th/id/R.********************************?rik=icjjfUg15cqgLw&pid=ImgRaw&r=0", description: "Sign for police." },
  "pool": { name: "Pool", gif: "https://th.bing.com/th/id/OIP.dhcMKyW2psDcA5uwsRaRagHaEc?w=276&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for pool." },
  "potty": { name: "Potty", gif: "https://th.bing.com/th/id/OIP.YcNMUjCg6f95xdgN5rnenwHaE-?w=264&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for potty." },
  "pretend": { name: "Pretend", gif: "https://lifeprint.com/asl101/gifs/p/pretend.gif", description: "Sign for pretend." },
  "pretty": { name: "Pretty", gif: "https://lifeprint.com/asl101/gifs/b/beautiful.gif", description: "Sign for pretty." },
  "puppy": { name: "Puppy", gif: "https://lifeprint.com/asl101/gifs/p/puppy.gif", description: "Sign for puppy." },
  "puzzle": { name: "Puzzle", gif: "https://res.cloudinary.com/spiralyze/image/upload/f_auto,w_auto/BabySignLanguage/DictionaryPages/puzzle.svg", description: "Sign for puzzle." },
  "quiet": { name: "Quiet", gif: "https://lifeprint.com/asl101/gifs-animated/quiet-03.gif", description: "Sign for quiet." },
  "radio": { name: "Radio", gif: "https://i.pinimg.com/originals/6d/5e/5e/6d5e5e2f78f80e9006293df853a2ba3b.gif", description: "Sign for radio." },
  "rain": { name: "Rain", gif: "https://lifeprint.com/asl101/gifs/r/rain.gif", description: "Sign for rain." },
  "read": { name: "Read", gif: "https://lifeprint.com/asl101/gifs/r/read.gif", description: "Sign for read." },
  "red": { name: "Red", gif: "https://lifeprint.com/asl101/gifs-animated/red.gif", description: "Sign for red." },
  "refrigerator": { name: "Refrigerator", gif: "https://lifeprint.com/asl101/gifs/r/refrigerator-r-e-f.gif", description: "Sign for refrigerator." },
  "ride": { name: "Ride", gif: "https://lifeprint.com/asl101/gifs/r/ride.gif", description: "Sign for ride." },
  "room": { name: "Room", gif: "https://lifeprint.com/asl101/gifs/r/room-box.gif", description: "Sign for room." },
  "sad": { name: "Sad", gif: "https://lifeprint.com/asl101/gifs/s/sad.gif", description: "Sign for sad." },
  "same": { name: "Same", gif: "https://lifeprint.com/asl101/gifs/s/same-similar.gif", description: "Sign for same." },
  "say": { name: "Say", gif: "https://asl.signlanguage.io/words/say/say-in-asl-a0a5e00000a44k0.jpg", description: "Sign for say." },
  "scissors": { name: "Scissors", gif: "https://i.makeagif.com/media/4-17-2023/pl4M4F.gif", description: "Sign for scissors." },
  "see": { name: "See", gif: "https://lifeprint.com/asl101/gifs/l/look-at-2.gif", description: "Sign for see." },
  "shhh": { name: "Shhh", gif: "https://lifeprint.com/asl101/signjpegs/s/shhh.jpg", description: "Sign for shhh." },
  "shirt": { name: "Shirt", gif: "https://lifeprint.com/asl101/gifs/s/shirt-volunteer.gif", description: "Sign for shirt." },
  "shoe": { name: "Shoe", gif: "https://media.giphy.com/media/3o7TKC4StpZKa6d2y4/giphy.gif", description: "Sign for shoe." },
  "shower": { name: "Shower", gif: "https://lifeprint.com/asl101/gifs/s/shower.gif", description: "Sign for shower." },
  "sick": { name: "Sick", gif: "https://lifeprint.com/asl101/gifs/s/sick.gif", description: "Sign for sick." },
  "sleep": { name: "Sleep", gif: "https://media4.giphy.com/media/3o7TKnRuBdakLslcaI/200.gif?cid=790b76110d8f185a9713f36dd65a0df801576e01b403c95c&rid=200.gif&ct=g", description: "Sign for sleep." },
  "sleepy": { name: "Sleepy", gif: "https://th.bing.com/th/id/R.********************************?rik=zdWvzvABcDHTdw&riu=http%3a%2f%2fwww.lifeprint.com%2fasl101%2fgifs-animated%2fsleepy.gif&ehk=zLqDFJMAs2nqG02RbbR6mEMvux4h85JGzls4uwgrePQ%3d&risl=&pid=ImgRaw&r=0", description: "Sign for sleepy." },
  "smile": { name: "Smile", gif: "https://th.bing.com/th/id/OIP.dpce-bMAh-1jorUrPQFW4AHaE-?w=263&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for smile." },
  "snack": { name: "Snack", gif: "https://media.giphy.com/media/26ybw1E1GTKzLuKDS/giphy.gif", description: "Sign for snack." },
  "snow": { name: "Snow", gif: "https://lifeprint.com/asl101/gifs/s/snow.gif", description: "Sign for snow." },
  "stairs": { name: "Stairs", gif: "https://th.bing.com/th/id/OIP.8BtYhPXXDQHRqodMyyy3HgHaEc?w=288&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for stairs." },
  "stay": { name: "Stay", gif: "https://i.pinimg.com/originals/f5/29/8e/f5298eaa46b91cd6de2a32bd76aadffc.gif", description: "Sign for stay." },
  "sticky": { name: "Sticky", gif: "https://th.bing.com/th/id/OIP.fffIgrX_DBAjxGMkskvTvQHaE-?w=240&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for sticky." },
  "store": { name: "Store", gif: "https://th.bing.com/th/id/R.********************************?rik=x7oUPJGckc7QDg&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fstore.gif&ehk=P7beooAyFUst%2bbVtqIqINeQGP0%2bIUlNSPXc1Du5zWfQ%3d&risl=&pid=ImgRaw&r=0", description: "Sign for store." },
  "story": { name: "Story", gif: "https://lifeprint.com/asl101/gifs/s/story.gif", description: "Sign for story." },
  "stuck": { name: "Stuck", gif: "https://lifeprint.com/asl101/signjpegs/s/stuck.2.jpg", description: "Sign for stuck." },
  "sun": { name: "Sun", gif: "https://media.giphy.com/media/3o6Zt7merN2zxEtNRK/giphy.gif", description: "Sign for sun." },
  "table": { name: "Table", gif: "https://lifeprint.com/asl101/gifs/t/table.gif", description: "Sign for table." },
  "talk": { name: "Talk", gif: "https://lifeprint.com/asl101/gifs/t/talk.gif", description: "Sign for talk." },
  "taste": { name: "Taste", gif: "https://lifeprint.com/asl101/gifs/t/taste.gif", description: "Sign for taste." },
  "thankyou": { name: "Thank You", gif: "https://lifeprint.com/asl101/gifs/t/thank-you.gif", description: "Sign for thank you." },
  "that": { name: "That", gif: "https://i.ytimg.com/vi/81Wr75AFDnQ/maxresdefault.jpg", description: "Sign for that." },
  "there": { name: "There", gif: "https://lifeprint.com/asl101/gifs-animated/there.gif", description: "Sign for there." },
  "think": { name: "Think", gif: "https://lifeprint.com/asl101/gifs/t/think.gif", description: "Sign for think." },
  "thirsty": { name: "Thirsty", gif: "https://media.giphy.com/media/l3vR0sYheBulL1P7W/giphy.gif", description: "Sign for thirsty." },
  "tiger": { name: "Tiger", gif: "https://lifeprint.com/asl101/gifs/t/tiger.gif", description: "Sign for tiger." },
  "time": { name: "Time", gif: "https://lifeprint.com/asl101/gifs/t/time-1.gif", description: "Sign for time." },
  "tomorrow": { name: "Tomorrow", gif: "https://lifeprint.com/asl101/gifs/t/tomorrow.gif", description: "Sign for tomorrow." },
  "tongue": { name: "Tongue", gif: "https://th.bing.com/th/id/R.********************************?rik=ZJJ2Ixdj0l0b5A&riu=http%3a%2f%2fwww.aslsearch.com%2fsigns%2fimages%2ftongue.jpg&ehk=MxZVUjfqPa3klIauPGpReg%2fYgnJUyIjlxOOvCYYG0hc%3d&risl=&pid=ImgRaw&r=0", description: "Sign for tongue." },
  "tooth": { name: "Tooth", gif: "https://th.bing.com/th/id/R.********************************?rik=ZF%2fsFUXvt5czGA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fsignjpegs%2ft%2fteeth1.jpg&ehk=vI5eDlD4HZWXhK1PQOQz4nA5e6oguHgeXqDo%2fcdcWg4%3d&risl=&pid=ImgRaw&r=0", description: "Sign for tooth." },
  "toothbrush": { name: "Toothbrush", gif: "https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia.giphy.com%2fmedia%2fl3vR0Rq2HVL2KHLUI%2fgiphy.gif&ehk=eC0Sq9sHjrrOrkyJvOogQbXVkTOL5OPCeyVymejL0RU%3d", description: "Sign for toothbrush." },
  "touch": { name: "Touch", gif: "https://th.bing.com/th/id/OIP.imGRfqjCtcHhof6Lc_0QJQHaE-?w=230&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7", description: "Sign for touch." },
  "toy": { name: "Toy", gif: "https://lifeprint.com/asl101/gifs-animated/play-02.gif", description: "Sign for toy." },
  "tree": { name: "Tree", gif: "https://lifeprint.com/asl101/gifs-animated/tree.gif", description: "Sign for tree." },
  "uncle": { name: "Uncle", gif: "https://lifeprint.com/asl101/gifs/u/uncle.gif", description: "Sign for uncle." },
  "underwear": { name: "Underwear", gif: "https://th.bing.com/th/id/OIP.c8g9T_lOhbZWRvKAA12J8wHaEO?pid=ImgDet&w=310&h=177&rs=1", description: "Sign for underwear." },
  "up": { name: "Up", gif: "https://www.babysignlanguage.com/signs/up.gif", description: "Sign for up." },
  "vacuum": { name: "Vacuum", gif: "https://www.babysignlanguage.com/signs/vacuum.gif", description: "Sign for vacuum." },
  "wait": { name: "Wait", gif: "https://lifeprint.com/asl101/gifs/w/wait.gif", description: "Sign for wait." },
  "wake": { name: "Wake", gif: "https://lifeprint.com/asl101/gifs/w/wake-up.gif", description: "Sign for wake." },
  "water": { name: "Water", gif: "https://lifeprint.com/asl101/gifs/w/water-2.gif", description: "Sign for water." },
  "wet": { name: "Wet", gif: "https://www.babysignlanguage.com/signs/wet.gif", description: "Sign for wet." },
  "weus": { name: "We/Us", gif: "https://lifeprint.com/asl101/gifs/w/we-us.gif", description: "Sign for we/us." },
  "where": { name: "Where", gif: "https://lifeprint.com/asl101/gifs/w/where.gif", description: "Sign for where." },
  "white": { name: "White", gif: "https://lifeprint.com/asl101/gifs/w/white.gif", description: "Sign for white." },
  "who": { name: "Who", gif: "https://lifeprint.com/asl101/gifs/w/who.gif", description: "Sign for who." },
  "why": { name: "Why", gif: "https://lifeprint.com/asl101/gifs/w/why.gif", description: "Sign for why." },
  "will": { name: "Will", gif: "https://lifeprint.com/asl101/gifs/f/future.gif", description: "Sign for will." },
  "wolf": { name: "Wolf", gif: "https://lifeprint.com/asl101/gifs/w/wolf-side-view.gif", description: "Sign for wolf." },
  "yellow": { name: "Yellow", gif: "https://lifeprint.com/asl101/gifs/y/yellow.gif", description: "Sign for yellow." },
  "yes": { name: "Yes", gif: "https://media.tenor.com/oYIirlyIih0AAAAC/yes-asl.gif", description: "Sign for yes." },
  "yesterday": { name: "Yesterday", gif: "https://lifeprint.com/asl101/gifs/y/yesterday.gif", description: "Sign for yesterday." },
  "yourself": { name: "Yourself", gif: "https://www.lifeprint.com/asl101/gifs/s/self-myself.gif", description: "Sign for yourself." },
  "yucky": { name: "Yucky", gif: "https://i.pinimg.com/originals/7f/66/7f/7f667f7eeb92c994829dcaf52c5bcf2d.gif", description: "Sign for yucky." },
  "zebra": { name: "Zebra", gif: "https://lifeprint.com/asl101/gifs/z/zebra-stripes-two-hands.gif", description: "Sign for zebra." },
  "zipper": { name: "Zipper", gif: "https://th.bing.com/th/id/R.********************************?rik=qPRTVGd2SzUBxw&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fzipper.gif&ehk=IGx68sSokNwU21zu3Z2D%2blmeehKYxpSNhX2VnrvQqYE%3d&risl=&pid=ImgRaw&r=0", description: "Sign for zipper." }
};

const TrainingPage = ({ onBackToHome }) => {
  // New flash card system state
  const [currentView, setCurrentView] = useState('levels'); // 'levels' or 'training'
  const [selectedLevel, setSelectedLevel] = useState(null);
  const [userProgress, setUserProgress] = useState(() => {
    // Load progress from localStorage
    const saved = localStorage.getItem('asl-training-progress');
    return saved ? JSON.parse(saved) : {};
  });

  // Legacy state (keeping for backward compatibility during transition)
  const [currentSign, setCurrentSign] = useState('hello');
  const [status, setStatus] = useState('');
  const [isCapturing, setIsCapturing] = useState(false);
  const [lastRecordingStatus, setLastRecordingStatus] = useState('');
  // eslint-disable-next-line no-unused-vars
  const [recordedVideos, setRecordedVideos] = useState([]);
  const [imgError, setImgError] = useState(false);

  const webcamRef = useRef(null);
  const autoRecordTimeoutRef = useRef(null);
  const matchCountRef = useRef(0);
  const [cameraError, setCameraError] = useState(null);
  const [cameraPermission, setCameraPermission] = useState('prompt');
  const [videoConstraints, setVideoConstraints] = useState({
    width: 640,
    height: 480,
    facingMode: "user",
    frameRate: { ideal: 30, max: 30 },
    aspectRatio: 4/3
  });

  // Use sign detection hook
  const {
    isConnected,
    prediction,
    isAIRecording,
    recordingStatus,
    signMatched,
    targetSign,
    startRecording: startAIRecording,
    stopRecording: stopAIRecording,
    startFrameCapture,
    retryConnection
  } = useSignDetection();

  // Detect Chrome browser
  const isChrome = useCallback(() => {
    return /Chrome/.test(navigator.userAgent) && /Google/.test(navigator.vendor || '');
  }, []);

  // Chrome-specific camera permission and initialization
  const checkCameraPermission = useCallback(async () => {
    try {
      if (navigator.permissions && navigator.permissions.query) {
        const permission = await navigator.permissions.query({ name: 'camera' });
        setCameraPermission(permission.state);

        permission.onchange = () => {
          setCameraPermission(permission.state);
        };
      }
    } catch (error) {
      console.warn('Permission API not supported:', error);
    }
  }, []);

  const initializeCamera = useCallback(async () => {
    try {
      setCameraError(null);

      // Check if getUserMedia is available
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Camera API not supported in this browser');
      }

      // Chrome-specific: Try multiple constraint configurations
      const constraintOptions = [
        // Primary constraints
        {
          video: {
            width: { ideal: 640, max: 1280 },
            height: { ideal: 480, max: 720 },
            facingMode: "user",
            frameRate: { ideal: 30, max: 30 }
          }
        },
        // Fallback 1: Simpler constraints
        {
          video: {
            width: 640,
            height: 480,
            facingMode: "user"
          }
        },
        // Fallback 2: Basic constraints
        {
          video: {
            facingMode: "user"
          }
        },
        // Fallback 3: Minimal constraints
        {
          video: true
        }
      ];

      let stream = null;
      let usedConstraints = null;

      for (const constraints of constraintOptions) {
        try {
          stream = await navigator.mediaDevices.getUserMedia(constraints);
          usedConstraints = constraints.video;
          break;
        } catch (err) {
          console.warn('Constraint failed, trying next:', err.message);
          continue;
        }
      }

      if (!stream) {
        throw new Error('Unable to access camera with any constraints');
      }

      // Update video constraints for the webcam component
      if (usedConstraints && typeof usedConstraints === 'object') {
        setVideoConstraints(usedConstraints);
      }

      // Stop the test stream immediately
      stream.getTracks().forEach(track => track.stop());

      console.log('Camera access verified with constraints:', usedConstraints);
      setStatus('Camera ready');

    } catch (error) {
      console.error('Camera initialization error:', error);
      setCameraError(error.message);

      if (error.name === 'NotAllowedError') {
        setStatus('Camera permission denied. Please allow camera access and refresh the page.');
      } else if (error.name === 'NotFoundError') {
        setStatus('No camera found. Please connect a camera and refresh the page.');
      } else if (error.name === 'NotReadableError') {
        setStatus('Camera is being used by another application. Please close other apps and refresh.');
      } else {
        setStatus(`Camera error: ${error.message}`);
      }
    }
  }, []);

  const handleSignChange = useCallback((event) => {
    setCurrentSign(event.target.value);
    setImgError(false);
  }, []);

  const startDetection = useCallback(() => {
    if (!webcamRef.current) {
      setStatus('Camera not available');
      return;
    }

    setIsCapturing(true);
    startFrameCapture(webcamRef, 100); // Send frame every 100ms
    setStatus('AI detection started');
  }, [startFrameCapture]);

  const startManualRecording = useCallback(() => {
    if (!isConnected) {
      setStatus('AI backend not connected');
      return;
    }

    if (!webcamRef.current) {
      setStatus('Camera not available');
      return;
    }

    if (isAIRecording) {
      setStatus('Already recording...');
      return;
    }

    // Start manual 3-second recording with selected sign name
    const selectedSignName = signLanguageData[currentSign].name;
    setStatus(`🎬 Starting 3-second recording for "${selectedSignName}"...`);
    setLastRecordingStatus(`🎬 Recording "${selectedSignName}"...`);
    startAIRecording(selectedSignName, true); // Pass true to immediately start recording session

    // Auto-stop after 3 seconds
    autoRecordTimeoutRef.current = setTimeout(() => {
      stopAIRecording();
      setStatus(`✅ Recording complete! "${selectedSignName}" saved to recordings folder with landmark data`);
      setLastRecordingStatus(`✅ Recording saved: "${selectedSignName}" (3 seconds)`);
    }, 3000);

    // Also start frame capture if not already started
    if (!isCapturing) {
      startDetection();
    }
  }, [currentSign, isConnected, isCapturing, startDetection, isAIRecording, startAIRecording, stopAIRecording]);

  const stopManualRecording = useCallback(() => {
    // Stop current recording
    if (isAIRecording) {
      stopAIRecording();
      setLastRecordingStatus(`✅ Recording saved: "${signLanguageData[currentSign].name}" (Manual stop)`);
    }
    matchCountRef.current = 0;
    if (autoRecordTimeoutRef.current) {
      clearTimeout(autoRecordTimeoutRef.current);
    }
    setStatus('Manual recording stopped');
  }, [stopAIRecording, isAIRecording, currentSign]);

  const downloadRecording = (video) => {
    const a = document.createElement('a');
    a.href = video.url;
    a.download = `sign_${video.sign}_${video.timestamp}.webm`;
    a.click();
  };

  // Initialize camera on mount
  useEffect(() => {
    checkCameraPermission();

    // Chrome-specific: Add a small delay for better camera initialization
    if (isChrome()) {
      setTimeout(() => {
        initializeCamera();
      }, 500);
    } else {
      initializeCamera();
    }
  }, [checkCameraPermission, initializeCamera, isChrome]);

  // Auto-start detection when connected
  useEffect(() => {
    if (isConnected && webcamRef.current && !isCapturing) {
      startDetection();
    }
  }, [isConnected, startDetection, isCapturing]);

  // Update last recording status when recordingStatus changes
  useEffect(() => {
    if (recordingStatus && recordingStatus.includes('saved')) {
      setLastRecordingStatus(recordingStatus);
    }
  }, [recordingStatus]);

  // Always-on auto-recording logic - records when confidence >= 50%
  useEffect(() => {
    if (!prediction || !isConnected) {
      matchCountRef.current = 0;
      return;
    }

    const predictedSign = prediction.sign.toLowerCase();
    const targetSignLower = signLanguageData[currentSign].name.toLowerCase();
    const confidence = prediction.confidence;

    // Auto-record when sign matches with >= 50% confidence
    if (predictedSign === targetSignLower && confidence >= 0.5) {
      matchCountRef.current += 1;

      // Start recording after 2 consecutive matches to avoid false positives
      if (matchCountRef.current >= 2 && !isAIRecording) {
        setStatus(`🎬 Auto-recording "${signLanguageData[currentSign].name}"... (${Math.round(confidence * 100)}% confidence)`);
        startAIRecording(signLanguageData[currentSign].name, false); // Auto-recording doesn't start session immediately

        // Auto-stop recording after 3 seconds
        autoRecordTimeoutRef.current = setTimeout(() => {
          stopAIRecording();
          setStatus(`✅ Auto-recording complete! "${signLanguageData[currentSign].name}" saved to recordings folder with landmark data`);
          setLastRecordingStatus(`✅ Auto-recording saved: "${signLanguageData[currentSign].name}" (3 seconds)`);
          matchCountRef.current = 0;
        }, 3000);
      }
    } else {
      // Reset match count if sign doesn't match or confidence is too low
      matchCountRef.current = 0;
    }

    return () => {
      if (autoRecordTimeoutRef.current) {
        clearTimeout(autoRecordTimeoutRef.current);
      }
    };
  }, [prediction, currentSign, isAIRecording, startAIRecording, stopAIRecording, isConnected]);

  // New flash card system handlers
  const handleLevelSelect = (level) => {
    setSelectedLevel(level);
    setCurrentView('training');
  };

  const handleBackToLevels = () => {
    setCurrentView('levels');
    setSelectedLevel(null);
  };

  const handleProgressUpdate = (level, completed, total) => {
    const newProgress = {
      ...userProgress,
      [level]: { completed, total }
    };
    setUserProgress(newProgress);
    // Save to localStorage
    localStorage.setItem('asl-training-progress', JSON.stringify(newProgress));
  };

  // Render new flash card system
  if (currentView === 'levels') {
    return (
      <LevelSelector
        currentLevel={selectedLevel}
        userProgress={userProgress}
        onLevelSelect={handleLevelSelect}
      />
    );
  }

  if (currentView === 'training' && selectedLevel) {
    return (
      <FlashCardTraining
        level={selectedLevel}
        onBack={handleBackToLevels}
        userProgress={userProgress}
        onProgressUpdate={handleProgressUpdate}
      />
    );
  }

  // Legacy training page (fallback - can be removed later)
  return (
    <TrainingContainer>
      <Navigation>
        <NavContainer>
          <Logo>
            <LogoIcon>
              <Brain size={24} />
            </LogoIcon>
            ASL Neural
          </Logo>
          <BackButton onClick={onBackToHome}>
            <ArrowLeft size={18} />
            Back to Home
          </BackButton>
        </NavContainer>
      </Navigation>

      <MainContent>
        <div style={{ textAlign: 'center', marginBottom: 'var(--space-12)' }}>
          <StatusBadge>
            <Eye size={16} />
            Neural Vision Active
          </StatusBadge>
        </div>

        <PageTitle>AI Training Session</PageTitle>
        <PageSubtitle>
          Experience real-time neural network analysis as our AI learns from your sign language practice
        </PageSubtitle>

        <TopControlsSection>
          <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-4)', flexWrap: 'wrap', justifyContent: 'center' }}>
            <ControlButton
              variant="primary"
              compact
              onClick={isAIRecording ? stopManualRecording : startManualRecording}
            >
              {isAIRecording ? (
                <>
                  <Square size={16} />
                  Stop Recording
                </>
              ) : (
                <>
                  <Play size={16} />
                  Record 3s Video
                </>
              )}
            </ControlButton>

            {lastRecordingStatus && (
              <RecordingStatus isRecording={isAIRecording}>
                {lastRecordingStatus}
              </RecordingStatus>
            )}

            {!isConnected && (
              <ControlButton
                variant="retry"
                compact
                onClick={retryConnection}
              >
                <RefreshCw size={16} />
                Retry Connection
              </ControlButton>
            )}
          </div>
        </TopControlsSection>

        <TrainingGrid>
          <SignSection>
            <SectionTitle>
              <SectionIcon>
                <Target size={24} />
              </SectionIcon>
              Select a Sign
            </SectionTitle>
            <SignSelector
              value={currentSign}
              onChange={handleSignChange}
              disabled={isAIRecording}
            >
              {Object.keys(signLanguageData).map(signKey => (
                <option key={signKey} value={signKey}>
                  {signLanguageData[signKey].name}
                </option>
              ))}
            </SignSelector>
            <SignDisplay>
              {!imgError ? (
                <img
                  src={signLanguageData[currentSign].gif}
                  alt={signLanguageData[currentSign].name}
                  onError={() => setImgError(true)}
                  style={{ display: imgError ? 'none' : 'block' }}
                />
              ) : (
                <div style={{display: 'flex', fontSize: '3rem', width: '100%', height: '100%', alignItems: 'center', justifyContent: 'center'}}>
                  📷
                </div>
              )}
            </SignDisplay>
            <SignName>{signLanguageData[currentSign].name}</SignName>
            <SignDescription>
              {signLanguageData[currentSign].description}
            </SignDescription>
          </SignSection>

          <CameraSection>
            <SectionTitle>
              <SectionIcon>
                <Camera size={24} />
              </SectionIcon>
              Neural Vision Feed
            </SectionTitle>

            <ConnectionStatus connected={isConnected}>
              {isConnected ? <Wifi size={16} /> : <WifiOff size={16} />}
              {isConnected ? 'AI Connected' : 'AI Disconnected'}
            </ConnectionStatus>

            {cameraError && (
              <div style={{
                marginBottom: 'var(--space-4)',
                textAlign: 'center',
                padding: 'var(--space-3)',
                background: 'var(--error-50)',
                border: '1px solid var(--error-200)',
                borderRadius: 'var(--radius-lg)',
                color: 'var(--error-700)'
              }}>
                <p style={{ margin: '0 0 var(--space-2) 0', fontSize: '0.875rem' }}>
                  Camera Issue: {cameraError}
                </p>
                {isChrome() && (
                  <p style={{ margin: '0 0 var(--space-2) 0', fontSize: '0.8rem', fontStyle: 'italic' }}>
                    Chrome users: Make sure you're using HTTPS and have allowed camera permissions in your browser settings.
                  </p>
                )}
                <button
                  onClick={initializeCamera}
                  style={{
                    background: 'var(--error-600)',
                    color: 'white',
                    border: 'none',
                    padding: 'var(--space-2) var(--space-4)',
                    borderRadius: 'var(--radius-md)',
                    fontSize: '0.8rem',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 'var(--space-2)',
                    margin: '0 auto'
                  }}
                >
                  <RefreshCw size={14} />
                  Retry Camera
                </button>
              </div>
            )}

            {prediction && (
              <PredictionDisplay matched={signMatched} isStale={prediction.isStale}>
                <PredictionText matched={signMatched} isStale={prediction.isStale}>
                  Detected: {prediction.sign}
                  {prediction.isStale && ' (previous)'}
                </PredictionText>
                <ConfidenceBar>
                  <ConfidenceFill confidence={prediction.confidence} />
                </ConfidenceBar>
                <div style={{ fontSize: '0.875rem', marginTop: '8px', color: 'var(--text-secondary)' }}>
                  Confidence: {Math.round(prediction.confidence * 100)}%
                  {signMatched && targetSign && (
                    <span style={{ color: 'var(--success-600)', marginLeft: '8px' }}>
                      ✓ Match! Recording...
                    </span>
                  )}
                  {!isAIRecording && (
                    <div style={{ color: 'var(--primary-600)', marginTop: '4px' }}>
                      🎯 Auto-recording active: Perform "{signLanguageData[currentSign].name}" sign (≥50% confidence)
                      <br />
                      💡 Or click "Record 3 Seconds" for manual recording
                    </div>
                  )}
                </div>
              </PredictionDisplay>
            )}

            {!prediction && (
              <PredictionDisplay>
                <PredictionText>
                  🎯 Ready to detect "{signLanguageData[currentSign].name}"
                </PredictionText>
                <div style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>
                  Auto-recording is active. Perform the sign with ≥50% confidence to trigger recording.
                </div>
              </PredictionDisplay>
            )}
            <WebcamContainer>
              <StyledWebcam
                ref={webcamRef}
                audio={false}
                screenshotFormat="image/jpeg"
                videoConstraints={videoConstraints}
                onUserMedia={() => {
                  console.log('Camera access granted');
                }}
                onUserMediaError={(error) => {
                  console.error('Camera access error:', error);
                  setCameraError(error.message || error.name || 'Unable to access camera');

                  // Chrome-specific error handling
                  if (error.name === 'NotAllowedError') {
                    setStatus('Camera permission denied. Please click the camera icon in the address bar and allow camera access.');
                  } else if (error.name === 'NotFoundError') {
                    setStatus('No camera found. Please connect a camera and refresh the page.');
                  } else if (error.name === 'NotReadableError') {
                    setStatus('Camera is being used by another application. Please close other camera apps and refresh.');
                  } else if (error.name === 'OverconstrainedError') {
                    setStatus('Camera constraints not supported. Trying with different settings...');
                    // Fallback with simpler constraints
                    setTimeout(() => {
                      initializeCamera();
                    }, 1000);
                  } else {
                    setStatus(`Camera error: ${error.message || 'Unable to access camera'}`);
                  }
                }}
                mirrored={true}
                forceScreenshotSourceSize={true}
              />
              <RecordingOverlay isRecording={isAIRecording}>
                {isAIRecording ? (
                  <>
                    <div style={{
                      width: '8px',
                      height: '8px',
                      borderRadius: '50%',
                      backgroundColor: 'white',
                      marginRight: '4px'
                    }} />
                    Recording
                  </>
                ) : (
                  <>
                    <Eye size={16} />
                    Ready
                  </>
                )}
              </RecordingOverlay>
            </WebcamContainer>
          </CameraSection>
        </TrainingGrid>

        {(status || recordingStatus) && (
          <StatusMessage type={(status || recordingStatus).includes('error') ? 'error' : (status || recordingStatus).includes('success') ? 'success' : 'info'}>
            {recordingStatus || status}
          </StatusMessage>
        )}

        {recordedVideos.length > 0 && (
          <RecordingsSection>
            <RecordingsTitle>Your Practice Recordings</RecordingsTitle>
            <RecordingsGrid>
              {recordedVideos.map((video) => (
                <RecordingCard key={video.id}>
                  <RecordingTitle>{video.sign}</RecordingTitle>
                  <RecordingTime>
                    {new Date(video.timestamp).toLocaleString()}
                  </RecordingTime>
                  <DownloadButton onClick={() => downloadRecording(video)}>
                    <Download size={16} />
                    Download
                  </DownloadButton>
                </RecordingCard>
              ))}
            </RecordingsGrid>
          </RecordingsSection>
        )}
      </MainContent>
    </TrainingContainer>
  );
};

export default TrainingPage; 