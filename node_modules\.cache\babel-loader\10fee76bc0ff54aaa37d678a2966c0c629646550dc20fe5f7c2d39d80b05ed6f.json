{"ast": null, "code": "var _jsxFileName = \"D:\\\\ASL\\\\ASL-Training\\\\src\\\\components\\\\AboutPage.js\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { Brain, Users, Award, TrendingUp, ArrowLeft, Cpu, Eye, Globe, Zap } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AboutContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow-x: hidden;\n  \n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: \n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n`;\n_c = AboutContainer;\nconst Navigation = styled.nav`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: var(--bg-glass);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-neural);\n  padding: var(--space-4) 0;\n  transition: var(--transition-normal);\n`;\n_c2 = Navigation;\nconst NavContainer = styled.div`\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  \n  @media (max-width: 768px) {\n    padding: 0 var(--space-4);\n  }\n`;\n_c3 = NavContainer;\nconst Logo = styled.div`\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  \n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n    gap: var(--space-2);\n  }\n`;\n_c4 = Logo;\nconst LogoIcon = styled.div`\n  width: 40px;\n  height: 40px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n  \n  @media (max-width: 768px) {\n    width: 36px;\n    height: 36px;\n  }\n`;\n_c5 = LogoIcon;\nconst BackButton = styled.button`\n  background: rgba(59, 130, 246, 0.15);\n  color: #3b82f6;\n  border: 1px solid rgba(59, 130, 246, 0.3);\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-xl);\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  backdrop-filter: blur(10px);\n  \n  &:hover {\n    background: rgba(59, 130, 246, 0.25);\n    color: #2563eb;\n    border-color: rgba(59, 130, 246, 0.5);\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);\n  }\n\n  &:active {\n    transform: translateY(0);\n    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);\n  }\n`;\n_c6 = BackButton;\nconst MainContent = styled.main`\n  padding: var(--space-24) var(--space-6) var(--space-20);\n  max-width: 1400px;\n  margin: 0 auto;\n  position: relative;\n  z-index: 1;\n  \n  @media (max-width: 768px) {\n    padding: var(--space-20) var(--space-4) var(--space-16);\n  }\n`;\n_c7 = MainContent;\nconst PageTitle = styled.h1`\n  font-family: var(--font-primary);\n  font-size: 3rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  text-align: center;\n  margin-bottom: var(--space-4);\n  letter-spacing: -0.02em;\n  \n  @media (max-width: 768px) {\n    font-size: 2.25rem;\n  }\n`;\n_c8 = PageTitle;\nconst PageSubtitle = styled.p`\n  font-size: 1.25rem;\n  color: var(--text-secondary);\n  text-align: center;\n  margin-bottom: var(--space-16);\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n  \n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    margin-bottom: var(--space-12);\n  }\n`;\n_c9 = PageSubtitle;\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: var(--space-8);\n  margin-bottom: var(--space-20);\n  \n  @media (max-width: 768px) {\n    gap: var(--space-6);\n    margin-bottom: var(--space-16);\n  }\n`;\n_c0 = StatsGrid;\nconst StatCard = styled.div`\n  background: var(--bg-glass);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-8);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-lg);\n  text-align: center;\n  transition: var(--transition-normal);\n  \n  &:hover {\n    transform: translateY(-4px);\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n    border-color: var(--primary-300);\n  }\n`;\n_c1 = StatCard;\nconst StatIcon = styled.div`\n  width: 64px;\n  height: 64px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-xl);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 0 auto var(--space-4);\n  box-shadow: var(--shadow-neural);\n`;\n_c10 = StatIcon;\nconst StatNumber = styled.div`\n  font-size: 2rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin-bottom: var(--space-2);\n  font-family: var(--font-primary);\n`;\n_c11 = StatNumber;\nconst StatLabel = styled.div`\n  font-size: 1rem;\n  color: var(--text-secondary);\n  font-weight: 500;\n`;\n_c12 = StatLabel;\nconst SectionTitle = styled.h2`\n  font-family: var(--font-primary);\n  font-size: 2rem;\n  font-weight: 600;\n  color: var(--text-primary);\n  margin-bottom: var(--space-6);\n  text-align: center;\n\n  @media (max-width: 768px) {\n    font-size: 1.75rem;\n  }\n`;\nconst AboutPage = ({\n  onBackToHome\n}) => {\n  return /*#__PURE__*/_jsxDEV(AboutContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Navigation, {\n      children: /*#__PURE__*/_jsxDEV(NavContainer, {\n        children: [/*#__PURE__*/_jsxDEV(Logo, {\n          children: [/*#__PURE__*/_jsxDEV(LogoIcon, {\n            children: /*#__PURE__*/_jsxDEV(Brain, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), \"ASL Neural\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(BackButton, {\n          onClick: onBackToHome,\n          children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this), \"Back to Home\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n      children: [/*#__PURE__*/_jsxDEV(PageTitle, {\n        children: \"About ASL Neural\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PageSubtitle, {\n        children: \"Pioneering the future of accessibility through advanced artificial intelligence and computer vision technology for sign language education\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsGrid, {\n        children: [/*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatIcon, {\n            children: /*#__PURE__*/_jsxDEV(Users, {\n              size: 28,\n              color: \"white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatNumber, {\n            children: \"100K+\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Trained on videos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatIcon, {\n            children: /*#__PURE__*/_jsxDEV(Cpu, {\n              size: 28,\n              color: \"white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatNumber, {\n            children: \"2.5M\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Neural Patterns\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatIcon, {\n            children: /*#__PURE__*/_jsxDEV(Award, {\n              size: 28,\n              color: \"white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatNumber, {\n            children: \"+88.7%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"AI Accuracy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatIcon, {\n            children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n              size: 28,\n              color: \"white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatNumber, {\n            children: \"15k+\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Users\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 244,\n    columnNumber: 5\n  }, this);\n};\n_c13 = AboutPage;\nexport default AboutPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13;\n$RefreshReg$(_c, \"AboutContainer\");\n$RefreshReg$(_c2, \"Navigation\");\n$RefreshReg$(_c3, \"NavContainer\");\n$RefreshReg$(_c4, \"Logo\");\n$RefreshReg$(_c5, \"LogoIcon\");\n$RefreshReg$(_c6, \"BackButton\");\n$RefreshReg$(_c7, \"MainContent\");\n$RefreshReg$(_c8, \"PageTitle\");\n$RefreshReg$(_c9, \"PageSubtitle\");\n$RefreshReg$(_c0, \"StatsGrid\");\n$RefreshReg$(_c1, \"StatCard\");\n$RefreshReg$(_c10, \"StatIcon\");\n$RefreshReg$(_c11, \"StatNumber\");\n$RefreshReg$(_c12, \"StatLabel\");\n$RefreshReg$(_c13, \"AboutPage\");", "map": {"version": 3, "names": ["React", "styled", "Brain", "Users", "Award", "TrendingUp", "ArrowLeft", "Cpu", "Eye", "Globe", "Zap", "jsxDEV", "_jsxDEV", "AboutC<PERSON>r", "div", "_c", "Navigation", "nav", "_c2", "NavContainer", "_c3", "Logo", "_c4", "LogoIcon", "_c5", "BackButton", "button", "_c6", "MainContent", "main", "_c7", "Page<PERSON><PERSON>le", "h1", "_c8", "PageSubtitle", "p", "_c9", "StatsGrid", "_c0", "StatCard", "_c1", "StatIcon", "_c10", "StatNumber", "_c11", "StatLabel", "_c12", "SectionTitle", "h2", "AboutPage", "onBackToHome", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "color", "_c13", "$RefreshReg$"], "sources": ["D:/ASL/ASL-Training/src/components/AboutPage.js"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport { \n  <PERSON>, \n  Users, \n  Award, \n  TrendingUp, \n  ArrowLeft,\n  Cpu,\n  Eye,\n  Globe,\n  Zap\n} from 'lucide-react';\n\nconst AboutContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow-x: hidden;\n  \n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: \n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n`;\n\nconst Navigation = styled.nav`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: var(--bg-glass);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-neural);\n  padding: var(--space-4) 0;\n  transition: var(--transition-normal);\n`;\n\nconst NavContainer = styled.div`\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  \n  @media (max-width: 768px) {\n    padding: 0 var(--space-4);\n  }\n`;\n\nconst Logo = styled.div`\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  \n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n    gap: var(--space-2);\n  }\n`;\n\nconst LogoIcon = styled.div`\n  width: 40px;\n  height: 40px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n  \n  @media (max-width: 768px) {\n    width: 36px;\n    height: 36px;\n  }\n`;\n\nconst BackButton = styled.button`\n  background: rgba(59, 130, 246, 0.15);\n  color: #3b82f6;\n  border: 1px solid rgba(59, 130, 246, 0.3);\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-xl);\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  backdrop-filter: blur(10px);\n  \n  &:hover {\n    background: rgba(59, 130, 246, 0.25);\n    color: #2563eb;\n    border-color: rgba(59, 130, 246, 0.5);\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);\n  }\n\n  &:active {\n    transform: translateY(0);\n    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);\n  }\n`;\n\nconst MainContent = styled.main`\n  padding: var(--space-24) var(--space-6) var(--space-20);\n  max-width: 1400px;\n  margin: 0 auto;\n  position: relative;\n  z-index: 1;\n  \n  @media (max-width: 768px) {\n    padding: var(--space-20) var(--space-4) var(--space-16);\n  }\n`;\n\nconst PageTitle = styled.h1`\n  font-family: var(--font-primary);\n  font-size: 3rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  text-align: center;\n  margin-bottom: var(--space-4);\n  letter-spacing: -0.02em;\n  \n  @media (max-width: 768px) {\n    font-size: 2.25rem;\n  }\n`;\n\nconst PageSubtitle = styled.p`\n  font-size: 1.25rem;\n  color: var(--text-secondary);\n  text-align: center;\n  margin-bottom: var(--space-16);\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n  \n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    margin-bottom: var(--space-12);\n  }\n`;\n\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: var(--space-8);\n  margin-bottom: var(--space-20);\n  \n  @media (max-width: 768px) {\n    gap: var(--space-6);\n    margin-bottom: var(--space-16);\n  }\n`;\n\nconst StatCard = styled.div`\n  background: var(--bg-glass);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-8);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-lg);\n  text-align: center;\n  transition: var(--transition-normal);\n  \n  &:hover {\n    transform: translateY(-4px);\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n    border-color: var(--primary-300);\n  }\n`;\n\nconst StatIcon = styled.div`\n  width: 64px;\n  height: 64px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-xl);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 0 auto var(--space-4);\n  box-shadow: var(--shadow-neural);\n`;\n\nconst StatNumber = styled.div`\n  font-size: 2rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin-bottom: var(--space-2);\n  font-family: var(--font-primary);\n`;\n\nconst StatLabel = styled.div`\n  font-size: 1rem;\n  color: var(--text-secondary);\n  font-weight: 500;\n`;\n\nconst SectionTitle = styled.h2`\n  font-family: var(--font-primary);\n  font-size: 2rem;\n  font-weight: 600;\n  color: var(--text-primary);\n  margin-bottom: var(--space-6);\n  text-align: center;\n\n  @media (max-width: 768px) {\n    font-size: 1.75rem;\n  }\n`;\n\nconst AboutPage = ({ onBackToHome }) => {\n  return (\n    <AboutContainer>\n      <Navigation>\n        <NavContainer>\n          <Logo>\n            <LogoIcon>\n              <Brain size={24} />\n            </LogoIcon>\n            ASL Neural\n          </Logo>\n          <BackButton onClick={onBackToHome}>\n            <ArrowLeft size={18} />\n            Back to Home\n          </BackButton>\n        </NavContainer>\n      </Navigation>\n\n      <MainContent>\n        <PageTitle>About ASL Neural</PageTitle>\n        <PageSubtitle>\n          Pioneering the future of accessibility through advanced artificial intelligence \n          and computer vision technology for sign language education\n        </PageSubtitle>\n\n        <StatsGrid>\n          <StatCard>\n            <StatIcon>\n              <Users size={28} color=\"white\" />\n            </StatIcon>\n            <StatNumber>100K+</StatNumber>\n            <StatLabel>Trained on videos</StatLabel>\n          </StatCard>\n\n          <StatCard>\n            <StatIcon>\n              <Cpu size={28} color=\"white\" />\n            </StatIcon>\n            <StatNumber>2.5M</StatNumber>\n            <StatLabel>Neural Patterns</StatLabel>\n          </StatCard>\n\n          <StatCard>\n            <StatIcon>\n              <Award size={28} color=\"white\" />\n            </StatIcon>\n            <StatNumber>+88.7%</StatNumber>\n            <StatLabel>AI Accuracy</StatLabel>\n          </StatCard>\n\n          <StatCard>\n            <StatIcon>\n              <TrendingUp size={28} color=\"white\" />\n            </StatIcon>\n            <StatNumber>15k+</StatNumber>\n            <StatLabel>Users</StatLabel>\n          </StatCard>\n        </StatsGrid>\n      </MainContent>\n    </AboutContainer>\n  );\n};\n\nexport default AboutPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SACEC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,UAAU,EACVC,SAAS,EACTC,GAAG,EACHC,GAAG,EACHC,KAAK,EACLC,GAAG,QACE,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,cAAc,GAAGZ,MAAM,CAACa,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAnBIF,cAAc;AAqBpB,MAAMG,UAAU,GAAGf,MAAM,CAACgB,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAXIF,UAAU;AAahB,MAAMG,YAAY,GAAGlB,MAAM,CAACa,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACM,GAAA,GAXID,YAAY;AAalB,MAAME,IAAI,GAAGpB,MAAM,CAACa,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GAhBID,IAAI;AAkBV,MAAME,QAAQ,GAAGtB,MAAM,CAACa,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GAfID,QAAQ;AAiBd,MAAME,UAAU,GAAGxB,MAAM,CAACyB,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GA3BIF,UAAU;AA6BhB,MAAMG,WAAW,GAAG3B,MAAM,CAAC4B,IAAI;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAVIF,WAAW;AAYjB,MAAMG,SAAS,GAAG9B,MAAM,CAAC+B,EAAE;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAfIF,SAAS;AAiBf,MAAMG,YAAY,GAAGjC,MAAM,CAACkC,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAdIF,YAAY;AAgBlB,MAAMG,SAAS,GAAGpC,MAAM,CAACa,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACwB,GAAA,GAVID,SAAS;AAYf,MAAME,QAAQ,GAAGtC,MAAM,CAACa,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC0B,GAAA,GAfID,QAAQ;AAiBd,MAAME,QAAQ,GAAGxC,MAAM,CAACa,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC4B,IAAA,GAVID,QAAQ;AAYd,MAAME,UAAU,GAAG1C,MAAM,CAACa,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC8B,IAAA,GATID,UAAU;AAWhB,MAAME,SAAS,GAAG5C,MAAM,CAACa,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACgC,IAAA,GAJID,SAAS;AAMf,MAAME,YAAY,GAAG9C,MAAM,CAAC+C,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,SAAS,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EACtC,oBACEtC,OAAA,CAACC,cAAc;IAAAsC,QAAA,gBACbvC,OAAA,CAACI,UAAU;MAAAmC,QAAA,eACTvC,OAAA,CAACO,YAAY;QAAAgC,QAAA,gBACXvC,OAAA,CAACS,IAAI;UAAA8B,QAAA,gBACHvC,OAAA,CAACW,QAAQ;YAAA4B,QAAA,eACPvC,OAAA,CAACV,KAAK;cAACkD,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,cAEb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP5C,OAAA,CAACa,UAAU;UAACgC,OAAO,EAAEP,YAAa;UAAAC,QAAA,gBAChCvC,OAAA,CAACN,SAAS;YAAC8C,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEzB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEb5C,OAAA,CAACgB,WAAW;MAAAuB,QAAA,gBACVvC,OAAA,CAACmB,SAAS;QAAAoB,QAAA,EAAC;MAAgB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eACvC5C,OAAA,CAACsB,YAAY;QAAAiB,QAAA,EAAC;MAGd;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eAEf5C,OAAA,CAACyB,SAAS;QAAAc,QAAA,gBACRvC,OAAA,CAAC2B,QAAQ;UAAAY,QAAA,gBACPvC,OAAA,CAAC6B,QAAQ;YAAAU,QAAA,eACPvC,OAAA,CAACT,KAAK;cAACiD,IAAI,EAAE,EAAG;cAACM,KAAK,EAAC;YAAO;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACX5C,OAAA,CAAC+B,UAAU;YAAAQ,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC9B5C,OAAA,CAACiC,SAAS;YAAAM,QAAA,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eAEX5C,OAAA,CAAC2B,QAAQ;UAAAY,QAAA,gBACPvC,OAAA,CAAC6B,QAAQ;YAAAU,QAAA,eACPvC,OAAA,CAACL,GAAG;cAAC6C,IAAI,EAAE,EAAG;cAACM,KAAK,EAAC;YAAO;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACX5C,OAAA,CAAC+B,UAAU;YAAAQ,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC7B5C,OAAA,CAACiC,SAAS;YAAAM,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eAEX5C,OAAA,CAAC2B,QAAQ;UAAAY,QAAA,gBACPvC,OAAA,CAAC6B,QAAQ;YAAAU,QAAA,eACPvC,OAAA,CAACR,KAAK;cAACgD,IAAI,EAAE,EAAG;cAACM,KAAK,EAAC;YAAO;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACX5C,OAAA,CAAC+B,UAAU;YAAAQ,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/B5C,OAAA,CAACiC,SAAS;YAAAM,QAAA,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAEX5C,OAAA,CAAC2B,QAAQ;UAAAY,QAAA,gBACPvC,OAAA,CAAC6B,QAAQ;YAAAU,QAAA,eACPvC,OAAA,CAACP,UAAU;cAAC+C,IAAI,EAAE,EAAG;cAACM,KAAK,EAAC;YAAO;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACX5C,OAAA,CAAC+B,UAAU;YAAAQ,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC7B5C,OAAA,CAACiC,SAAS;YAAAM,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAErB,CAAC;AAACG,IAAA,GA7DIV,SAAS;AA+Df,eAAeA,SAAS;AAAC,IAAAlC,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAa,IAAA;AAAAC,YAAA,CAAA7C,EAAA;AAAA6C,YAAA,CAAA1C,GAAA;AAAA0C,YAAA,CAAAxC,GAAA;AAAAwC,YAAA,CAAAtC,GAAA;AAAAsC,YAAA,CAAApC,GAAA;AAAAoC,YAAA,CAAAjC,GAAA;AAAAiC,YAAA,CAAA9B,GAAA;AAAA8B,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAAxB,GAAA;AAAAwB,YAAA,CAAAtB,GAAA;AAAAsB,YAAA,CAAApB,GAAA;AAAAoB,YAAA,CAAAlB,IAAA;AAAAkB,YAAA,CAAAhB,IAAA;AAAAgB,YAAA,CAAAd,IAAA;AAAAc,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}