{"ast": null, "code": "var _jsxFileName = \"D:\\\\ASL\\\\training-frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport HomePage from './components/HomePage';\nimport TrainingPage from './components/TrainingPage';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppContainer = styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n`;\n_c = AppContainer;\nfunction App() {\n  _s();\n  const [currentPage, setCurrentPage] = useState('home');\n  const navigateToTraining = () => {\n    setCurrentPage('training');\n  };\n  const navigateToHome = () => {\n    setCurrentPage('home');\n  };\n  return /*#__PURE__*/_jsxDEV(AppContainer, {\n    children: currentPage === 'home' ? /*#__PURE__*/_jsxDEV(HomePage, {\n      onStartTraining: navigateToTraining\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(TrainingPage, {\n      onBackToHome: navigateToHome\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"KLlrbvIFn6o4dTsrFf/Szg7G3bM=\");\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppContainer\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "useState", "styled", "HomePage", "TrainingPage", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "App", "_s", "currentPage", "setCurrentPage", "navigateToTraining", "navigateToHome", "children", "onStartTraining", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onBackToHome", "_c2", "$RefreshReg$"], "sources": ["D:/ASL/training-frontend/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport HomePage from './components/HomePage';\nimport TrainingPage from './components/TrainingPage';\nimport './App.css';\n\nconst AppContainer = styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n`;\n\nfunction App() {\n  const [currentPage, setCurrentPage] = useState('home');\n\n  const navigateToTraining = () => {\n    setCurrentPage('training');\n  };\n\n  const navigateToHome = () => {\n    setCurrentPage('home');\n  };\n\n  return (\n    <AppContainer>\n      {currentPage === 'home' ? (\n        <HomePage onStartTraining={navigateToTraining} />\n      ) : (\n        <TrainingPage onBackToHome={navigateToHome} />\n      )}\n    </AppContainer>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,MAAMC,YAAY,GAAGL,MAAM,CAACM,GAAG;AAC/B;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAJIF,YAAY;AAMlB,SAASG,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,MAAM,CAAC;EAEtD,MAAMa,kBAAkB,GAAGA,CAAA,KAAM;IAC/BD,cAAc,CAAC,UAAU,CAAC;EAC5B,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IAC3BF,cAAc,CAAC,MAAM,CAAC;EACxB,CAAC;EAED,oBACEP,OAAA,CAACC,YAAY;IAAAS,QAAA,EACVJ,WAAW,KAAK,MAAM,gBACrBN,OAAA,CAACH,QAAQ;MAACc,eAAe,EAAEH;IAAmB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAEjDf,OAAA,CAACF,YAAY;MAACkB,YAAY,EAAEP;IAAe;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC9C;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAEnB;AAACV,EAAA,CApBQD,GAAG;AAAAa,GAAA,GAAHb,GAAG;AAsBZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAc,GAAA;AAAAC,YAAA,CAAAf,EAAA;AAAAe,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}