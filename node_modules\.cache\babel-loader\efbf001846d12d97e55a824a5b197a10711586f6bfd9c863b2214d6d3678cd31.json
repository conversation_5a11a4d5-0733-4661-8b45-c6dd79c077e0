{"ast": null, "code": "var _jsxFileName = \"D:\\\\ASL\\\\training-frontend\\\\src\\\\components\\\\TrainingPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useCallback } from 'react';\nimport styled from 'styled-components';\nimport Webcam from 'react-webcam';\nimport { Brain, Camera, ArrowLeft, RotateCcw, Play, Square, Download, Zap, Eye, Target } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TrainingContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow-x: hidden;\n`;\n_c = TrainingContainer;\nconst Navigation = styled.nav`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-light);\n  padding: var(--space-4) 0;\n`;\n_c2 = Navigation;\nconst NavContainer = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 var(--space-4);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n`;\n_c3 = NavContainer;\nconst Logo = styled.div`\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: var(--text-primary);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n\n  &::before {\n    content: '🤟';\n    font-size: 1.75rem;\n  }\n`;\n_c4 = Logo;\nconst BackButton = styled.button`\n  background: transparent;\n  color: var(--text-secondary);\n  border: 1px solid var(--border-medium);\n  padding: var(--space-2) var(--space-4);\n  border-radius: var(--radius-lg);\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n\n  &:hover {\n    background: var(--gray-50);\n    color: var(--text-primary);\n    border-color: var(--border-dark);\n  }\n`;\n_c5 = BackButton;\nconst PageTitle = styled.h1`\n  font-family: var(--font-primary);\n  font-size: 2.5rem;\n  font-weight: 700;\n  color: var(--text-primary);\n  text-align: center;\n  margin-bottom: var(--space-2);\n\n  @media (max-width: 768px) {\n    font-size: 2rem;\n  }\n`;\n_c6 = PageTitle;\nconst PageSubtitle = styled.p`\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  text-align: center;\n  margin-bottom: var(--space-12);\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n\n  @media (max-width: 768px) {\n    margin-bottom: var(--space-10);\n  }\n`;\n_c7 = PageSubtitle;\nconst MainContent = styled.main`\n  padding: var(--space-20) var(--space-4) var(--space-16);\n  max-width: 1200px;\n  margin: 0 auto;\n\n  @media (max-width: 768px) {\n    padding: var(--space-16) var(--space-4) var(--space-12);\n  }\n`;\n_c8 = MainContent;\nconst TrainingGrid = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--space-8);\n  max-width: 1200px;\n  margin: 0 auto var(--space-12);\n\n  @media (max-width: 1024px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-6);\n  }\n`;\n_c9 = TrainingGrid;\nconst CameraSection = styled.div`\n  background: var(--bg-primary);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-8);\n  border: 1px solid var(--border-light);\n  box-shadow: var(--shadow-lg);\n  transition: all 0.3s ease;\n\n  &:hover {\n    box-shadow: var(--shadow-xl);\n    border-color: var(--primary-200);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-6);\n  }\n`;\n_c0 = CameraSection;\nconst WebcamContainer = styled.div`\n  position: relative;\n  border-radius: var(--radius-xl);\n  overflow: hidden;\n  background: var(--gray-100);\n  aspect-ratio: 4/3;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 2px solid var(--border-light);\n  margin-bottom: var(--space-4);\n`;\n_c1 = WebcamContainer;\nconst StyledWebcam = styled(Webcam)`\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n`;\n_c10 = StyledWebcam;\nconst RecordingOverlay = styled.div`\n  position: absolute;\n  top: var(--space-4);\n  right: var(--space-4);\n  background: ${props => props.isRecording ? 'var(--error-500)' : 'var(--gray-600)'};\n  color: white;\n  padding: var(--space-2) var(--space-4);\n  border-radius: var(--radius-full);\n  font-size: 0.875rem;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  animation: ${props => props.isRecording ? 'pulse 1.5s infinite' : 'none'};\n\n  @keyframes pulse {\n    0%, 100% { opacity: 1; }\n    50% { opacity: 0.7; }\n  }\n`;\n_c11 = RecordingOverlay;\nconst SignSection = styled.div`\n  background: var(--bg-primary);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-8);\n  border: 1px solid var(--border-light);\n  box-shadow: var(--shadow-lg);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  transition: all 0.3s ease;\n\n  &:hover {\n    box-shadow: var(--shadow-xl);\n    border-color: var(--primary-200);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-6);\n  }\n`;\n_c12 = SignSection;\nconst SignDisplay = styled.div`\n  width: 200px;\n  height: 200px;\n  background: var(--primary-50);\n  border-radius: var(--radius-2xl);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 4rem;\n  margin-bottom: var(--space-6);\n  border: 2px solid var(--primary-200);\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: scale(1.02);\n    border-color: var(--primary-300);\n    background: var(--primary-100);\n  }\n\n  @media (max-width: 768px) {\n    width: 150px;\n    height: 150px;\n    font-size: 3rem;\n  }\n  }\n\n  @media (max-width: 768px) {\n    width: 220px;\n    height: 220px;\n    font-size: 4rem;\n  }\n`;\n_c13 = SignDisplay;\nconst SignName = styled.h3`\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  margin-bottom: var(--space-3);\n  color: var(--text-primary);\n  font-weight: 700;\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n  }\n`;\n_c14 = SignName;\nconst SignDescription = styled.p`\n  text-align: center;\n  line-height: 1.6;\n  color: var(--text-secondary);\n  font-size: 0.9rem;\n  font-weight: 400;\n  max-width: 280px;\n`;\n_c15 = SignDescription;\nconst ControlsSection = styled.div`\n  display: flex;\n  justify-content: center;\n  gap: var(--space-4);\n  margin-top: var(--space-8);\n\n  @media (max-width: 768px) {\n    flex-direction: column;\n    align-items: center;\n    gap: var(--space-3);\n  }\n`;\n_c16 = ControlsSection;\nconst ControlButton = styled.button`\n  background: ${props => props.variant === 'primary' ? 'var(--primary-600)' : 'var(--bg-primary)'};\n  border: ${props => props.variant === 'primary' ? 'none' : '1px solid var(--border-medium)'};\n  color: ${props => props.variant === 'primary' ? 'white' : 'var(--text-primary)'};\n  padding: var(--space-3) var(--space-6);\n  border-radius: var(--radius-lg);\n  cursor: pointer;\n  font-size: 0.9rem;\n  font-weight: 600;\n  transition: all 0.2s ease;\n  min-width: 160px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--space-2);\n  box-shadow: ${props => props.variant === 'primary' ? 'var(--shadow-lg)' : 'var(--shadow-sm)'};\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: ${props => props.variant === 'primary' ? 'var(--shadow-xl)' : 'var(--shadow-md)'};\n    background: ${props => props.variant === 'primary' ? 'var(--primary-700)' : 'var(--gray-50)'};\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none;\n  }\n\n  @media (max-width: 768px) {\n    width: 100%;\n    max-width: 280px;\n  }\n`;\n_c17 = ControlButton;\nconst StatusMessage = styled.div`\n  text-align: center;\n  margin-top: var(--space-6);\n  padding: var(--space-4) var(--space-6);\n  border-radius: var(--radius-lg);\n  background: ${props => props.type === 'success' ? 'var(--success-500)' : props.type === 'error' ? 'var(--error-500)' : 'var(--primary-600)'};\n  color: white;\n  font-weight: 500;\n  font-size: 0.875rem;\n  max-width: 400px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n_c18 = StatusMessage;\nconst RecordingsSection = styled.div`\n  margin-top: var(--space-16);\n  background: var(--bg-secondary);\n  padding: var(--space-12) var(--space-4);\n  border-radius: var(--radius-2xl);\n  max-width: 1200px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n_c19 = RecordingsSection;\nconst RecordingsTitle = styled.h3`\n  font-family: var(--font-primary);\n  color: var(--text-primary);\n  margin-bottom: var(--space-8);\n  font-size: 1.5rem;\n  font-weight: 600;\n  text-align: center;\n`;\n_c20 = RecordingsTitle;\nconst RecordingsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: var(--space-6);\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-4);\n  }\n`;\n_c21 = RecordingsGrid;\nconst RecordingCard = styled.div`\n  background: var(--bg-primary);\n  padding: var(--space-6);\n  border-radius: var(--radius-xl);\n  border: 1px solid var(--border-light);\n  box-shadow: var(--shadow-sm);\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: translateY(-2px);\n    border-color: var(--primary-200);\n    box-shadow: var(--shadow-lg);\n  }\n`;\n_c22 = RecordingCard;\nconst RecordingTitle = styled.p`\n  margin: 0 0 var(--space-2) 0;\n  color: var(--text-primary);\n  font-weight: 600;\n  font-size: 1rem;\n  font-family: var(--font-primary);\n`;\n_c23 = RecordingTitle;\nconst RecordingTime = styled.p`\n  margin: 0 0 var(--space-4) 0;\n  font-size: 0.8rem;\n  color: var(--text-tertiary);\n`;\n_c24 = RecordingTime;\nconst DownloadButton = styled.button`\n  background: var(--primary-600);\n  border: none;\n  border-radius: var(--radius-lg);\n  padding: var(--space-2) var(--space-4);\n  color: white;\n  cursor: pointer;\n  font-size: 0.8rem;\n  font-weight: 500;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  margin: 0 auto;\n\n  &:hover {\n    background: var(--primary-700);\n    transform: translateY(-1px);\n  }\n`;\n\n// Sample sign language data\n_c25 = DownloadButton;\nconst signLanguageData = [{\n  name: \"Hello\",\n  emoji: \"👋\",\n  description: \"Wave your hand from side to side with your palm facing forward\"\n}, {\n  name: \"Thank You\",\n  emoji: \"🙏\",\n  description: \"Touch your chin with your fingertips and move your hand forward\"\n}, {\n  name: \"Yes\",\n  emoji: \"👍\",\n  description: \"Make a fist and nod it up and down\"\n}, {\n  name: \"No\",\n  emoji: \"👎\",\n  description: \"Make a fist and shake it from side to side\"\n}, {\n  name: \"Please\",\n  emoji: \"🤲\",\n  description: \"Place your open hand on your chest and move it in a circular motion\"\n}, {\n  name: \"Sorry\",\n  emoji: \"😔\",\n  description: \"Make a fist and rub it in a circular motion on your chest\"\n}];\nconst TrainingPage = ({\n  onBackToHome\n}) => {\n  _s();\n  const [isRecording, setIsRecording] = useState(false);\n  const [currentSign, setCurrentSign] = useState(0);\n  const [status, setStatus] = useState('');\n  const [recordedVideos, setRecordedVideos] = useState([]);\n  const webcamRef = useRef(null);\n  const mediaRecorderRef = useRef(null);\n  const recordedChunksRef = useRef([]);\n  const getRandomSign = useCallback(() => {\n    const randomIndex = Math.floor(Math.random() * signLanguageData.length);\n    setCurrentSign(randomIndex);\n  }, []);\n  const startRecording = useCallback(() => {\n    if (!webcamRef.current) {\n      setStatus('Camera not available');\n      return;\n    }\n    mediaRecorderRef.current = new MediaRecorder(webcamRef.current.stream, {\n      mimeType: 'video/webm'\n    });\n    mediaRecorderRef.current.ondataavailable = event => {\n      if (event.data.size > 0) {\n        recordedChunksRef.current.push(event.data);\n      }\n    };\n    mediaRecorderRef.current.onstop = () => {\n      const blob = new Blob(recordedChunksRef.current, {\n        type: 'video/webm'\n      });\n      const url = URL.createObjectURL(blob);\n      const timestamp = new Date().toISOString();\n      setRecordedVideos(prev => [...prev, {\n        id: timestamp,\n        url,\n        sign: signLanguageData[currentSign].name,\n        timestamp\n      }]);\n      recordedChunksRef.current = [];\n      setStatus('Recording saved successfully!');\n    };\n    mediaRecorderRef.current.start();\n    setIsRecording(true);\n    setStatus('Recording started...');\n  }, [currentSign]);\n  const stopRecording = useCallback(() => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n      setIsRecording(false);\n      setStatus('Processing recording...');\n    }\n  }, [isRecording]);\n  const downloadRecording = video => {\n    const a = document.createElement('a');\n    a.href = video.url;\n    a.download = `sign_${video.sign}_${video.timestamp}.webm`;\n    a.click();\n  };\n  React.useEffect(() => {\n    getRandomSign();\n  }, [getRandomSign]);\n  return /*#__PURE__*/_jsxDEV(TrainingContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Navigation, {\n      children: /*#__PURE__*/_jsxDEV(NavContainer, {\n        children: [/*#__PURE__*/_jsxDEV(Logo, {\n          children: \"ASL Trainer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(BackButton, {\n          onClick: onBackToHome,\n          children: \"\\u2190 Back to Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 535,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 534,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n      children: [/*#__PURE__*/_jsxDEV(PageTitle, {\n        children: \"Practice Session\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 544,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PageSubtitle, {\n        children: \"Practice sign language with real-time camera feedback and contribute to AI training\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 545,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TrainingGrid, {\n        children: [/*#__PURE__*/_jsxDEV(CameraSection, {\n          children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n            children: \"\\uD83D\\uDCF9 Your Camera\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(WebcamContainer, {\n            children: [/*#__PURE__*/_jsxDEV(StyledWebcam, {\n              ref: webcamRef,\n              audio: false,\n              screenshotFormat: \"image/jpeg\",\n              videoConstraints: {\n                width: 640,\n                height: 480,\n                facingMode: \"user\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(RecordingOverlay, {\n              isRecording: isRecording,\n              children: isRecording ? '🔴 Recording' : '📹 Ready'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 550,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SignSection, {\n          children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n            children: \"\\uD83C\\uDFAF Practice This Sign\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SignDisplay, {\n            children: signLanguageData[currentSign].emoji\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SignName, {\n            children: signLanguageData[currentSign].name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SignDescription, {\n            children: signLanguageData[currentSign].description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 549,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ControlsSection, {\n        children: [/*#__PURE__*/_jsxDEV(ControlButton, {\n          variant: \"secondary\",\n          onClick: getRandomSign,\n          disabled: isRecording,\n          children: \"\\uD83D\\uDD04 New Sign\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 586,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ControlButton, {\n          variant: \"primary\",\n          onClick: isRecording ? stopRecording : startRecording,\n          children: isRecording ? '⏹️ Stop Recording' : '🎬 Start Recording'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 594,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 585,\n        columnNumber: 9\n      }, this), status && /*#__PURE__*/_jsxDEV(StatusMessage, {\n        type: status.includes('error') ? 'error' : status.includes('success') ? 'success' : 'info',\n        children: status\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 603,\n        columnNumber: 11\n      }, this), recordedVideos.length > 0 && /*#__PURE__*/_jsxDEV(RecordingsSection, {\n        children: [/*#__PURE__*/_jsxDEV(RecordingsTitle, {\n          children: \"Your Practice Recordings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(RecordingsGrid, {\n          children: recordedVideos.map(video => /*#__PURE__*/_jsxDEV(RecordingCard, {\n            children: [/*#__PURE__*/_jsxDEV(RecordingTitle, {\n              children: video.sign\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(RecordingTime, {\n              children: new Date(video.timestamp).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(DownloadButton, {\n              onClick: () => downloadRecording(video),\n              children: \"\\uD83D\\uDCE5 Download\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 19\n            }, this)]\n          }, video.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 611,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 609,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 543,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 533,\n    columnNumber: 5\n  }, this);\n};\n_s(TrainingPage, \"31VvV1/5i7G4yeUVu//5AOKbgnc=\");\n_c26 = TrainingPage;\nexport default TrainingPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26;\n$RefreshReg$(_c, \"TrainingContainer\");\n$RefreshReg$(_c2, \"Navigation\");\n$RefreshReg$(_c3, \"NavContainer\");\n$RefreshReg$(_c4, \"Logo\");\n$RefreshReg$(_c5, \"BackButton\");\n$RefreshReg$(_c6, \"PageTitle\");\n$RefreshReg$(_c7, \"PageSubtitle\");\n$RefreshReg$(_c8, \"MainContent\");\n$RefreshReg$(_c9, \"TrainingGrid\");\n$RefreshReg$(_c0, \"CameraSection\");\n$RefreshReg$(_c1, \"WebcamContainer\");\n$RefreshReg$(_c10, \"StyledWebcam\");\n$RefreshReg$(_c11, \"RecordingOverlay\");\n$RefreshReg$(_c12, \"SignSection\");\n$RefreshReg$(_c13, \"SignDisplay\");\n$RefreshReg$(_c14, \"SignName\");\n$RefreshReg$(_c15, \"SignDescription\");\n$RefreshReg$(_c16, \"ControlsSection\");\n$RefreshReg$(_c17, \"ControlButton\");\n$RefreshReg$(_c18, \"StatusMessage\");\n$RefreshReg$(_c19, \"RecordingsSection\");\n$RefreshReg$(_c20, \"RecordingsTitle\");\n$RefreshReg$(_c21, \"RecordingsGrid\");\n$RefreshReg$(_c22, \"RecordingCard\");\n$RefreshReg$(_c23, \"RecordingTitle\");\n$RefreshReg$(_c24, \"RecordingTime\");\n$RefreshReg$(_c25, \"DownloadButton\");\n$RefreshReg$(_c26, \"TrainingPage\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "styled", "Webcam", "Brain", "Camera", "ArrowLeft", "RotateCcw", "Play", "Square", "Download", "Zap", "Eye", "Target", "jsxDEV", "_jsxDEV", "TrainingContainer", "div", "_c", "Navigation", "nav", "_c2", "NavContainer", "_c3", "Logo", "_c4", "BackButton", "button", "_c5", "Page<PERSON><PERSON>le", "h1", "_c6", "PageSubtitle", "p", "_c7", "MainContent", "main", "_c8", "TrainingGrid", "_c9", "CameraSection", "_c0", "WebcamContainer", "_c1", "StyledWebcam", "_c10", "RecordingOverlay", "props", "isRecording", "_c11", "SignSection", "_c12", "SignDisplay", "_c13", "SignName", "h3", "_c14", "SignDescription", "_c15", "ControlsSection", "_c16", "ControlButton", "variant", "_c17", "StatusMessage", "type", "_c18", "RecordingsSection", "_c19", "RecordingsTitle", "_c20", "RecordingsGrid", "_c21", "RecordingCard", "_c22", "RecordingTitle", "_c23", "RecordingTime", "_c24", "DownloadButton", "_c25", "signLanguageData", "name", "emoji", "description", "TrainingPage", "onBackToHome", "_s", "setIsRecording", "currentSign", "setCurrentSign", "status", "setStatus", "recordedVideos", "setRecordedVideos", "webcamRef", "mediaRecorderRef", "recordedChunksRef", "getRandomSign", "randomIndex", "Math", "floor", "random", "length", "startRecording", "current", "MediaRecorder", "stream", "mimeType", "ondataavailable", "event", "data", "size", "push", "onstop", "blob", "Blob", "url", "URL", "createObjectURL", "timestamp", "Date", "toISOString", "prev", "id", "sign", "start", "stopRecording", "stop", "downloadRecording", "video", "a", "document", "createElement", "href", "download", "click", "useEffect", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "SectionTitle", "ref", "audio", "screenshotFormat", "videoConstraints", "width", "height", "facingMode", "disabled", "includes", "map", "toLocaleString", "_c26", "$RefreshReg$"], "sources": ["D:/ASL/training-frontend/src/components/TrainingPage.js"], "sourcesContent": ["import React, { useState, useRef, useCallback } from 'react';\r\nimport styled from 'styled-components';\r\nimport Webcam from 'react-webcam';\r\nimport {\r\n  Brain,\r\n  Camera,\r\n  ArrowLeft,\r\n  RotateCcw,\r\n  Play,\r\n  Square,\r\n  Download,\r\n  Zap,\r\n  Eye,\r\n  Target\r\n} from 'lucide-react';\r\n\r\nconst TrainingContainer = styled.div`\r\n  min-height: 100vh;\r\n  background: var(--bg-primary);\r\n  position: relative;\r\n  overflow-x: hidden;\r\n`;\r\n\r\nconst Navigation = styled.nav`\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 50;\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(20px);\r\n  border-bottom: 1px solid var(--border-light);\r\n  padding: var(--space-4) 0;\r\n`;\r\n\r\nconst NavContainer = styled.div`\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 var(--space-4);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n`;\r\n\r\nconst Logo = styled.div`\r\n  font-family: var(--font-primary);\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n  color: var(--text-primary);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n\r\n  &::before {\r\n    content: '🤟';\r\n    font-size: 1.75rem;\r\n  }\r\n`;\r\n\r\nconst BackButton = styled.button`\r\n  background: transparent;\r\n  color: var(--text-secondary);\r\n  border: 1px solid var(--border-medium);\r\n  padding: var(--space-2) var(--space-4);\r\n  border-radius: var(--radius-lg);\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n\r\n  &:hover {\r\n    background: var(--gray-50);\r\n    color: var(--text-primary);\r\n    border-color: var(--border-dark);\r\n  }\r\n`;\r\n\r\nconst PageTitle = styled.h1`\r\n  font-family: var(--font-primary);\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  color: var(--text-primary);\r\n  text-align: center;\r\n  margin-bottom: var(--space-2);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 2rem;\r\n  }\r\n`;\r\n\r\nconst PageSubtitle = styled.p`\r\n  font-size: 1.125rem;\r\n  color: var(--text-secondary);\r\n  text-align: center;\r\n  margin-bottom: var(--space-12);\r\n  max-width: 600px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n\r\n  @media (max-width: 768px) {\r\n    margin-bottom: var(--space-10);\r\n  }\r\n`;\r\n\r\nconst MainContent = styled.main`\r\n  padding: var(--space-20) var(--space-4) var(--space-16);\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-16) var(--space-4) var(--space-12);\r\n  }\r\n`;\r\n\r\nconst TrainingGrid = styled.div`\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: var(--space-8);\r\n  max-width: 1200px;\r\n  margin: 0 auto var(--space-12);\r\n\r\n  @media (max-width: 1024px) {\r\n    grid-template-columns: 1fr;\r\n    gap: var(--space-6);\r\n  }\r\n`;\r\n\r\nconst CameraSection = styled.div`\r\n  background: var(--bg-primary);\r\n  border-radius: var(--radius-2xl);\r\n  padding: var(--space-8);\r\n  border: 1px solid var(--border-light);\r\n  box-shadow: var(--shadow-lg);\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    box-shadow: var(--shadow-xl);\r\n    border-color: var(--primary-200);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-6);\r\n  }\r\n`;\r\n\r\nconst WebcamContainer = styled.div`\r\n  position: relative;\r\n  border-radius: var(--radius-xl);\r\n  overflow: hidden;\r\n  background: var(--gray-100);\r\n  aspect-ratio: 4/3;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 2px solid var(--border-light);\r\n  margin-bottom: var(--space-4);\r\n`;\r\n\r\nconst StyledWebcam = styled(Webcam)`\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n`;\r\n\r\nconst RecordingOverlay = styled.div`\r\n  position: absolute;\r\n  top: var(--space-4);\r\n  right: var(--space-4);\r\n  background: ${props => props.isRecording ?\r\n    'var(--error-500)' :\r\n    'var(--gray-600)'\r\n  };\r\n  color: white;\r\n  padding: var(--space-2) var(--space-4);\r\n  border-radius: var(--radius-full);\r\n  font-size: 0.875rem;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  animation: ${props => props.isRecording ? 'pulse 1.5s infinite' : 'none'};\r\n\r\n  @keyframes pulse {\r\n    0%, 100% { opacity: 1; }\r\n    50% { opacity: 0.7; }\r\n  }\r\n`;\r\n\r\nconst SignSection = styled.div`\r\n  background: var(--bg-primary);\r\n  border-radius: var(--radius-2xl);\r\n  padding: var(--space-8);\r\n  border: 1px solid var(--border-light);\r\n  box-shadow: var(--shadow-lg);\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  text-align: center;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    box-shadow: var(--shadow-xl);\r\n    border-color: var(--primary-200);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-6);\r\n  }\r\n`;\r\n\r\nconst SignDisplay = styled.div`\r\n  width: 200px;\r\n  height: 200px;\r\n  background: var(--primary-50);\r\n  border-radius: var(--radius-2xl);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 4rem;\r\n  margin-bottom: var(--space-6);\r\n  border: 2px solid var(--primary-200);\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: scale(1.02);\r\n    border-color: var(--primary-300);\r\n    background: var(--primary-100);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    width: 150px;\r\n    height: 150px;\r\n    font-size: 3rem;\r\n  }\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    width: 220px;\r\n    height: 220px;\r\n    font-size: 4rem;\r\n  }\r\n`;\r\n\r\nconst SignName = styled.h3`\r\n  font-family: var(--font-primary);\r\n  font-size: 1.5rem;\r\n  margin-bottom: var(--space-3);\r\n  color: var(--text-primary);\r\n  font-weight: 700;\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.25rem;\r\n  }\r\n`;\r\n\r\nconst SignDescription = styled.p`\r\n  text-align: center;\r\n  line-height: 1.6;\r\n  color: var(--text-secondary);\r\n  font-size: 0.9rem;\r\n  font-weight: 400;\r\n  max-width: 280px;\r\n`;\r\n\r\nconst ControlsSection = styled.div`\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: var(--space-4);\r\n  margin-top: var(--space-8);\r\n\r\n  @media (max-width: 768px) {\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: var(--space-3);\r\n  }\r\n`;\r\n\r\nconst ControlButton = styled.button`\r\n  background: ${props => props.variant === 'primary'\r\n    ? 'var(--primary-600)'\r\n    : 'var(--bg-primary)'};\r\n  border: ${props => props.variant === 'primary'\r\n    ? 'none'\r\n    : '1px solid var(--border-medium)'};\r\n  color: ${props => props.variant === 'primary'\r\n    ? 'white'\r\n    : 'var(--text-primary)'};\r\n  padding: var(--space-3) var(--space-6);\r\n  border-radius: var(--radius-lg);\r\n  cursor: pointer;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  transition: all 0.2s ease;\r\n  min-width: 160px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: var(--space-2);\r\n  box-shadow: ${props => props.variant === 'primary'\r\n    ? 'var(--shadow-lg)'\r\n    : 'var(--shadow-sm)'};\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: ${props => props.variant === 'primary'\r\n      ? 'var(--shadow-xl)'\r\n      : 'var(--shadow-md)'};\r\n    background: ${props => props.variant === 'primary'\r\n      ? 'var(--primary-700)'\r\n      : 'var(--gray-50)'};\r\n  }\r\n\r\n  &:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    width: 100%;\r\n    max-width: 280px;\r\n  }\r\n`;\r\n\r\nconst StatusMessage = styled.div`\r\n  text-align: center;\r\n  margin-top: var(--space-6);\r\n  padding: var(--space-4) var(--space-6);\r\n  border-radius: var(--radius-lg);\r\n  background: ${props =>\r\n    props.type === 'success' ? 'var(--success-500)' :\r\n    props.type === 'error' ? 'var(--error-500)' :\r\n    'var(--primary-600)'\r\n  };\r\n  color: white;\r\n  font-weight: 500;\r\n  font-size: 0.875rem;\r\n  max-width: 400px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n`;\r\n\r\nconst RecordingsSection = styled.div`\r\n  margin-top: var(--space-16);\r\n  background: var(--bg-secondary);\r\n  padding: var(--space-12) var(--space-4);\r\n  border-radius: var(--radius-2xl);\r\n  max-width: 1200px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n`;\r\n\r\nconst RecordingsTitle = styled.h3`\r\n  font-family: var(--font-primary);\r\n  color: var(--text-primary);\r\n  margin-bottom: var(--space-8);\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n  text-align: center;\r\n`;\r\n\r\nconst RecordingsGrid = styled.div`\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\r\n  gap: var(--space-6);\r\n\r\n  @media (max-width: 768px) {\r\n    grid-template-columns: 1fr;\r\n    gap: var(--space-4);\r\n  }\r\n`;\r\n\r\nconst RecordingCard = styled.div`\r\n  background: var(--bg-primary);\r\n  padding: var(--space-6);\r\n  border-radius: var(--radius-xl);\r\n  border: 1px solid var(--border-light);\r\n  box-shadow: var(--shadow-sm);\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n    border-color: var(--primary-200);\r\n    box-shadow: var(--shadow-lg);\r\n  }\r\n`;\r\n\r\nconst RecordingTitle = styled.p`\r\n  margin: 0 0 var(--space-2) 0;\r\n  color: var(--text-primary);\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  font-family: var(--font-primary);\r\n`;\r\n\r\nconst RecordingTime = styled.p`\r\n  margin: 0 0 var(--space-4) 0;\r\n  font-size: 0.8rem;\r\n  color: var(--text-tertiary);\r\n`;\r\n\r\nconst DownloadButton = styled.button`\r\n  background: var(--primary-600);\r\n  border: none;\r\n  border-radius: var(--radius-lg);\r\n  padding: var(--space-2) var(--space-4);\r\n  color: white;\r\n  cursor: pointer;\r\n  font-size: 0.8rem;\r\n  font-weight: 500;\r\n  transition: all 0.2s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  margin: 0 auto;\r\n\r\n  &:hover {\r\n    background: var(--primary-700);\r\n    transform: translateY(-1px);\r\n  }\r\n`;\r\n\r\n// Sample sign language data\r\nconst signLanguageData = [\r\n  {\r\n    name: \"Hello\",\r\n    emoji: \"👋\",\r\n    description: \"Wave your hand from side to side with your palm facing forward\"\r\n  },\r\n  {\r\n    name: \"Thank You\",\r\n    emoji: \"🙏\",\r\n    description: \"Touch your chin with your fingertips and move your hand forward\"\r\n  },\r\n  {\r\n    name: \"Yes\",\r\n    emoji: \"👍\",\r\n    description: \"Make a fist and nod it up and down\"\r\n  },\r\n  {\r\n    name: \"No\",\r\n    emoji: \"👎\",\r\n    description: \"Make a fist and shake it from side to side\"\r\n  },\r\n  {\r\n    name: \"Please\",\r\n    emoji: \"🤲\",\r\n    description: \"Place your open hand on your chest and move it in a circular motion\"\r\n  },\r\n  {\r\n    name: \"Sorry\",\r\n    emoji: \"😔\",\r\n    description: \"Make a fist and rub it in a circular motion on your chest\"\r\n  }\r\n];\r\n\r\nconst TrainingPage = ({ onBackToHome }) => {\r\n  const [isRecording, setIsRecording] = useState(false);\r\n  const [currentSign, setCurrentSign] = useState(0);\r\n  const [status, setStatus] = useState('');\r\n  const [recordedVideos, setRecordedVideos] = useState([]);\r\n  const webcamRef = useRef(null);\r\n  const mediaRecorderRef = useRef(null);\r\n  const recordedChunksRef = useRef([]);\r\n\r\n  const getRandomSign = useCallback(() => {\r\n    const randomIndex = Math.floor(Math.random() * signLanguageData.length);\r\n    setCurrentSign(randomIndex);\r\n  }, []);\r\n\r\n  const startRecording = useCallback(() => {\r\n    if (!webcamRef.current) {\r\n      setStatus('Camera not available');\r\n      return;\r\n    }\r\n\r\n    mediaRecorderRef.current = new MediaRecorder(webcamRef.current.stream, {\r\n      mimeType: 'video/webm'\r\n    });\r\n\r\n    mediaRecorderRef.current.ondataavailable = (event) => {\r\n      if (event.data.size > 0) {\r\n        recordedChunksRef.current.push(event.data);\r\n      }\r\n    };\r\n\r\n    mediaRecorderRef.current.onstop = () => {\r\n      const blob = new Blob(recordedChunksRef.current, {\r\n        type: 'video/webm'\r\n      });\r\n      const url = URL.createObjectURL(blob);\r\n      const timestamp = new Date().toISOString();\r\n      \r\n      setRecordedVideos(prev => [...prev, {\r\n        id: timestamp,\r\n        url,\r\n        sign: signLanguageData[currentSign].name,\r\n        timestamp\r\n      }]);\r\n      \r\n      recordedChunksRef.current = [];\r\n      setStatus('Recording saved successfully!');\r\n    };\r\n\r\n    mediaRecorderRef.current.start();\r\n    setIsRecording(true);\r\n    setStatus('Recording started...');\r\n  }, [currentSign]);\r\n\r\n  const stopRecording = useCallback(() => {\r\n    if (mediaRecorderRef.current && isRecording) {\r\n      mediaRecorderRef.current.stop();\r\n      setIsRecording(false);\r\n      setStatus('Processing recording...');\r\n    }\r\n  }, [isRecording]);\r\n\r\n  const downloadRecording = (video) => {\r\n    const a = document.createElement('a');\r\n    a.href = video.url;\r\n    a.download = `sign_${video.sign}_${video.timestamp}.webm`;\r\n    a.click();\r\n  };\r\n\r\n  React.useEffect(() => {\r\n    getRandomSign();\r\n  }, [getRandomSign]);\r\n\r\n  return (\r\n    <TrainingContainer>\r\n      <Navigation>\r\n        <NavContainer>\r\n          <Logo>ASL Trainer</Logo>\r\n          <BackButton onClick={onBackToHome}>\r\n            ← Back to Home\r\n          </BackButton>\r\n        </NavContainer>\r\n      </Navigation>\r\n\r\n      <MainContent>\r\n        <PageTitle>Practice Session</PageTitle>\r\n        <PageSubtitle>\r\n          Practice sign language with real-time camera feedback and contribute to AI training\r\n        </PageSubtitle>\r\n\r\n        <TrainingGrid>\r\n          <CameraSection>\r\n            <SectionTitle>\r\n              📹 Your Camera\r\n            </SectionTitle>\r\n            <WebcamContainer>\r\n              <StyledWebcam\r\n                ref={webcamRef}\r\n                audio={false}\r\n                screenshotFormat=\"image/jpeg\"\r\n                videoConstraints={{\r\n                  width: 640,\r\n                  height: 480,\r\n                  facingMode: \"user\"\r\n                }}\r\n              />\r\n              <RecordingOverlay isRecording={isRecording}>\r\n                {isRecording ? '🔴 Recording' : '📹 Ready'}\r\n              </RecordingOverlay>\r\n            </WebcamContainer>\r\n          </CameraSection>\r\n\r\n          <SignSection>\r\n            <SectionTitle>\r\n              🎯 Practice This Sign\r\n            </SectionTitle>\r\n            <SignDisplay>\r\n              {signLanguageData[currentSign].emoji}\r\n            </SignDisplay>\r\n            <SignName>{signLanguageData[currentSign].name}</SignName>\r\n            <SignDescription>\r\n              {signLanguageData[currentSign].description}\r\n            </SignDescription>\r\n          </SignSection>\r\n        </TrainingGrid>\r\n\r\n        <ControlsSection>\r\n          <ControlButton\r\n            variant=\"secondary\"\r\n            onClick={getRandomSign}\r\n            disabled={isRecording}\r\n          >\r\n            🔄 New Sign\r\n          </ControlButton>\r\n\r\n          <ControlButton\r\n            variant=\"primary\"\r\n            onClick={isRecording ? stopRecording : startRecording}\r\n          >\r\n            {isRecording ? '⏹️ Stop Recording' : '🎬 Start Recording'}\r\n          </ControlButton>\r\n        </ControlsSection>\r\n\r\n        {status && (\r\n          <StatusMessage type={status.includes('error') ? 'error' : status.includes('success') ? 'success' : 'info'}>\r\n            {status}\r\n          </StatusMessage>\r\n        )}\r\n\r\n        {recordedVideos.length > 0 && (\r\n          <RecordingsSection>\r\n            <RecordingsTitle>Your Practice Recordings</RecordingsTitle>\r\n            <RecordingsGrid>\r\n              {recordedVideos.map((video) => (\r\n                <RecordingCard key={video.id}>\r\n                  <RecordingTitle>{video.sign}</RecordingTitle>\r\n                  <RecordingTime>\r\n                    {new Date(video.timestamp).toLocaleString()}\r\n                  </RecordingTime>\r\n                  <DownloadButton onClick={() => downloadRecording(video)}>\r\n                    📥 Download\r\n                  </DownloadButton>\r\n                </RecordingCard>\r\n              ))}\r\n            </RecordingsGrid>\r\n          </RecordingsSection>\r\n        )}\r\n      </MainContent>\r\n    </TrainingContainer>\r\n  );\r\n};\r\n\r\nexport default TrainingPage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAC5D,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,cAAc;AACjC,SACEC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,SAAS,EACTC,IAAI,EACJC,MAAM,EACNC,QAAQ,EACRC,GAAG,EACHC,GAAG,EACHC,MAAM,QACD,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,iBAAiB,GAAGd,MAAM,CAACe,GAAG;AACpC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,iBAAiB;AAOvB,MAAMG,UAAU,GAAGjB,MAAM,CAACkB,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAVIF,UAAU;AAYhB,MAAMG,YAAY,GAAGpB,MAAM,CAACe,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACM,GAAA,GAPID,YAAY;AASlB,MAAME,IAAI,GAAGtB,MAAM,CAACe,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GAbID,IAAI;AAeV,MAAME,UAAU,GAAGxB,MAAM,CAACyB,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAnBIF,UAAU;AAqBhB,MAAMG,SAAS,GAAG3B,MAAM,CAAC4B,EAAE;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAXIF,SAAS;AAaf,MAAMG,YAAY,GAAG9B,MAAM,CAAC+B,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAZIF,YAAY;AAclB,MAAMG,WAAW,GAAGjC,MAAM,CAACkC,IAAI;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GARIF,WAAW;AAUjB,MAAMG,YAAY,GAAGpC,MAAM,CAACe,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsB,GAAA,GAXID,YAAY;AAalB,MAAME,aAAa,GAAGtC,MAAM,CAACe,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACwB,GAAA,GAhBID,aAAa;AAkBnB,MAAME,eAAe,GAAGxC,MAAM,CAACe,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC0B,GAAA,GAXID,eAAe;AAarB,MAAME,YAAY,GAAG1C,MAAM,CAACC,MAAM,CAAC;AACnC;AACA;AACA;AACA,CAAC;AAAC0C,IAAA,GAJID,YAAY;AAMlB,MAAME,gBAAgB,GAAG5C,MAAM,CAACe,GAAG;AACnC;AACA;AACA;AACA,gBAAgB8B,KAAK,IAAIA,KAAK,CAACC,WAAW,GACtC,kBAAkB,GAClB,iBAAiB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eACeD,KAAK,IAAIA,KAAK,CAACC,WAAW,GAAG,qBAAqB,GAAG,MAAM;AAC1E;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAtBIH,gBAAgB;AAwBtB,MAAMI,WAAW,GAAGhD,MAAM,CAACe,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkC,IAAA,GApBID,WAAW;AAsBjB,MAAME,WAAW,GAAGlD,MAAM,CAACe,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACoC,IAAA,GA/BID,WAAW;AAiCjB,MAAME,QAAQ,GAAGpD,MAAM,CAACqD,EAAE;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAVIF,QAAQ;AAYd,MAAMG,eAAe,GAAGvD,MAAM,CAAC+B,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyB,IAAA,GAPID,eAAe;AASrB,MAAME,eAAe,GAAGzD,MAAM,CAACe,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC2C,IAAA,GAXID,eAAe;AAarB,MAAME,aAAa,GAAG3D,MAAM,CAACyB,MAAM;AACnC,gBAAgBoB,KAAK,IAAIA,KAAK,CAACe,OAAO,KAAK,SAAS,GAC9C,oBAAoB,GACpB,mBAAmB;AACzB,YAAYf,KAAK,IAAIA,KAAK,CAACe,OAAO,KAAK,SAAS,GAC1C,MAAM,GACN,gCAAgC;AACtC,WAAWf,KAAK,IAAIA,KAAK,CAACe,OAAO,KAAK,SAAS,GACzC,OAAO,GACP,qBAAqB;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBf,KAAK,IAAIA,KAAK,CAACe,OAAO,KAAK,SAAS,GAC9C,kBAAkB,GAClB,kBAAkB;AACxB;AACA;AACA;AACA,kBAAkBf,KAAK,IAAIA,KAAK,CAACe,OAAO,KAAK,SAAS,GAC9C,kBAAkB,GAClB,kBAAkB;AAC1B,kBAAkBf,KAAK,IAAIA,KAAK,CAACe,OAAO,KAAK,SAAS,GAC9C,oBAAoB,GACpB,gBAAgB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GA7CIF,aAAa;AA+CnB,MAAMG,aAAa,GAAG9D,MAAM,CAACe,GAAG;AAChC;AACA;AACA;AACA;AACA,gBAAgB8B,KAAK,IACjBA,KAAK,CAACkB,IAAI,KAAK,SAAS,GAAG,oBAAoB,GAC/ClB,KAAK,CAACkB,IAAI,KAAK,OAAO,GAAG,kBAAkB,GAC3C,oBAAoB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,CACC;AAACC,IAAA,GAhBIF,aAAa;AAkBnB,MAAMG,iBAAiB,GAAGjE,MAAM,CAACe,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmD,IAAA,GARID,iBAAiB;AAUvB,MAAME,eAAe,GAAGnE,MAAM,CAACqD,EAAE;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACe,IAAA,GAPID,eAAe;AASrB,MAAME,cAAc,GAAGrE,MAAM,CAACe,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuD,IAAA,GATID,cAAc;AAWpB,MAAME,aAAa,GAAGvE,MAAM,CAACe,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyD,IAAA,GAbID,aAAa;AAenB,MAAME,cAAc,GAAGzE,MAAM,CAAC+B,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC2C,IAAA,GANID,cAAc;AAQpB,MAAME,aAAa,GAAG3E,MAAM,CAAC+B,CAAC;AAC9B;AACA;AACA;AACA,CAAC;AAAC6C,IAAA,GAJID,aAAa;AAMnB,MAAME,cAAc,GAAG7E,MAAM,CAACyB,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAqD,IAAA,GArBMD,cAAc;AAsBpB,MAAME,gBAAgB,GAAG,CACvB;EACEC,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,IAAI,EAAE,WAAW;EACjBC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,IAAI,EAAE,KAAK;EACXC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE;AACf,CAAC,CACF;AAED,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM,CAACvC,WAAW,EAAEwC,cAAc,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC0F,WAAW,EAAEC,cAAc,CAAC,GAAG3F,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC4F,MAAM,EAAEC,SAAS,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC8F,cAAc,EAAEC,iBAAiB,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAMgG,SAAS,GAAG/F,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMgG,gBAAgB,GAAGhG,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMiG,iBAAiB,GAAGjG,MAAM,CAAC,EAAE,CAAC;EAEpC,MAAMkG,aAAa,GAAGjG,WAAW,CAAC,MAAM;IACtC,MAAMkG,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGrB,gBAAgB,CAACsB,MAAM,CAAC;IACvEb,cAAc,CAACS,WAAW,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,cAAc,GAAGvG,WAAW,CAAC,MAAM;IACvC,IAAI,CAAC8F,SAAS,CAACU,OAAO,EAAE;MACtBb,SAAS,CAAC,sBAAsB,CAAC;MACjC;IACF;IAEAI,gBAAgB,CAACS,OAAO,GAAG,IAAIC,aAAa,CAACX,SAAS,CAACU,OAAO,CAACE,MAAM,EAAE;MACrEC,QAAQ,EAAE;IACZ,CAAC,CAAC;IAEFZ,gBAAgB,CAACS,OAAO,CAACI,eAAe,GAAIC,KAAK,IAAK;MACpD,IAAIA,KAAK,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;QACvBf,iBAAiB,CAACQ,OAAO,CAACQ,IAAI,CAACH,KAAK,CAACC,IAAI,CAAC;MAC5C;IACF,CAAC;IAEDf,gBAAgB,CAACS,OAAO,CAACS,MAAM,GAAG,MAAM;MACtC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACnB,iBAAiB,CAACQ,OAAO,EAAE;QAC/CxC,IAAI,EAAE;MACR,CAAC,CAAC;MACF,MAAMoD,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MACrC,MAAMK,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAE1C5B,iBAAiB,CAAC6B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAClCC,EAAE,EAAEJ,SAAS;QACbH,GAAG;QACHQ,IAAI,EAAE5C,gBAAgB,CAACQ,WAAW,CAAC,CAACP,IAAI;QACxCsC;MACF,CAAC,CAAC,CAAC;MAEHvB,iBAAiB,CAACQ,OAAO,GAAG,EAAE;MAC9Bb,SAAS,CAAC,+BAA+B,CAAC;IAC5C,CAAC;IAEDI,gBAAgB,CAACS,OAAO,CAACqB,KAAK,CAAC,CAAC;IAChCtC,cAAc,CAAC,IAAI,CAAC;IACpBI,SAAS,CAAC,sBAAsB,CAAC;EACnC,CAAC,EAAE,CAACH,WAAW,CAAC,CAAC;EAEjB,MAAMsC,aAAa,GAAG9H,WAAW,CAAC,MAAM;IACtC,IAAI+F,gBAAgB,CAACS,OAAO,IAAIzD,WAAW,EAAE;MAC3CgD,gBAAgB,CAACS,OAAO,CAACuB,IAAI,CAAC,CAAC;MAC/BxC,cAAc,CAAC,KAAK,CAAC;MACrBI,SAAS,CAAC,yBAAyB,CAAC;IACtC;EACF,CAAC,EAAE,CAAC5C,WAAW,CAAC,CAAC;EAEjB,MAAMiF,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMC,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGJ,KAAK,CAACb,GAAG;IAClBc,CAAC,CAACI,QAAQ,GAAG,QAAQL,KAAK,CAACL,IAAI,IAAIK,KAAK,CAACV,SAAS,OAAO;IACzDW,CAAC,CAACK,KAAK,CAAC,CAAC;EACX,CAAC;EAED1I,KAAK,CAAC2I,SAAS,CAAC,MAAM;IACpBvC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB,oBACEnF,OAAA,CAACC,iBAAiB;IAAA0H,QAAA,gBAChB3H,OAAA,CAACI,UAAU;MAAAuH,QAAA,eACT3H,OAAA,CAACO,YAAY;QAAAoH,QAAA,gBACX3H,OAAA,CAACS,IAAI;UAAAkH,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxB/H,OAAA,CAACW,UAAU;UAACqH,OAAO,EAAEzD,YAAa;UAAAoD,QAAA,EAAC;QAEnC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEb/H,OAAA,CAACoB,WAAW;MAAAuG,QAAA,gBACV3H,OAAA,CAACc,SAAS;QAAA6G,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eACvC/H,OAAA,CAACiB,YAAY;QAAA0G,QAAA,EAAC;MAEd;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eAEf/H,OAAA,CAACuB,YAAY;QAAAoG,QAAA,gBACX3H,OAAA,CAACyB,aAAa;UAAAkG,QAAA,gBACZ3H,OAAA,CAACiI,YAAY;YAAAN,QAAA,EAAC;UAEd;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACf/H,OAAA,CAAC2B,eAAe;YAAAgG,QAAA,gBACd3H,OAAA,CAAC6B,YAAY;cACXqG,GAAG,EAAElD,SAAU;cACfmD,KAAK,EAAE,KAAM;cACbC,gBAAgB,EAAC,YAAY;cAC7BC,gBAAgB,EAAE;gBAChBC,KAAK,EAAE,GAAG;gBACVC,MAAM,EAAE,GAAG;gBACXC,UAAU,EAAE;cACd;YAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF/H,OAAA,CAAC+B,gBAAgB;cAACE,WAAW,EAAEA,WAAY;cAAA0F,QAAA,EACxC1F,WAAW,GAAG,cAAc,GAAG;YAAU;cAAA2F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEhB/H,OAAA,CAACmC,WAAW;UAAAwF,QAAA,gBACV3H,OAAA,CAACiI,YAAY;YAAAN,QAAA,EAAC;UAEd;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACf/H,OAAA,CAACqC,WAAW;YAAAsF,QAAA,EACTzD,gBAAgB,CAACQ,WAAW,CAAC,CAACN;UAAK;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACd/H,OAAA,CAACuC,QAAQ;YAAAoF,QAAA,EAAEzD,gBAAgB,CAACQ,WAAW,CAAC,CAACP;UAAI;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACzD/H,OAAA,CAAC0C,eAAe;YAAAiF,QAAA,EACbzD,gBAAgB,CAACQ,WAAW,CAAC,CAACL;UAAW;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEf/H,OAAA,CAAC4C,eAAe;QAAA+E,QAAA,gBACd3H,OAAA,CAAC8C,aAAa;UACZC,OAAO,EAAC,WAAW;UACnBiF,OAAO,EAAE7C,aAAc;UACvBsD,QAAQ,EAAExG,WAAY;UAAA0F,QAAA,EACvB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eAEhB/H,OAAA,CAAC8C,aAAa;UACZC,OAAO,EAAC,SAAS;UACjBiF,OAAO,EAAE/F,WAAW,GAAG+E,aAAa,GAAGvB,cAAe;UAAAkC,QAAA,EAErD1F,WAAW,GAAG,mBAAmB,GAAG;QAAoB;UAAA2F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAEjBnD,MAAM,iBACL5E,OAAA,CAACiD,aAAa;QAACC,IAAI,EAAE0B,MAAM,CAAC8D,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG9D,MAAM,CAAC8D,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,MAAO;QAAAf,QAAA,EACvG/C;MAAM;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAChB,EAEAjD,cAAc,CAACU,MAAM,GAAG,CAAC,iBACxBxF,OAAA,CAACoD,iBAAiB;QAAAuE,QAAA,gBAChB3H,OAAA,CAACsD,eAAe;UAAAqE,QAAA,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAiB,CAAC,eAC3D/H,OAAA,CAACwD,cAAc;UAAAmE,QAAA,EACZ7C,cAAc,CAAC6D,GAAG,CAAExB,KAAK,iBACxBnH,OAAA,CAAC0D,aAAa;YAAAiE,QAAA,gBACZ3H,OAAA,CAAC4D,cAAc;cAAA+D,QAAA,EAAER,KAAK,CAACL;YAAI;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiB,CAAC,eAC7C/H,OAAA,CAAC8D,aAAa;cAAA6D,QAAA,EACX,IAAIjB,IAAI,CAACS,KAAK,CAACV,SAAS,CAAC,CAACmC,cAAc,CAAC;YAAC;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eAChB/H,OAAA,CAACgE,cAAc;cAACgE,OAAO,EAAEA,CAAA,KAAMd,iBAAiB,CAACC,KAAK,CAAE;cAAAQ,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAgB,CAAC;UAAA,GAPCZ,KAAK,CAACN,EAAE;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQb,CAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACpB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAExB,CAAC;AAACvD,EAAA,CAzKIF,YAAY;AAAAuE,IAAA,GAAZvE,YAAY;AA2KlB,eAAeA,YAAY;AAAC,IAAAnE,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAA4E,IAAA;AAAAC,YAAA,CAAA3I,EAAA;AAAA2I,YAAA,CAAAxI,GAAA;AAAAwI,YAAA,CAAAtI,GAAA;AAAAsI,YAAA,CAAApI,GAAA;AAAAoI,YAAA,CAAAjI,GAAA;AAAAiI,YAAA,CAAA9H,GAAA;AAAA8H,YAAA,CAAA3H,GAAA;AAAA2H,YAAA,CAAAxH,GAAA;AAAAwH,YAAA,CAAAtH,GAAA;AAAAsH,YAAA,CAAApH,GAAA;AAAAoH,YAAA,CAAAlH,GAAA;AAAAkH,YAAA,CAAAhH,IAAA;AAAAgH,YAAA,CAAA5G,IAAA;AAAA4G,YAAA,CAAA1G,IAAA;AAAA0G,YAAA,CAAAxG,IAAA;AAAAwG,YAAA,CAAArG,IAAA;AAAAqG,YAAA,CAAAnG,IAAA;AAAAmG,YAAA,CAAAjG,IAAA;AAAAiG,YAAA,CAAA9F,IAAA;AAAA8F,YAAA,CAAA3F,IAAA;AAAA2F,YAAA,CAAAzF,IAAA;AAAAyF,YAAA,CAAAvF,IAAA;AAAAuF,YAAA,CAAArF,IAAA;AAAAqF,YAAA,CAAAnF,IAAA;AAAAmF,YAAA,CAAAjF,IAAA;AAAAiF,YAAA,CAAA/E,IAAA;AAAA+E,YAAA,CAAA7E,IAAA;AAAA6E,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}