{"ast": null, "code": "var _jsxFileName = \"D:\\\\ASL\\\\training-frontend\\\\src\\\\components\\\\HomePage.js\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { Brain, Camera, Users, Shield, Smartphone, Target, Play, BookOpen, Zap, Cpu, Eye, Award, TrendingUp, Globe } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomeContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 40% 60%, rgba(16, 185, 129, 0.02) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n`;\n_c = HomeContainer;\nconst Navigation = styled.nav`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: var(--bg-glass);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-neural);\n  padding: var(--space-4) 0;\n  transition: var(--transition-normal);\n\n  @media (max-width: 768px) {\n    padding: var(--space-3) 0;\n  }\n`;\n_c2 = Navigation;\nconst NavContainer = styled.div`\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  @media (max-width: 768px) {\n    padding: 0 var(--space-4);\n  }\n`;\n_c3 = NavContainer;\nconst Logo = styled.div`\n  font-family: var(--font-primary);\n  font-size: 1.75rem;\n  font-weight: 800;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.5rem;\n    gap: var(--space-2);\n  }\n`;\n_c4 = Logo;\nconst LogoIcon = styled.div`\n  width: 40px;\n  height: 40px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    width: 36px;\n    height: 36px;\n  }\n`;\n_c5 = LogoIcon;\nconst NavLinks = styled.div`\n  display: flex;\n  align-items: center;\n  gap: var(--space-8);\n\n  @media (max-width: 768px) {\n    gap: var(--space-4);\n  }\n`;\n_c6 = NavLinks;\nconst NavLink = styled.a`\n  color: var(--text-secondary);\n  text-decoration: none;\n  font-weight: 500;\n  font-size: 0.9rem;\n  transition: var(--transition-fast);\n  position: relative;\n\n  &:hover {\n    color: var(--text-accent);\n  }\n\n  &::after {\n    content: '';\n    position: absolute;\n    bottom: -4px;\n    left: 0;\n    width: 0;\n    height: 2px;\n    background: var(--bg-neural);\n    transition: var(--transition-normal);\n  }\n\n  &:hover::after {\n    width: 100%;\n  }\n\n  @media (max-width: 768px) {\n    font-size: 0.85rem;\n  }\n`;\n_c7 = NavLink;\nconst HeroSection = styled.section`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: var(--space-24) var(--space-6) var(--space-20);\n  text-align: center;\n  position: relative;\n  z-index: 1;\n\n  @media (max-width: 768px) {\n    padding: var(--space-20) var(--space-4) var(--space-16);\n    min-height: calc(100vh - 80px);\n  }\n`;\n_c8 = HeroSection;\nconst HeroContent = styled.div`\n  max-width: 900px;\n  width: 100%;\n  position: relative;\n  z-index: 2;\n`;\n_c9 = HeroContent;\nconst HeroBadge = styled.div`\n  display: inline-flex;\n  align-items: center;\n  gap: var(--space-2);\n  background: var(--bg-glass);\n  border: 1px solid var(--border-neural);\n  border-radius: var(--radius-full);\n  padding: var(--space-2) var(--space-4);\n  margin-bottom: var(--space-6);\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: var(--text-accent);\n  backdrop-filter: blur(10px);\n  box-shadow: var(--shadow-glow);\n\n  @media (max-width: 768px) {\n    font-size: 0.8rem;\n    padding: var(--space-2) var(--space-3);\n  }\n`;\n_c0 = HeroBadge;\nconst HeroTitle = styled.h1`\n  font-family: var(--font-primary);\n  font-size: 4.5rem;\n  font-weight: 900;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin-bottom: var(--space-6);\n  line-height: 1.1;\n  letter-spacing: -0.03em;\n\n  @media (max-width: 768px) {\n    font-size: 3rem;\n    margin-bottom: var(--space-4);\n  }\n\n  @media (max-width: 480px) {\n    font-size: 2.25rem;\n  }\n`;\n_c1 = HeroTitle;\nconst HeroSubtitle = styled.p`\n  font-size: 1.375rem;\n  font-weight: 400;\n  color: var(--text-secondary);\n  margin-bottom: var(--space-8);\n  line-height: 1.6;\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n    margin-bottom: var(--space-6);\n  }\n\n  @media (max-width: 480px) {\n    font-size: 1.125rem;\n  }\n`;\n_c10 = HeroSubtitle;\nconst HeroDescription = styled.p`\n  font-size: 1.125rem;\n  line-height: 1.7;\n  color: var(--text-tertiary);\n  margin-bottom: var(--space-10);\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n\n  @media (max-width: 768px) {\n    font-size: 1rem;\n    margin-bottom: var(--space-8);\n    line-height: 1.6;\n  }\n`;\n_c11 = HeroDescription;\nconst HeroActions = styled.div`\n  display: flex;\n  gap: var(--space-5);\n  justify-content: center;\n  align-items: center;\n  flex-wrap: wrap;\n  margin-bottom: var(--space-16);\n\n  @media (max-width: 768px) {\n    gap: var(--space-4);\n    margin-bottom: var(--space-12);\n    flex-direction: column;\n  }\n`;\n_c12 = HeroActions;\nconst PrimaryButton = styled.button`\n  background: var(--bg-neural);\n  color: white;\n  border: none;\n  padding: var(--space-4) var(--space-10);\n  border-radius: var(--radius-xl);\n  font-size: 1.125rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  box-shadow: var(--shadow-neural);\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: -100%;\n    width: 100%;\n    height: 100%;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n    transition: var(--transition-slow);\n  }\n\n  &:hover {\n    transform: translateY(-3px);\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n\n    &::before {\n      left: 100%;\n    }\n  }\n\n  &:active {\n    transform: translateY(-1px);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-4) var(--space-8);\n    font-size: 1rem;\n    width: 100%;\n    max-width: 300px;\n  }\n`;\n_c13 = PrimaryButton;\nconst SecondaryButton = styled.button`\n  background: var(--bg-glass);\n  color: var(--text-primary);\n  border: 2px solid var(--border-neural);\n  padding: var(--space-4) var(--space-8);\n  border-radius: var(--radius-xl);\n  font-size: 1.125rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  backdrop-filter: blur(10px);\n\n  &:hover {\n    border-color: var(--primary-600);\n    color: var(--primary-600);\n    background: var(--primary-50);\n    transform: translateY(-2px);\n    box-shadow: var(--shadow-lg);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-4) var(--space-6);\n    font-size: 1rem;\n    width: 100%;\n    max-width: 300px;\n  }\n`;\n_c14 = SecondaryButton;\nconst FeaturesSection = styled.section`\n  padding: var(--space-24) var(--space-6);\n  background: var(--bg-tertiary);\n  position: relative;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),\n      radial-gradient(circle at 70% 70%, rgba(147, 51, 234, 0.05) 0%, transparent 50%);\n    pointer-events: none;\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-20) var(--space-4);\n  }\n`;\n_c15 = FeaturesSection;\nconst FeaturesContainer = styled.div`\n  max-width: 1400px;\n  margin: 0 auto;\n  text-align: center;\n  position: relative;\n  z-index: 1;\n`;\n_c16 = FeaturesContainer;\nconst SectionTitle = styled.h2`\n  font-family: var(--font-primary);\n  font-size: 3.5rem;\n  font-weight: 800;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin-bottom: var(--space-6);\n  letter-spacing: -0.02em;\n\n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n  }\n`;\n_c17 = SectionTitle;\nconst SectionSubtitle = styled.p`\n  font-size: 1.25rem;\n  color: var(--text-secondary);\n  margin-bottom: var(--space-16);\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n\n  @media (max-width: 768px) {\n    margin-bottom: var(--space-12);\n    font-size: 1.125rem;\n  }\n`;\n_c18 = SectionSubtitle;\nconst FeatureGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));\n  gap: var(--space-8);\n  margin-bottom: var(--space-20);\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-6);\n  }\n`;\n_c19 = FeatureGrid;\nconst FeatureCard = styled.div`\n  background: var(--bg-glass);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-10);\n  border: 1px solid var(--border-neural);\n  transition: var(--transition-normal);\n  text-align: left;\n  position: relative;\n  overflow: hidden;\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-lg);\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 4px;\n    background: var(--bg-neural);\n    transform: scaleX(0);\n    transition: var(--transition-normal);\n  }\n\n  &:hover {\n    transform: translateY(-6px);\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n    border-color: var(--primary-300);\n\n    &::before {\n      transform: scaleX(1);\n    }\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-8);\n    text-align: center;\n  }\n`;\n_c20 = FeatureCard;\nconst FeatureIconWrapper = styled.div`\n  width: 64px;\n  height: 64px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-xl);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: var(--space-6);\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    margin: 0 auto var(--space-6);\n  }\n`;\n_c21 = FeatureIconWrapper;\nconst FeatureTitle = styled.h3`\n  font-size: 1.5rem;\n  margin-bottom: var(--space-4);\n  color: var(--text-primary);\n  font-weight: 700;\n  font-family: var(--font-primary);\n\n  @media (max-width: 768px) {\n    text-align: center;\n  }\n`;\n_c22 = FeatureTitle;\nconst FeatureDescription = styled.p`\n  font-size: 1rem;\n  color: var(--text-secondary);\n  line-height: 1.7;\n  font-weight: 400;\n\n  @media (max-width: 768px) {\n    text-align: center;\n  }\n`;\n_c23 = FeatureDescription;\nconst HeroStats = styled.div`\n  display: flex;\n  justify-content: center;\n  gap: var(--space-8);\n  margin-top: var(--space-8);\n\n  @media (max-width: 768px) {\n    gap: var(--space-6);\n    flex-wrap: wrap;\n  }\n`;\n_c24 = HeroStats;\nconst StatItem = styled.div`\n  text-align: center;\n`;\n_c25 = StatItem;\nconst StatNumber = styled.div`\n  font-size: 2rem;\n  font-weight: 700;\n  color: var(--primary-600);\n  font-family: var(--font-primary);\n\n  @media (max-width: 768px) {\n    font-size: 1.5rem;\n  }\n`;\n_c26 = StatNumber;\nconst StatLabel = styled.div`\n  font-size: 0.875rem;\n  color: var(--text-tertiary);\n  font-weight: 500;\n  margin-top: var(--space-1);\n`;\n_c27 = StatLabel;\nconst HomePage = ({\n  onStartTraining,\n  onNavigateToAbout,\n  onNavigateToContact\n}) => {\n  return /*#__PURE__*/_jsxDEV(HomeContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Navigation, {\n      children: /*#__PURE__*/_jsxDEV(NavContainer, {\n        children: [/*#__PURE__*/_jsxDEV(Logo, {\n          children: [/*#__PURE__*/_jsxDEV(LogoIcon, {\n            children: /*#__PURE__*/_jsxDEV(Brain, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 13\n          }, this), \"ASL Neural\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(NavLinks, {\n          children: [/*#__PURE__*/_jsxDEV(NavLink, {\n            href: \"#features\",\n            children: \"Features\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NavLink, {\n            onClick: onNavigateToAbout,\n            style: {\n              cursor: 'pointer'\n            },\n            children: \"About\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NavLink, {\n            onClick: onNavigateToContact,\n            style: {\n              cursor: 'pointer'\n            },\n            children: \"Contact\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 537,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 536,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HeroSection, {\n      children: /*#__PURE__*/_jsxDEV(HeroContent, {\n        children: [/*#__PURE__*/_jsxDEV(HeroBadge, {\n          children: [/*#__PURE__*/_jsxDEV(Zap, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 13\n          }, this), \"AI-Powered Learning Platform\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(HeroTitle, {\n          children: \"Master Sign Language with Neural Intelligence\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 559,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(HeroSubtitle, {\n          children: \"Revolutionary AI platform that transforms sign language learning through real-time computer vision and adaptive neural networks\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 560,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(HeroDescription, {\n          children: \"Experience the future of accessibility education. Our advanced machine learning algorithms provide instant feedback while contributing to breakthrough AI research for the deaf and hard-of-hearing community.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(HeroActions, {\n          children: [/*#__PURE__*/_jsxDEV(PrimaryButton, {\n            onClick: onStartTraining,\n            children: [/*#__PURE__*/_jsxDEV(Play, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 15\n            }, this), \"Start Neural Training\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n            onClick: onNavigateToAbout,\n            children: [/*#__PURE__*/_jsxDEV(BookOpen, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 15\n            }, this), \"Explore Technology\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 570,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(HeroStats, {\n          children: [/*#__PURE__*/_jsxDEV(StatItem, {\n            children: [/*#__PURE__*/_jsxDEV(StatNumber, {\n              children: \"25K+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n              children: \"Neural Sessions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n            children: [/*#__PURE__*/_jsxDEV(StatNumber, {\n              children: \"150+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n              children: \"Sign Patterns\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n            children: [/*#__PURE__*/_jsxDEV(StatNumber, {\n              children: \"98.7%\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n              children: \"AI Accuracy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 553,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 552,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FeaturesSection, {\n      id: \"features\",\n      children: /*#__PURE__*/_jsxDEV(FeaturesContainer, {\n        children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: \"Neural Network Capabilities\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 600,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SectionSubtitle, {\n          children: \"Discover how our advanced AI technology revolutionizes sign language learning through cutting-edge computer vision and machine learning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FeatureGrid, {\n          children: [/*#__PURE__*/_jsxDEV(FeatureCard, {\n            children: [/*#__PURE__*/_jsxDEV(FeatureIconWrapper, {\n              children: /*#__PURE__*/_jsxDEV(Camera, {\n                size: 28,\n                color: \"white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureTitle, {\n              children: \"Real-time Computer Vision\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureDescription, {\n              children: \"Advanced neural networks analyze your hand movements in real-time, providing instant feedback with 98.7% accuracy using state-of-the-art pose estimation algorithms\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 607,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureCard, {\n            children: [/*#__PURE__*/_jsxDEV(FeatureIconWrapper, {\n              children: /*#__PURE__*/_jsxDEV(Cpu, {\n                size: 28,\n                color: \"white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 620,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureTitle, {\n              children: \"Adaptive AI Learning\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureDescription, {\n              children: \"Our deep learning models continuously adapt to your learning style, creating personalized training paths that optimize skill acquisition and retention rates\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 618,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureCard, {\n            children: [/*#__PURE__*/_jsxDEV(FeatureIconWrapper, {\n              children: /*#__PURE__*/_jsxDEV(Globe, {\n                size: 28,\n                color: \"white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 631,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureTitle, {\n              children: \"Global Impact Network\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureDescription, {\n              children: \"Join a worldwide community contributing to breakthrough AI research that advances accessibility technology for millions in the deaf and hard-of-hearing community\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 629,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureCard, {\n            children: [/*#__PURE__*/_jsxDEV(FeatureIconWrapper, {\n              children: /*#__PURE__*/_jsxDEV(Smartphone, {\n                size: 28,\n                color: \"white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureTitle, {\n              children: \"Cross-Platform Intelligence\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 644,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureDescription, {\n              children: \"Seamless AI-powered experience across all devices with cloud-synchronized progress and edge computing for lightning-fast response times\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 645,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 640,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureCard, {\n            children: [/*#__PURE__*/_jsxDEV(FeatureIconWrapper, {\n              children: /*#__PURE__*/_jsxDEV(Target, {\n                size: 28,\n                color: \"white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 653,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 652,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureTitle, {\n              children: \"Precision Learning Analytics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 655,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureDescription, {\n              children: \"Advanced analytics track micro-movements and learning patterns, providing data-driven insights to accelerate your mastery of sign language\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 656,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureCard, {\n            children: [/*#__PURE__*/_jsxDEV(FeatureIconWrapper, {\n              children: /*#__PURE__*/_jsxDEV(Shield, {\n                size: 28,\n                color: \"white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 663,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureTitle, {\n              children: \"Privacy-First Architecture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureDescription, {\n              children: \"Enterprise-grade security with local processing ensures your data remains private while contributing anonymized insights to advance AI research\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 667,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 606,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 599,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 598,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 535,\n    columnNumber: 5\n  }, this);\n};\n_c28 = HomePage;\nexport default HomePage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28;\n$RefreshReg$(_c, \"HomeContainer\");\n$RefreshReg$(_c2, \"Navigation\");\n$RefreshReg$(_c3, \"NavContainer\");\n$RefreshReg$(_c4, \"Logo\");\n$RefreshReg$(_c5, \"LogoIcon\");\n$RefreshReg$(_c6, \"NavLinks\");\n$RefreshReg$(_c7, \"NavLink\");\n$RefreshReg$(_c8, \"HeroSection\");\n$RefreshReg$(_c9, \"HeroContent\");\n$RefreshReg$(_c0, \"HeroBadge\");\n$RefreshReg$(_c1, \"HeroTitle\");\n$RefreshReg$(_c10, \"HeroSubtitle\");\n$RefreshReg$(_c11, \"HeroDescription\");\n$RefreshReg$(_c12, \"HeroActions\");\n$RefreshReg$(_c13, \"PrimaryButton\");\n$RefreshReg$(_c14, \"SecondaryButton\");\n$RefreshReg$(_c15, \"FeaturesSection\");\n$RefreshReg$(_c16, \"FeaturesContainer\");\n$RefreshReg$(_c17, \"SectionTitle\");\n$RefreshReg$(_c18, \"SectionSubtitle\");\n$RefreshReg$(_c19, \"FeatureGrid\");\n$RefreshReg$(_c20, \"FeatureCard\");\n$RefreshReg$(_c21, \"FeatureIconWrapper\");\n$RefreshReg$(_c22, \"FeatureTitle\");\n$RefreshReg$(_c23, \"FeatureDescription\");\n$RefreshReg$(_c24, \"HeroStats\");\n$RefreshReg$(_c25, \"StatItem\");\n$RefreshReg$(_c26, \"StatNumber\");\n$RefreshReg$(_c27, \"StatLabel\");\n$RefreshReg$(_c28, \"HomePage\");", "map": {"version": 3, "names": ["React", "styled", "Brain", "Camera", "Users", "Shield", "Smartphone", "Target", "Play", "BookOpen", "Zap", "Cpu", "Eye", "Award", "TrendingUp", "Globe", "jsxDEV", "_jsxDEV", "HomeContainer", "div", "_c", "Navigation", "nav", "_c2", "NavContainer", "_c3", "Logo", "_c4", "LogoIcon", "_c5", "NavLinks", "_c6", "NavLink", "a", "_c7", "HeroSection", "section", "_c8", "Hero<PERSON><PERSON><PERSON>", "_c9", "HeroBadge", "_c0", "<PERSON><PERSON><PERSON><PERSON>", "h1", "_c1", "HeroSubtitle", "p", "_c10", "HeroDescription", "_c11", "HeroActions", "_c12", "PrimaryButton", "button", "_c13", "SecondaryButton", "_c14", "FeaturesSection", "_c15", "FeaturesContainer", "_c16", "SectionTitle", "h2", "_c17", "SectionSubtitle", "_c18", "FeatureGrid", "_c19", "FeatureCard", "_c20", "FeatureIconWrapper", "_c21", "FeatureTitle", "h3", "_c22", "FeatureDescription", "_c23", "HeroStats", "_c24", "StatItem", "_c25", "StatNumber", "_c26", "StatLabel", "_c27", "HomePage", "onStartTraining", "onNavigateToAbout", "onNavigateToContact", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "onClick", "style", "cursor", "id", "color", "_c28", "$RefreshReg$"], "sources": ["D:/ASL/training-frontend/src/components/HomePage.js"], "sourcesContent": ["import React from 'react';\r\nimport styled from 'styled-components';\r\nimport {\r\n  Brain,\r\n  Camera,\r\n  Users,\r\n  Shield,\r\n  Smartphone,\r\n  Target,\r\n  Play,\r\n  BookOpen,\r\n  Zap,\r\n  Cpu,\r\n  Eye,\r\n  Award,\r\n  TrendingUp,\r\n  Globe\r\n} from 'lucide-react';\r\n\r\nconst HomeContainer = styled.div`\r\n  min-height: 100vh;\r\n  background: var(--bg-primary);\r\n  position: relative;\r\n  overflow: hidden;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background:\r\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\r\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%),\r\n      radial-gradient(circle at 40% 60%, rgba(16, 185, 129, 0.02) 0%, transparent 50%);\r\n    pointer-events: none;\r\n    z-index: 0;\r\n  }\r\n`;\r\n\r\nconst Navigation = styled.nav`\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 50;\r\n  background: var(--bg-glass);\r\n  backdrop-filter: blur(20px);\r\n  border-bottom: 1px solid var(--border-neural);\r\n  padding: var(--space-4) 0;\r\n  transition: var(--transition-normal);\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-3) 0;\r\n  }\r\n`;\r\n\r\nconst NavContainer = styled.div`\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 0 var(--space-6);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n\r\n  @media (max-width: 768px) {\r\n    padding: 0 var(--space-4);\r\n  }\r\n`;\r\n\r\nconst Logo = styled.div`\r\n  font-family: var(--font-primary);\r\n  font-size: 1.75rem;\r\n  font-weight: 800;\r\n  background: var(--text-gradient);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-3);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.5rem;\r\n    gap: var(--space-2);\r\n  }\r\n`;\r\n\r\nconst LogoIcon = styled.div`\r\n  width: 40px;\r\n  height: 40px;\r\n  background: var(--bg-neural);\r\n  border-radius: var(--radius-lg);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  box-shadow: var(--shadow-neural);\r\n\r\n  @media (max-width: 768px) {\r\n    width: 36px;\r\n    height: 36px;\r\n  }\r\n`;\r\n\r\nconst NavLinks = styled.div`\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-8);\r\n\r\n  @media (max-width: 768px) {\r\n    gap: var(--space-4);\r\n  }\r\n`;\r\n\r\nconst NavLink = styled.a`\r\n  color: var(--text-secondary);\r\n  text-decoration: none;\r\n  font-weight: 500;\r\n  font-size: 0.9rem;\r\n  transition: var(--transition-fast);\r\n  position: relative;\r\n\r\n  &:hover {\r\n    color: var(--text-accent);\r\n  }\r\n\r\n  &::after {\r\n    content: '';\r\n    position: absolute;\r\n    bottom: -4px;\r\n    left: 0;\r\n    width: 0;\r\n    height: 2px;\r\n    background: var(--bg-neural);\r\n    transition: var(--transition-normal);\r\n  }\r\n\r\n  &:hover::after {\r\n    width: 100%;\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 0.85rem;\r\n  }\r\n`;\r\n\r\nconst HeroSection = styled.section`\r\n  min-height: 100vh;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: var(--space-24) var(--space-6) var(--space-20);\r\n  text-align: center;\r\n  position: relative;\r\n  z-index: 1;\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-20) var(--space-4) var(--space-16);\r\n    min-height: calc(100vh - 80px);\r\n  }\r\n`;\r\n\r\nconst HeroContent = styled.div`\r\n  max-width: 900px;\r\n  width: 100%;\r\n  position: relative;\r\n  z-index: 2;\r\n`;\r\n\r\nconst HeroBadge = styled.div`\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  background: var(--bg-glass);\r\n  border: 1px solid var(--border-neural);\r\n  border-radius: var(--radius-full);\r\n  padding: var(--space-2) var(--space-4);\r\n  margin-bottom: var(--space-6);\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  color: var(--text-accent);\r\n  backdrop-filter: blur(10px);\r\n  box-shadow: var(--shadow-glow);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 0.8rem;\r\n    padding: var(--space-2) var(--space-3);\r\n  }\r\n`;\r\n\r\nconst HeroTitle = styled.h1`\r\n  font-family: var(--font-primary);\r\n  font-size: 4.5rem;\r\n  font-weight: 900;\r\n  background: var(--text-gradient);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  margin-bottom: var(--space-6);\r\n  line-height: 1.1;\r\n  letter-spacing: -0.03em;\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 3rem;\r\n    margin-bottom: var(--space-4);\r\n  }\r\n\r\n  @media (max-width: 480px) {\r\n    font-size: 2.25rem;\r\n  }\r\n`;\r\n\r\nconst HeroSubtitle = styled.p`\r\n  font-size: 1.375rem;\r\n  font-weight: 400;\r\n  color: var(--text-secondary);\r\n  margin-bottom: var(--space-8);\r\n  line-height: 1.6;\r\n  max-width: 700px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.25rem;\r\n    margin-bottom: var(--space-6);\r\n  }\r\n\r\n  @media (max-width: 480px) {\r\n    font-size: 1.125rem;\r\n  }\r\n`;\r\n\r\nconst HeroDescription = styled.p`\r\n  font-size: 1.125rem;\r\n  line-height: 1.7;\r\n  color: var(--text-tertiary);\r\n  margin-bottom: var(--space-10);\r\n  max-width: 600px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1rem;\r\n    margin-bottom: var(--space-8);\r\n    line-height: 1.6;\r\n  }\r\n`;\r\n\r\nconst HeroActions = styled.div`\r\n  display: flex;\r\n  gap: var(--space-5);\r\n  justify-content: center;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  margin-bottom: var(--space-16);\r\n\r\n  @media (max-width: 768px) {\r\n    gap: var(--space-4);\r\n    margin-bottom: var(--space-12);\r\n    flex-direction: column;\r\n  }\r\n`;\r\n\r\nconst PrimaryButton = styled.button`\r\n  background: var(--bg-neural);\r\n  color: white;\r\n  border: none;\r\n  padding: var(--space-4) var(--space-10);\r\n  border-radius: var(--radius-xl);\r\n  font-size: 1.125rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: var(--transition-normal);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-3);\r\n  box-shadow: var(--shadow-neural);\r\n  position: relative;\r\n  overflow: hidden;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: -100%;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n    transition: var(--transition-slow);\r\n  }\r\n\r\n  &:hover {\r\n    transform: translateY(-3px);\r\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\r\n\r\n    &::before {\r\n      left: 100%;\r\n    }\r\n  }\r\n\r\n  &:active {\r\n    transform: translateY(-1px);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-4) var(--space-8);\r\n    font-size: 1rem;\r\n    width: 100%;\r\n    max-width: 300px;\r\n  }\r\n`;\r\n\r\nconst SecondaryButton = styled.button`\r\n  background: var(--bg-glass);\r\n  color: var(--text-primary);\r\n  border: 2px solid var(--border-neural);\r\n  padding: var(--space-4) var(--space-8);\r\n  border-radius: var(--radius-xl);\r\n  font-size: 1.125rem;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: var(--transition-normal);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-3);\r\n  backdrop-filter: blur(10px);\r\n\r\n  &:hover {\r\n    border-color: var(--primary-600);\r\n    color: var(--primary-600);\r\n    background: var(--primary-50);\r\n    transform: translateY(-2px);\r\n    box-shadow: var(--shadow-lg);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-4) var(--space-6);\r\n    font-size: 1rem;\r\n    width: 100%;\r\n    max-width: 300px;\r\n  }\r\n`;\r\n\r\nconst FeaturesSection = styled.section`\r\n  padding: var(--space-24) var(--space-6);\r\n  background: var(--bg-tertiary);\r\n  position: relative;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background:\r\n      radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),\r\n      radial-gradient(circle at 70% 70%, rgba(147, 51, 234, 0.05) 0%, transparent 50%);\r\n    pointer-events: none;\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-20) var(--space-4);\r\n  }\r\n`;\r\n\r\nconst FeaturesContainer = styled.div`\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  text-align: center;\r\n  position: relative;\r\n  z-index: 1;\r\n`;\r\n\r\nconst SectionTitle = styled.h2`\r\n  font-family: var(--font-primary);\r\n  font-size: 3.5rem;\r\n  font-weight: 800;\r\n  background: var(--text-gradient);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  margin-bottom: var(--space-6);\r\n  letter-spacing: -0.02em;\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 2.5rem;\r\n  }\r\n`;\r\n\r\nconst SectionSubtitle = styled.p`\r\n  font-size: 1.25rem;\r\n  color: var(--text-secondary);\r\n  margin-bottom: var(--space-16);\r\n  max-width: 700px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n  line-height: 1.6;\r\n\r\n  @media (max-width: 768px) {\r\n    margin-bottom: var(--space-12);\r\n    font-size: 1.125rem;\r\n  }\r\n`;\r\n\r\nconst FeatureGrid = styled.div`\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));\r\n  gap: var(--space-8);\r\n  margin-bottom: var(--space-20);\r\n\r\n  @media (max-width: 768px) {\r\n    grid-template-columns: 1fr;\r\n    gap: var(--space-6);\r\n  }\r\n`;\r\n\r\nconst FeatureCard = styled.div`\r\n  background: var(--bg-glass);\r\n  border-radius: var(--radius-2xl);\r\n  padding: var(--space-10);\r\n  border: 1px solid var(--border-neural);\r\n  transition: var(--transition-normal);\r\n  text-align: left;\r\n  position: relative;\r\n  overflow: hidden;\r\n  backdrop-filter: blur(20px);\r\n  box-shadow: var(--shadow-lg);\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 4px;\r\n    background: var(--bg-neural);\r\n    transform: scaleX(0);\r\n    transition: var(--transition-normal);\r\n  }\r\n\r\n  &:hover {\r\n    transform: translateY(-6px);\r\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\r\n    border-color: var(--primary-300);\r\n\r\n    &::before {\r\n      transform: scaleX(1);\r\n    }\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-8);\r\n    text-align: center;\r\n  }\r\n`;\r\n\r\nconst FeatureIconWrapper = styled.div`\r\n  width: 64px;\r\n  height: 64px;\r\n  background: var(--bg-neural);\r\n  border-radius: var(--radius-xl);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-bottom: var(--space-6);\r\n  box-shadow: var(--shadow-neural);\r\n\r\n  @media (max-width: 768px) {\r\n    margin: 0 auto var(--space-6);\r\n  }\r\n`;\r\n\r\nconst FeatureTitle = styled.h3`\r\n  font-size: 1.5rem;\r\n  margin-bottom: var(--space-4);\r\n  color: var(--text-primary);\r\n  font-weight: 700;\r\n  font-family: var(--font-primary);\r\n\r\n  @media (max-width: 768px) {\r\n    text-align: center;\r\n  }\r\n`;\r\n\r\nconst FeatureDescription = styled.p`\r\n  font-size: 1rem;\r\n  color: var(--text-secondary);\r\n  line-height: 1.7;\r\n  font-weight: 400;\r\n\r\n  @media (max-width: 768px) {\r\n    text-align: center;\r\n  }\r\n`;\r\n\r\nconst HeroStats = styled.div`\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: var(--space-8);\r\n  margin-top: var(--space-8);\r\n\r\n  @media (max-width: 768px) {\r\n    gap: var(--space-6);\r\n    flex-wrap: wrap;\r\n  }\r\n`;\r\n\r\nconst StatItem = styled.div`\r\n  text-align: center;\r\n`;\r\n\r\nconst StatNumber = styled.div`\r\n  font-size: 2rem;\r\n  font-weight: 700;\r\n  color: var(--primary-600);\r\n  font-family: var(--font-primary);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.5rem;\r\n  }\r\n`;\r\n\r\nconst StatLabel = styled.div`\r\n  font-size: 0.875rem;\r\n  color: var(--text-tertiary);\r\n  font-weight: 500;\r\n  margin-top: var(--space-1);\r\n`;\r\n\r\nconst HomePage = ({ onStartTraining, onNavigateToAbout, onNavigateToContact }) => {\r\n  return (\r\n    <HomeContainer>\r\n      <Navigation>\r\n        <NavContainer>\r\n          <Logo>\r\n            <LogoIcon>\r\n              <Brain size={24} />\r\n            </LogoIcon>\r\n            ASL Neural\r\n          </Logo>\r\n          <NavLinks>\r\n            <NavLink href=\"#features\">Features</NavLink>\r\n            <NavLink onClick={onNavigateToAbout} style={{ cursor: 'pointer' }}>About</NavLink>\r\n            <NavLink onClick={onNavigateToContact} style={{ cursor: 'pointer' }}>Contact</NavLink>\r\n          </NavLinks>\r\n        </NavContainer>\r\n      </Navigation>\r\n\r\n      <HeroSection>\r\n        <HeroContent>\r\n          <HeroBadge>\r\n            <Zap size={16} />\r\n            AI-Powered Learning Platform\r\n          </HeroBadge>\r\n\r\n          <HeroTitle>Master Sign Language with Neural Intelligence</HeroTitle>\r\n          <HeroSubtitle>\r\n            Revolutionary AI platform that transforms sign language learning through\r\n            real-time computer vision and adaptive neural networks\r\n          </HeroSubtitle>\r\n          <HeroDescription>\r\n            Experience the future of accessibility education. Our advanced machine learning\r\n            algorithms provide instant feedback while contributing to breakthrough AI research\r\n            for the deaf and hard-of-hearing community.\r\n          </HeroDescription>\r\n\r\n          <HeroActions>\r\n            <PrimaryButton onClick={onStartTraining}>\r\n              <Play size={20} />\r\n              Start Neural Training\r\n            </PrimaryButton>\r\n            <SecondaryButton onClick={onNavigateToAbout}>\r\n              <BookOpen size={20} />\r\n              Explore Technology\r\n            </SecondaryButton>\r\n          </HeroActions>\r\n\r\n          <HeroStats>\r\n            <StatItem>\r\n              <StatNumber>25K+</StatNumber>\r\n              <StatLabel>Neural Sessions</StatLabel>\r\n            </StatItem>\r\n            <StatItem>\r\n              <StatNumber>150+</StatNumber>\r\n              <StatLabel>Sign Patterns</StatLabel>\r\n            </StatItem>\r\n            <StatItem>\r\n              <StatNumber>98.7%</StatNumber>\r\n              <StatLabel>AI Accuracy</StatLabel>\r\n            </StatItem>\r\n          </HeroStats>\r\n        </HeroContent>\r\n      </HeroSection>\r\n\r\n      <FeaturesSection id=\"features\">\r\n        <FeaturesContainer>\r\n          <SectionTitle>Neural Network Capabilities</SectionTitle>\r\n          <SectionSubtitle>\r\n            Discover how our advanced AI technology revolutionizes sign language learning\r\n            through cutting-edge computer vision and machine learning\r\n          </SectionSubtitle>\r\n\r\n          <FeatureGrid>\r\n            <FeatureCard>\r\n              <FeatureIconWrapper>\r\n                <Camera size={28} color=\"white\" />\r\n              </FeatureIconWrapper>\r\n              <FeatureTitle>Real-time Computer Vision</FeatureTitle>\r\n              <FeatureDescription>\r\n                Advanced neural networks analyze your hand movements in real-time, providing\r\n                instant feedback with 98.7% accuracy using state-of-the-art pose estimation algorithms\r\n              </FeatureDescription>\r\n            </FeatureCard>\r\n\r\n            <FeatureCard>\r\n              <FeatureIconWrapper>\r\n                <Cpu size={28} color=\"white\" />\r\n              </FeatureIconWrapper>\r\n              <FeatureTitle>Adaptive AI Learning</FeatureTitle>\r\n              <FeatureDescription>\r\n                Our deep learning models continuously adapt to your learning style, creating\r\n                personalized training paths that optimize skill acquisition and retention rates\r\n              </FeatureDescription>\r\n            </FeatureCard>\r\n\r\n            <FeatureCard>\r\n              <FeatureIconWrapper>\r\n                <Globe size={28} color=\"white\" />\r\n              </FeatureIconWrapper>\r\n              <FeatureTitle>Global Impact Network</FeatureTitle>\r\n              <FeatureDescription>\r\n                Join a worldwide community contributing to breakthrough AI research that advances\r\n                accessibility technology for millions in the deaf and hard-of-hearing community\r\n              </FeatureDescription>\r\n            </FeatureCard>\r\n\r\n            <FeatureCard>\r\n              <FeatureIconWrapper>\r\n                <Smartphone size={28} color=\"white\" />\r\n              </FeatureIconWrapper>\r\n              <FeatureTitle>Cross-Platform Intelligence</FeatureTitle>\r\n              <FeatureDescription>\r\n                Seamless AI-powered experience across all devices with cloud-synchronized progress\r\n                and edge computing for lightning-fast response times\r\n              </FeatureDescription>\r\n            </FeatureCard>\r\n\r\n            <FeatureCard>\r\n              <FeatureIconWrapper>\r\n                <Target size={28} color=\"white\" />\r\n              </FeatureIconWrapper>\r\n              <FeatureTitle>Precision Learning Analytics</FeatureTitle>\r\n              <FeatureDescription>\r\n                Advanced analytics track micro-movements and learning patterns, providing\r\n                data-driven insights to accelerate your mastery of sign language\r\n              </FeatureDescription>\r\n            </FeatureCard>\r\n\r\n            <FeatureCard>\r\n              <FeatureIconWrapper>\r\n                <Shield size={28} color=\"white\" />\r\n              </FeatureIconWrapper>\r\n              <FeatureTitle>Privacy-First Architecture</FeatureTitle>\r\n              <FeatureDescription>\r\n                Enterprise-grade security with local processing ensures your data remains private\r\n                while contributing anonymized insights to advance AI research\r\n              </FeatureDescription>\r\n            </FeatureCard>\r\n          </FeatureGrid>\r\n        </FeaturesContainer>\r\n      </FeaturesSection>\r\n    </HomeContainer>\r\n  );\r\n};\r\n\r\nexport default HomePage; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SACEC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,GAAG,EACHC,GAAG,EACHC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,KAAK,QACA,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,aAAa,GAAGjB,MAAM,CAACkB,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GApBIF,aAAa;AAsBnB,MAAMG,UAAU,GAAGpB,MAAM,CAACqB,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAfIF,UAAU;AAiBhB,MAAMG,YAAY,GAAGvB,MAAM,CAACkB,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACM,GAAA,GAXID,YAAY;AAalB,MAAME,IAAI,GAAGzB,MAAM,CAACkB,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GAhBID,IAAI;AAkBV,MAAME,QAAQ,GAAG3B,MAAM,CAACkB,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GAfID,QAAQ;AAiBd,MAAME,QAAQ,GAAG7B,MAAM,CAACkB,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACY,GAAA,GARID,QAAQ;AAUd,MAAME,OAAO,GAAG/B,MAAM,CAACgC,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GA9BIF,OAAO;AAgCb,MAAMG,WAAW,GAAGlC,MAAM,CAACmC,OAAO;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAdIF,WAAW;AAgBjB,MAAMG,WAAW,GAAGrC,MAAM,CAACkB,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACoB,GAAA,GALID,WAAW;AAOjB,MAAME,SAAS,GAAGvC,MAAM,CAACkB,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsB,GAAA,GAnBID,SAAS;AAqBf,MAAME,SAAS,GAAGzC,MAAM,CAAC0C,EAAE;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GApBIF,SAAS;AAsBf,MAAMG,YAAY,GAAG5C,MAAM,CAAC6C,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAlBIF,YAAY;AAoBlB,MAAMG,eAAe,GAAG/C,MAAM,CAAC6C,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,IAAA,GAdID,eAAe;AAgBrB,MAAME,WAAW,GAAGjD,MAAM,CAACkB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgC,IAAA,GAbID,WAAW;AAejB,MAAME,aAAa,GAAGnD,MAAM,CAACoD,MAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GA/CIF,aAAa;AAiDnB,MAAMG,eAAe,GAAGtD,MAAM,CAACoD,MAAM;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,IAAA,GA7BID,eAAe;AA+BrB,MAAME,eAAe,GAAGxD,MAAM,CAACmC,OAAO;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsB,IAAA,GArBID,eAAe;AAuBrB,MAAME,iBAAiB,GAAG1D,MAAM,CAACkB,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyC,IAAA,GANID,iBAAiB;AAQvB,MAAME,YAAY,GAAG5D,MAAM,CAAC6D,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAdIF,YAAY;AAgBlB,MAAMG,eAAe,GAAG/D,MAAM,CAAC6C,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmB,IAAA,GAbID,eAAe;AAerB,MAAME,WAAW,GAAGjE,MAAM,CAACkB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgD,IAAA,GAVID,WAAW;AAYjB,MAAME,WAAW,GAAGnE,MAAM,CAACkB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkD,IAAA,GAtCID,WAAW;AAwCjB,MAAME,kBAAkB,GAAGrE,MAAM,CAACkB,GAAG;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACoD,IAAA,GAdID,kBAAkB;AAgBxB,MAAME,YAAY,GAAGvE,MAAM,CAACwE,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAVIF,YAAY;AAYlB,MAAMG,kBAAkB,GAAG1E,MAAM,CAAC6C,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC8B,IAAA,GATID,kBAAkB;AAWxB,MAAME,SAAS,GAAG5E,MAAM,CAACkB,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC2D,IAAA,GAVID,SAAS;AAYf,MAAME,QAAQ,GAAG9E,MAAM,CAACkB,GAAG;AAC3B;AACA,CAAC;AAAC6D,IAAA,GAFID,QAAQ;AAId,MAAME,UAAU,GAAGhF,MAAM,CAACkB,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC+D,IAAA,GATID,UAAU;AAWhB,MAAME,SAAS,GAAGlF,MAAM,CAACkB,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAACiE,IAAA,GALID,SAAS;AAOf,MAAME,QAAQ,GAAGA,CAAC;EAAEC,eAAe;EAAEC,iBAAiB;EAAEC;AAAoB,CAAC,KAAK;EAChF,oBACEvE,OAAA,CAACC,aAAa;IAAAuE,QAAA,gBACZxE,OAAA,CAACI,UAAU;MAAAoE,QAAA,eACTxE,OAAA,CAACO,YAAY;QAAAiE,QAAA,gBACXxE,OAAA,CAACS,IAAI;UAAA+D,QAAA,gBACHxE,OAAA,CAACW,QAAQ;YAAA6D,QAAA,eACPxE,OAAA,CAACf,KAAK;cAACwF,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,cAEb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP7E,OAAA,CAACa,QAAQ;UAAA2D,QAAA,gBACPxE,OAAA,CAACe,OAAO;YAAC+D,IAAI,EAAC,WAAW;YAAAN,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eAC5C7E,OAAA,CAACe,OAAO;YAACgE,OAAO,EAAET,iBAAkB;YAACU,KAAK,EAAE;cAAEC,MAAM,EAAE;YAAU,CAAE;YAAAT,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eAClF7E,OAAA,CAACe,OAAO;YAACgE,OAAO,EAAER,mBAAoB;YAACS,KAAK,EAAE;cAAEC,MAAM,EAAE;YAAU,CAAE;YAAAT,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEb7E,OAAA,CAACkB,WAAW;MAAAsD,QAAA,eACVxE,OAAA,CAACqB,WAAW;QAAAmD,QAAA,gBACVxE,OAAA,CAACuB,SAAS;UAAAiD,QAAA,gBACRxE,OAAA,CAACP,GAAG;YAACgF,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gCAEnB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eAEZ7E,OAAA,CAACyB,SAAS;UAAA+C,QAAA,EAAC;QAA6C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACpE7E,OAAA,CAAC4B,YAAY;UAAA4C,QAAA,EAAC;QAGd;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACf7E,OAAA,CAAC+B,eAAe;UAAAyC,QAAA,EAAC;QAIjB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAiB,CAAC,eAElB7E,OAAA,CAACiC,WAAW;UAAAuC,QAAA,gBACVxE,OAAA,CAACmC,aAAa;YAAC4C,OAAO,EAAEV,eAAgB;YAAAG,QAAA,gBACtCxE,OAAA,CAACT,IAAI;cAACkF,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,yBAEpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,eAChB7E,OAAA,CAACsC,eAAe;YAACyC,OAAO,EAAET,iBAAkB;YAAAE,QAAA,gBAC1CxE,OAAA,CAACR,QAAQ;cAACiF,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sBAExB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAiB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAEd7E,OAAA,CAAC4D,SAAS;UAAAY,QAAA,gBACRxE,OAAA,CAAC8D,QAAQ;YAAAU,QAAA,gBACPxE,OAAA,CAACgE,UAAU;cAAAQ,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7B7E,OAAA,CAACkE,SAAS;cAAAM,QAAA,EAAC;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACX7E,OAAA,CAAC8D,QAAQ;YAAAU,QAAA,gBACPxE,OAAA,CAACgE,UAAU;cAAAQ,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7B7E,OAAA,CAACkE,SAAS;cAAAM,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACX7E,OAAA,CAAC8D,QAAQ;YAAAU,QAAA,gBACPxE,OAAA,CAACgE,UAAU;cAAAQ,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC9B7E,OAAA,CAACkE,SAAS;cAAAM,QAAA,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEd7E,OAAA,CAACwC,eAAe;MAAC0C,EAAE,EAAC,UAAU;MAAAV,QAAA,eAC5BxE,OAAA,CAAC0C,iBAAiB;QAAA8B,QAAA,gBAChBxE,OAAA,CAAC4C,YAAY;UAAA4B,QAAA,EAAC;QAA2B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACxD7E,OAAA,CAAC+C,eAAe;UAAAyB,QAAA,EAAC;QAGjB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAiB,CAAC,eAElB7E,OAAA,CAACiD,WAAW;UAAAuB,QAAA,gBACVxE,OAAA,CAACmD,WAAW;YAAAqB,QAAA,gBACVxE,OAAA,CAACqD,kBAAkB;cAAAmB,QAAA,eACjBxE,OAAA,CAACd,MAAM;gBAACuF,IAAI,EAAE,EAAG;gBAACU,KAAK,EAAC;cAAO;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACrB7E,OAAA,CAACuD,YAAY;cAAAiB,QAAA,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC,eACtD7E,OAAA,CAAC0D,kBAAkB;cAAAc,QAAA,EAAC;YAGpB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEd7E,OAAA,CAACmD,WAAW;YAAAqB,QAAA,gBACVxE,OAAA,CAACqD,kBAAkB;cAAAmB,QAAA,eACjBxE,OAAA,CAACN,GAAG;gBAAC+E,IAAI,EAAE,EAAG;gBAACU,KAAK,EAAC;cAAO;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACrB7E,OAAA,CAACuD,YAAY;cAAAiB,QAAA,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC,eACjD7E,OAAA,CAAC0D,kBAAkB;cAAAc,QAAA,EAAC;YAGpB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEd7E,OAAA,CAACmD,WAAW;YAAAqB,QAAA,gBACVxE,OAAA,CAACqD,kBAAkB;cAAAmB,QAAA,eACjBxE,OAAA,CAACF,KAAK;gBAAC2E,IAAI,EAAE,EAAG;gBAACU,KAAK,EAAC;cAAO;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACrB7E,OAAA,CAACuD,YAAY;cAAAiB,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC,eAClD7E,OAAA,CAAC0D,kBAAkB;cAAAc,QAAA,EAAC;YAGpB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEd7E,OAAA,CAACmD,WAAW;YAAAqB,QAAA,gBACVxE,OAAA,CAACqD,kBAAkB;cAAAmB,QAAA,eACjBxE,OAAA,CAACX,UAAU;gBAACoF,IAAI,EAAE,EAAG;gBAACU,KAAK,EAAC;cAAO;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACrB7E,OAAA,CAACuD,YAAY;cAAAiB,QAAA,EAAC;YAA2B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC,eACxD7E,OAAA,CAAC0D,kBAAkB;cAAAc,QAAA,EAAC;YAGpB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEd7E,OAAA,CAACmD,WAAW;YAAAqB,QAAA,gBACVxE,OAAA,CAACqD,kBAAkB;cAAAmB,QAAA,eACjBxE,OAAA,CAACV,MAAM;gBAACmF,IAAI,EAAE,EAAG;gBAACU,KAAK,EAAC;cAAO;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACrB7E,OAAA,CAACuD,YAAY;cAAAiB,QAAA,EAAC;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC,eACzD7E,OAAA,CAAC0D,kBAAkB;cAAAc,QAAA,EAAC;YAGpB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEd7E,OAAA,CAACmD,WAAW;YAAAqB,QAAA,gBACVxE,OAAA,CAACqD,kBAAkB;cAAAmB,QAAA,eACjBxE,OAAA,CAACZ,MAAM;gBAACqF,IAAI,EAAE,EAAG;gBAACU,KAAK,EAAC;cAAO;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACrB7E,OAAA,CAACuD,YAAY;cAAAiB,QAAA,EAAC;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC,eACvD7E,OAAA,CAAC0D,kBAAkB;cAAAc,QAAA,EAAC;YAGpB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEpB,CAAC;AAACO,IAAA,GAhJIhB,QAAQ;AAkJd,eAAeA,QAAQ;AAAC,IAAAjE,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAiB,IAAA;AAAAC,YAAA,CAAAlF,EAAA;AAAAkF,YAAA,CAAA/E,GAAA;AAAA+E,YAAA,CAAA7E,GAAA;AAAA6E,YAAA,CAAA3E,GAAA;AAAA2E,YAAA,CAAAzE,GAAA;AAAAyE,YAAA,CAAAvE,GAAA;AAAAuE,YAAA,CAAApE,GAAA;AAAAoE,YAAA,CAAAjE,GAAA;AAAAiE,YAAA,CAAA/D,GAAA;AAAA+D,YAAA,CAAA7D,GAAA;AAAA6D,YAAA,CAAA1D,GAAA;AAAA0D,YAAA,CAAAvD,IAAA;AAAAuD,YAAA,CAAArD,IAAA;AAAAqD,YAAA,CAAAnD,IAAA;AAAAmD,YAAA,CAAAhD,IAAA;AAAAgD,YAAA,CAAA9C,IAAA;AAAA8C,YAAA,CAAA5C,IAAA;AAAA4C,YAAA,CAAA1C,IAAA;AAAA0C,YAAA,CAAAvC,IAAA;AAAAuC,YAAA,CAAArC,IAAA;AAAAqC,YAAA,CAAAnC,IAAA;AAAAmC,YAAA,CAAAjC,IAAA;AAAAiC,YAAA,CAAA/B,IAAA;AAAA+B,YAAA,CAAA5B,IAAA;AAAA4B,YAAA,CAAA1B,IAAA;AAAA0B,YAAA,CAAAxB,IAAA;AAAAwB,YAAA,CAAAtB,IAAA;AAAAsB,YAAA,CAAApB,IAAA;AAAAoB,YAAA,CAAAlB,IAAA;AAAAkB,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}