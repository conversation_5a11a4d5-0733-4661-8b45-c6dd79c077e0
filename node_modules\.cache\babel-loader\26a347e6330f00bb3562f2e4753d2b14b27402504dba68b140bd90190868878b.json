{"ast": null, "code": "import { PacketType } from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n  connect: 1,\n  connect_error: 1,\n  disconnect: 1,\n  disconnecting: 1,\n  // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n  newListener: 1,\n  removeListener: 1\n});\n/**\n * A Socket is the fundamental class for interacting with the server.\n *\n * A Socket belongs to a certain Namespace (by default /) and uses an underlying {@link Manager} to communicate.\n *\n * @example\n * const socket = io();\n *\n * socket.on(\"connect\", () => {\n *   console.log(\"connected\");\n * });\n *\n * // send an event to the server\n * socket.emit(\"foo\", \"bar\");\n *\n * socket.on(\"foobar\", () => {\n *   // an event was received from the server\n * });\n *\n * // upon disconnection\n * socket.on(\"disconnect\", (reason) => {\n *   console.log(`disconnected due to ${reason}`);\n * });\n */\nexport class Socket extends Emitter {\n  /**\n   * `Socket` constructor.\n   */\n  constructor(io, nsp, opts) {\n    super();\n    /**\n     * Whether the socket is currently connected to the server.\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"connect\", () => {\n     *   console.log(socket.connected); // true\n     * });\n     *\n     * socket.on(\"disconnect\", () => {\n     *   console.log(socket.connected); // false\n     * });\n     */\n    this.connected = false;\n    /**\n     * Whether the connection state was recovered after a temporary disconnection. In that case, any missed packets will\n     * be transmitted by the server.\n     */\n    this.recovered = false;\n    /**\n     * Buffer for packets received before the CONNECT packet\n     */\n    this.receiveBuffer = [];\n    /**\n     * Buffer for packets that will be sent once the socket is connected\n     */\n    this.sendBuffer = [];\n    /**\n     * The queue of packets to be sent with retry in case of failure.\n     *\n     * Packets are sent one by one, each waiting for the server acknowledgement, in order to guarantee the delivery order.\n     * @private\n     */\n    this._queue = [];\n    /**\n     * A sequence to generate the ID of the {@link QueuedPacket}.\n     * @private\n     */\n    this._queueSeq = 0;\n    this.ids = 0;\n    /**\n     * A map containing acknowledgement handlers.\n     *\n     * The `withError` attribute is used to differentiate handlers that accept an error as first argument:\n     *\n     * - `socket.emit(\"test\", (err, value) => { ... })` with `ackTimeout` option\n     * - `socket.timeout(5000).emit(\"test\", (err, value) => { ... })`\n     * - `const value = await socket.emitWithAck(\"test\")`\n     *\n     * From those that don't:\n     *\n     * - `socket.emit(\"test\", (value) => { ... });`\n     *\n     * In the first case, the handlers will be called with an error when:\n     *\n     * - the timeout is reached\n     * - the socket gets disconnected\n     *\n     * In the second case, the handlers will be simply discarded upon disconnection, since the client will never receive\n     * an acknowledgement from the server.\n     *\n     * @private\n     */\n    this.acks = {};\n    this.flags = {};\n    this.io = io;\n    this.nsp = nsp;\n    if (opts && opts.auth) {\n      this.auth = opts.auth;\n    }\n    this._opts = Object.assign({}, opts);\n    if (this.io._autoConnect) this.open();\n  }\n  /**\n   * Whether the socket is currently disconnected\n   *\n   * @example\n   * const socket = io();\n   *\n   * socket.on(\"connect\", () => {\n   *   console.log(socket.disconnected); // false\n   * });\n   *\n   * socket.on(\"disconnect\", () => {\n   *   console.log(socket.disconnected); // true\n   * });\n   */\n  get disconnected() {\n    return !this.connected;\n  }\n  /**\n   * Subscribe to open, close and packet events\n   *\n   * @private\n   */\n  subEvents() {\n    if (this.subs) return;\n    const io = this.io;\n    this.subs = [on(io, \"open\", this.onopen.bind(this)), on(io, \"packet\", this.onpacket.bind(this)), on(io, \"error\", this.onerror.bind(this)), on(io, \"close\", this.onclose.bind(this))];\n  }\n  /**\n   * Whether the Socket will try to reconnect when its Manager connects or reconnects.\n   *\n   * @example\n   * const socket = io();\n   *\n   * console.log(socket.active); // true\n   *\n   * socket.on(\"disconnect\", (reason) => {\n   *   if (reason === \"io server disconnect\") {\n   *     // the disconnection was initiated by the server, you need to manually reconnect\n   *     console.log(socket.active); // false\n   *   }\n   *   // else the socket will automatically try to reconnect\n   *   console.log(socket.active); // true\n   * });\n   */\n  get active() {\n    return !!this.subs;\n  }\n  /**\n   * \"Opens\" the socket.\n   *\n   * @example\n   * const socket = io({\n   *   autoConnect: false\n   * });\n   *\n   * socket.connect();\n   */\n  connect() {\n    if (this.connected) return this;\n    this.subEvents();\n    if (!this.io[\"_reconnecting\"]) this.io.open(); // ensure open\n    if (\"open\" === this.io._readyState) this.onopen();\n    return this;\n  }\n  /**\n   * Alias for {@link connect()}.\n   */\n  open() {\n    return this.connect();\n  }\n  /**\n   * Sends a `message` event.\n   *\n   * This method mimics the WebSocket.send() method.\n   *\n   * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send\n   *\n   * @example\n   * socket.send(\"hello\");\n   *\n   * // this is equivalent to\n   * socket.emit(\"message\", \"hello\");\n   *\n   * @return self\n   */\n  send() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    args.unshift(\"message\");\n    this.emit.apply(this, args);\n    return this;\n  }\n  /**\n   * Override `emit`.\n   * If the event is in `events`, it's emitted normally.\n   *\n   * @example\n   * socket.emit(\"hello\", \"world\");\n   *\n   * // all serializable datastructures are supported (no need to call JSON.stringify)\n   * socket.emit(\"hello\", 1, \"2\", { 3: [\"4\"], 5: Uint8Array.from([6]) });\n   *\n   * // with an acknowledgement from the server\n   * socket.emit(\"hello\", \"world\", (val) => {\n   *   // ...\n   * });\n   *\n   * @return self\n   */\n  emit(ev) {\n    var _a, _b, _c;\n    if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n      throw new Error('\"' + ev.toString() + '\" is a reserved event name');\n    }\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n    args.unshift(ev);\n    if (this._opts.retries && !this.flags.fromQueue && !this.flags.volatile) {\n      this._addToQueue(args);\n      return this;\n    }\n    const packet = {\n      type: PacketType.EVENT,\n      data: args\n    };\n    packet.options = {};\n    packet.options.compress = this.flags.compress !== false;\n    // event ack callback\n    if (\"function\" === typeof args[args.length - 1]) {\n      const id = this.ids++;\n      const ack = args.pop();\n      this._registerAckCallback(id, ack);\n      packet.id = id;\n    }\n    const isTransportWritable = (_b = (_a = this.io.engine) === null || _a === void 0 ? void 0 : _a.transport) === null || _b === void 0 ? void 0 : _b.writable;\n    const isConnected = this.connected && !((_c = this.io.engine) === null || _c === void 0 ? void 0 : _c._hasPingExpired());\n    const discardPacket = this.flags.volatile && !isTransportWritable;\n    if (discardPacket) {} else if (isConnected) {\n      this.notifyOutgoingListeners(packet);\n      this.packet(packet);\n    } else {\n      this.sendBuffer.push(packet);\n    }\n    this.flags = {};\n    return this;\n  }\n  /**\n   * @private\n   */\n  _registerAckCallback(id, ack) {\n    var _this = this;\n    var _a;\n    const timeout = (_a = this.flags.timeout) !== null && _a !== void 0 ? _a : this._opts.ackTimeout;\n    if (timeout === undefined) {\n      this.acks[id] = ack;\n      return;\n    }\n    // @ts-ignore\n    const timer = this.io.setTimeoutFn(() => {\n      delete this.acks[id];\n      for (let i = 0; i < this.sendBuffer.length; i++) {\n        if (this.sendBuffer[i].id === id) {\n          this.sendBuffer.splice(i, 1);\n        }\n      }\n      ack.call(this, new Error(\"operation has timed out\"));\n    }, timeout);\n    const fn = function () {\n      // @ts-ignore\n      _this.io.clearTimeoutFn(timer);\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n      ack.apply(_this, args);\n    };\n    fn.withError = true;\n    this.acks[id] = fn;\n  }\n  /**\n   * Emits an event and waits for an acknowledgement\n   *\n   * @example\n   * // without timeout\n   * const response = await socket.emitWithAck(\"hello\", \"world\");\n   *\n   * // with a specific timeout\n   * try {\n   *   const response = await socket.timeout(1000).emitWithAck(\"hello\", \"world\");\n   * } catch (err) {\n   *   // the server did not acknowledge the event in the given delay\n   * }\n   *\n   * @return a Promise that will be fulfilled when the server acknowledges the event\n   */\n  emitWithAck(ev) {\n    for (var _len4 = arguments.length, args = new Array(_len4 > 1 ? _len4 - 1 : 0), _key4 = 1; _key4 < _len4; _key4++) {\n      args[_key4 - 1] = arguments[_key4];\n    }\n    return new Promise((resolve, reject) => {\n      const fn = (arg1, arg2) => {\n        return arg1 ? reject(arg1) : resolve(arg2);\n      };\n      fn.withError = true;\n      args.push(fn);\n      this.emit(ev, ...args);\n    });\n  }\n  /**\n   * Add the packet to the queue.\n   * @param args\n   * @private\n   */\n  _addToQueue(args) {\n    var _this2 = this;\n    let ack;\n    if (typeof args[args.length - 1] === \"function\") {\n      ack = args.pop();\n    }\n    const packet = {\n      id: this._queueSeq++,\n      tryCount: 0,\n      pending: false,\n      args,\n      flags: Object.assign({\n        fromQueue: true\n      }, this.flags)\n    };\n    args.push(function (err) {\n      if (packet !== _this2._queue[0]) {\n        // the packet has already been acknowledged\n        return;\n      }\n      const hasError = err !== null;\n      if (hasError) {\n        if (packet.tryCount > _this2._opts.retries) {\n          _this2._queue.shift();\n          if (ack) {\n            ack(err);\n          }\n        }\n      } else {\n        _this2._queue.shift();\n        if (ack) {\n          for (var _len5 = arguments.length, responseArgs = new Array(_len5 > 1 ? _len5 - 1 : 0), _key5 = 1; _key5 < _len5; _key5++) {\n            responseArgs[_key5 - 1] = arguments[_key5];\n          }\n          ack(null, ...responseArgs);\n        }\n      }\n      packet.pending = false;\n      return _this2._drainQueue();\n    });\n    this._queue.push(packet);\n    this._drainQueue();\n  }\n  /**\n   * Send the first packet of the queue, and wait for an acknowledgement from the server.\n   * @param force - whether to resend a packet that has not been acknowledged yet\n   *\n   * @private\n   */\n  _drainQueue() {\n    let force = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    if (!this.connected || this._queue.length === 0) {\n      return;\n    }\n    const packet = this._queue[0];\n    if (packet.pending && !force) {\n      return;\n    }\n    packet.pending = true;\n    packet.tryCount++;\n    this.flags = packet.flags;\n    this.emit.apply(this, packet.args);\n  }\n  /**\n   * Sends a packet.\n   *\n   * @param packet\n   * @private\n   */\n  packet(packet) {\n    packet.nsp = this.nsp;\n    this.io._packet(packet);\n  }\n  /**\n   * Called upon engine `open`.\n   *\n   * @private\n   */\n  onopen() {\n    if (typeof this.auth == \"function\") {\n      this.auth(data => {\n        this._sendConnectPacket(data);\n      });\n    } else {\n      this._sendConnectPacket(this.auth);\n    }\n  }\n  /**\n   * Sends a CONNECT packet to initiate the Socket.IO session.\n   *\n   * @param data\n   * @private\n   */\n  _sendConnectPacket(data) {\n    this.packet({\n      type: PacketType.CONNECT,\n      data: this._pid ? Object.assign({\n        pid: this._pid,\n        offset: this._lastOffset\n      }, data) : data\n    });\n  }\n  /**\n   * Called upon engine or manager `error`.\n   *\n   * @param err\n   * @private\n   */\n  onerror(err) {\n    if (!this.connected) {\n      this.emitReserved(\"connect_error\", err);\n    }\n  }\n  /**\n   * Called upon engine `close`.\n   *\n   * @param reason\n   * @param description\n   * @private\n   */\n  onclose(reason, description) {\n    this.connected = false;\n    delete this.id;\n    this.emitReserved(\"disconnect\", reason, description);\n    this._clearAcks();\n  }\n  /**\n   * Clears the acknowledgement handlers upon disconnection, since the client will never receive an acknowledgement from\n   * the server.\n   *\n   * @private\n   */\n  _clearAcks() {\n    Object.keys(this.acks).forEach(id => {\n      const isBuffered = this.sendBuffer.some(packet => String(packet.id) === id);\n      if (!isBuffered) {\n        // note: handlers that do not accept an error as first argument are ignored here\n        const ack = this.acks[id];\n        delete this.acks[id];\n        if (ack.withError) {\n          ack.call(this, new Error(\"socket has been disconnected\"));\n        }\n      }\n    });\n  }\n  /**\n   * Called with socket packet.\n   *\n   * @param packet\n   * @private\n   */\n  onpacket(packet) {\n    const sameNamespace = packet.nsp === this.nsp;\n    if (!sameNamespace) return;\n    switch (packet.type) {\n      case PacketType.CONNECT:\n        if (packet.data && packet.data.sid) {\n          this.onconnect(packet.data.sid, packet.data.pid);\n        } else {\n          this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n        }\n        break;\n      case PacketType.EVENT:\n      case PacketType.BINARY_EVENT:\n        this.onevent(packet);\n        break;\n      case PacketType.ACK:\n      case PacketType.BINARY_ACK:\n        this.onack(packet);\n        break;\n      case PacketType.DISCONNECT:\n        this.ondisconnect();\n        break;\n      case PacketType.CONNECT_ERROR:\n        this.destroy();\n        const err = new Error(packet.data.message);\n        // @ts-ignore\n        err.data = packet.data.data;\n        this.emitReserved(\"connect_error\", err);\n        break;\n    }\n  }\n  /**\n   * Called upon a server event.\n   *\n   * @param packet\n   * @private\n   */\n  onevent(packet) {\n    const args = packet.data || [];\n    if (null != packet.id) {\n      args.push(this.ack(packet.id));\n    }\n    if (this.connected) {\n      this.emitEvent(args);\n    } else {\n      this.receiveBuffer.push(Object.freeze(args));\n    }\n  }\n  emitEvent(args) {\n    if (this._anyListeners && this._anyListeners.length) {\n      const listeners = this._anyListeners.slice();\n      for (const listener of listeners) {\n        listener.apply(this, args);\n      }\n    }\n    super.emit.apply(this, args);\n    if (this._pid && args.length && typeof args[args.length - 1] === \"string\") {\n      this._lastOffset = args[args.length - 1];\n    }\n  }\n  /**\n   * Produces an ack callback to emit with an event.\n   *\n   * @private\n   */\n  ack(id) {\n    const self = this;\n    let sent = false;\n    return function () {\n      // prevent double callbacks\n      if (sent) return;\n      sent = true;\n      for (var _len6 = arguments.length, args = new Array(_len6), _key6 = 0; _key6 < _len6; _key6++) {\n        args[_key6] = arguments[_key6];\n      }\n      self.packet({\n        type: PacketType.ACK,\n        id: id,\n        data: args\n      });\n    };\n  }\n  /**\n   * Called upon a server acknowledgement.\n   *\n   * @param packet\n   * @private\n   */\n  onack(packet) {\n    const ack = this.acks[packet.id];\n    if (typeof ack !== \"function\") {\n      return;\n    }\n    delete this.acks[packet.id];\n    // @ts-ignore FIXME ack is incorrectly inferred as 'never'\n    if (ack.withError) {\n      packet.data.unshift(null);\n    }\n    // @ts-ignore\n    ack.apply(this, packet.data);\n  }\n  /**\n   * Called upon server connect.\n   *\n   * @private\n   */\n  onconnect(id, pid) {\n    this.id = id;\n    this.recovered = pid && this._pid === pid;\n    this._pid = pid; // defined only if connection state recovery is enabled\n    this.connected = true;\n    this.emitBuffered();\n    this.emitReserved(\"connect\");\n    this._drainQueue(true);\n  }\n  /**\n   * Emit buffered events (received and emitted).\n   *\n   * @private\n   */\n  emitBuffered() {\n    this.receiveBuffer.forEach(args => this.emitEvent(args));\n    this.receiveBuffer = [];\n    this.sendBuffer.forEach(packet => {\n      this.notifyOutgoingListeners(packet);\n      this.packet(packet);\n    });\n    this.sendBuffer = [];\n  }\n  /**\n   * Called upon server disconnect.\n   *\n   * @private\n   */\n  ondisconnect() {\n    this.destroy();\n    this.onclose(\"io server disconnect\");\n  }\n  /**\n   * Called upon forced client/server side disconnections,\n   * this method ensures the manager stops tracking us and\n   * that reconnections don't get triggered for this.\n   *\n   * @private\n   */\n  destroy() {\n    if (this.subs) {\n      // clean subscriptions to avoid reconnections\n      this.subs.forEach(subDestroy => subDestroy());\n      this.subs = undefined;\n    }\n    this.io[\"_destroy\"](this);\n  }\n  /**\n   * Disconnects the socket manually. In that case, the socket will not try to reconnect.\n   *\n   * If this is the last active Socket instance of the {@link Manager}, the low-level connection will be closed.\n   *\n   * @example\n   * const socket = io();\n   *\n   * socket.on(\"disconnect\", (reason) => {\n   *   // console.log(reason); prints \"io client disconnect\"\n   * });\n   *\n   * socket.disconnect();\n   *\n   * @return self\n   */\n  disconnect() {\n    if (this.connected) {\n      this.packet({\n        type: PacketType.DISCONNECT\n      });\n    }\n    // remove socket from pool\n    this.destroy();\n    if (this.connected) {\n      // fire events\n      this.onclose(\"io client disconnect\");\n    }\n    return this;\n  }\n  /**\n   * Alias for {@link disconnect()}.\n   *\n   * @return self\n   */\n  close() {\n    return this.disconnect();\n  }\n  /**\n   * Sets the compress flag.\n   *\n   * @example\n   * socket.compress(false).emit(\"hello\");\n   *\n   * @param compress - if `true`, compresses the sending data\n   * @return self\n   */\n  compress(compress) {\n    this.flags.compress = compress;\n    return this;\n  }\n  /**\n   * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n   * ready to send messages.\n   *\n   * @example\n   * socket.volatile.emit(\"hello\"); // the server may or may not receive it\n   *\n   * @returns self\n   */\n  get volatile() {\n    this.flags.volatile = true;\n    return this;\n  }\n  /**\n   * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n   * given number of milliseconds have elapsed without an acknowledgement from the server:\n   *\n   * @example\n   * socket.timeout(5000).emit(\"my-event\", (err) => {\n   *   if (err) {\n   *     // the server did not acknowledge the event in the given delay\n   *   }\n   * });\n   *\n   * @returns self\n   */\n  timeout(timeout) {\n    this.flags.timeout = timeout;\n    return this;\n  }\n  /**\n   * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n   * callback.\n   *\n   * @example\n   * socket.onAny((event, ...args) => {\n   *   console.log(`got ${event}`);\n   * });\n   *\n   * @param listener\n   */\n  onAny(listener) {\n    this._anyListeners = this._anyListeners || [];\n    this._anyListeners.push(listener);\n    return this;\n  }\n  /**\n   * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n   * callback. The listener is added to the beginning of the listeners array.\n   *\n   * @example\n   * socket.prependAny((event, ...args) => {\n   *   console.log(`got event ${event}`);\n   * });\n   *\n   * @param listener\n   */\n  prependAny(listener) {\n    this._anyListeners = this._anyListeners || [];\n    this._anyListeners.unshift(listener);\n    return this;\n  }\n  /**\n   * Removes the listener that will be fired when any event is emitted.\n   *\n   * @example\n   * const catchAllListener = (event, ...args) => {\n   *   console.log(`got event ${event}`);\n   * }\n   *\n   * socket.onAny(catchAllListener);\n   *\n   * // remove a specific listener\n   * socket.offAny(catchAllListener);\n   *\n   * // or remove all listeners\n   * socket.offAny();\n   *\n   * @param listener\n   */\n  offAny(listener) {\n    if (!this._anyListeners) {\n      return this;\n    }\n    if (listener) {\n      const listeners = this._anyListeners;\n      for (let i = 0; i < listeners.length; i++) {\n        if (listener === listeners[i]) {\n          listeners.splice(i, 1);\n          return this;\n        }\n      }\n    } else {\n      this._anyListeners = [];\n    }\n    return this;\n  }\n  /**\n   * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n   * e.g. to remove listeners.\n   */\n  listenersAny() {\n    return this._anyListeners || [];\n  }\n  /**\n   * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n   * callback.\n   *\n   * Note: acknowledgements sent to the server are not included.\n   *\n   * @example\n   * socket.onAnyOutgoing((event, ...args) => {\n   *   console.log(`sent event ${event}`);\n   * });\n   *\n   * @param listener\n   */\n  onAnyOutgoing(listener) {\n    this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n    this._anyOutgoingListeners.push(listener);\n    return this;\n  }\n  /**\n   * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n   * callback. The listener is added to the beginning of the listeners array.\n   *\n   * Note: acknowledgements sent to the server are not included.\n   *\n   * @example\n   * socket.prependAnyOutgoing((event, ...args) => {\n   *   console.log(`sent event ${event}`);\n   * });\n   *\n   * @param listener\n   */\n  prependAnyOutgoing(listener) {\n    this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n    this._anyOutgoingListeners.unshift(listener);\n    return this;\n  }\n  /**\n   * Removes the listener that will be fired when any event is emitted.\n   *\n   * @example\n   * const catchAllListener = (event, ...args) => {\n   *   console.log(`sent event ${event}`);\n   * }\n   *\n   * socket.onAnyOutgoing(catchAllListener);\n   *\n   * // remove a specific listener\n   * socket.offAnyOutgoing(catchAllListener);\n   *\n   * // or remove all listeners\n   * socket.offAnyOutgoing();\n   *\n   * @param [listener] - the catch-all listener (optional)\n   */\n  offAnyOutgoing(listener) {\n    if (!this._anyOutgoingListeners) {\n      return this;\n    }\n    if (listener) {\n      const listeners = this._anyOutgoingListeners;\n      for (let i = 0; i < listeners.length; i++) {\n        if (listener === listeners[i]) {\n          listeners.splice(i, 1);\n          return this;\n        }\n      }\n    } else {\n      this._anyOutgoingListeners = [];\n    }\n    return this;\n  }\n  /**\n   * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n   * e.g. to remove listeners.\n   */\n  listenersAnyOutgoing() {\n    return this._anyOutgoingListeners || [];\n  }\n  /**\n   * Notify the listeners for each packet sent\n   *\n   * @param packet\n   *\n   * @private\n   */\n  notifyOutgoingListeners(packet) {\n    if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n      const listeners = this._anyOutgoingListeners.slice();\n      for (const listener of listeners) {\n        listener.apply(this, packet.data);\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["PacketType", "on", "Emitter", "RESERVED_EVENTS", "Object", "freeze", "connect", "connect_error", "disconnect", "disconnecting", "newListener", "removeListener", "Socket", "constructor", "io", "nsp", "opts", "connected", "recovered", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "_queue", "_queueSeq", "ids", "acks", "flags", "auth", "_opts", "assign", "_autoConnect", "open", "disconnected", "subEvents", "subs", "onopen", "bind", "onpacket", "onerror", "onclose", "active", "_readyState", "send", "_len", "arguments", "length", "args", "Array", "_key", "unshift", "emit", "apply", "ev", "_a", "_b", "_c", "hasOwnProperty", "Error", "toString", "_len2", "_key2", "retries", "fromQueue", "volatile", "_addToQueue", "packet", "type", "EVENT", "data", "options", "compress", "id", "ack", "pop", "_registerAckCallback", "isTransportWritable", "engine", "transport", "writable", "isConnected", "_hasPingExpired", "discardPacket", "notifyOutgoingListeners", "push", "_this", "timeout", "ackTimeout", "undefined", "timer", "setTimeoutFn", "i", "splice", "call", "fn", "clearTimeoutFn", "_len3", "_key3", "with<PERSON><PERSON><PERSON>", "emitWithAck", "_len4", "_key4", "Promise", "resolve", "reject", "arg1", "arg2", "_this2", "tryCount", "pending", "err", "<PERSON><PERSON><PERSON><PERSON>", "shift", "_len5", "responseArgs", "_key5", "_drainQueue", "force", "_packet", "_sendConnectPacket", "CONNECT", "_pid", "pid", "offset", "_lastOffset", "emit<PERSON><PERSON><PERSON><PERSON>", "reason", "description", "_clearAcks", "keys", "for<PERSON>ach", "isBuffered", "some", "String", "sameNamespace", "sid", "onconnect", "BINARY_EVENT", "onevent", "ACK", "BINARY_ACK", "onack", "DISCONNECT", "ondisconnect", "CONNECT_ERROR", "destroy", "message", "emitEvent", "_anyListeners", "listeners", "slice", "listener", "self", "sent", "_len6", "_key6", "emitBuffered", "subDestroy", "close", "onAny", "prependAny", "offAny", "listenersAny", "onAnyOutgoing", "_anyOutgoingListeners", "prependAnyOutgoing", "offAnyOutgoing", "listenersAnyOutgoing"], "sources": ["D:/ASL/training-frontend/node_modules/socket.io-client/build/esm/socket.js"], "sourcesContent": ["import { PacketType } from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\n/**\n * A Socket is the fundamental class for interacting with the server.\n *\n * A Socket belongs to a certain Namespace (by default /) and uses an underlying {@link Manager} to communicate.\n *\n * @example\n * const socket = io();\n *\n * socket.on(\"connect\", () => {\n *   console.log(\"connected\");\n * });\n *\n * // send an event to the server\n * socket.emit(\"foo\", \"bar\");\n *\n * socket.on(\"foobar\", () => {\n *   // an event was received from the server\n * });\n *\n * // upon disconnection\n * socket.on(\"disconnect\", (reason) => {\n *   console.log(`disconnected due to ${reason}`);\n * });\n */\nexport class Socket extends Emitter {\n    /**\n     * `Socket` constructor.\n     */\n    constructor(io, nsp, opts) {\n        super();\n        /**\n         * Whether the socket is currently connected to the server.\n         *\n         * @example\n         * const socket = io();\n         *\n         * socket.on(\"connect\", () => {\n         *   console.log(socket.connected); // true\n         * });\n         *\n         * socket.on(\"disconnect\", () => {\n         *   console.log(socket.connected); // false\n         * });\n         */\n        this.connected = false;\n        /**\n         * Whether the connection state was recovered after a temporary disconnection. In that case, any missed packets will\n         * be transmitted by the server.\n         */\n        this.recovered = false;\n        /**\n         * Buffer for packets received before the CONNECT packet\n         */\n        this.receiveBuffer = [];\n        /**\n         * Buffer for packets that will be sent once the socket is connected\n         */\n        this.sendBuffer = [];\n        /**\n         * The queue of packets to be sent with retry in case of failure.\n         *\n         * Packets are sent one by one, each waiting for the server acknowledgement, in order to guarantee the delivery order.\n         * @private\n         */\n        this._queue = [];\n        /**\n         * A sequence to generate the ID of the {@link QueuedPacket}.\n         * @private\n         */\n        this._queueSeq = 0;\n        this.ids = 0;\n        /**\n         * A map containing acknowledgement handlers.\n         *\n         * The `withError` attribute is used to differentiate handlers that accept an error as first argument:\n         *\n         * - `socket.emit(\"test\", (err, value) => { ... })` with `ackTimeout` option\n         * - `socket.timeout(5000).emit(\"test\", (err, value) => { ... })`\n         * - `const value = await socket.emitWithAck(\"test\")`\n         *\n         * From those that don't:\n         *\n         * - `socket.emit(\"test\", (value) => { ... });`\n         *\n         * In the first case, the handlers will be called with an error when:\n         *\n         * - the timeout is reached\n         * - the socket gets disconnected\n         *\n         * In the second case, the handlers will be simply discarded upon disconnection, since the client will never receive\n         * an acknowledgement from the server.\n         *\n         * @private\n         */\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        this._opts = Object.assign({}, opts);\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Whether the socket is currently disconnected\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"connect\", () => {\n     *   console.log(socket.disconnected); // false\n     * });\n     *\n     * socket.on(\"disconnect\", () => {\n     *   console.log(socket.disconnected); // true\n     * });\n     */\n    get disconnected() {\n        return !this.connected;\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            on(io, \"open\", this.onopen.bind(this)),\n            on(io, \"packet\", this.onpacket.bind(this)),\n            on(io, \"error\", this.onerror.bind(this)),\n            on(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects.\n     *\n     * @example\n     * const socket = io();\n     *\n     * console.log(socket.active); // true\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   if (reason === \"io server disconnect\") {\n     *     // the disconnection was initiated by the server, you need to manually reconnect\n     *     console.log(socket.active); // false\n     *   }\n     *   // else the socket will automatically try to reconnect\n     *   console.log(socket.active); // true\n     * });\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @example\n     * const socket = io({\n     *   autoConnect: false\n     * });\n     *\n     * socket.connect();\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for {@link connect()}.\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * This method mimics the WebSocket.send() method.\n     *\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send\n     *\n     * @example\n     * socket.send(\"hello\");\n     *\n     * // this is equivalent to\n     * socket.emit(\"message\", \"hello\");\n     *\n     * @return self\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @example\n     * socket.emit(\"hello\", \"world\");\n     *\n     * // all serializable datastructures are supported (no need to call JSON.stringify)\n     * socket.emit(\"hello\", 1, \"2\", { 3: [\"4\"], 5: Uint8Array.from([6]) });\n     *\n     * // with an acknowledgement from the server\n     * socket.emit(\"hello\", \"world\", (val) => {\n     *   // ...\n     * });\n     *\n     * @return self\n     */\n    emit(ev, ...args) {\n        var _a, _b, _c;\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev.toString() + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        if (this._opts.retries && !this.flags.fromQueue && !this.flags.volatile) {\n            this._addToQueue(args);\n            return this;\n        }\n        const packet = {\n            type: PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            const id = this.ids++;\n            const ack = args.pop();\n            this._registerAckCallback(id, ack);\n            packet.id = id;\n        }\n        const isTransportWritable = (_b = (_a = this.io.engine) === null || _a === void 0 ? void 0 : _a.transport) === null || _b === void 0 ? void 0 : _b.writable;\n        const isConnected = this.connected && !((_c = this.io.engine) === null || _c === void 0 ? void 0 : _c._hasPingExpired());\n        const discardPacket = this.flags.volatile && !isTransportWritable;\n        if (discardPacket) {\n        }\n        else if (isConnected) {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * @private\n     */\n    _registerAckCallback(id, ack) {\n        var _a;\n        const timeout = (_a = this.flags.timeout) !== null && _a !== void 0 ? _a : this._opts.ackTimeout;\n        if (timeout === undefined) {\n            this.acks[id] = ack;\n            return;\n        }\n        // @ts-ignore\n        const timer = this.io.setTimeoutFn(() => {\n            delete this.acks[id];\n            for (let i = 0; i < this.sendBuffer.length; i++) {\n                if (this.sendBuffer[i].id === id) {\n                    this.sendBuffer.splice(i, 1);\n                }\n            }\n            ack.call(this, new Error(\"operation has timed out\"));\n        }, timeout);\n        const fn = (...args) => {\n            // @ts-ignore\n            this.io.clearTimeoutFn(timer);\n            ack.apply(this, args);\n        };\n        fn.withError = true;\n        this.acks[id] = fn;\n    }\n    /**\n     * Emits an event and waits for an acknowledgement\n     *\n     * @example\n     * // without timeout\n     * const response = await socket.emitWithAck(\"hello\", \"world\");\n     *\n     * // with a specific timeout\n     * try {\n     *   const response = await socket.timeout(1000).emitWithAck(\"hello\", \"world\");\n     * } catch (err) {\n     *   // the server did not acknowledge the event in the given delay\n     * }\n     *\n     * @return a Promise that will be fulfilled when the server acknowledges the event\n     */\n    emitWithAck(ev, ...args) {\n        return new Promise((resolve, reject) => {\n            const fn = (arg1, arg2) => {\n                return arg1 ? reject(arg1) : resolve(arg2);\n            };\n            fn.withError = true;\n            args.push(fn);\n            this.emit(ev, ...args);\n        });\n    }\n    /**\n     * Add the packet to the queue.\n     * @param args\n     * @private\n     */\n    _addToQueue(args) {\n        let ack;\n        if (typeof args[args.length - 1] === \"function\") {\n            ack = args.pop();\n        }\n        const packet = {\n            id: this._queueSeq++,\n            tryCount: 0,\n            pending: false,\n            args,\n            flags: Object.assign({ fromQueue: true }, this.flags),\n        };\n        args.push((err, ...responseArgs) => {\n            if (packet !== this._queue[0]) {\n                // the packet has already been acknowledged\n                return;\n            }\n            const hasError = err !== null;\n            if (hasError) {\n                if (packet.tryCount > this._opts.retries) {\n                    this._queue.shift();\n                    if (ack) {\n                        ack(err);\n                    }\n                }\n            }\n            else {\n                this._queue.shift();\n                if (ack) {\n                    ack(null, ...responseArgs);\n                }\n            }\n            packet.pending = false;\n            return this._drainQueue();\n        });\n        this._queue.push(packet);\n        this._drainQueue();\n    }\n    /**\n     * Send the first packet of the queue, and wait for an acknowledgement from the server.\n     * @param force - whether to resend a packet that has not been acknowledged yet\n     *\n     * @private\n     */\n    _drainQueue(force = false) {\n        if (!this.connected || this._queue.length === 0) {\n            return;\n        }\n        const packet = this._queue[0];\n        if (packet.pending && !force) {\n            return;\n        }\n        packet.pending = true;\n        packet.tryCount++;\n        this.flags = packet.flags;\n        this.emit.apply(this, packet.args);\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this._sendConnectPacket(data);\n            });\n        }\n        else {\n            this._sendConnectPacket(this.auth);\n        }\n    }\n    /**\n     * Sends a CONNECT packet to initiate the Socket.IO session.\n     *\n     * @param data\n     * @private\n     */\n    _sendConnectPacket(data) {\n        this.packet({\n            type: PacketType.CONNECT,\n            data: this._pid\n                ? Object.assign({ pid: this._pid, offset: this._lastOffset }, data)\n                : data,\n        });\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @param description\n     * @private\n     */\n    onclose(reason, description) {\n        this.connected = false;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason, description);\n        this._clearAcks();\n    }\n    /**\n     * Clears the acknowledgement handlers upon disconnection, since the client will never receive an acknowledgement from\n     * the server.\n     *\n     * @private\n     */\n    _clearAcks() {\n        Object.keys(this.acks).forEach((id) => {\n            const isBuffered = this.sendBuffer.some((packet) => String(packet.id) === id);\n            if (!isBuffered) {\n                // note: handlers that do not accept an error as first argument are ignored here\n                const ack = this.acks[id];\n                delete this.acks[id];\n                if (ack.withError) {\n                    ack.call(this, new Error(\"socket has been disconnected\"));\n                }\n            }\n        });\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    this.onconnect(packet.data.sid, packet.data.pid);\n                }\n                else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case PacketType.CONNECT_ERROR:\n                this.destroy();\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n        if (null != packet.id) {\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n        if (this._pid && args.length && typeof args[args.length - 1] === \"string\") {\n            this._lastOffset = args[args.length - 1];\n        }\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n            self.packet({\n                type: PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowledgement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (typeof ack !== \"function\") {\n            return;\n        }\n        delete this.acks[packet.id];\n        // @ts-ignore FIXME ack is incorrectly inferred as 'never'\n        if (ack.withError) {\n            packet.data.unshift(null);\n        }\n        // @ts-ignore\n        ack.apply(this, packet.data);\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id, pid) {\n        this.id = id;\n        this.recovered = pid && this._pid === pid;\n        this._pid = pid; // defined only if connection state recovery is enabled\n        this.connected = true;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n        this._drainQueue(true);\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        });\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually. In that case, the socket will not try to reconnect.\n     *\n     * If this is the last active Socket instance of the {@link Manager}, the low-level connection will be closed.\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   // console.log(reason); prints \"io client disconnect\"\n     * });\n     *\n     * socket.disconnect();\n     *\n     * @return self\n     */\n    disconnect() {\n        if (this.connected) {\n            this.packet({ type: PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for {@link disconnect()}.\n     *\n     * @return self\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @example\n     * socket.compress(false).emit(\"hello\");\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @example\n     * socket.volatile.emit(\"hello\"); // the server may or may not receive it\n     *\n     * @returns self\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n     * given number of milliseconds have elapsed without an acknowledgement from the server:\n     *\n     * @example\n     * socket.timeout(5000).emit(\"my-event\", (err) => {\n     *   if (err) {\n     *     // the server did not acknowledge the event in the given delay\n     *   }\n     * });\n     *\n     * @returns self\n     */\n    timeout(timeout) {\n        this.flags.timeout = timeout;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @example\n     * socket.onAny((event, ...args) => {\n     *   console.log(`got ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @example\n     * socket.prependAny((event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * }\n     *\n     * socket.onAny(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAny(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAny();\n     *\n     * @param listener\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.onAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.prependAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * }\n     *\n     * socket.onAnyOutgoing(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAnyOutgoing(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAnyOutgoing();\n     *\n     * @param [listener] - the catch-all listener (optional)\n     */\n    offAnyOutgoing(listener) {\n        if (!this._anyOutgoingListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyOutgoingListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyOutgoingListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAnyOutgoing() {\n        return this._anyOutgoingListeners || [];\n    }\n    /**\n     * Notify the listeners for each packet sent\n     *\n     * @param packet\n     *\n     * @private\n     */\n    notifyOutgoingListeners(packet) {\n        if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n            const listeners = this._anyOutgoingListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, packet.data);\n            }\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,EAAE,QAAQ,SAAS;AAC5B,SAASC,OAAO,QAAS,8BAA8B;AACvD;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAGC,MAAM,CAACC,MAAM,CAAC;EAClCC,OAAO,EAAE,CAAC;EACVC,aAAa,EAAE,CAAC;EAChBC,UAAU,EAAE,CAAC;EACbC,aAAa,EAAE,CAAC;EAChB;EACAC,WAAW,EAAE,CAAC;EACdC,cAAc,EAAE;AACpB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,MAAM,SAASV,OAAO,CAAC;EAChC;AACJ;AACA;EACIW,WAAWA,CAACC,EAAE,EAAEC,GAAG,EAAEC,IAAI,EAAE;IACvB,KAAK,CAAC,CAAC;IACP;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;AACR;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;AACR;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB;AACR;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB;AACR;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,GAAG,GAAG,CAAC;IACZ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC;IACd,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACX,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAIC,IAAI,IAAIA,IAAI,CAACU,IAAI,EAAE;MACnB,IAAI,CAACA,IAAI,GAAGV,IAAI,CAACU,IAAI;IACzB;IACA,IAAI,CAACC,KAAK,GAAGvB,MAAM,CAACwB,MAAM,CAAC,CAAC,CAAC,EAAEZ,IAAI,CAAC;IACpC,IAAI,IAAI,CAACF,EAAE,CAACe,YAAY,EACpB,IAAI,CAACC,IAAI,CAAC,CAAC;EACnB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO,CAAC,IAAI,CAACd,SAAS;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACIe,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACC,IAAI,EACT;IACJ,MAAMnB,EAAE,GAAG,IAAI,CAACA,EAAE;IAClB,IAAI,CAACmB,IAAI,GAAG,CACRhC,EAAE,CAACa,EAAE,EAAE,MAAM,EAAE,IAAI,CAACoB,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,EACtClC,EAAE,CAACa,EAAE,EAAE,QAAQ,EAAE,IAAI,CAACsB,QAAQ,CAACD,IAAI,CAAC,IAAI,CAAC,CAAC,EAC1ClC,EAAE,CAACa,EAAE,EAAE,OAAO,EAAE,IAAI,CAACuB,OAAO,CAACF,IAAI,CAAC,IAAI,CAAC,CAAC,EACxClC,EAAE,CAACa,EAAE,EAAE,OAAO,EAAE,IAAI,CAACwB,OAAO,CAACH,IAAI,CAAC,IAAI,CAAC,CAAC,CAC3C;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAII,MAAMA,CAAA,EAAG;IACT,OAAO,CAAC,CAAC,IAAI,CAACN,IAAI;EACtB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI3B,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACW,SAAS,EACd,OAAO,IAAI;IACf,IAAI,CAACe,SAAS,CAAC,CAAC;IAChB,IAAI,CAAC,IAAI,CAAClB,EAAE,CAAC,eAAe,CAAC,EACzB,IAAI,CAACA,EAAE,CAACgB,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB,IAAI,MAAM,KAAK,IAAI,CAAChB,EAAE,CAAC0B,WAAW,EAC9B,IAAI,CAACN,MAAM,CAAC,CAAC;IACjB,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIJ,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAACxB,OAAO,CAAC,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACImC,IAAIA,CAAA,EAAU;IAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IACRF,IAAI,CAACG,OAAO,CAAC,SAAS,CAAC;IACvB,IAAI,CAACC,IAAI,CAACC,KAAK,CAAC,IAAI,EAAEL,IAAI,CAAC;IAC3B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACII,IAAIA,CAACE,EAAE,EAAW;IACd,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IACd,IAAInD,eAAe,CAACoD,cAAc,CAACJ,EAAE,CAAC,EAAE;MACpC,MAAM,IAAIK,KAAK,CAAC,GAAG,GAAGL,EAAE,CAACM,QAAQ,CAAC,CAAC,GAAG,4BAA4B,CAAC;IACvE;IAAC,SAAAC,KAAA,GAAAf,SAAA,CAAAC,MAAA,EAJOC,IAAI,OAAAC,KAAA,CAAAY,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJd,IAAI,CAAAc,KAAA,QAAAhB,SAAA,CAAAgB,KAAA;IAAA;IAKZd,IAAI,CAACG,OAAO,CAACG,EAAE,CAAC;IAChB,IAAI,IAAI,CAACxB,KAAK,CAACiC,OAAO,IAAI,CAAC,IAAI,CAACnC,KAAK,CAACoC,SAAS,IAAI,CAAC,IAAI,CAACpC,KAAK,CAACqC,QAAQ,EAAE;MACrE,IAAI,CAACC,WAAW,CAAClB,IAAI,CAAC;MACtB,OAAO,IAAI;IACf;IACA,MAAMmB,MAAM,GAAG;MACXC,IAAI,EAAEjE,UAAU,CAACkE,KAAK;MACtBC,IAAI,EAAEtB;IACV,CAAC;IACDmB,MAAM,CAACI,OAAO,GAAG,CAAC,CAAC;IACnBJ,MAAM,CAACI,OAAO,CAACC,QAAQ,GAAG,IAAI,CAAC5C,KAAK,CAAC4C,QAAQ,KAAK,KAAK;IACvD;IACA,IAAI,UAAU,KAAK,OAAOxB,IAAI,CAACA,IAAI,CAACD,MAAM,GAAG,CAAC,CAAC,EAAE;MAC7C,MAAM0B,EAAE,GAAG,IAAI,CAAC/C,GAAG,EAAE;MACrB,MAAMgD,GAAG,GAAG1B,IAAI,CAAC2B,GAAG,CAAC,CAAC;MACtB,IAAI,CAACC,oBAAoB,CAACH,EAAE,EAAEC,GAAG,CAAC;MAClCP,MAAM,CAACM,EAAE,GAAGA,EAAE;IAClB;IACA,MAAMI,mBAAmB,GAAG,CAACrB,EAAE,GAAG,CAACD,EAAE,GAAG,IAAI,CAACtC,EAAE,CAAC6D,MAAM,MAAM,IAAI,IAAIvB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwB,SAAS,MAAM,IAAI,IAAIvB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwB,QAAQ;IAC3J,MAAMC,WAAW,GAAG,IAAI,CAAC7D,SAAS,IAAI,EAAE,CAACqC,EAAE,GAAG,IAAI,CAACxC,EAAE,CAAC6D,MAAM,MAAM,IAAI,IAAIrB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyB,eAAe,CAAC,CAAC,CAAC;IACxH,MAAMC,aAAa,GAAG,IAAI,CAACvD,KAAK,CAACqC,QAAQ,IAAI,CAACY,mBAAmB;IACjE,IAAIM,aAAa,EAAE,CACnB,CAAC,MACI,IAAIF,WAAW,EAAE;MAClB,IAAI,CAACG,uBAAuB,CAACjB,MAAM,CAAC;MACpC,IAAI,CAACA,MAAM,CAACA,MAAM,CAAC;IACvB,CAAC,MACI;MACD,IAAI,CAAC5C,UAAU,CAAC8D,IAAI,CAAClB,MAAM,CAAC;IAChC;IACA,IAAI,CAACvC,KAAK,GAAG,CAAC,CAAC;IACf,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIgD,oBAAoBA,CAACH,EAAE,EAAEC,GAAG,EAAE;IAAA,IAAAY,KAAA;IAC1B,IAAI/B,EAAE;IACN,MAAMgC,OAAO,GAAG,CAAChC,EAAE,GAAG,IAAI,CAAC3B,KAAK,CAAC2D,OAAO,MAAM,IAAI,IAAIhC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAACzB,KAAK,CAAC0D,UAAU;IAChG,IAAID,OAAO,KAAKE,SAAS,EAAE;MACvB,IAAI,CAAC9D,IAAI,CAAC8C,EAAE,CAAC,GAAGC,GAAG;MACnB;IACJ;IACA;IACA,MAAMgB,KAAK,GAAG,IAAI,CAACzE,EAAE,CAAC0E,YAAY,CAAC,MAAM;MACrC,OAAO,IAAI,CAAChE,IAAI,CAAC8C,EAAE,CAAC;MACpB,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACrE,UAAU,CAACwB,MAAM,EAAE6C,CAAC,EAAE,EAAE;QAC7C,IAAI,IAAI,CAACrE,UAAU,CAACqE,CAAC,CAAC,CAACnB,EAAE,KAAKA,EAAE,EAAE;UAC9B,IAAI,CAAClD,UAAU,CAACsE,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;QAChC;MACJ;MACAlB,GAAG,CAACoB,IAAI,CAAC,IAAI,EAAE,IAAInC,KAAK,CAAC,yBAAyB,CAAC,CAAC;IACxD,CAAC,EAAE4B,OAAO,CAAC;IACX,MAAMQ,EAAE,GAAG,SAAAA,CAAA,EAAa;MACpB;MACAT,KAAI,CAACrE,EAAE,CAAC+E,cAAc,CAACN,KAAK,CAAC;MAAC,SAAAO,KAAA,GAAAnD,SAAA,CAAAC,MAAA,EAFnBC,IAAI,OAAAC,KAAA,CAAAgD,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAAJlD,IAAI,CAAAkD,KAAA,IAAApD,SAAA,CAAAoD,KAAA;MAAA;MAGfxB,GAAG,CAACrB,KAAK,CAACiC,KAAI,EAAEtC,IAAI,CAAC;IACzB,CAAC;IACD+C,EAAE,CAACI,SAAS,GAAG,IAAI;IACnB,IAAI,CAACxE,IAAI,CAAC8C,EAAE,CAAC,GAAGsB,EAAE;EACtB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIK,WAAWA,CAAC9C,EAAE,EAAW;IAAA,SAAA+C,KAAA,GAAAvD,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAoD,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJtD,IAAI,CAAAsD,KAAA,QAAAxD,SAAA,CAAAwD,KAAA;IAAA;IACnB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACpC,MAAMV,EAAE,GAAGA,CAACW,IAAI,EAAEC,IAAI,KAAK;QACvB,OAAOD,IAAI,GAAGD,MAAM,CAACC,IAAI,CAAC,GAAGF,OAAO,CAACG,IAAI,CAAC;MAC9C,CAAC;MACDZ,EAAE,CAACI,SAAS,GAAG,IAAI;MACnBnD,IAAI,CAACqC,IAAI,CAACU,EAAE,CAAC;MACb,IAAI,CAAC3C,IAAI,CAACE,EAAE,EAAE,GAAGN,IAAI,CAAC;IAC1B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIkB,WAAWA,CAAClB,IAAI,EAAE;IAAA,IAAA4D,MAAA;IACd,IAAIlC,GAAG;IACP,IAAI,OAAO1B,IAAI,CAACA,IAAI,CAACD,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;MAC7C2B,GAAG,GAAG1B,IAAI,CAAC2B,GAAG,CAAC,CAAC;IACpB;IACA,MAAMR,MAAM,GAAG;MACXM,EAAE,EAAE,IAAI,CAAChD,SAAS,EAAE;MACpBoF,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE,KAAK;MACd9D,IAAI;MACJpB,KAAK,EAAErB,MAAM,CAACwB,MAAM,CAAC;QAAEiC,SAAS,EAAE;MAAK,CAAC,EAAE,IAAI,CAACpC,KAAK;IACxD,CAAC;IACDoB,IAAI,CAACqC,IAAI,CAAC,UAAC0B,GAAG,EAAsB;MAChC,IAAI5C,MAAM,KAAKyC,MAAI,CAACpF,MAAM,CAAC,CAAC,CAAC,EAAE;QAC3B;QACA;MACJ;MACA,MAAMwF,QAAQ,GAAGD,GAAG,KAAK,IAAI;MAC7B,IAAIC,QAAQ,EAAE;QACV,IAAI7C,MAAM,CAAC0C,QAAQ,GAAGD,MAAI,CAAC9E,KAAK,CAACiC,OAAO,EAAE;UACtC6C,MAAI,CAACpF,MAAM,CAACyF,KAAK,CAAC,CAAC;UACnB,IAAIvC,GAAG,EAAE;YACLA,GAAG,CAACqC,GAAG,CAAC;UACZ;QACJ;MACJ,CAAC,MACI;QACDH,MAAI,CAACpF,MAAM,CAACyF,KAAK,CAAC,CAAC;QACnB,IAAIvC,GAAG,EAAE;UAAA,SAAAwC,KAAA,GAAApE,SAAA,CAAAC,MAAA,EAhBEoE,YAAY,OAAAlE,KAAA,CAAAiE,KAAA,OAAAA,KAAA,WAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;YAAZD,YAAY,CAAAC,KAAA,QAAAtE,SAAA,CAAAsE,KAAA;UAAA;UAiBnB1C,GAAG,CAAC,IAAI,EAAE,GAAGyC,YAAY,CAAC;QAC9B;MACJ;MACAhD,MAAM,CAAC2C,OAAO,GAAG,KAAK;MACtB,OAAOF,MAAI,CAACS,WAAW,CAAC,CAAC;IAC7B,CAAC,CAAC;IACF,IAAI,CAAC7F,MAAM,CAAC6D,IAAI,CAAClB,MAAM,CAAC;IACxB,IAAI,CAACkD,WAAW,CAAC,CAAC;EACtB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIA,WAAWA,CAAA,EAAgB;IAAA,IAAfC,KAAK,GAAAxE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA2C,SAAA,GAAA3C,SAAA,MAAG,KAAK;IACrB,IAAI,CAAC,IAAI,CAAC1B,SAAS,IAAI,IAAI,CAACI,MAAM,CAACuB,MAAM,KAAK,CAAC,EAAE;MAC7C;IACJ;IACA,MAAMoB,MAAM,GAAG,IAAI,CAAC3C,MAAM,CAAC,CAAC,CAAC;IAC7B,IAAI2C,MAAM,CAAC2C,OAAO,IAAI,CAACQ,KAAK,EAAE;MAC1B;IACJ;IACAnD,MAAM,CAAC2C,OAAO,GAAG,IAAI;IACrB3C,MAAM,CAAC0C,QAAQ,EAAE;IACjB,IAAI,CAACjF,KAAK,GAAGuC,MAAM,CAACvC,KAAK;IACzB,IAAI,CAACwB,IAAI,CAACC,KAAK,CAAC,IAAI,EAAEc,MAAM,CAACnB,IAAI,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;AACA;EACImB,MAAMA,CAACA,MAAM,EAAE;IACXA,MAAM,CAACjD,GAAG,GAAG,IAAI,CAACA,GAAG;IACrB,IAAI,CAACD,EAAE,CAACsG,OAAO,CAACpD,MAAM,CAAC;EAC3B;EACA;AACJ;AACA;AACA;AACA;EACI9B,MAAMA,CAAA,EAAG;IACL,IAAI,OAAO,IAAI,CAACR,IAAI,IAAI,UAAU,EAAE;MAChC,IAAI,CAACA,IAAI,CAAEyC,IAAI,IAAK;QAChB,IAAI,CAACkD,kBAAkB,CAAClD,IAAI,CAAC;MACjC,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACkD,kBAAkB,CAAC,IAAI,CAAC3F,IAAI,CAAC;IACtC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI2F,kBAAkBA,CAAClD,IAAI,EAAE;IACrB,IAAI,CAACH,MAAM,CAAC;MACRC,IAAI,EAAEjE,UAAU,CAACsH,OAAO;MACxBnD,IAAI,EAAE,IAAI,CAACoD,IAAI,GACTnH,MAAM,CAACwB,MAAM,CAAC;QAAE4F,GAAG,EAAE,IAAI,CAACD,IAAI;QAAEE,MAAM,EAAE,IAAI,CAACC;MAAY,CAAC,EAAEvD,IAAI,CAAC,GACjEA;IACV,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACI9B,OAAOA,CAACuE,GAAG,EAAE;IACT,IAAI,CAAC,IAAI,CAAC3F,SAAS,EAAE;MACjB,IAAI,CAAC0G,YAAY,CAAC,eAAe,EAAEf,GAAG,CAAC;IAC3C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACItE,OAAOA,CAACsF,MAAM,EAAEC,WAAW,EAAE;IACzB,IAAI,CAAC5G,SAAS,GAAG,KAAK;IACtB,OAAO,IAAI,CAACqD,EAAE;IACd,IAAI,CAACqD,YAAY,CAAC,YAAY,EAAEC,MAAM,EAAEC,WAAW,CAAC;IACpD,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIA,UAAUA,CAAA,EAAG;IACT1H,MAAM,CAAC2H,IAAI,CAAC,IAAI,CAACvG,IAAI,CAAC,CAACwG,OAAO,CAAE1D,EAAE,IAAK;MACnC,MAAM2D,UAAU,GAAG,IAAI,CAAC7G,UAAU,CAAC8G,IAAI,CAAElE,MAAM,IAAKmE,MAAM,CAACnE,MAAM,CAACM,EAAE,CAAC,KAAKA,EAAE,CAAC;MAC7E,IAAI,CAAC2D,UAAU,EAAE;QACb;QACA,MAAM1D,GAAG,GAAG,IAAI,CAAC/C,IAAI,CAAC8C,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC9C,IAAI,CAAC8C,EAAE,CAAC;QACpB,IAAIC,GAAG,CAACyB,SAAS,EAAE;UACfzB,GAAG,CAACoB,IAAI,CAAC,IAAI,EAAE,IAAInC,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAC7D;MACJ;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIpB,QAAQA,CAAC4B,MAAM,EAAE;IACb,MAAMoE,aAAa,GAAGpE,MAAM,CAACjD,GAAG,KAAK,IAAI,CAACA,GAAG;IAC7C,IAAI,CAACqH,aAAa,EACd;IACJ,QAAQpE,MAAM,CAACC,IAAI;MACf,KAAKjE,UAAU,CAACsH,OAAO;QACnB,IAAItD,MAAM,CAACG,IAAI,IAAIH,MAAM,CAACG,IAAI,CAACkE,GAAG,EAAE;UAChC,IAAI,CAACC,SAAS,CAACtE,MAAM,CAACG,IAAI,CAACkE,GAAG,EAAErE,MAAM,CAACG,IAAI,CAACqD,GAAG,CAAC;QACpD,CAAC,MACI;UACD,IAAI,CAACG,YAAY,CAAC,eAAe,EAAE,IAAInE,KAAK,CAAC,2LAA2L,CAAC,CAAC;QAC9O;QACA;MACJ,KAAKxD,UAAU,CAACkE,KAAK;MACrB,KAAKlE,UAAU,CAACuI,YAAY;QACxB,IAAI,CAACC,OAAO,CAACxE,MAAM,CAAC;QACpB;MACJ,KAAKhE,UAAU,CAACyI,GAAG;MACnB,KAAKzI,UAAU,CAAC0I,UAAU;QACtB,IAAI,CAACC,KAAK,CAAC3E,MAAM,CAAC;QAClB;MACJ,KAAKhE,UAAU,CAAC4I,UAAU;QACtB,IAAI,CAACC,YAAY,CAAC,CAAC;QACnB;MACJ,KAAK7I,UAAU,CAAC8I,aAAa;QACzB,IAAI,CAACC,OAAO,CAAC,CAAC;QACd,MAAMnC,GAAG,GAAG,IAAIpD,KAAK,CAACQ,MAAM,CAACG,IAAI,CAAC6E,OAAO,CAAC;QAC1C;QACApC,GAAG,CAACzC,IAAI,GAAGH,MAAM,CAACG,IAAI,CAACA,IAAI;QAC3B,IAAI,CAACwD,YAAY,CAAC,eAAe,EAAEf,GAAG,CAAC;QACvC;IACR;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI4B,OAAOA,CAACxE,MAAM,EAAE;IACZ,MAAMnB,IAAI,GAAGmB,MAAM,CAACG,IAAI,IAAI,EAAE;IAC9B,IAAI,IAAI,IAAIH,MAAM,CAACM,EAAE,EAAE;MACnBzB,IAAI,CAACqC,IAAI,CAAC,IAAI,CAACX,GAAG,CAACP,MAAM,CAACM,EAAE,CAAC,CAAC;IAClC;IACA,IAAI,IAAI,CAACrD,SAAS,EAAE;MAChB,IAAI,CAACgI,SAAS,CAACpG,IAAI,CAAC;IACxB,CAAC,MACI;MACD,IAAI,CAAC1B,aAAa,CAAC+D,IAAI,CAAC9E,MAAM,CAACC,MAAM,CAACwC,IAAI,CAAC,CAAC;IAChD;EACJ;EACAoG,SAASA,CAACpG,IAAI,EAAE;IACZ,IAAI,IAAI,CAACqG,aAAa,IAAI,IAAI,CAACA,aAAa,CAACtG,MAAM,EAAE;MACjD,MAAMuG,SAAS,GAAG,IAAI,CAACD,aAAa,CAACE,KAAK,CAAC,CAAC;MAC5C,KAAK,MAAMC,QAAQ,IAAIF,SAAS,EAAE;QAC9BE,QAAQ,CAACnG,KAAK,CAAC,IAAI,EAAEL,IAAI,CAAC;MAC9B;IACJ;IACA,KAAK,CAACI,IAAI,CAACC,KAAK,CAAC,IAAI,EAAEL,IAAI,CAAC;IAC5B,IAAI,IAAI,CAAC0E,IAAI,IAAI1E,IAAI,CAACD,MAAM,IAAI,OAAOC,IAAI,CAACA,IAAI,CAACD,MAAM,GAAG,CAAC,CAAC,KAAK,QAAQ,EAAE;MACvE,IAAI,CAAC8E,WAAW,GAAG7E,IAAI,CAACA,IAAI,CAACD,MAAM,GAAG,CAAC,CAAC;IAC5C;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI2B,GAAGA,CAACD,EAAE,EAAE;IACJ,MAAMgF,IAAI,GAAG,IAAI;IACjB,IAAIC,IAAI,GAAG,KAAK;IAChB,OAAO,YAAmB;MACtB;MACA,IAAIA,IAAI,EACJ;MACJA,IAAI,GAAG,IAAI;MAAC,SAAAC,KAAA,GAAA7G,SAAA,CAAAC,MAAA,EAJIC,IAAI,OAAAC,KAAA,CAAA0G,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAAJ5G,IAAI,CAAA4G,KAAA,IAAA9G,SAAA,CAAA8G,KAAA;MAAA;MAKpBH,IAAI,CAACtF,MAAM,CAAC;QACRC,IAAI,EAAEjE,UAAU,CAACyI,GAAG;QACpBnE,EAAE,EAAEA,EAAE;QACNH,IAAI,EAAEtB;MACV,CAAC,CAAC;IACN,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;EACI8F,KAAKA,CAAC3E,MAAM,EAAE;IACV,MAAMO,GAAG,GAAG,IAAI,CAAC/C,IAAI,CAACwC,MAAM,CAACM,EAAE,CAAC;IAChC,IAAI,OAAOC,GAAG,KAAK,UAAU,EAAE;MAC3B;IACJ;IACA,OAAO,IAAI,CAAC/C,IAAI,CAACwC,MAAM,CAACM,EAAE,CAAC;IAC3B;IACA,IAAIC,GAAG,CAACyB,SAAS,EAAE;MACfhC,MAAM,CAACG,IAAI,CAACnB,OAAO,CAAC,IAAI,CAAC;IAC7B;IACA;IACAuB,GAAG,CAACrB,KAAK,CAAC,IAAI,EAAEc,MAAM,CAACG,IAAI,CAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;EACImE,SAASA,CAAChE,EAAE,EAAEkD,GAAG,EAAE;IACf,IAAI,CAAClD,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACpD,SAAS,GAAGsG,GAAG,IAAI,IAAI,CAACD,IAAI,KAAKC,GAAG;IACzC,IAAI,CAACD,IAAI,GAAGC,GAAG,CAAC,CAAC;IACjB,IAAI,CAACvG,SAAS,GAAG,IAAI;IACrB,IAAI,CAACyI,YAAY,CAAC,CAAC;IACnB,IAAI,CAAC/B,YAAY,CAAC,SAAS,CAAC;IAC5B,IAAI,CAACT,WAAW,CAAC,IAAI,CAAC;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACIwC,YAAYA,CAAA,EAAG;IACX,IAAI,CAACvI,aAAa,CAAC6G,OAAO,CAAEnF,IAAI,IAAK,IAAI,CAACoG,SAAS,CAACpG,IAAI,CAAC,CAAC;IAC1D,IAAI,CAAC1B,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,UAAU,CAAC4G,OAAO,CAAEhE,MAAM,IAAK;MAChC,IAAI,CAACiB,uBAAuB,CAACjB,MAAM,CAAC;MACpC,IAAI,CAACA,MAAM,CAACA,MAAM,CAAC;IACvB,CAAC,CAAC;IACF,IAAI,CAAC5C,UAAU,GAAG,EAAE;EACxB;EACA;AACJ;AACA;AACA;AACA;EACIyH,YAAYA,CAAA,EAAG;IACX,IAAI,CAACE,OAAO,CAAC,CAAC;IACd,IAAI,CAACzG,OAAO,CAAC,sBAAsB,CAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIyG,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAAC9G,IAAI,EAAE;MACX;MACA,IAAI,CAACA,IAAI,CAAC+F,OAAO,CAAE2B,UAAU,IAAKA,UAAU,CAAC,CAAC,CAAC;MAC/C,IAAI,CAAC1H,IAAI,GAAGqD,SAAS;IACzB;IACA,IAAI,CAACxE,EAAE,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;EAC7B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIN,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACS,SAAS,EAAE;MAChB,IAAI,CAAC+C,MAAM,CAAC;QAAEC,IAAI,EAAEjE,UAAU,CAAC4I;MAAW,CAAC,CAAC;IAChD;IACA;IACA,IAAI,CAACG,OAAO,CAAC,CAAC;IACd,IAAI,IAAI,CAAC9H,SAAS,EAAE;MAChB;MACA,IAAI,CAACqB,OAAO,CAAC,sBAAsB,CAAC;IACxC;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIsH,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACpJ,UAAU,CAAC,CAAC;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI6D,QAAQA,CAACA,QAAQ,EAAE;IACf,IAAI,CAAC5C,KAAK,CAAC4C,QAAQ,GAAGA,QAAQ;IAC9B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIP,QAAQA,CAAA,EAAG;IACX,IAAI,CAACrC,KAAK,CAACqC,QAAQ,GAAG,IAAI;IAC1B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIsB,OAAOA,CAACA,OAAO,EAAE;IACb,IAAI,CAAC3D,KAAK,CAAC2D,OAAO,GAAGA,OAAO;IAC5B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIyE,KAAKA,CAACR,QAAQ,EAAE;IACZ,IAAI,CAACH,aAAa,GAAG,IAAI,CAACA,aAAa,IAAI,EAAE;IAC7C,IAAI,CAACA,aAAa,CAAChE,IAAI,CAACmE,QAAQ,CAAC;IACjC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIS,UAAUA,CAACT,QAAQ,EAAE;IACjB,IAAI,CAACH,aAAa,GAAG,IAAI,CAACA,aAAa,IAAI,EAAE;IAC7C,IAAI,CAACA,aAAa,CAAClG,OAAO,CAACqG,QAAQ,CAAC;IACpC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIU,MAAMA,CAACV,QAAQ,EAAE;IACb,IAAI,CAAC,IAAI,CAACH,aAAa,EAAE;MACrB,OAAO,IAAI;IACf;IACA,IAAIG,QAAQ,EAAE;MACV,MAAMF,SAAS,GAAG,IAAI,CAACD,aAAa;MACpC,KAAK,IAAIzD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0D,SAAS,CAACvG,MAAM,EAAE6C,CAAC,EAAE,EAAE;QACvC,IAAI4D,QAAQ,KAAKF,SAAS,CAAC1D,CAAC,CAAC,EAAE;UAC3B0D,SAAS,CAACzD,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;UACtB,OAAO,IAAI;QACf;MACJ;IACJ,CAAC,MACI;MACD,IAAI,CAACyD,aAAa,GAAG,EAAE;IAC3B;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIc,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACd,aAAa,IAAI,EAAE;EACnC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIe,aAAaA,CAACZ,QAAQ,EAAE;IACpB,IAAI,CAACa,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,IAAI,EAAE;IAC7D,IAAI,CAACA,qBAAqB,CAAChF,IAAI,CAACmE,QAAQ,CAAC;IACzC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIc,kBAAkBA,CAACd,QAAQ,EAAE;IACzB,IAAI,CAACa,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,IAAI,EAAE;IAC7D,IAAI,CAACA,qBAAqB,CAAClH,OAAO,CAACqG,QAAQ,CAAC;IAC5C,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIe,cAAcA,CAACf,QAAQ,EAAE;IACrB,IAAI,CAAC,IAAI,CAACa,qBAAqB,EAAE;MAC7B,OAAO,IAAI;IACf;IACA,IAAIb,QAAQ,EAAE;MACV,MAAMF,SAAS,GAAG,IAAI,CAACe,qBAAqB;MAC5C,KAAK,IAAIzE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0D,SAAS,CAACvG,MAAM,EAAE6C,CAAC,EAAE,EAAE;QACvC,IAAI4D,QAAQ,KAAKF,SAAS,CAAC1D,CAAC,CAAC,EAAE;UAC3B0D,SAAS,CAACzD,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;UACtB,OAAO,IAAI;QACf;MACJ;IACJ,CAAC,MACI;MACD,IAAI,CAACyE,qBAAqB,GAAG,EAAE;IACnC;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIG,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACH,qBAAqB,IAAI,EAAE;EAC3C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIjF,uBAAuBA,CAACjB,MAAM,EAAE;IAC5B,IAAI,IAAI,CAACkG,qBAAqB,IAAI,IAAI,CAACA,qBAAqB,CAACtH,MAAM,EAAE;MACjE,MAAMuG,SAAS,GAAG,IAAI,CAACe,qBAAqB,CAACd,KAAK,CAAC,CAAC;MACpD,KAAK,MAAMC,QAAQ,IAAIF,SAAS,EAAE;QAC9BE,QAAQ,CAACnG,KAAK,CAAC,IAAI,EAAEc,MAAM,CAACG,IAAI,CAAC;MACrC;IACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}