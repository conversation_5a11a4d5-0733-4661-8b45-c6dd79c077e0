{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"4\",\n  key: \"4exip2\"\n}], [\"path\", {\n  d: \"M12 4h.01\",\n  key: \"1ujb9j\"\n}], [\"path\", {\n  d: \"M20 12h.01\",\n  key: \"1ykeid\"\n}], [\"path\", {\n  d: \"M12 20h.01\",\n  key: \"zekei9\"\n}], [\"path\", {\n  d: \"M4 12h.01\",\n  key: \"158zrr\"\n}], [\"path\", {\n  d: \"M17.657 6.343h.01\",\n  key: \"31pqzk\"\n}], [\"path\", {\n  d: \"M17.657 17.657h.01\",\n  key: \"jehnf4\"\n}], [\"path\", {\n  d: \"M6.343 17.657h.01\",\n  key: \"gdk6ow\"\n}], [\"path\", {\n  d: \"M6.343 6.343h.01\",\n  key: \"1uurf0\"\n}]];\nconst SunDim = createLucideIcon(\"sun-dim\", __iconNode);\nexport { __iconNode, SunDim as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "d", "<PERSON><PERSON><PERSON>", "createLucideIcon"], "sources": ["D:\\ASL\\training-frontend\\node_modules\\lucide-react\\src\\icons\\sun-dim.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '4', key: '4exip2' }],\n  ['path', { d: 'M12 4h.01', key: '1ujb9j' }],\n  ['path', { d: 'M20 12h.01', key: '1ykeid' }],\n  ['path', { d: 'M12 20h.01', key: 'zekei9' }],\n  ['path', { d: 'M4 12h.01', key: '158zrr' }],\n  ['path', { d: 'M17.657 6.343h.01', key: '31pqzk' }],\n  ['path', { d: 'M17.657 17.657h.01', key: 'jehnf4' }],\n  ['path', { d: 'M6.343 17.657h.01', key: 'gdk6ow' }],\n  ['path', { d: 'M6.343 6.343h.01', key: '1uurf0' }],\n];\n\n/**\n * @component @name SunDim\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSI0IiAvPgogIDxwYXRoIGQ9Ik0xMiA0aC4wMSIgLz4KICA8cGF0aCBkPSJNMjAgMTJoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xMiAyMGguMDEiIC8+CiAgPHBhdGggZD0iTTQgMTJoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xNy42NTcgNi4zNDNoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xNy42NTcgMTcuNjU3aC4wMSIgLz4KICA8cGF0aCBkPSJNNi4zNDMgMTcuNjU3aC4wMSIgLz4KICA8cGF0aCBkPSJNNi4zNDMgNi4zNDNoLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/sun-dim\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SunDim = createLucideIcon('sun-dim', __iconNode);\n\nexport default SunDim;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,mBAAqB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,MAAQ;EAAEC,CAAA,EAAG,oBAAsB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACnD,CAAC,MAAQ;EAAEC,CAAA,EAAG,mBAAqB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,MAAQ;EAAEC,CAAA,EAAG,kBAAoB;EAAAD,GAAA,EAAK;AAAU,GACnD;AAaM,MAAAE,MAAA,GAASC,gBAAiB,YAAWP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}