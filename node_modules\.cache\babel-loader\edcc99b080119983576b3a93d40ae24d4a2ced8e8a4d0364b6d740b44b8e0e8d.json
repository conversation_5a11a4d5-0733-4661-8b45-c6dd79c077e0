{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12.67 19a2 2 0 0 0 1.416-.588l6.154-6.172a6 6 0 0 0-8.49-8.49L5.586 9.914A2 2 0 0 0 5 11.328V18a1 1 0 0 0 1 1z\",\n  key: \"18jl4k\"\n}], [\"path\", {\n  d: \"M16 8 2 22\",\n  key: \"vp34q\"\n}], [\"path\", {\n  d: \"M17.5 15H9\",\n  key: \"1oz8nu\"\n}]];\nconst Feather = createLucideIcon(\"feather\", __iconNode);\nexport { __iconNode, Feather as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "<PERSON><PERSON>", "createLucideIcon"], "sources": ["D:\\ASL\\ASL-Training\\node_modules\\lucide-react\\src\\icons\\feather.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M12.67 19a2 2 0 0 0 1.416-.588l6.154-6.172a6 6 0 0 0-8.49-8.49L5.586 9.914A2 2 0 0 0 5 11.328V18a1 1 0 0 0 1 1z',\n      key: '18jl4k',\n    },\n  ],\n  ['path', { d: 'M16 8 2 22', key: 'vp34q' }],\n  ['path', { d: 'M17.5 15H9', key: '1oz8nu' }],\n];\n\n/**\n * @component @name Feather\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIuNjcgMTlhMiAyIDAgMCAwIDEuNDE2LS41ODhsNi4xNTQtNi4xNzJhNiA2IDAgMCAwLTguNDktOC40OUw1LjU4NiA5LjkxNEEyIDIgMCAwIDAgNSAxMS4zMjhWMThhMSAxIDAgMCAwIDEgMXoiIC8+CiAgPHBhdGggZD0iTTE2IDggMiAyMiIgLz4KICA8cGF0aCBkPSJNMTcuNSAxNUg5IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/feather\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Feather = createLucideIcon('feather', __iconNode);\n\nexport default Feather;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAS,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAU,GAC7C;AAaM,MAAAC,OAAA,GAAUC,gBAAiB,YAAWJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}