{"ast": null, "code": "// Frontend configuration\nconst config = {\n  // Backend API URL\n  BACKEND_URL: process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000',\n  // WebSocket URL for real-time detection\n  WEBSOCKET_URL: process.env.REACT_APP_WEBSOCKET_URL || 'ws://localhost:8000/ws/detect',\n  // API endpoints\n  API_ENDPOINTS: {\n    HEALTH: '/health',\n    SIGNS: '/signs',\n    RECORDINGS: '/recordings',\n    TRAIN: '/train',\n    TRAINING_STATUS: '/training/status'\n  },\n  // WebSocket endpoints\n  WS_ENDPOINTS: {\n    DETECT: '/ws/detect'\n  },\n  // App settings\n  APP: {\n    NAME: 'ASL Sign Language Trainer',\n    VERSION: '1.0.0',\n    DESCRIPTION: 'Real-time American Sign Language detection and training'\n  },\n  // Detection settings\n  DETECTION: {\n    CONFIDENCE_THRESHOLD: 0.5,\n    FRAME_INTERVAL: 100,\n    // ms\n    MAX_PREDICTION_HISTORY: 5\n  },\n  // Recording settings\n  RECORDING: {\n    SESSION_DURATION: 3000,\n    // 3 seconds\n    MIN_FRAMES: 30\n  }\n};\nexport default config;", "map": {"version": 3, "names": ["config", "BACKEND_URL", "process", "env", "REACT_APP_BACKEND_URL", "WEBSOCKET_URL", "REACT_APP_WEBSOCKET_URL", "API_ENDPOINTS", "HEALTH", "SIGNS", "RECORDINGS", "TRAIN", "TRAINING_STATUS", "WS_ENDPOINTS", "DETECT", "APP", "NAME", "VERSION", "DESCRIPTION", "DETECTION", "CONFIDENCE_THRESHOLD", "FRAME_INTERVAL", "MAX_PREDICTION_HISTORY", "RECORDING", "SESSION_DURATION", "MIN_FRAMES"], "sources": ["D:/ASL/ASL-Training/src/config.js"], "sourcesContent": ["// Frontend configuration\r\nconst config = {\r\n  // Backend API URL\r\n  BACKEND_URL: process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000',\r\n  \r\n  // WebSocket URL for real-time detection\r\n  WEBSOCKET_URL: process.env.REACT_APP_WEBSOCKET_URL || 'ws://localhost:8000/ws/detect',\r\n  \r\n  // API endpoints\r\n  API_ENDPOINTS: {\r\n    HEALTH: '/health',\r\n    SIGNS: '/signs',\r\n    RECORDINGS: '/recordings',\r\n    TRAIN: '/train',\r\n    TRAINING_STATUS: '/training/status',\r\n  },\r\n  \r\n  // WebSocket endpoints\r\n  WS_ENDPOINTS: {\r\n    DETECT: '/ws/detect',\r\n  },\r\n  \r\n  // App settings\r\n  APP: {\r\n    NAME: 'ASL Sign Language Trainer',\r\n    VERSION: '1.0.0',\r\n    DESCRIPTION: 'Real-time American Sign Language detection and training',\r\n  },\r\n  \r\n  // Detection settings\r\n  DETECTION: {\r\n    CONFIDENCE_THRESHOLD: 0.5,\r\n    FRAME_INTERVAL: 100, // ms\r\n    MAX_PREDICTION_HISTORY: 5,\r\n  },\r\n  \r\n  // Recording settings\r\n  RECORDING: {\r\n    SESSION_DURATION: 3000, // 3 seconds\r\n    MIN_FRAMES: 30,\r\n  },\r\n};\r\n\r\nexport default config; "], "mappings": "AAAA;AACA,MAAMA,MAAM,GAAG;EACb;EACAC,WAAW,EAAEC,OAAO,CAACC,GAAG,CAACC,qBAAqB,IAAI,uBAAuB;EAEzE;EACAC,aAAa,EAAEH,OAAO,CAACC,GAAG,CAACG,uBAAuB,IAAI,+BAA+B;EAErF;EACAC,aAAa,EAAE;IACbC,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE,QAAQ;IACfC,UAAU,EAAE,aAAa;IACzBC,KAAK,EAAE,QAAQ;IACfC,eAAe,EAAE;EACnB,CAAC;EAED;EACAC,YAAY,EAAE;IACZC,MAAM,EAAE;EACV,CAAC;EAED;EACAC,GAAG,EAAE;IACHC,IAAI,EAAE,2BAA2B;IACjCC,OAAO,EAAE,OAAO;IAChBC,WAAW,EAAE;EACf,CAAC;EAED;EACAC,SAAS,EAAE;IACTC,oBAAoB,EAAE,GAAG;IACzBC,cAAc,EAAE,GAAG;IAAE;IACrBC,sBAAsB,EAAE;EAC1B,CAAC;EAED;EACAC,SAAS,EAAE;IACTC,gBAAgB,EAAE,IAAI;IAAE;IACxBC,UAAU,EAAE;EACd;AACF,CAAC;AAED,eAAezB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}