{"ast": null, "code": "var _jsxFileName = \"D:\\\\ASL\\\\ASL-Training\\\\src\\\\components\\\\LevelSelector.js\";\nimport React from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport { Play, Lock, CheckCircle, Star, Trophy, Target, Sparkles, Zap } from 'lucide-react';\nimport { SIGN_LEVELS, getTotalLevels } from '../data/signLevels';\nimport { theme } from '../styles/theme';\nimport { Container, Section, Grid, Card, Button, Heading, Text, ProgressBar, ProgressFill, Badge } from './ui/ModernComponents';\n\n// Modern Animations\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst modernFadeInUp = keyframes`\n  from {\n    opacity: 0;\n    transform: translateY(30px) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n`;\nconst modernPulse = keyframes`\n  0%, 100% {\n    transform: scale(1);\n    box-shadow: ${theme.shadows.lg};\n  }\n  50% {\n    transform: scale(1.02);\n    box-shadow: ${theme.shadows.glow};\n  }\n`;\nconst floatingAnimation = keyframes`\n  0%, 100% { transform: translateY(0px); }\n  50% { transform: translateY(-10px); }\n`;\n\n// Modern Styled Components\nconst ModernContainer = styled(Container)`\n  background: var(--bg-primary);\n  min-height: 100vh;\n  padding: ${theme.spacing[8]} ${theme.spacing[4]};\n  position: relative;\n  overflow-x: hidden;\n\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    padding: ${theme.spacing[6]} ${theme.spacing[3]};\n  }\n`;\n_c = ModernContainer;\nconst ModernHeader = styled(Section)`\n  text-align: center;\n  padding: ${theme.spacing[8]} 0 ${theme.spacing[12]};\n  animation: ${modernFadeInUp} 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    padding: ${theme.spacing[6]} 0 ${theme.spacing[8]};\n  }\n`;\n_c2 = ModernHeader;\nconst HeroTitle = styled(Heading)`\n  margin-bottom: ${theme.spacing[4]};\n  position: relative;\n\n  &::after {\n    content: '✨';\n    position: absolute;\n    top: -10px;\n    right: -20px;\n    font-size: 2rem;\n    animation: ${floatingAnimation} 3s ease-in-out infinite;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    &::after {\n      display: none;\n    }\n  }\n`;\n_c3 = HeroTitle;\nconst HeroSubtitle = styled(Text)`\n  max-width: 600px;\n  margin: 0 auto ${theme.spacing[8]};\n`;\n_c4 = HeroSubtitle;\nconst ModernLevelsGrid = styled(Grid)`\n  margin-bottom: ${theme.spacing[8]};\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    grid-template-columns: 1fr;\n  }\n`;\n_c5 = ModernLevelsGrid;\nconst ModernLevelCard = styled(Card)`\n  cursor: ${props => props.isLocked ? 'not-allowed' : 'pointer'};\n  opacity: ${props => props.isLocked ? 0.6 : 1};\n  animation: ${modernFadeInUp} 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);\n  animation-delay: ${props => props.index * 0.1}s;\n  animation-fill-mode: both;\n  position: relative;\n  overflow: hidden;\n  border: 2px solid ${props => {\n  if (props.isCompleted) return theme.colors.success[200];\n  if (props.isLocked) return theme.colors.neutral[200];\n  if (props.isActive) return theme.colors.primary[300];\n  return theme.colors.neutral[100];\n}};\n\n  &:hover {\n    transform: ${props => props.isLocked ? 'none' : 'translateY(-8px) scale(1.02)'};\n    box-shadow: ${props => {\n  if (props.isLocked) return theme.shadows.lg;\n  if (props.isCompleted) return theme.shadows.glowSuccess;\n  return theme.shadows.glow;\n}};\n  }\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 6px;\n    background: ${props => {\n  if (props.isCompleted) return theme.colors.gradients.success;\n  if (props.isLocked) return theme.colors.gradients.secondary;\n  return theme.colors.gradients.primary;\n}};\n    border-radius: ${theme.borderRadius['2xl']} ${theme.borderRadius['2xl']} 0 0;\n  }\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: -50%;\n    left: -50%;\n    width: 200%;\n    height: 200%;\n    background: ${props => {\n  if (props.isCompleted) return `radial-gradient(circle, ${theme.colors.success[50]} 0%, transparent 70%)`;\n  if (props.isActive) return `radial-gradient(circle, ${theme.colors.primary[50]} 0%, transparent 70%)`;\n  return 'transparent';\n}};\n    opacity: 0.5;\n    pointer-events: none;\n    z-index: 0;\n  }\n\n  > * {\n    position: relative;\n    z-index: 1;\n  }\n\n  ${props => props.isActive && `\n    animation: ${modernPulse} 2s ease infinite;\n    box-shadow: 0 0 0 4px ${theme.colors.primary[200]};\n  `}\n\n  ${props => props.isCompleted && `\n    &::before {\n      background: ${theme.colors.gradients.success};\n      box-shadow: 0 2px 10px ${theme.colors.success[300]};\n    }\n  `}\n`;\n_c6 = ModernLevelCard;\nconst ModernLevelHeader = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${theme.spacing[4]};\n`;\n_c7 = ModernLevelHeader;\nconst ModernLevelNumber = styled.div`\n  background: ${props => {\n  if (props.isCompleted) return theme.colors.gradients.success;\n  if (props.isLocked) return theme.colors.gradients.secondary;\n  return theme.colors.gradients.primary;\n}};\n  color: ${theme.colors.text.inverse};\n  width: 60px;\n  height: 60px;\n  border-radius: ${theme.borderRadius.full};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: ${theme.typography.fontWeight.bold};\n  font-size: ${theme.typography.fontSize.xl};\n  box-shadow: ${theme.shadows.md};\n  position: relative;\n\n  ${props => props.isCompleted && `\n    &::after {\n      content: '✨';\n      position: absolute;\n      top: -5px;\n      right: -5px;\n      font-size: 1rem;\n      animation: ${floatingAnimation} 2s ease-in-out infinite;\n    }\n  `}\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    width: 50px;\n    height: 50px;\n    font-size: ${theme.typography.fontSize.lg};\n  }\n`;\n_c8 = ModernLevelNumber;\nconst ModernLevelStatus = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${theme.spacing[2]};\n  color: ${props => {\n  if (props.isCompleted) return theme.colors.success[600];\n  if (props.isLocked) return theme.colors.neutral[400];\n  return theme.colors.primary[600];\n}};\n  font-weight: ${theme.typography.fontWeight.medium};\n`;\n_c9 = ModernLevelStatus;\nconst ModernLevelTitle = styled(Heading)`\n  margin-bottom: ${theme.spacing[2]};\n  color: ${theme.colors.text.primary};\n`;\n_c0 = ModernLevelTitle;\nconst ModernLevelTheme = styled.div`\n  font-size: ${theme.typography.fontSize.xl};\n  margin-bottom: ${theme.spacing[3]};\n  text-align: center;\n  padding: ${theme.spacing[2]};\n  background: ${theme.colors.gradients.surface};\n  border-radius: ${theme.borderRadius.lg};\n  border: 1px solid ${theme.colors.neutral[100]};\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    font-size: ${theme.typography.fontSize.lg};\n  }\n`;\n_c1 = ModernLevelTheme;\nconst ModernLevelDescription = styled(Text)`\n  margin-bottom: ${theme.spacing[4]};\n  text-align: center;\n`;\n_c10 = ModernLevelDescription;\nconst ModernLevelProgress = styled.div`\n  margin-bottom: ${theme.spacing[5]};\n`;\n_c11 = ModernLevelProgress;\nconst ProgressHeader = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${theme.spacing[2]};\n`;\n_c12 = ProgressHeader;\nconst ProgressText = styled(Text)`\n  font-weight: ${theme.typography.fontWeight.medium};\n`;\n_c13 = ProgressText;\nconst ProgressPercentage = styled(Badge)`\n  font-weight: ${theme.typography.fontWeight.bold};\n`;\n_c14 = ProgressPercentage;\nconst ModernProgressBar = styled(ProgressBar)`\n  height: 12px;\n  background: ${theme.colors.neutral[200]};\n  margin-bottom: ${theme.spacing[3]};\n  position: relative;\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n    animation: shimmer 2s infinite;\n  }\n`;\n_c15 = ModernProgressBar;\nconst ModernProgressFill = styled(ProgressFill)`\n  background: ${props => {\n  if (props.isCompleted) return theme.colors.gradients.success;\n  return theme.colors.gradients.primary;\n}};\n  position: relative;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\n    animation: shimmer 3s infinite;\n  }\n`;\n_c16 = ModernProgressFill;\nconst ModernActionButton = styled(Button)`\n  width: 100%;\n  height: ${theme.components.button.height.lg};\n  font-size: ${theme.typography.fontSize.base};\n  font-weight: ${theme.typography.fontWeight.semibold};\n\n  ${props => {\n  if (props.disabled) {\n    return `\n        background: ${theme.colors.neutral[200]};\n        color: ${theme.colors.neutral[400]};\n        cursor: not-allowed;\n\n        &:hover {\n          transform: none;\n          box-shadow: ${theme.shadows.base};\n        }\n      `;\n  }\n  if (props.isCompleted) {\n    return `\n        variant: success;\n      `;\n  }\n  return `\n      variant: primary;\n    `;\n}}\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    height: ${theme.components.button.height.xl};\n    font-size: ${theme.typography.fontSize.lg};\n  }\n`;\n_c17 = ModernActionButton;\nconst shimmer = keyframes`\n  0% { transform: translateX(-100%); }\n  100% { transform: translateX(100%); }\n`;\nconst LevelSelector = ({\n  currentLevel,\n  userProgress = {},\n  onLevelSelect,\n  className\n}) => {\n  const totalLevels = getTotalLevels();\n  const getLevelProgress = level => {\n    const progress = userProgress[level] || {\n      completed: 0,\n      total: 20\n    };\n    return progress.completed / progress.total * 100;\n  };\n  const isLevelLocked = level => {\n    if (level === 1) return false;\n    const prevLevelProgress = userProgress[level - 1] || {\n      completed: 0,\n      total: 20\n    };\n    return prevLevelProgress.completed < prevLevelProgress.total;\n  };\n  const isLevelCompleted = level => {\n    const progress = userProgress[level] || {\n      completed: 0,\n      total: 20\n    };\n    return progress.completed >= progress.total;\n  };\n  const getStatusIcon = level => {\n    if (isLevelCompleted(level)) return /*#__PURE__*/_jsxDEV(CheckCircle, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 41\n    }, this);\n    if (isLevelLocked(level)) return /*#__PURE__*/_jsxDEV(Lock, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 38\n    }, this);\n    if (level === currentLevel) return /*#__PURE__*/_jsxDEV(Target, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 40\n    }, this);\n    return /*#__PURE__*/_jsxDEV(Star, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 12\n    }, this);\n  };\n  const getButtonText = level => {\n    if (isLevelLocked(level)) return 'Locked';\n    if (isLevelCompleted(level)) return 'Review Level';\n    if (level === currentLevel) return 'Continue';\n    return 'Start Level';\n  };\n  const getButtonIcon = level => {\n    if (isLevelLocked(level)) return /*#__PURE__*/_jsxDEV(Lock, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 38\n    }, this);\n    if (isLevelCompleted(level)) return /*#__PURE__*/_jsxDEV(Trophy, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 396,\n      columnNumber: 41\n    }, this);\n    return /*#__PURE__*/_jsxDEV(Play, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 12\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(ModernContainer, {\n    className: className,\n    children: [/*#__PURE__*/_jsxDEV(ModernHeader, {\n      children: [/*#__PURE__*/_jsxDEV(HeroTitle, {\n        level: 1,\n        gradient: \"primary\",\n        children: \"ASL Learning Journey\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(HeroSubtitle, {\n        size: \"xl\",\n        muted: true,\n        children: \"Master American Sign Language through interactive flash cards. Complete each level to unlock the next challenge!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n        variant: \"primary\",\n        style: {\n          fontSize: theme.typography.fontSize.base,\n          padding: `${theme.spacing[2]} ${theme.spacing[4]}`\n        },\n        children: [getTotalLevels(), \" Levels \\u2022 260 Signs Total\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 402,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ModernLevelsGrid, {\n      children: Object.entries(SIGN_LEVELS).map(([levelNum, levelData], index) => {\n        var _userProgress$level;\n        const level = parseInt(levelNum);\n        const isLocked = isLevelLocked(level);\n        const isCompleted = isLevelCompleted(level);\n        const isActive = level === currentLevel;\n        const progress = getLevelProgress(level);\n        return /*#__PURE__*/_jsxDEV(ModernLevelCard, {\n          index: index,\n          isLocked: isLocked,\n          isCompleted: isCompleted,\n          isActive: isActive,\n          hover: !isLocked,\n          size: \"lg\",\n          onClick: () => !isLocked && onLevelSelect(level),\n          children: [/*#__PURE__*/_jsxDEV(ModernLevelHeader, {\n            children: [/*#__PURE__*/_jsxDEV(ModernLevelNumber, {\n              isCompleted: isCompleted,\n              isLocked: isLocked,\n              children: level\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ModernLevelStatus, {\n              isCompleted: isCompleted,\n              isLocked: isLocked,\n              children: getStatusIcon(level)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ModernLevelTheme, {\n            children: levelData.theme\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ModernLevelTitle, {\n            level: 3,\n            children: levelData.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ModernLevelDescription, {\n            size: \"base\",\n            muted: true,\n            children: levelData.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ModernLevelProgress, {\n            children: [/*#__PURE__*/_jsxDEV(ProgressHeader, {\n              children: [/*#__PURE__*/_jsxDEV(ProgressText, {\n                size: \"sm\",\n                weight: \"medium\",\n                children: [((_userProgress$level = userProgress[level]) === null || _userProgress$level === void 0 ? void 0 : _userProgress$level.completed) || 0, \" / 20 signs\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ProgressPercentage, {\n                variant: isCompleted ? \"success\" : \"primary\",\n                children: [Math.round(progress), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ModernProgressBar, {\n              children: /*#__PURE__*/_jsxDEV(ModernProgressFill, {\n                style: {\n                  width: `${progress}%`\n                },\n                isCompleted: isCompleted\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ModernActionButton, {\n            variant: isCompleted ? \"success\" : \"primary\",\n            size: \"lg\",\n            disabled: isLocked,\n            isCompleted: isCompleted,\n            onClick: e => {\n              e.stopPropagation();\n              if (!isLocked) onLevelSelect(level);\n            },\n            children: [getButtonIcon(level), getButtonText(level)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 15\n          }, this)]\n        }, level, true, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 401,\n    columnNumber: 5\n  }, this);\n};\n_c18 = LevelSelector;\nexport default LevelSelector;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18;\n$RefreshReg$(_c, \"ModernContainer\");\n$RefreshReg$(_c2, \"ModernHeader\");\n$RefreshReg$(_c3, \"HeroTitle\");\n$RefreshReg$(_c4, \"HeroSubtitle\");\n$RefreshReg$(_c5, \"ModernLevelsGrid\");\n$RefreshReg$(_c6, \"ModernLevelCard\");\n$RefreshReg$(_c7, \"ModernLevelHeader\");\n$RefreshReg$(_c8, \"ModernLevelNumber\");\n$RefreshReg$(_c9, \"ModernLevelStatus\");\n$RefreshReg$(_c0, \"ModernLevelTitle\");\n$RefreshReg$(_c1, \"ModernLevelTheme\");\n$RefreshReg$(_c10, \"ModernLevelDescription\");\n$RefreshReg$(_c11, \"ModernLevelProgress\");\n$RefreshReg$(_c12, \"ProgressHeader\");\n$RefreshReg$(_c13, \"ProgressText\");\n$RefreshReg$(_c14, \"ProgressPercentage\");\n$RefreshReg$(_c15, \"ModernProgressBar\");\n$RefreshReg$(_c16, \"ModernProgressFill\");\n$RefreshReg$(_c17, \"ModernActionButton\");\n$RefreshReg$(_c18, \"LevelSelector\");", "map": {"version": 3, "names": ["React", "styled", "keyframes", "Play", "Lock", "CheckCircle", "Star", "Trophy", "Target", "<PERSON><PERSON><PERSON>", "Zap", "SIGN_LEVELS", "getTotalLevels", "theme", "Container", "Section", "Grid", "Card", "<PERSON><PERSON>", "Heading", "Text", "ProgressBar", "ProgressFill", "Badge", "jsxDEV", "_jsxDEV", "modernFadeInUp", "modernPulse", "shadows", "lg", "glow", "floatingAnimation", "ModernContainer", "spacing", "breakpoints", "md", "_c", "ModernHeader", "_c2", "<PERSON><PERSON><PERSON><PERSON>", "sm", "_c3", "HeroSubtitle", "_c4", "ModernLevelsGrid", "_c5", "ModernLevelCard", "props", "isLocked", "index", "isCompleted", "colors", "success", "neutral", "isActive", "primary", "glowSuccess", "gradients", "secondary", "borderRadius", "_c6", "ModernLevelHeader", "div", "_c7", "ModernLevelNumber", "text", "inverse", "full", "typography", "fontWeight", "bold", "fontSize", "xl", "_c8", "ModernLevelStatus", "medium", "_c9", "ModernLevelTitle", "_c0", "ModernLevelTheme", "surface", "_c1", "ModernLevelDescription", "_c10", "ModernLevelProgress", "_c11", "ProgressHeader", "_c12", "ProgressText", "_c13", "ProgressPercentage", "_c14", "ModernProgressBar", "_c15", "ModernProgressFill", "_c16", "ModernActionButton", "components", "button", "height", "base", "semibold", "disabled", "_c17", "shimmer", "LevelSelector", "currentLevel", "userProgress", "onLevelSelect", "className", "totalLevels", "getLevelProgress", "level", "progress", "completed", "total", "isLevelLocked", "prevLevelProgress", "isLevelCompleted", "getStatusIcon", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getButtonText", "getButtonIcon", "children", "gradient", "muted", "variant", "style", "padding", "Object", "entries", "map", "levelNum", "levelData", "_userProgress$level", "parseInt", "hover", "onClick", "name", "description", "weight", "Math", "round", "width", "e", "stopPropagation", "_c18", "$RefreshReg$"], "sources": ["D:/ASL/ASL-Training/src/components/LevelSelector.js"], "sourcesContent": ["import React from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport { Play, Lock, CheckCircle, Star, Trophy, Target, Sparkles, Zap } from 'lucide-react';\nimport { SIGN_LEVELS, getTotalLevels } from '../data/signLevels';\nimport { theme } from '../styles/theme';\nimport { Container, Section, Grid, Card, Button, Heading, Text, ProgressBar, ProgressFill, Badge } from './ui/ModernComponents';\n\n// Modern Animations\nconst modernFadeInUp = keyframes`\n  from {\n    opacity: 0;\n    transform: translateY(30px) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n`;\n\nconst modernPulse = keyframes`\n  0%, 100% {\n    transform: scale(1);\n    box-shadow: ${theme.shadows.lg};\n  }\n  50% {\n    transform: scale(1.02);\n    box-shadow: ${theme.shadows.glow};\n  }\n`;\n\nconst floatingAnimation = keyframes`\n  0%, 100% { transform: translateY(0px); }\n  50% { transform: translateY(-10px); }\n`;\n\n// Modern Styled Components\nconst ModernContainer = styled(Container)`\n  background: var(--bg-primary);\n  min-height: 100vh;\n  padding: ${theme.spacing[8]} ${theme.spacing[4]};\n  position: relative;\n  overflow-x: hidden;\n\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    padding: ${theme.spacing[6]} ${theme.spacing[3]};\n  }\n`;\n\nconst ModernHeader = styled(Section)`\n  text-align: center;\n  padding: ${theme.spacing[8]} 0 ${theme.spacing[12]};\n  animation: ${modernFadeInUp} 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);\n\n  @media (max-width: ${theme.breakpoints.md}) {\n    padding: ${theme.spacing[6]} 0 ${theme.spacing[8]};\n  }\n`;\n\nconst HeroTitle = styled(Heading)`\n  margin-bottom: ${theme.spacing[4]};\n  position: relative;\n\n  &::after {\n    content: '✨';\n    position: absolute;\n    top: -10px;\n    right: -20px;\n    font-size: 2rem;\n    animation: ${floatingAnimation} 3s ease-in-out infinite;\n  }\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    &::after {\n      display: none;\n    }\n  }\n`;\n\nconst HeroSubtitle = styled(Text)`\n  max-width: 600px;\n  margin: 0 auto ${theme.spacing[8]};\n`;\n\nconst ModernLevelsGrid = styled(Grid)`\n  margin-bottom: ${theme.spacing[8]};\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    grid-template-columns: 1fr;\n  }\n`;\n\nconst ModernLevelCard = styled(Card)`\n  cursor: ${props => props.isLocked ? 'not-allowed' : 'pointer'};\n  opacity: ${props => props.isLocked ? 0.6 : 1};\n  animation: ${modernFadeInUp} 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);\n  animation-delay: ${props => props.index * 0.1}s;\n  animation-fill-mode: both;\n  position: relative;\n  overflow: hidden;\n  border: 2px solid ${props => {\n    if (props.isCompleted) return theme.colors.success[200];\n    if (props.isLocked) return theme.colors.neutral[200];\n    if (props.isActive) return theme.colors.primary[300];\n    return theme.colors.neutral[100];\n  }};\n\n  &:hover {\n    transform: ${props => props.isLocked ? 'none' : 'translateY(-8px) scale(1.02)'};\n    box-shadow: ${props => {\n      if (props.isLocked) return theme.shadows.lg;\n      if (props.isCompleted) return theme.shadows.glowSuccess;\n      return theme.shadows.glow;\n    }};\n  }\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 6px;\n    background: ${props => {\n      if (props.isCompleted) return theme.colors.gradients.success;\n      if (props.isLocked) return theme.colors.gradients.secondary;\n      return theme.colors.gradients.primary;\n    }};\n    border-radius: ${theme.borderRadius['2xl']} ${theme.borderRadius['2xl']} 0 0;\n  }\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: -50%;\n    left: -50%;\n    width: 200%;\n    height: 200%;\n    background: ${props => {\n      if (props.isCompleted) return `radial-gradient(circle, ${theme.colors.success[50]} 0%, transparent 70%)`;\n      if (props.isActive) return `radial-gradient(circle, ${theme.colors.primary[50]} 0%, transparent 70%)`;\n      return 'transparent';\n    }};\n    opacity: 0.5;\n    pointer-events: none;\n    z-index: 0;\n  }\n\n  > * {\n    position: relative;\n    z-index: 1;\n  }\n\n  ${props => props.isActive && `\n    animation: ${modernPulse} 2s ease infinite;\n    box-shadow: 0 0 0 4px ${theme.colors.primary[200]};\n  `}\n\n  ${props => props.isCompleted && `\n    &::before {\n      background: ${theme.colors.gradients.success};\n      box-shadow: 0 2px 10px ${theme.colors.success[300]};\n    }\n  `}\n`;\n\nconst ModernLevelHeader = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${theme.spacing[4]};\n`;\n\nconst ModernLevelNumber = styled.div`\n  background: ${props => {\n    if (props.isCompleted) return theme.colors.gradients.success;\n    if (props.isLocked) return theme.colors.gradients.secondary;\n    return theme.colors.gradients.primary;\n  }};\n  color: ${theme.colors.text.inverse};\n  width: 60px;\n  height: 60px;\n  border-radius: ${theme.borderRadius.full};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: ${theme.typography.fontWeight.bold};\n  font-size: ${theme.typography.fontSize.xl};\n  box-shadow: ${theme.shadows.md};\n  position: relative;\n\n  ${props => props.isCompleted && `\n    &::after {\n      content: '✨';\n      position: absolute;\n      top: -5px;\n      right: -5px;\n      font-size: 1rem;\n      animation: ${floatingAnimation} 2s ease-in-out infinite;\n    }\n  `}\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    width: 50px;\n    height: 50px;\n    font-size: ${theme.typography.fontSize.lg};\n  }\n`;\n\nconst ModernLevelStatus = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${theme.spacing[2]};\n  color: ${props => {\n    if (props.isCompleted) return theme.colors.success[600];\n    if (props.isLocked) return theme.colors.neutral[400];\n    return theme.colors.primary[600];\n  }};\n  font-weight: ${theme.typography.fontWeight.medium};\n`;\n\nconst ModernLevelTitle = styled(Heading)`\n  margin-bottom: ${theme.spacing[2]};\n  color: ${theme.colors.text.primary};\n`;\n\nconst ModernLevelTheme = styled.div`\n  font-size: ${theme.typography.fontSize.xl};\n  margin-bottom: ${theme.spacing[3]};\n  text-align: center;\n  padding: ${theme.spacing[2]};\n  background: ${theme.colors.gradients.surface};\n  border-radius: ${theme.borderRadius.lg};\n  border: 1px solid ${theme.colors.neutral[100]};\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    font-size: ${theme.typography.fontSize.lg};\n  }\n`;\n\nconst ModernLevelDescription = styled(Text)`\n  margin-bottom: ${theme.spacing[4]};\n  text-align: center;\n`;\n\nconst ModernLevelProgress = styled.div`\n  margin-bottom: ${theme.spacing[5]};\n`;\n\nconst ProgressHeader = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${theme.spacing[2]};\n`;\n\nconst ProgressText = styled(Text)`\n  font-weight: ${theme.typography.fontWeight.medium};\n`;\n\nconst ProgressPercentage = styled(Badge)`\n  font-weight: ${theme.typography.fontWeight.bold};\n`;\n\nconst ModernProgressBar = styled(ProgressBar)`\n  height: 12px;\n  background: ${theme.colors.neutral[200]};\n  margin-bottom: ${theme.spacing[3]};\n  position: relative;\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n    animation: shimmer 2s infinite;\n  }\n`;\n\nconst ModernProgressFill = styled(ProgressFill)`\n  background: ${props => {\n    if (props.isCompleted) return theme.colors.gradients.success;\n    return theme.colors.gradients.primary;\n  }};\n  position: relative;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\n    animation: shimmer 3s infinite;\n  }\n`;\n\nconst ModernActionButton = styled(Button)`\n  width: 100%;\n  height: ${theme.components.button.height.lg};\n  font-size: ${theme.typography.fontSize.base};\n  font-weight: ${theme.typography.fontWeight.semibold};\n\n  ${props => {\n    if (props.disabled) {\n      return `\n        background: ${theme.colors.neutral[200]};\n        color: ${theme.colors.neutral[400]};\n        cursor: not-allowed;\n\n        &:hover {\n          transform: none;\n          box-shadow: ${theme.shadows.base};\n        }\n      `;\n    }\n    if (props.isCompleted) {\n      return `\n        variant: success;\n      `;\n    }\n    return `\n      variant: primary;\n    `;\n  }}\n\n  @media (max-width: ${theme.breakpoints.sm}) {\n    height: ${theme.components.button.height.xl};\n    font-size: ${theme.typography.fontSize.lg};\n  }\n`;\n\nconst shimmer = keyframes`\n  0% { transform: translateX(-100%); }\n  100% { transform: translateX(100%); }\n`;\n\nconst LevelSelector = ({ \n  currentLevel, \n  userProgress = {}, \n  onLevelSelect,\n  className \n}) => {\n  const totalLevels = getTotalLevels();\n\n  const getLevelProgress = (level) => {\n    const progress = userProgress[level] || { completed: 0, total: 20 };\n    return (progress.completed / progress.total) * 100;\n  };\n\n  const isLevelLocked = (level) => {\n    if (level === 1) return false;\n    const prevLevelProgress = userProgress[level - 1] || { completed: 0, total: 20 };\n    return prevLevelProgress.completed < prevLevelProgress.total;\n  };\n\n  const isLevelCompleted = (level) => {\n    const progress = userProgress[level] || { completed: 0, total: 20 };\n    return progress.completed >= progress.total;\n  };\n\n  const getStatusIcon = (level) => {\n    if (isLevelCompleted(level)) return <CheckCircle size={20} />;\n    if (isLevelLocked(level)) return <Lock size={20} />;\n    if (level === currentLevel) return <Target size={20} />;\n    return <Star size={20} />;\n  };\n\n  const getButtonText = (level) => {\n    if (isLevelLocked(level)) return 'Locked';\n    if (isLevelCompleted(level)) return 'Review Level';\n    if (level === currentLevel) return 'Continue';\n    return 'Start Level';\n  };\n\n  const getButtonIcon = (level) => {\n    if (isLevelLocked(level)) return <Lock size={20} />;\n    if (isLevelCompleted(level)) return <Trophy size={20} />;\n    return <Play size={20} />;\n  };\n\n  return (\n    <ModernContainer className={className}>\n      <ModernHeader>\n        <HeroTitle level={1} gradient=\"primary\">\n          ASL Learning Journey\n        </HeroTitle>\n        <HeroSubtitle size=\"xl\" muted>\n          Master American Sign Language through interactive flash cards.\n          Complete each level to unlock the next challenge!\n        </HeroSubtitle>\n        <Badge variant=\"primary\" style={{ fontSize: theme.typography.fontSize.base, padding: `${theme.spacing[2]} ${theme.spacing[4]}` }}>\n          {getTotalLevels()} Levels • 260 Signs Total\n        </Badge>\n      </ModernHeader>\n\n      <ModernLevelsGrid>\n        {Object.entries(SIGN_LEVELS).map(([levelNum, levelData], index) => {\n          const level = parseInt(levelNum);\n          const isLocked = isLevelLocked(level);\n          const isCompleted = isLevelCompleted(level);\n          const isActive = level === currentLevel;\n          const progress = getLevelProgress(level);\n\n          return (\n            <ModernLevelCard\n              key={level}\n              index={index}\n              isLocked={isLocked}\n              isCompleted={isCompleted}\n              isActive={isActive}\n              hover={!isLocked}\n              size=\"lg\"\n              onClick={() => !isLocked && onLevelSelect(level)}\n            >\n              <ModernLevelHeader>\n                <ModernLevelNumber\n                  isCompleted={isCompleted}\n                  isLocked={isLocked}\n                >\n                  {level}\n                </ModernLevelNumber>\n                <ModernLevelStatus\n                  isCompleted={isCompleted}\n                  isLocked={isLocked}\n                >\n                  {getStatusIcon(level)}\n                </ModernLevelStatus>\n              </ModernLevelHeader>\n\n              <ModernLevelTheme>{levelData.theme}</ModernLevelTheme>\n              <ModernLevelTitle level={3}>{levelData.name}</ModernLevelTitle>\n              <ModernLevelDescription size=\"base\" muted>\n                {levelData.description}\n              </ModernLevelDescription>\n\n              <ModernLevelProgress>\n                <ProgressHeader>\n                  <ProgressText size=\"sm\" weight=\"medium\">\n                    {userProgress[level]?.completed || 0} / 20 signs\n                  </ProgressText>\n                  <ProgressPercentage variant={isCompleted ? \"success\" : \"primary\"}>\n                    {Math.round(progress)}%\n                  </ProgressPercentage>\n                </ProgressHeader>\n                <ModernProgressBar>\n                  <ModernProgressFill\n                    style={{ width: `${progress}%` }}\n                    isCompleted={isCompleted}\n                  />\n                </ModernProgressBar>\n              </ModernLevelProgress>\n\n              <ModernActionButton\n                variant={isCompleted ? \"success\" : \"primary\"}\n                size=\"lg\"\n                disabled={isLocked}\n                isCompleted={isCompleted}\n                onClick={(e) => {\n                  e.stopPropagation();\n                  if (!isLocked) onLevelSelect(level);\n                }}\n              >\n                {getButtonIcon(level)}\n                {getButtonText(level)}\n              </ModernActionButton>\n            </ModernLevelCard>\n          );\n        })}\n      </ModernLevelsGrid>\n    </ModernContainer>\n  );\n};\n\nexport default LevelSelector;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,IAAIC,SAAS,QAAQ,mBAAmB;AACrD,SAASC,IAAI,EAAEC,IAAI,EAAEC,WAAW,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,cAAc;AAC3F,SAASC,WAAW,EAAEC,cAAc,QAAQ,oBAAoB;AAChE,SAASC,KAAK,QAAQ,iBAAiB;AACvC,SAASC,SAAS,EAAEC,OAAO,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAEC,WAAW,EAAEC,YAAY,EAAEC,KAAK,QAAQ,uBAAuB;;AAE/H;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAGxB,SAAS;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMyB,WAAW,GAAGzB,SAAS;AAC7B;AACA;AACA,kBAAkBW,KAAK,CAACe,OAAO,CAACC,EAAE;AAClC;AACA;AACA;AACA,kBAAkBhB,KAAK,CAACe,OAAO,CAACE,IAAI;AACpC;AACA,CAAC;AAED,MAAMC,iBAAiB,GAAG7B,SAAS;AACnC;AACA;AACA,CAAC;;AAED;AACA,MAAM8B,eAAe,GAAG/B,MAAM,CAACa,SAAS,CAAC;AACzC;AACA;AACA,aAAaD,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC,IAAIpB,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBpB,KAAK,CAACqB,WAAW,CAACC,EAAE;AAC3C,eAAetB,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC,IAAIpB,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACnD;AACA,CAAC;AAACG,EAAA,GAxBIJ,eAAe;AA0BrB,MAAMK,YAAY,GAAGpC,MAAM,CAACc,OAAO,CAAC;AACpC;AACA,aAAaF,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC,MAAMpB,KAAK,CAACoB,OAAO,CAAC,EAAE,CAAC;AACpD,eAAeP,cAAc;AAC7B;AACA,uBAAuBb,KAAK,CAACqB,WAAW,CAACC,EAAE;AAC3C,eAAetB,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC,MAAMpB,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACrD;AACA,CAAC;AAACK,GAAA,GARID,YAAY;AAUlB,MAAME,SAAS,GAAGtC,MAAM,CAACkB,OAAO,CAAC;AACjC,mBAAmBN,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiBF,iBAAiB;AAClC;AACA;AACA,uBAAuBlB,KAAK,CAACqB,WAAW,CAACM,EAAE;AAC3C;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAlBIF,SAAS;AAoBf,MAAMG,YAAY,GAAGzC,MAAM,CAACmB,IAAI,CAAC;AACjC;AACA,mBAAmBP,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACnC,CAAC;AAACU,GAAA,GAHID,YAAY;AAKlB,MAAME,gBAAgB,GAAG3C,MAAM,CAACe,IAAI,CAAC;AACrC,mBAAmBH,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACnC;AACA;AACA,uBAAuBpB,KAAK,CAACqB,WAAW,CAACM,EAAE;AAC3C;AACA;AACA,CAAC;AAACK,GAAA,GAPID,gBAAgB;AAStB,MAAME,eAAe,GAAG7C,MAAM,CAACgB,IAAI,CAAC;AACpC,YAAY8B,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG,aAAa,GAAG,SAAS;AAC/D,aAAaD,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG,GAAG,GAAG,CAAC;AAC9C,eAAetB,cAAc;AAC7B,qBAAqBqB,KAAK,IAAIA,KAAK,CAACE,KAAK,GAAG,GAAG;AAC/C;AACA;AACA;AACA,sBAAsBF,KAAK,IAAI;EAC3B,IAAIA,KAAK,CAACG,WAAW,EAAE,OAAOrC,KAAK,CAACsC,MAAM,CAACC,OAAO,CAAC,GAAG,CAAC;EACvD,IAAIL,KAAK,CAACC,QAAQ,EAAE,OAAOnC,KAAK,CAACsC,MAAM,CAACE,OAAO,CAAC,GAAG,CAAC;EACpD,IAAIN,KAAK,CAACO,QAAQ,EAAE,OAAOzC,KAAK,CAACsC,MAAM,CAACI,OAAO,CAAC,GAAG,CAAC;EACpD,OAAO1C,KAAK,CAACsC,MAAM,CAACE,OAAO,CAAC,GAAG,CAAC;AAClC,CAAC;AACH;AACA;AACA,iBAAiBN,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG,MAAM,GAAG,8BAA8B;AAClF,kBAAkBD,KAAK,IAAI;EACrB,IAAIA,KAAK,CAACC,QAAQ,EAAE,OAAOnC,KAAK,CAACe,OAAO,CAACC,EAAE;EAC3C,IAAIkB,KAAK,CAACG,WAAW,EAAE,OAAOrC,KAAK,CAACe,OAAO,CAAC4B,WAAW;EACvD,OAAO3C,KAAK,CAACe,OAAO,CAACE,IAAI;AAC3B,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBiB,KAAK,IAAI;EACrB,IAAIA,KAAK,CAACG,WAAW,EAAE,OAAOrC,KAAK,CAACsC,MAAM,CAACM,SAAS,CAACL,OAAO;EAC5D,IAAIL,KAAK,CAACC,QAAQ,EAAE,OAAOnC,KAAK,CAACsC,MAAM,CAACM,SAAS,CAACC,SAAS;EAC3D,OAAO7C,KAAK,CAACsC,MAAM,CAACM,SAAS,CAACF,OAAO;AACvC,CAAC;AACL,qBAAqB1C,KAAK,CAAC8C,YAAY,CAAC,KAAK,CAAC,IAAI9C,KAAK,CAAC8C,YAAY,CAAC,KAAK,CAAC;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBZ,KAAK,IAAI;EACrB,IAAIA,KAAK,CAACG,WAAW,EAAE,OAAO,2BAA2BrC,KAAK,CAACsC,MAAM,CAACC,OAAO,CAAC,EAAE,CAAC,uBAAuB;EACxG,IAAIL,KAAK,CAACO,QAAQ,EAAE,OAAO,2BAA2BzC,KAAK,CAACsC,MAAM,CAACI,OAAO,CAAC,EAAE,CAAC,uBAAuB;EACrG,OAAO,aAAa;AACtB,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIR,KAAK,IAAIA,KAAK,CAACO,QAAQ,IAAI;AAC/B,iBAAiB3B,WAAW;AAC5B,4BAA4Bd,KAAK,CAACsC,MAAM,CAACI,OAAO,CAAC,GAAG,CAAC;AACrD,GAAG;AACH;AACA,IAAIR,KAAK,IAAIA,KAAK,CAACG,WAAW,IAAI;AAClC;AACA,oBAAoBrC,KAAK,CAACsC,MAAM,CAACM,SAAS,CAACL,OAAO;AAClD,+BAA+BvC,KAAK,CAACsC,MAAM,CAACC,OAAO,CAAC,GAAG,CAAC;AACxD;AACA,GAAG;AACH,CAAC;AAACQ,GAAA,GAxEId,eAAe;AA0ErB,MAAMe,iBAAiB,GAAG5D,MAAM,CAAC6D,GAAG;AACpC;AACA;AACA;AACA,mBAAmBjD,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACnC,CAAC;AAAC8B,GAAA,GALIF,iBAAiB;AAOvB,MAAMG,iBAAiB,GAAG/D,MAAM,CAAC6D,GAAG;AACpC,gBAAgBf,KAAK,IAAI;EACrB,IAAIA,KAAK,CAACG,WAAW,EAAE,OAAOrC,KAAK,CAACsC,MAAM,CAACM,SAAS,CAACL,OAAO;EAC5D,IAAIL,KAAK,CAACC,QAAQ,EAAE,OAAOnC,KAAK,CAACsC,MAAM,CAACM,SAAS,CAACC,SAAS;EAC3D,OAAO7C,KAAK,CAACsC,MAAM,CAACM,SAAS,CAACF,OAAO;AACvC,CAAC;AACH,WAAW1C,KAAK,CAACsC,MAAM,CAACc,IAAI,CAACC,OAAO;AACpC;AACA;AACA,mBAAmBrD,KAAK,CAAC8C,YAAY,CAACQ,IAAI;AAC1C;AACA;AACA;AACA,iBAAiBtD,KAAK,CAACuD,UAAU,CAACC,UAAU,CAACC,IAAI;AACjD,eAAezD,KAAK,CAACuD,UAAU,CAACG,QAAQ,CAACC,EAAE;AAC3C,gBAAgB3D,KAAK,CAACe,OAAO,CAACO,EAAE;AAChC;AACA;AACA,IAAIY,KAAK,IAAIA,KAAK,CAACG,WAAW,IAAI;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmBnB,iBAAiB;AACpC;AACA,GAAG;AACH;AACA,uBAAuBlB,KAAK,CAACqB,WAAW,CAACM,EAAE;AAC3C;AACA;AACA,iBAAiB3B,KAAK,CAACuD,UAAU,CAACG,QAAQ,CAAC1C,EAAE;AAC7C;AACA,CAAC;AAAC4C,GAAA,GAlCIT,iBAAiB;AAoCvB,MAAMU,iBAAiB,GAAGzE,MAAM,CAAC6D,GAAG;AACpC;AACA;AACA,SAASjD,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACzB,WAAWc,KAAK,IAAI;EAChB,IAAIA,KAAK,CAACG,WAAW,EAAE,OAAOrC,KAAK,CAACsC,MAAM,CAACC,OAAO,CAAC,GAAG,CAAC;EACvD,IAAIL,KAAK,CAACC,QAAQ,EAAE,OAAOnC,KAAK,CAACsC,MAAM,CAACE,OAAO,CAAC,GAAG,CAAC;EACpD,OAAOxC,KAAK,CAACsC,MAAM,CAACI,OAAO,CAAC,GAAG,CAAC;AAClC,CAAC;AACH,iBAAiB1C,KAAK,CAACuD,UAAU,CAACC,UAAU,CAACM,MAAM;AACnD,CAAC;AAACC,GAAA,GAVIF,iBAAiB;AAYvB,MAAMG,gBAAgB,GAAG5E,MAAM,CAACkB,OAAO,CAAC;AACxC,mBAAmBN,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACnC,WAAWpB,KAAK,CAACsC,MAAM,CAACc,IAAI,CAACV,OAAO;AACpC,CAAC;AAACuB,GAAA,GAHID,gBAAgB;AAKtB,MAAME,gBAAgB,GAAG9E,MAAM,CAAC6D,GAAG;AACnC,eAAejD,KAAK,CAACuD,UAAU,CAACG,QAAQ,CAACC,EAAE;AAC3C,mBAAmB3D,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACnC;AACA,aAAapB,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AAC7B,gBAAgBpB,KAAK,CAACsC,MAAM,CAACM,SAAS,CAACuB,OAAO;AAC9C,mBAAmBnE,KAAK,CAAC8C,YAAY,CAAC9B,EAAE;AACxC,sBAAsBhB,KAAK,CAACsC,MAAM,CAACE,OAAO,CAAC,GAAG,CAAC;AAC/C;AACA,uBAAuBxC,KAAK,CAACqB,WAAW,CAACM,EAAE;AAC3C,iBAAiB3B,KAAK,CAACuD,UAAU,CAACG,QAAQ,CAAC1C,EAAE;AAC7C;AACA,CAAC;AAACoD,GAAA,GAZIF,gBAAgB;AActB,MAAMG,sBAAsB,GAAGjF,MAAM,CAACmB,IAAI,CAAC;AAC3C,mBAAmBP,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACnC;AACA,CAAC;AAACkD,IAAA,GAHID,sBAAsB;AAK5B,MAAME,mBAAmB,GAAGnF,MAAM,CAAC6D,GAAG;AACtC,mBAAmBjD,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACnC,CAAC;AAACoD,IAAA,GAFID,mBAAmB;AAIzB,MAAME,cAAc,GAAGrF,MAAM,CAAC6D,GAAG;AACjC;AACA;AACA;AACA,mBAAmBjD,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACnC,CAAC;AAACsD,IAAA,GALID,cAAc;AAOpB,MAAME,YAAY,GAAGvF,MAAM,CAACmB,IAAI,CAAC;AACjC,iBAAiBP,KAAK,CAACuD,UAAU,CAACC,UAAU,CAACM,MAAM;AACnD,CAAC;AAACc,IAAA,GAFID,YAAY;AAIlB,MAAME,kBAAkB,GAAGzF,MAAM,CAACsB,KAAK,CAAC;AACxC,iBAAiBV,KAAK,CAACuD,UAAU,CAACC,UAAU,CAACC,IAAI;AACjD,CAAC;AAACqB,IAAA,GAFID,kBAAkB;AAIxB,MAAME,iBAAiB,GAAG3F,MAAM,CAACoB,WAAW,CAAC;AAC7C;AACA,gBAAgBR,KAAK,CAACsC,MAAM,CAACE,OAAO,CAAC,GAAG,CAAC;AACzC,mBAAmBxC,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC4D,IAAA,GAhBID,iBAAiB;AAkBvB,MAAME,kBAAkB,GAAG7F,MAAM,CAACqB,YAAY,CAAC;AAC/C,gBAAgByB,KAAK,IAAI;EACrB,IAAIA,KAAK,CAACG,WAAW,EAAE,OAAOrC,KAAK,CAACsC,MAAM,CAACM,SAAS,CAACL,OAAO;EAC5D,OAAOvC,KAAK,CAACsC,MAAM,CAACM,SAAS,CAACF,OAAO;AACvC,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACwC,IAAA,GAjBID,kBAAkB;AAmBxB,MAAME,kBAAkB,GAAG/F,MAAM,CAACiB,MAAM,CAAC;AACzC;AACA,YAAYL,KAAK,CAACoF,UAAU,CAACC,MAAM,CAACC,MAAM,CAACtE,EAAE;AAC7C,eAAehB,KAAK,CAACuD,UAAU,CAACG,QAAQ,CAAC6B,IAAI;AAC7C,iBAAiBvF,KAAK,CAACuD,UAAU,CAACC,UAAU,CAACgC,QAAQ;AACrD;AACA,IAAItD,KAAK,IAAI;EACT,IAAIA,KAAK,CAACuD,QAAQ,EAAE;IAClB,OAAO;AACb,sBAAsBzF,KAAK,CAACsC,MAAM,CAACE,OAAO,CAAC,GAAG,CAAC;AAC/C,iBAAiBxC,KAAK,CAACsC,MAAM,CAACE,OAAO,CAAC,GAAG,CAAC;AAC1C;AACA;AACA;AACA;AACA,wBAAwBxC,KAAK,CAACe,OAAO,CAACwE,IAAI;AAC1C;AACA,OAAO;EACH;EACA,IAAIrD,KAAK,CAACG,WAAW,EAAE;IACrB,OAAO;AACb;AACA,OAAO;EACH;EACA,OAAO;AACX;AACA,KAAK;AACH,CAAC;AACH;AACA,uBAAuBrC,KAAK,CAACqB,WAAW,CAACM,EAAE;AAC3C,cAAc3B,KAAK,CAACoF,UAAU,CAACC,MAAM,CAACC,MAAM,CAAC3B,EAAE;AAC/C,iBAAiB3D,KAAK,CAACuD,UAAU,CAACG,QAAQ,CAAC1C,EAAE;AAC7C;AACA,CAAC;AAAC0E,IAAA,GAjCIP,kBAAkB;AAmCxB,MAAMQ,OAAO,GAAGtG,SAAS;AACzB;AACA;AACA,CAAC;AAED,MAAMuG,aAAa,GAAGA,CAAC;EACrBC,YAAY;EACZC,YAAY,GAAG,CAAC,CAAC;EACjBC,aAAa;EACbC;AACF,CAAC,KAAK;EACJ,MAAMC,WAAW,GAAGlG,cAAc,CAAC,CAAC;EAEpC,MAAMmG,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,QAAQ,GAAGN,YAAY,CAACK,KAAK,CAAC,IAAI;MAAEE,SAAS,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAG,CAAC;IACnE,OAAQF,QAAQ,CAACC,SAAS,GAAGD,QAAQ,CAACE,KAAK,GAAI,GAAG;EACpD,CAAC;EAED,MAAMC,aAAa,GAAIJ,KAAK,IAAK;IAC/B,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,KAAK;IAC7B,MAAMK,iBAAiB,GAAGV,YAAY,CAACK,KAAK,GAAG,CAAC,CAAC,IAAI;MAAEE,SAAS,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAG,CAAC;IAChF,OAAOE,iBAAiB,CAACH,SAAS,GAAGG,iBAAiB,CAACF,KAAK;EAC9D,CAAC;EAED,MAAMG,gBAAgB,GAAIN,KAAK,IAAK;IAClC,MAAMC,QAAQ,GAAGN,YAAY,CAACK,KAAK,CAAC,IAAI;MAAEE,SAAS,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAG,CAAC;IACnE,OAAOF,QAAQ,CAACC,SAAS,IAAID,QAAQ,CAACE,KAAK;EAC7C,CAAC;EAED,MAAMI,aAAa,GAAIP,KAAK,IAAK;IAC/B,IAAIM,gBAAgB,CAACN,KAAK,CAAC,EAAE,oBAAOvF,OAAA,CAACpB,WAAW;MAACmH,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7D,IAAIR,aAAa,CAACJ,KAAK,CAAC,EAAE,oBAAOvF,OAAA,CAACrB,IAAI;MAACoH,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnD,IAAIZ,KAAK,KAAKN,YAAY,EAAE,oBAAOjF,OAAA,CAACjB,MAAM;MAACgH,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvD,oBAAOnG,OAAA,CAACnB,IAAI;MAACkH,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3B,CAAC;EAED,MAAMC,aAAa,GAAIb,KAAK,IAAK;IAC/B,IAAII,aAAa,CAACJ,KAAK,CAAC,EAAE,OAAO,QAAQ;IACzC,IAAIM,gBAAgB,CAACN,KAAK,CAAC,EAAE,OAAO,cAAc;IAClD,IAAIA,KAAK,KAAKN,YAAY,EAAE,OAAO,UAAU;IAC7C,OAAO,aAAa;EACtB,CAAC;EAED,MAAMoB,aAAa,GAAId,KAAK,IAAK;IAC/B,IAAII,aAAa,CAACJ,KAAK,CAAC,EAAE,oBAAOvF,OAAA,CAACrB,IAAI;MAACoH,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnD,IAAIN,gBAAgB,CAACN,KAAK,CAAC,EAAE,oBAAOvF,OAAA,CAAClB,MAAM;MAACiH,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxD,oBAAOnG,OAAA,CAACtB,IAAI;MAACqH,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3B,CAAC;EAED,oBACEnG,OAAA,CAACO,eAAe;IAAC6E,SAAS,EAAEA,SAAU;IAAAkB,QAAA,gBACpCtG,OAAA,CAACY,YAAY;MAAA0F,QAAA,gBACXtG,OAAA,CAACc,SAAS;QAACyE,KAAK,EAAE,CAAE;QAACgB,QAAQ,EAAC,SAAS;QAAAD,QAAA,EAAC;MAExC;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eACZnG,OAAA,CAACiB,YAAY;QAAC8E,IAAI,EAAC,IAAI;QAACS,KAAK;QAAAF,QAAA,EAAC;MAG9B;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eACfnG,OAAA,CAACF,KAAK;QAAC2G,OAAO,EAAC,SAAS;QAACC,KAAK,EAAE;UAAE5D,QAAQ,EAAE1D,KAAK,CAACuD,UAAU,CAACG,QAAQ,CAAC6B,IAAI;UAAEgC,OAAO,EAAE,GAAGvH,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC,IAAIpB,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;QAAG,CAAE;QAAA8F,QAAA,GAC9HnH,cAAc,CAAC,CAAC,EAAC,gCACpB;MAAA;QAAA6G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAEfnG,OAAA,CAACmB,gBAAgB;MAAAmF,QAAA,EACdM,MAAM,CAACC,OAAO,CAAC3H,WAAW,CAAC,CAAC4H,GAAG,CAAC,CAAC,CAACC,QAAQ,EAAEC,SAAS,CAAC,EAAExF,KAAK,KAAK;QAAA,IAAAyF,mBAAA;QACjE,MAAM1B,KAAK,GAAG2B,QAAQ,CAACH,QAAQ,CAAC;QAChC,MAAMxF,QAAQ,GAAGoE,aAAa,CAACJ,KAAK,CAAC;QACrC,MAAM9D,WAAW,GAAGoE,gBAAgB,CAACN,KAAK,CAAC;QAC3C,MAAM1D,QAAQ,GAAG0D,KAAK,KAAKN,YAAY;QACvC,MAAMO,QAAQ,GAAGF,gBAAgB,CAACC,KAAK,CAAC;QAExC,oBACEvF,OAAA,CAACqB,eAAe;UAEdG,KAAK,EAAEA,KAAM;UACbD,QAAQ,EAAEA,QAAS;UACnBE,WAAW,EAAEA,WAAY;UACzBI,QAAQ,EAAEA,QAAS;UACnBsF,KAAK,EAAE,CAAC5F,QAAS;UACjBwE,IAAI,EAAC,IAAI;UACTqB,OAAO,EAAEA,CAAA,KAAM,CAAC7F,QAAQ,IAAI4D,aAAa,CAACI,KAAK,CAAE;UAAAe,QAAA,gBAEjDtG,OAAA,CAACoC,iBAAiB;YAAAkE,QAAA,gBAChBtG,OAAA,CAACuC,iBAAiB;cAChBd,WAAW,EAAEA,WAAY;cACzBF,QAAQ,EAAEA,QAAS;cAAA+E,QAAA,EAElBf;YAAK;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACW,CAAC,eACpBnG,OAAA,CAACiD,iBAAiB;cAChBxB,WAAW,EAAEA,WAAY;cACzBF,QAAQ,EAAEA,QAAS;cAAA+E,QAAA,EAElBR,aAAa,CAACP,KAAK;YAAC;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEpBnG,OAAA,CAACsD,gBAAgB;YAAAgD,QAAA,EAAEU,SAAS,CAAC5H;UAAK;YAAA4G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAmB,CAAC,eACtDnG,OAAA,CAACoD,gBAAgB;YAACmC,KAAK,EAAE,CAAE;YAAAe,QAAA,EAAEU,SAAS,CAACK;UAAI;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAmB,CAAC,eAC/DnG,OAAA,CAACyD,sBAAsB;YAACsC,IAAI,EAAC,MAAM;YAACS,KAAK;YAAAF,QAAA,EACtCU,SAAS,CAACM;UAAW;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEzBnG,OAAA,CAAC2D,mBAAmB;YAAA2C,QAAA,gBAClBtG,OAAA,CAAC6D,cAAc;cAAAyC,QAAA,gBACbtG,OAAA,CAAC+D,YAAY;gBAACgC,IAAI,EAAC,IAAI;gBAACwB,MAAM,EAAC,QAAQ;gBAAAjB,QAAA,GACpC,EAAAW,mBAAA,GAAA/B,YAAY,CAACK,KAAK,CAAC,cAAA0B,mBAAA,uBAAnBA,mBAAA,CAAqBxB,SAAS,KAAI,CAAC,EAAC,aACvC;cAAA;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAc,CAAC,eACfnG,OAAA,CAACiE,kBAAkB;gBAACwC,OAAO,EAAEhF,WAAW,GAAG,SAAS,GAAG,SAAU;gBAAA6E,QAAA,GAC9DkB,IAAI,CAACC,KAAK,CAACjC,QAAQ,CAAC,EAAC,GACxB;cAAA;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAoB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACjBnG,OAAA,CAACmE,iBAAiB;cAAAmC,QAAA,eAChBtG,OAAA,CAACqE,kBAAkB;gBACjBqC,KAAK,EAAE;kBAAEgB,KAAK,EAAE,GAAGlC,QAAQ;gBAAI,CAAE;gBACjC/D,WAAW,EAAEA;cAAY;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAEtBnG,OAAA,CAACuE,kBAAkB;YACjBkC,OAAO,EAAEhF,WAAW,GAAG,SAAS,GAAG,SAAU;YAC7CsE,IAAI,EAAC,IAAI;YACTlB,QAAQ,EAAEtD,QAAS;YACnBE,WAAW,EAAEA,WAAY;YACzB2F,OAAO,EAAGO,CAAC,IAAK;cACdA,CAAC,CAACC,eAAe,CAAC,CAAC;cACnB,IAAI,CAACrG,QAAQ,EAAE4D,aAAa,CAACI,KAAK,CAAC;YACrC,CAAE;YAAAe,QAAA,GAEDD,aAAa,CAACd,KAAK,CAAC,EACpBa,aAAa,CAACb,KAAK,CAAC;UAAA;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GA3DhBZ,KAAK;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4DK,CAAC;MAEtB,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEtB,CAAC;AAAC0B,IAAA,GAvII7C,aAAa;AAyInB,eAAeA,aAAa;AAAC,IAAArE,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAe,GAAA,EAAAG,GAAA,EAAAU,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAQ,IAAA,EAAA+C,IAAA;AAAAC,YAAA,CAAAnH,EAAA;AAAAmH,YAAA,CAAAjH,GAAA;AAAAiH,YAAA,CAAA9G,GAAA;AAAA8G,YAAA,CAAA5G,GAAA;AAAA4G,YAAA,CAAA1G,GAAA;AAAA0G,YAAA,CAAA3F,GAAA;AAAA2F,YAAA,CAAAxF,GAAA;AAAAwF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAA3E,GAAA;AAAA2E,YAAA,CAAAzE,GAAA;AAAAyE,YAAA,CAAAtE,GAAA;AAAAsE,YAAA,CAAApE,IAAA;AAAAoE,YAAA,CAAAlE,IAAA;AAAAkE,YAAA,CAAAhE,IAAA;AAAAgE,YAAA,CAAA9D,IAAA;AAAA8D,YAAA,CAAA5D,IAAA;AAAA4D,YAAA,CAAA1D,IAAA;AAAA0D,YAAA,CAAAxD,IAAA;AAAAwD,YAAA,CAAAhD,IAAA;AAAAgD,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}