/*! For license information please see main.78e46417.js.LICENSE.txt */
(()=>{var e={4:(e,n,t)=>{"use strict";var r=t(853),i=t(43),a=t(950);function o(e){var n="https://react.dev/errors/"+e;if(1<arguments.length){n+="?args[]="+encodeURIComponent(arguments[1]);for(var t=2;t<arguments.length;t++)n+="&args[]="+encodeURIComponent(arguments[t])}return"Minified React error #"+e+"; visit "+n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function l(e){var n=e,t=e;if(e.alternate)for(;n.return;)n=n.return;else{e=n;do{0!==(4098&(n=e).flags)&&(t=n.return),e=n.return}while(e)}return 3===n.tag?t:null}function c(e){if(13===e.tag){var n=e.memoizedState;if(null===n&&(null!==(e=e.alternate)&&(n=e.memoizedState)),null!==n)return n.dehydrated}return null}function d(e){if(l(e)!==e)throw Error(o(188))}function u(e){var n=e.tag;if(5===n||26===n||27===n||6===n)return e;for(e=e.child;null!==e;){if(null!==(n=u(e)))return n;e=e.sibling}return null}var f=Object.assign,p=Symbol.for("react.element"),g=Symbol.for("react.transitional.element"),m=Symbol.for("react.portal"),h=Symbol.for("react.fragment"),b=Symbol.for("react.strict_mode"),y=Symbol.for("react.profiler"),v=Symbol.for("react.provider"),w=Symbol.for("react.consumer"),k=Symbol.for("react.context"),x=Symbol.for("react.forward_ref"),S=Symbol.for("react.suspense"),E=Symbol.for("react.suspense_list"),C=Symbol.for("react.memo"),z=Symbol.for("react.lazy");Symbol.for("react.scope");var j=Symbol.for("react.activity");Symbol.for("react.legacy_hidden"),Symbol.for("react.tracing_marker");var P=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.view_transition");var R=Symbol.iterator;function _(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=R&&e[R]||e["@@iterator"])?e:null}var O=Symbol.for("react.client.reference");function T(e){if(null==e)return null;if("function"===typeof e)return e.$$typeof===O?null:e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case h:return"Fragment";case y:return"Profiler";case b:return"StrictMode";case S:return"Suspense";case E:return"SuspenseList";case j:return"Activity"}if("object"===typeof e)switch(e.$$typeof){case m:return"Portal";case k:return(e.displayName||"Context")+".Provider";case w:return(e._context.displayName||"Context")+".Consumer";case x:var n=e.render;return(e=e.displayName)||(e=""!==(e=n.displayName||n.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case C:return null!==(n=e.displayName||null)?n:T(e.type)||"Memo";case z:n=e._payload,e=e._init;try{return T(e(n))}catch(t){}}return null}var A=Array.isArray,N=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,L=a.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,M={pending:!1,data:null,method:null,action:null},I=[],D=-1;function F(e){return{current:e}}function H(e){0>D||(e.current=I[D],I[D]=null,D--)}function U(e,n){D++,I[D]=e.current,e.current=n}var W=F(null),B=F(null),V=F(null),q=F(null);function Y(e,n){switch(U(V,n),U(B,e),U(W,null),n.nodeType){case 9:case 11:e=(e=n.documentElement)&&(e=e.namespaceURI)?iu(e):0;break;default:if(e=n.tagName,n=n.namespaceURI)e=au(n=iu(n),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}H(W),U(W,e)}function G(){H(W),H(B),H(V)}function K(e){null!==e.memoizedState&&U(q,e);var n=W.current,t=au(n,e.type);n!==t&&(U(B,e),U(W,t))}function Q(e){B.current===e&&(H(W),H(B)),q.current===e&&(H(q),Ku._currentValue=M)}var $=Object.prototype.hasOwnProperty,Z=r.unstable_scheduleCallback,X=r.unstable_cancelCallback,J=r.unstable_shouldYield,ee=r.unstable_requestPaint,ne=r.unstable_now,te=r.unstable_getCurrentPriorityLevel,re=r.unstable_ImmediatePriority,ie=r.unstable_UserBlockingPriority,ae=r.unstable_NormalPriority,oe=r.unstable_LowPriority,se=r.unstable_IdlePriority,le=r.log,ce=r.unstable_setDisableYieldValue,de=null,ue=null;function fe(e){if("function"===typeof le&&ce(e),ue&&"function"===typeof ue.setStrictMode)try{ue.setStrictMode(de,e)}catch(n){}}var pe=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(ge(e)/me|0)|0},ge=Math.log,me=Math.LN2;var he=256,be=4194304;function ye(e){var n=42&e;if(0!==n)return n;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function ve(e,n,t){var r=e.pendingLanes;if(0===r)return 0;var i=0,a=e.suspendedLanes,o=e.pingedLanes;e=e.warmLanes;var s=134217727&r;return 0!==s?0!==(r=s&~a)?i=ye(r):0!==(o&=s)?i=ye(o):t||0!==(t=s&~e)&&(i=ye(t)):0!==(s=r&~a)?i=ye(s):0!==o?i=ye(o):t||0!==(t=r&~e)&&(i=ye(t)),0===i?0:0!==n&&n!==i&&0===(n&a)&&((a=i&-i)>=(t=n&-n)||32===a&&0!==(4194048&t))?n:i}function we(e,n){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&n)}function ke(e,n){switch(e){case 1:case 2:case 4:case 8:case 64:return n+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return n+5e3;default:return-1}}function xe(){var e=he;return 0===(4194048&(he<<=1))&&(he=256),e}function Se(){var e=be;return 0===(62914560&(be<<=1))&&(be=4194304),e}function Ee(e){for(var n=[],t=0;31>t;t++)n.push(e);return n}function Ce(e,n){e.pendingLanes|=n,268435456!==n&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function ze(e,n,t){e.pendingLanes|=n,e.suspendedLanes&=~n;var r=31-pe(n);e.entangledLanes|=n,e.entanglements[r]=1073741824|e.entanglements[r]|4194090&t}function je(e,n){var t=e.entangledLanes|=n;for(e=e.entanglements;t;){var r=31-pe(t),i=1<<r;i&n|e[r]&n&&(e[r]|=n),t&=~i}}function Pe(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Re(e){return 2<(e&=-e)?8<e?0!==(134217727&e)?32:268435456:8:2}function _e(){var e=L.p;return 0!==e?e:void 0===(e=window.event)?32:df(e.type)}var Oe=Math.random().toString(36).slice(2),Te="__reactFiber$"+Oe,Ae="__reactProps$"+Oe,Ne="__reactContainer$"+Oe,Le="__reactEvents$"+Oe,Me="__reactListeners$"+Oe,Ie="__reactHandles$"+Oe,De="__reactResources$"+Oe,Fe="__reactMarker$"+Oe;function He(e){delete e[Te],delete e[Ae],delete e[Le],delete e[Me],delete e[Ie]}function Ue(e){var n=e[Te];if(n)return n;for(var t=e.parentNode;t;){if(n=t[Ne]||t[Te]){if(t=n.alternate,null!==n.child||null!==t&&null!==t.child)for(e=vu(e);null!==e;){if(t=e[Te])return t;e=vu(e)}return n}t=(e=t).parentNode}return null}function We(e){if(e=e[Te]||e[Ne]){var n=e.tag;if(5===n||6===n||13===n||26===n||27===n||3===n)return e}return null}function Be(e){var n=e.tag;if(5===n||26===n||27===n||6===n)return e.stateNode;throw Error(o(33))}function Ve(e){var n=e[De];return n||(n=e[De]={hoistableStyles:new Map,hoistableScripts:new Map}),n}function qe(e){e[Fe]=!0}var Ye=new Set,Ge={};function Ke(e,n){Qe(e,n),Qe(e+"Capture",n)}function Qe(e,n){for(Ge[e]=n,e=0;e<n.length;e++)Ye.add(n[e])}var $e,Ze,Xe=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Je={},en={};function nn(e,n,t){if(i=n,$.call(en,i)||!$.call(Je,i)&&(Xe.test(i)?en[i]=!0:(Je[i]=!0,0)))if(null===t)e.removeAttribute(n);else{switch(typeof t){case"undefined":case"function":case"symbol":return void e.removeAttribute(n);case"boolean":var r=n.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(n)}e.setAttribute(n,""+t)}var i}function tn(e,n,t){if(null===t)e.removeAttribute(n);else{switch(typeof t){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttribute(n,""+t)}}function rn(e,n,t,r){if(null===r)e.removeAttribute(t);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttributeNS(n,t,""+r)}}function an(e){if(void 0===$e)try{throw Error()}catch(t){var n=t.stack.trim().match(/\n( *(at )?)/);$e=n&&n[1]||"",Ze=-1<t.stack.indexOf("\n    at")?" (<anonymous>)":-1<t.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+$e+e+Ze}var on=!1;function sn(e,n){if(!e||on)return"";on=!0;var t=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(n){var t=function(){throw Error()};if(Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(i){var r=i}Reflect.construct(e,[],t)}else{try{t.call()}catch(a){r=a}e.call(t.prototype)}}else{try{throw Error()}catch(o){r=o}(t=e())&&"function"===typeof t.catch&&t.catch(function(){})}}catch(s){if(s&&r&&"string"===typeof s.stack)return[s.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var a=r.DetermineComponentFrameRoot(),o=a[0],s=a[1];if(o&&s){var l=o.split("\n"),c=s.split("\n");for(i=r=0;r<l.length&&!l[r].includes("DetermineComponentFrameRoot");)r++;for(;i<c.length&&!c[i].includes("DetermineComponentFrameRoot");)i++;if(r===l.length||i===c.length)for(r=l.length-1,i=c.length-1;1<=r&&0<=i&&l[r]!==c[i];)i--;for(;1<=r&&0<=i;r--,i--)if(l[r]!==c[i]){if(1!==r||1!==i)do{if(r--,0>--i||l[r]!==c[i]){var d="\n"+l[r].replace(" at new "," at ");return e.displayName&&d.includes("<anonymous>")&&(d=d.replace("<anonymous>",e.displayName)),d}}while(1<=r&&0<=i);break}}}finally{on=!1,Error.prepareStackTrace=t}return(t=e?e.displayName||e.name:"")?an(t):""}function ln(e){switch(e.tag){case 26:case 27:case 5:return an(e.type);case 16:return an("Lazy");case 13:return an("Suspense");case 19:return an("SuspenseList");case 0:case 15:return sn(e.type,!1);case 11:return sn(e.type.render,!1);case 1:return sn(e.type,!0);case 31:return an("Activity");default:return""}}function cn(e){try{var n="";do{n+=ln(e),e=e.return}while(e);return n}catch(t){return"\nError generating stack: "+t.message+"\n"+t.stack}}function dn(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function un(e){var n=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===n||"radio"===n)}function fn(e){e._valueTracker||(e._valueTracker=function(e){var n=un(e)?"checked":"value",t=Object.getOwnPropertyDescriptor(e.constructor.prototype,n),r=""+e[n];if(!e.hasOwnProperty(n)&&"undefined"!==typeof t&&"function"===typeof t.get&&"function"===typeof t.set){var i=t.get,a=t.set;return Object.defineProperty(e,n,{configurable:!0,get:function(){return i.call(this)},set:function(e){r=""+e,a.call(this,e)}}),Object.defineProperty(e,n,{enumerable:t.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[n]}}}}(e))}function pn(e){if(!e)return!1;var n=e._valueTracker;if(!n)return!0;var t=n.getValue(),r="";return e&&(r=un(e)?e.checked?"true":"false":e.value),(e=r)!==t&&(n.setValue(e),!0)}function gn(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(n){return e.body}}var mn=/[\n"\\]/g;function hn(e){return e.replace(mn,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function bn(e,n,t,r,i,a,o,s){e.name="",null!=o&&"function"!==typeof o&&"symbol"!==typeof o&&"boolean"!==typeof o?e.type=o:e.removeAttribute("type"),null!=n?"number"===o?(0===n&&""===e.value||e.value!=n)&&(e.value=""+dn(n)):e.value!==""+dn(n)&&(e.value=""+dn(n)):"submit"!==o&&"reset"!==o||e.removeAttribute("value"),null!=n?vn(e,o,dn(n)):null!=t?vn(e,o,dn(t)):null!=r&&e.removeAttribute("value"),null==i&&null!=a&&(e.defaultChecked=!!a),null!=i&&(e.checked=i&&"function"!==typeof i&&"symbol"!==typeof i),null!=s&&"function"!==typeof s&&"symbol"!==typeof s&&"boolean"!==typeof s?e.name=""+dn(s):e.removeAttribute("name")}function yn(e,n,t,r,i,a,o,s){if(null!=a&&"function"!==typeof a&&"symbol"!==typeof a&&"boolean"!==typeof a&&(e.type=a),null!=n||null!=t){if(!("submit"!==a&&"reset"!==a||void 0!==n&&null!==n))return;t=null!=t?""+dn(t):"",n=null!=n?""+dn(n):t,s||n===e.value||(e.value=n),e.defaultValue=n}r="function"!==typeof(r=null!=r?r:i)&&"symbol"!==typeof r&&!!r,e.checked=s?e.checked:!!r,e.defaultChecked=!!r,null!=o&&"function"!==typeof o&&"symbol"!==typeof o&&"boolean"!==typeof o&&(e.name=o)}function vn(e,n,t){"number"===n&&gn(e.ownerDocument)===e||e.defaultValue===""+t||(e.defaultValue=""+t)}function wn(e,n,t,r){if(e=e.options,n){n={};for(var i=0;i<t.length;i++)n["$"+t[i]]=!0;for(t=0;t<e.length;t++)i=n.hasOwnProperty("$"+e[t].value),e[t].selected!==i&&(e[t].selected=i),i&&r&&(e[t].defaultSelected=!0)}else{for(t=""+dn(t),n=null,i=0;i<e.length;i++){if(e[i].value===t)return e[i].selected=!0,void(r&&(e[i].defaultSelected=!0));null!==n||e[i].disabled||(n=e[i])}null!==n&&(n.selected=!0)}}function kn(e,n,t){null==n||((n=""+dn(n))!==e.value&&(e.value=n),null!=t)?e.defaultValue=null!=t?""+dn(t):"":e.defaultValue!==n&&(e.defaultValue=n)}function xn(e,n,t,r){if(null==n){if(null!=r){if(null!=t)throw Error(o(92));if(A(r)){if(1<r.length)throw Error(o(93));r=r[0]}t=r}null==t&&(t=""),n=t}t=dn(n),e.defaultValue=t,(r=e.textContent)===t&&""!==r&&null!==r&&(e.value=r)}function Sn(e,n){if(n){var t=e.firstChild;if(t&&t===e.lastChild&&3===t.nodeType)return void(t.nodeValue=n)}e.textContent=n}var En=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Cn(e,n,t){var r=0===n.indexOf("--");null==t||"boolean"===typeof t||""===t?r?e.setProperty(n,""):"float"===n?e.cssFloat="":e[n]="":r?e.setProperty(n,t):"number"!==typeof t||0===t||En.has(n)?"float"===n?e.cssFloat=t:e[n]=(""+t).trim():e[n]=t+"px"}function zn(e,n,t){if(null!=n&&"object"!==typeof n)throw Error(o(62));if(e=e.style,null!=t){for(var r in t)!t.hasOwnProperty(r)||null!=n&&n.hasOwnProperty(r)||(0===r.indexOf("--")?e.setProperty(r,""):"float"===r?e.cssFloat="":e[r]="");for(var i in n)r=n[i],n.hasOwnProperty(i)&&t[i]!==r&&Cn(e,i,r)}else for(var a in n)n.hasOwnProperty(a)&&Cn(e,a,n[a])}function jn(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Pn=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Rn=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function _n(e){return Rn.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var On=null;function Tn(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var An=null,Nn=null;function Ln(e){var n=We(e);if(n&&(e=n.stateNode)){var t=e[Ae]||null;e:switch(e=n.stateNode,n.type){case"input":if(bn(e,t.value,t.defaultValue,t.defaultValue,t.checked,t.defaultChecked,t.type,t.name),n=t.name,"radio"===t.type&&null!=n){for(t=e;t.parentNode;)t=t.parentNode;for(t=t.querySelectorAll('input[name="'+hn(""+n)+'"][type="radio"]'),n=0;n<t.length;n++){var r=t[n];if(r!==e&&r.form===e.form){var i=r[Ae]||null;if(!i)throw Error(o(90));bn(r,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(n=0;n<t.length;n++)(r=t[n]).form===e.form&&pn(r)}break e;case"textarea":kn(e,t.value,t.defaultValue);break e;case"select":null!=(n=t.value)&&wn(e,!!t.multiple,n,!1)}}}var Mn=!1;function In(e,n,t){if(Mn)return e(n,t);Mn=!0;try{return e(n)}finally{if(Mn=!1,(null!==An||null!==Nn)&&(Uc(),An&&(n=An,e=Nn,Nn=An=null,Ln(n),e)))for(n=0;n<e.length;n++)Ln(e[n])}}function Dn(e,n){var t=e.stateNode;if(null===t)return null;var r=t[Ae]||null;if(null===r)return null;t=r[n];e:switch(n){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(t&&"function"!==typeof t)throw Error(o(231,n,typeof t));return t}var Fn=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),Hn=!1;if(Fn)try{var Un={};Object.defineProperty(Un,"passive",{get:function(){Hn=!0}}),window.addEventListener("test",Un,Un),window.removeEventListener("test",Un,Un)}catch(Nf){Hn=!1}var Wn=null,Bn=null,Vn=null;function qn(){if(Vn)return Vn;var e,n,t=Bn,r=t.length,i="value"in Wn?Wn.value:Wn.textContent,a=i.length;for(e=0;e<r&&t[e]===i[e];e++);var o=r-e;for(n=1;n<=o&&t[r-n]===i[a-n];n++);return Vn=i.slice(e,1<n?1-n:void 0)}function Yn(e){var n=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===n&&(e=13):e=n,10===e&&(e=13),32<=e||13===e?e:0}function Gn(){return!0}function Kn(){return!1}function Qn(e){function n(n,t,r,i,a){for(var o in this._reactName=n,this._targetInst=r,this.type=t,this.nativeEvent=i,this.target=a,this.currentTarget=null,e)e.hasOwnProperty(o)&&(n=e[o],this[o]=n?n(i):i[o]);return this.isDefaultPrevented=(null!=i.defaultPrevented?i.defaultPrevented:!1===i.returnValue)?Gn:Kn,this.isPropagationStopped=Kn,this}return f(n.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Gn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Gn)},persist:function(){},isPersistent:Gn}),n}var $n,Zn,Xn,Jn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},et=Qn(Jn),nt=f({},Jn,{view:0,detail:0}),tt=Qn(nt),rt=f({},nt,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:gt,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Xn&&(Xn&&"mousemove"===e.type?($n=e.screenX-Xn.screenX,Zn=e.screenY-Xn.screenY):Zn=$n=0,Xn=e),$n)},movementY:function(e){return"movementY"in e?e.movementY:Zn}}),it=Qn(rt),at=Qn(f({},rt,{dataTransfer:0})),ot=Qn(f({},nt,{relatedTarget:0})),st=Qn(f({},Jn,{animationName:0,elapsedTime:0,pseudoElement:0})),lt=Qn(f({},Jn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),ct=Qn(f({},Jn,{data:0})),dt={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},ut={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ft={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function pt(e){var n=this.nativeEvent;return n.getModifierState?n.getModifierState(e):!!(e=ft[e])&&!!n[e]}function gt(){return pt}var mt=Qn(f({},nt,{key:function(e){if(e.key){var n=dt[e.key]||e.key;if("Unidentified"!==n)return n}return"keypress"===e.type?13===(e=Yn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?ut[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:gt,charCode:function(e){return"keypress"===e.type?Yn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Yn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),ht=Qn(f({},rt,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),bt=Qn(f({},nt,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:gt})),yt=Qn(f({},Jn,{propertyName:0,elapsedTime:0,pseudoElement:0})),vt=Qn(f({},rt,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),wt=Qn(f({},Jn,{newState:0,oldState:0})),kt=[9,13,27,32],xt=Fn&&"CompositionEvent"in window,St=null;Fn&&"documentMode"in document&&(St=document.documentMode);var Et=Fn&&"TextEvent"in window&&!St,Ct=Fn&&(!xt||St&&8<St&&11>=St),zt=String.fromCharCode(32),jt=!1;function Pt(e,n){switch(e){case"keyup":return-1!==kt.indexOf(n.keyCode);case"keydown":return 229!==n.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Rt(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var _t=!1;var Ot={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Tt(e){var n=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===n?!!Ot[e.type]:"textarea"===n}function At(e,n,t,r){An?Nn?Nn.push(r):Nn=[r]:An=r,0<(n=Vd(n,"onChange")).length&&(t=new et("onChange","change",null,t,r),e.push({event:t,listeners:n}))}var Nt=null,Lt=null;function Mt(e){Md(e,0)}function It(e){if(pn(Be(e)))return e}function Dt(e,n){if("change"===e)return n}var Ft=!1;if(Fn){var Ht;if(Fn){var Ut="oninput"in document;if(!Ut){var Wt=document.createElement("div");Wt.setAttribute("oninput","return;"),Ut="function"===typeof Wt.oninput}Ht=Ut}else Ht=!1;Ft=Ht&&(!document.documentMode||9<document.documentMode)}function Bt(){Nt&&(Nt.detachEvent("onpropertychange",Vt),Lt=Nt=null)}function Vt(e){if("value"===e.propertyName&&It(Lt)){var n=[];At(n,Lt,e,Tn(e)),In(Mt,n)}}function qt(e,n,t){"focusin"===e?(Bt(),Lt=t,(Nt=n).attachEvent("onpropertychange",Vt)):"focusout"===e&&Bt()}function Yt(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return It(Lt)}function Gt(e,n){if("click"===e)return It(n)}function Kt(e,n){if("input"===e||"change"===e)return It(n)}var Qt="function"===typeof Object.is?Object.is:function(e,n){return e===n&&(0!==e||1/e===1/n)||e!==e&&n!==n};function $t(e,n){if(Qt(e,n))return!0;if("object"!==typeof e||null===e||"object"!==typeof n||null===n)return!1;var t=Object.keys(e),r=Object.keys(n);if(t.length!==r.length)return!1;for(r=0;r<t.length;r++){var i=t[r];if(!$.call(n,i)||!Qt(e[i],n[i]))return!1}return!0}function Zt(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Xt(e,n){var t,r=Zt(e);for(e=0;r;){if(3===r.nodeType){if(t=e+r.textContent.length,e<=n&&t>=n)return{node:r,offset:n-e};e=t}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Zt(r)}}function Jt(e,n){return!(!e||!n)&&(e===n||(!e||3!==e.nodeType)&&(n&&3===n.nodeType?Jt(e,n.parentNode):"contains"in e?e.contains(n):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(n))))}function er(e){for(var n=gn((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);n instanceof e.HTMLIFrameElement;){try{var t="string"===typeof n.contentWindow.location.href}catch(r){t=!1}if(!t)break;n=gn((e=n.contentWindow).document)}return n}function nr(e){var n=e&&e.nodeName&&e.nodeName.toLowerCase();return n&&("input"===n&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===n||"true"===e.contentEditable)}var tr=Fn&&"documentMode"in document&&11>=document.documentMode,rr=null,ir=null,ar=null,or=!1;function sr(e,n,t){var r=t.window===t?t.document:9===t.nodeType?t:t.ownerDocument;or||null==rr||rr!==gn(r)||("selectionStart"in(r=rr)&&nr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},ar&&$t(ar,r)||(ar=r,0<(r=Vd(ir,"onSelect")).length&&(n=new et("onSelect","select",null,n,t),e.push({event:n,listeners:r}),n.target=rr)))}function lr(e,n){var t={};return t[e.toLowerCase()]=n.toLowerCase(),t["Webkit"+e]="webkit"+n,t["Moz"+e]="moz"+n,t}var cr={animationend:lr("Animation","AnimationEnd"),animationiteration:lr("Animation","AnimationIteration"),animationstart:lr("Animation","AnimationStart"),transitionrun:lr("Transition","TransitionRun"),transitionstart:lr("Transition","TransitionStart"),transitioncancel:lr("Transition","TransitionCancel"),transitionend:lr("Transition","TransitionEnd")},dr={},ur={};function fr(e){if(dr[e])return dr[e];if(!cr[e])return e;var n,t=cr[e];for(n in t)if(t.hasOwnProperty(n)&&n in ur)return dr[e]=t[n];return e}Fn&&(ur=document.createElement("div").style,"AnimationEvent"in window||(delete cr.animationend.animation,delete cr.animationiteration.animation,delete cr.animationstart.animation),"TransitionEvent"in window||delete cr.transitionend.transition);var pr=fr("animationend"),gr=fr("animationiteration"),mr=fr("animationstart"),hr=fr("transitionrun"),br=fr("transitionstart"),yr=fr("transitioncancel"),vr=fr("transitionend"),wr=new Map,kr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function xr(e,n){wr.set(e,n),Ke(n,[e])}kr.push("scrollEnd");var Sr=new WeakMap;function Er(e,n){if("object"===typeof e&&null!==e){var t=Sr.get(e);return void 0!==t?t:(n={value:e,source:n,stack:cn(n)},Sr.set(e,n),n)}return{value:e,source:n,stack:cn(n)}}var Cr=[],zr=0,jr=0;function Pr(){for(var e=zr,n=jr=zr=0;n<e;){var t=Cr[n];Cr[n++]=null;var r=Cr[n];Cr[n++]=null;var i=Cr[n];Cr[n++]=null;var a=Cr[n];if(Cr[n++]=null,null!==r&&null!==i){var o=r.pending;null===o?i.next=i:(i.next=o.next,o.next=i),r.pending=i}0!==a&&Tr(t,i,a)}}function Rr(e,n,t,r){Cr[zr++]=e,Cr[zr++]=n,Cr[zr++]=t,Cr[zr++]=r,jr|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function _r(e,n,t,r){return Rr(e,n,t,r),Ar(e)}function Or(e,n){return Rr(e,null,null,n),Ar(e)}function Tr(e,n,t){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t);for(var i=!1,a=e.return;null!==a;)a.childLanes|=t,null!==(r=a.alternate)&&(r.childLanes|=t),22===a.tag&&(null===(e=a.stateNode)||1&e._visibility||(i=!0)),e=a,a=a.return;return 3===e.tag?(a=e.stateNode,i&&null!==n&&(i=31-pe(t),null===(r=(e=a.hiddenUpdates)[i])?e[i]=[n]:r.push(n),n.lane=536870912|t),a):null}function Ar(e){if(50<Tc)throw Tc=0,Ac=null,Error(o(185));for(var n=e.return;null!==n;)n=(e=n).return;return 3===e.tag?e.stateNode:null}var Nr={};function Lr(e,n,t,r){this.tag=e,this.key=t,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=n,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Mr(e,n,t,r){return new Lr(e,n,t,r)}function Ir(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Dr(e,n){var t=e.alternate;return null===t?((t=Mr(e.tag,n,e.key,e.mode)).elementType=e.elementType,t.type=e.type,t.stateNode=e.stateNode,t.alternate=e,e.alternate=t):(t.pendingProps=n,t.type=e.type,t.flags=0,t.subtreeFlags=0,t.deletions=null),t.flags=65011712&e.flags,t.childLanes=e.childLanes,t.lanes=e.lanes,t.child=e.child,t.memoizedProps=e.memoizedProps,t.memoizedState=e.memoizedState,t.updateQueue=e.updateQueue,n=e.dependencies,t.dependencies=null===n?null:{lanes:n.lanes,firstContext:n.firstContext},t.sibling=e.sibling,t.index=e.index,t.ref=e.ref,t.refCleanup=e.refCleanup,t}function Fr(e,n){e.flags&=65011714;var t=e.alternate;return null===t?(e.childLanes=0,e.lanes=n,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=t.childLanes,e.lanes=t.lanes,e.child=t.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=t.memoizedProps,e.memoizedState=t.memoizedState,e.updateQueue=t.updateQueue,e.type=t.type,n=t.dependencies,e.dependencies=null===n?null:{lanes:n.lanes,firstContext:n.firstContext}),e}function Hr(e,n,t,r,i,a){var s=0;if(r=e,"function"===typeof e)Ir(e)&&(s=1);else if("string"===typeof e)s=function(e,n,t){if(1===t||null!=n.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!==typeof n.precedence||"string"!==typeof n.href||""===n.href)break;return!0;case"link":if("string"!==typeof n.rel||"string"!==typeof n.href||""===n.href||n.onLoad||n.onError)break;return"stylesheet"!==n.rel||(e=n.disabled,"string"===typeof n.precedence&&null==e);case"script":if(n.async&&"function"!==typeof n.async&&"symbol"!==typeof n.async&&!n.onLoad&&!n.onError&&n.src&&"string"===typeof n.src)return!0}return!1}(e,t,W.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case j:return(e=Mr(31,t,n,i)).elementType=j,e.lanes=a,e;case h:return Ur(t.children,i,a,n);case b:s=8,i|=24;break;case y:return(e=Mr(12,t,n,2|i)).elementType=y,e.lanes=a,e;case S:return(e=Mr(13,t,n,i)).elementType=S,e.lanes=a,e;case E:return(e=Mr(19,t,n,i)).elementType=E,e.lanes=a,e;default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case v:case k:s=10;break e;case w:s=9;break e;case x:s=11;break e;case C:s=14;break e;case z:s=16,r=null;break e}s=29,t=Error(o(130,null===e?"null":typeof e,"")),r=null}return(n=Mr(s,t,n,i)).elementType=e,n.type=r,n.lanes=a,n}function Ur(e,n,t,r){return(e=Mr(7,e,r,n)).lanes=t,e}function Wr(e,n,t){return(e=Mr(6,e,null,n)).lanes=t,e}function Br(e,n,t){return(n=Mr(4,null!==e.children?e.children:[],e.key,n)).lanes=t,n.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},n}var Vr=[],qr=0,Yr=null,Gr=0,Kr=[],Qr=0,$r=null,Zr=1,Xr="";function Jr(e,n){Vr[qr++]=Gr,Vr[qr++]=Yr,Yr=e,Gr=n}function ei(e,n,t){Kr[Qr++]=Zr,Kr[Qr++]=Xr,Kr[Qr++]=$r,$r=e;var r=Zr;e=Xr;var i=32-pe(r)-1;r&=~(1<<i),t+=1;var a=32-pe(n)+i;if(30<a){var o=i-i%5;a=(r&(1<<o)-1).toString(32),r>>=o,i-=o,Zr=1<<32-pe(n)+i|t<<i|r,Xr=a+e}else Zr=1<<a|t<<i|r,Xr=e}function ni(e){null!==e.return&&(Jr(e,1),ei(e,1,0))}function ti(e){for(;e===Yr;)Yr=Vr[--qr],Vr[qr]=null,Gr=Vr[--qr],Vr[qr]=null;for(;e===$r;)$r=Kr[--Qr],Kr[Qr]=null,Xr=Kr[--Qr],Kr[Qr]=null,Zr=Kr[--Qr],Kr[Qr]=null}var ri=null,ii=null,ai=!1,oi=null,si=!1,li=Error(o(519));function ci(e){throw mi(Er(Error(o(418,"")),e)),li}function di(e){var n=e.stateNode,t=e.type,r=e.memoizedProps;switch(n[Te]=e,n[Ae]=r,t){case"dialog":Id("cancel",n),Id("close",n);break;case"iframe":case"object":case"embed":Id("load",n);break;case"video":case"audio":for(t=0;t<Nd.length;t++)Id(Nd[t],n);break;case"source":Id("error",n);break;case"img":case"image":case"link":Id("error",n),Id("load",n);break;case"details":Id("toggle",n);break;case"input":Id("invalid",n),yn(n,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),fn(n);break;case"select":Id("invalid",n);break;case"textarea":Id("invalid",n),xn(n,r.value,r.defaultValue,r.children),fn(n)}"string"!==typeof(t=r.children)&&"number"!==typeof t&&"bigint"!==typeof t||n.textContent===""+t||!0===r.suppressHydrationWarning||$d(n.textContent,t)?(null!=r.popover&&(Id("beforetoggle",n),Id("toggle",n)),null!=r.onScroll&&Id("scroll",n),null!=r.onScrollEnd&&Id("scrollend",n),null!=r.onClick&&(n.onclick=Zd),n=!0):n=!1,n||ci(e)}function ui(e){for(ri=e.return;ri;)switch(ri.tag){case 5:case 13:return void(si=!1);case 27:case 3:return void(si=!0);default:ri=ri.return}}function fi(e){if(e!==ri)return!1;if(!ai)return ui(e),ai=!0,!1;var n,t=e.tag;if((n=3!==t&&27!==t)&&((n=5===t)&&(n=!("form"!==(n=e.type)&&"button"!==n)||ou(e.type,e.memoizedProps)),n=!n),n&&ii&&ci(e),ui(e),13===t){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType)if("/$"===(n=e.data)){if(0===t){ii=bu(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++;e=e.nextSibling}ii=null}}else 27===t?(t=ii,pu(e.type)?(e=yu,yu=null,ii=e):ii=t):ii=ri?bu(e.stateNode.nextSibling):null;return!0}function pi(){ii=ri=null,ai=!1}function gi(){var e=oi;return null!==e&&(null===vc?vc=e:vc.push.apply(vc,e),oi=null),e}function mi(e){null===oi?oi=[e]:oi.push(e)}var hi=F(null),bi=null,yi=null;function vi(e,n,t){U(hi,n._currentValue),n._currentValue=t}function wi(e){e._currentValue=hi.current,H(hi)}function ki(e,n,t){for(;null!==e;){var r=e.alternate;if((e.childLanes&n)!==n?(e.childLanes|=n,null!==r&&(r.childLanes|=n)):null!==r&&(r.childLanes&n)!==n&&(r.childLanes|=n),e===t)break;e=e.return}}function xi(e,n,t,r){var i=e.child;for(null!==i&&(i.return=e);null!==i;){var a=i.dependencies;if(null!==a){var s=i.child;a=a.firstContext;e:for(;null!==a;){var l=a;a=i;for(var c=0;c<n.length;c++)if(l.context===n[c]){a.lanes|=t,null!==(l=a.alternate)&&(l.lanes|=t),ki(a.return,t,e),r||(s=null);break e}a=l.next}}else if(18===i.tag){if(null===(s=i.return))throw Error(o(341));s.lanes|=t,null!==(a=s.alternate)&&(a.lanes|=t),ki(s,t,e),s=null}else s=i.child;if(null!==s)s.return=i;else for(s=i;null!==s;){if(s===e){s=null;break}if(null!==(i=s.sibling)){i.return=s.return,s=i;break}s=s.return}i=s}}function Si(e,n,t,r){e=null;for(var i=n,a=!1;null!==i;){if(!a)if(0!==(524288&i.flags))a=!0;else if(0!==(262144&i.flags))break;if(10===i.tag){var s=i.alternate;if(null===s)throw Error(o(387));if(null!==(s=s.memoizedProps)){var l=i.type;Qt(i.pendingProps.value,s.value)||(null!==e?e.push(l):e=[l])}}else if(i===q.current){if(null===(s=i.alternate))throw Error(o(387));s.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(null!==e?e.push(Ku):e=[Ku])}i=i.return}null!==e&&xi(n,e,t,r),n.flags|=262144}function Ei(e){for(e=e.firstContext;null!==e;){if(!Qt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ci(e){bi=e,yi=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function zi(e){return Pi(bi,e)}function ji(e,n){return null===bi&&Ci(e),Pi(e,n)}function Pi(e,n){var t=n._currentValue;if(n={context:n,memoizedValue:t,next:null},null===yi){if(null===e)throw Error(o(308));yi=n,e.dependencies={lanes:0,firstContext:n},e.flags|=524288}else yi=yi.next=n;return t}var Ri="undefined"!==typeof AbortController?AbortController:function(){var e=[],n=this.signal={aborted:!1,addEventListener:function(n,t){e.push(t)}};this.abort=function(){n.aborted=!0,e.forEach(function(e){return e()})}},_i=r.unstable_scheduleCallback,Oi=r.unstable_NormalPriority,Ti={$$typeof:k,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Ai(){return{controller:new Ri,data:new Map,refCount:0}}function Ni(e){e.refCount--,0===e.refCount&&_i(Oi,function(){e.controller.abort()})}var Li=null,Mi=0,Ii=0,Di=null;function Fi(){if(0===--Mi&&null!==Li){null!==Di&&(Di.status="fulfilled");var e=Li;Li=null,Ii=0,Di=null;for(var n=0;n<e.length;n++)(0,e[n])()}}var Hi=N.S;N.S=function(e,n){"object"===typeof n&&null!==n&&"function"===typeof n.then&&function(e,n){if(null===Li){var t=Li=[];Mi=0,Ii=Rd(),Di={status:"pending",value:void 0,then:function(e){t.push(e)}}}Mi++,n.then(Fi,Fi)}(0,n),null!==Hi&&Hi(e,n)};var Ui=F(null);function Wi(){var e=Ui.current;return null!==e?e:rc.pooledCache}function Bi(e,n){U(Ui,null===n?Ui.current:n.pool)}function Vi(){var e=Wi();return null===e?null:{parent:Ti._currentValue,pool:e}}var qi=Error(o(460)),Yi=Error(o(474)),Gi=Error(o(542)),Ki={then:function(){}};function Qi(e){return"fulfilled"===(e=e.status)||"rejected"===e}function $i(){}function Zi(e,n,t){switch(void 0===(t=e[t])?e.push(n):t!==n&&(n.then($i,$i),n=t),n.status){case"fulfilled":return n.value;case"rejected":throw ea(e=n.reason),e;default:if("string"===typeof n.status)n.then($i,$i);else{if(null!==(e=rc)&&100<e.shellSuspendCounter)throw Error(o(482));(e=n).status="pending",e.then(function(e){if("pending"===n.status){var t=n;t.status="fulfilled",t.value=e}},function(e){if("pending"===n.status){var t=n;t.status="rejected",t.reason=e}})}switch(n.status){case"fulfilled":return n.value;case"rejected":throw ea(e=n.reason),e}throw Xi=n,qi}}var Xi=null;function Ji(){if(null===Xi)throw Error(o(459));var e=Xi;return Xi=null,e}function ea(e){if(e===qi||e===Gi)throw Error(o(483))}var na=!1;function ta(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function ra(e,n){e=e.updateQueue,n.updateQueue===e&&(n.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function ia(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function aa(e,n,t){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&tc)){var i=r.pending;return null===i?n.next=n:(n.next=i.next,i.next=n),r.pending=n,n=Ar(e),Tr(e,null,t),n}return Rr(e,r,n,t),Ar(e)}function oa(e,n,t){if(null!==(n=n.updateQueue)&&(n=n.shared,0!==(4194048&t))){var r=n.lanes;t|=r&=e.pendingLanes,n.lanes=t,je(e,t)}}function sa(e,n){var t=e.updateQueue,r=e.alternate;if(null!==r&&t===(r=r.updateQueue)){var i=null,a=null;if(null!==(t=t.firstBaseUpdate)){do{var o={lane:t.lane,tag:t.tag,payload:t.payload,callback:null,next:null};null===a?i=a=o:a=a.next=o,t=t.next}while(null!==t);null===a?i=a=n:a=a.next=n}else i=a=n;return t={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:a,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=t)}null===(e=t.lastBaseUpdate)?t.firstBaseUpdate=n:e.next=n,t.lastBaseUpdate=n}var la=!1;function ca(){if(la){if(null!==Di)throw Di}}function da(e,n,t,r){la=!1;var i=e.updateQueue;na=!1;var a=i.firstBaseUpdate,o=i.lastBaseUpdate,s=i.shared.pending;if(null!==s){i.shared.pending=null;var l=s,c=l.next;l.next=null,null===o?a=c:o.next=c,o=l;var d=e.alternate;null!==d&&((s=(d=d.updateQueue).lastBaseUpdate)!==o&&(null===s?d.firstBaseUpdate=c:s.next=c,d.lastBaseUpdate=l))}if(null!==a){var u=i.baseState;for(o=0,d=c=l=null,s=a;;){var p=-536870913&s.lane,g=p!==s.lane;if(g?(ac&p)===p:(r&p)===p){0!==p&&p===Ii&&(la=!0),null!==d&&(d=d.next={lane:0,tag:s.tag,payload:s.payload,callback:null,next:null});e:{var m=e,h=s;p=n;var b=t;switch(h.tag){case 1:if("function"===typeof(m=h.payload)){u=m.call(b,u,p);break e}u=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null===(p="function"===typeof(m=h.payload)?m.call(b,u,p):m)||void 0===p)break e;u=f({},u,p);break e;case 2:na=!0}}null!==(p=s.callback)&&(e.flags|=64,g&&(e.flags|=8192),null===(g=i.callbacks)?i.callbacks=[p]:g.push(p))}else g={lane:p,tag:s.tag,payload:s.payload,callback:s.callback,next:null},null===d?(c=d=g,l=u):d=d.next=g,o|=p;if(null===(s=s.next)){if(null===(s=i.shared.pending))break;s=(g=s).next,g.next=null,i.lastBaseUpdate=g,i.shared.pending=null}}null===d&&(l=u),i.baseState=l,i.firstBaseUpdate=c,i.lastBaseUpdate=d,null===a&&(i.shared.lanes=0),pc|=o,e.lanes=o,e.memoizedState=u}}function ua(e,n){if("function"!==typeof e)throw Error(o(191,e));e.call(n)}function fa(e,n){var t=e.callbacks;if(null!==t)for(e.callbacks=null,e=0;e<t.length;e++)ua(t[e],n)}var pa=F(null),ga=F(0);function ma(e,n){U(ga,e=uc),U(pa,n),uc=e|n.baseLanes}function ha(){U(ga,uc),U(pa,pa.current)}function ba(){uc=ga.current,H(pa),H(ga)}var ya=0,va=null,wa=null,ka=null,xa=!1,Sa=!1,Ea=!1,Ca=0,za=0,ja=null,Pa=0;function Ra(){throw Error(o(321))}function _a(e,n){if(null===n)return!1;for(var t=0;t<n.length&&t<e.length;t++)if(!Qt(e[t],n[t]))return!1;return!0}function Oa(e,n,t,r,i,a){return ya=a,va=n,n.memoizedState=null,n.updateQueue=null,n.lanes=0,N.H=null===e||null===e.memoizedState?Yo:Go,Ea=!1,a=t(r,i),Ea=!1,Sa&&(a=Aa(n,t,r,i)),Ta(e),a}function Ta(e){N.H=qo;var n=null!==wa&&null!==wa.next;if(ya=0,ka=wa=va=null,xa=!1,za=0,ja=null,n)throw Error(o(300));null===e||js||null!==(e=e.dependencies)&&Ei(e)&&(js=!0)}function Aa(e,n,t,r){va=e;var i=0;do{if(Sa&&(ja=null),za=0,Sa=!1,25<=i)throw Error(o(301));if(i+=1,ka=wa=null,null!=e.updateQueue){var a=e.updateQueue;a.lastEffect=null,a.events=null,a.stores=null,null!=a.memoCache&&(a.memoCache.index=0)}N.H=Ko,a=n(t,r)}while(Sa);return a}function Na(){var e=N.H,n=e.useState()[0];return n="function"===typeof n.then?Ha(n):n,e=e.useState()[0],(null!==wa?wa.memoizedState:null)!==e&&(va.flags|=1024),n}function La(){var e=0!==Ca;return Ca=0,e}function Ma(e,n,t){n.updateQueue=e.updateQueue,n.flags&=-2053,e.lanes&=~t}function Ia(e){if(xa){for(e=e.memoizedState;null!==e;){var n=e.queue;null!==n&&(n.pending=null),e=e.next}xa=!1}ya=0,ka=wa=va=null,Sa=!1,za=Ca=0,ja=null}function Da(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===ka?va.memoizedState=ka=e:ka=ka.next=e,ka}function Fa(){if(null===wa){var e=va.alternate;e=null!==e?e.memoizedState:null}else e=wa.next;var n=null===ka?va.memoizedState:ka.next;if(null!==n)ka=n,wa=e;else{if(null===e){if(null===va.alternate)throw Error(o(467));throw Error(o(310))}e={memoizedState:(wa=e).memoizedState,baseState:wa.baseState,baseQueue:wa.baseQueue,queue:wa.queue,next:null},null===ka?va.memoizedState=ka=e:ka=ka.next=e}return ka}function Ha(e){var n=za;return za+=1,null===ja&&(ja=[]),e=Zi(ja,e,n),n=va,null===(null===ka?n.memoizedState:ka.next)&&(n=n.alternate,N.H=null===n||null===n.memoizedState?Yo:Go),e}function Ua(e){if(null!==e&&"object"===typeof e){if("function"===typeof e.then)return Ha(e);if(e.$$typeof===k)return zi(e)}throw Error(o(438,String(e)))}function Wa(e){var n=null,t=va.updateQueue;if(null!==t&&(n=t.memoCache),null==n){var r=va.alternate;null!==r&&(null!==(r=r.updateQueue)&&(null!=(r=r.memoCache)&&(n={data:r.data.map(function(e){return e.slice()}),index:0})))}if(null==n&&(n={data:[],index:0}),null===t&&(t={lastEffect:null,events:null,stores:null,memoCache:null},va.updateQueue=t),t.memoCache=n,void 0===(t=n.data[n.index]))for(t=n.data[n.index]=Array(e),r=0;r<e;r++)t[r]=P;return n.index++,t}function Ba(e,n){return"function"===typeof n?n(e):n}function Va(e){return qa(Fa(),wa,e)}function qa(e,n,t){var r=e.queue;if(null===r)throw Error(o(311));r.lastRenderedReducer=t;var i=e.baseQueue,a=r.pending;if(null!==a){if(null!==i){var s=i.next;i.next=a.next,a.next=s}n.baseQueue=i=a,r.pending=null}if(a=e.baseState,null===i)e.memoizedState=a;else{var l=s=null,c=null,d=n=i.next,u=!1;do{var f=-536870913&d.lane;if(f!==d.lane?(ac&f)===f:(ya&f)===f){var p=d.revertLane;if(0===p)null!==c&&(c=c.next={lane:0,revertLane:0,action:d.action,hasEagerState:d.hasEagerState,eagerState:d.eagerState,next:null}),f===Ii&&(u=!0);else{if((ya&p)===p){d=d.next,p===Ii&&(u=!0);continue}f={lane:0,revertLane:d.revertLane,action:d.action,hasEagerState:d.hasEagerState,eagerState:d.eagerState,next:null},null===c?(l=c=f,s=a):c=c.next=f,va.lanes|=p,pc|=p}f=d.action,Ea&&t(a,f),a=d.hasEagerState?d.eagerState:t(a,f)}else p={lane:f,revertLane:d.revertLane,action:d.action,hasEagerState:d.hasEagerState,eagerState:d.eagerState,next:null},null===c?(l=c=p,s=a):c=c.next=p,va.lanes|=f,pc|=f;d=d.next}while(null!==d&&d!==n);if(null===c?s=a:c.next=l,!Qt(a,e.memoizedState)&&(js=!0,u&&null!==(t=Di)))throw t;e.memoizedState=a,e.baseState=s,e.baseQueue=c,r.lastRenderedState=a}return null===i&&(r.lanes=0),[e.memoizedState,r.dispatch]}function Ya(e){var n=Fa(),t=n.queue;if(null===t)throw Error(o(311));t.lastRenderedReducer=e;var r=t.dispatch,i=t.pending,a=n.memoizedState;if(null!==i){t.pending=null;var s=i=i.next;do{a=e(a,s.action),s=s.next}while(s!==i);Qt(a,n.memoizedState)||(js=!0),n.memoizedState=a,null===n.baseQueue&&(n.baseState=a),t.lastRenderedState=a}return[a,r]}function Ga(e,n,t){var r=va,i=Fa(),a=ai;if(a){if(void 0===t)throw Error(o(407));t=t()}else t=n();var s=!Qt((wa||i).memoizedState,t);if(s&&(i.memoizedState=t,js=!0),i=i.queue,bo(2048,8,$a.bind(null,r,i,e),[e]),i.getSnapshot!==n||s||null!==ka&&1&ka.memoizedState.tag){if(r.flags|=2048,go(9,{destroy:void 0,resource:void 0},Qa.bind(null,r,i,t,n),null),null===rc)throw Error(o(349));a||0!==(124&ya)||Ka(r,n,t)}return t}function Ka(e,n,t){e.flags|=16384,e={getSnapshot:n,value:t},null===(n=va.updateQueue)?(n={lastEffect:null,events:null,stores:null,memoCache:null},va.updateQueue=n,n.stores=[e]):null===(t=n.stores)?n.stores=[e]:t.push(e)}function Qa(e,n,t,r){n.value=t,n.getSnapshot=r,Za(n)&&Xa(e)}function $a(e,n,t){return t(function(){Za(n)&&Xa(e)})}function Za(e){var n=e.getSnapshot;e=e.value;try{var t=n();return!Qt(e,t)}catch(r){return!0}}function Xa(e){var n=Or(e,2);null!==n&&Mc(n,e,2)}function Ja(e){var n=Da();if("function"===typeof e){var t=e;if(e=t(),Ea){fe(!0);try{t()}finally{fe(!1)}}}return n.memoizedState=n.baseState=e,n.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ba,lastRenderedState:e},n}function eo(e,n,t,r){return e.baseState=t,qa(e,wa,"function"===typeof r?r:Ba)}function no(e,n,t,r,i){if(Wo(e))throw Error(o(485));if(null!==(e=n.action)){var a={payload:i,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){a.listeners.push(e)}};null!==N.T?t(!0):a.isTransition=!1,r(a),null===(t=n.pending)?(a.next=n.pending=a,to(n,a)):(a.next=t.next,n.pending=t.next=a)}}function to(e,n){var t=n.action,r=n.payload,i=e.state;if(n.isTransition){var a=N.T,o={};N.T=o;try{var s=t(i,r),l=N.S;null!==l&&l(o,s),ro(e,n,s)}catch(c){ao(e,n,c)}finally{N.T=a}}else try{ro(e,n,a=t(i,r))}catch(d){ao(e,n,d)}}function ro(e,n,t){null!==t&&"object"===typeof t&&"function"===typeof t.then?t.then(function(t){io(e,n,t)},function(t){return ao(e,n,t)}):io(e,n,t)}function io(e,n,t){n.status="fulfilled",n.value=t,oo(n),e.state=t,null!==(n=e.pending)&&((t=n.next)===n?e.pending=null:(t=t.next,n.next=t,to(e,t)))}function ao(e,n,t){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{n.status="rejected",n.reason=t,oo(n),n=n.next}while(n!==r)}e.action=null}function oo(e){e=e.listeners;for(var n=0;n<e.length;n++)(0,e[n])()}function so(e,n){return n}function lo(e,n){if(ai){var t=rc.formState;if(null!==t){e:{var r=va;if(ai){if(ii){n:{for(var i=ii,a=si;8!==i.nodeType;){if(!a){i=null;break n}if(null===(i=bu(i.nextSibling))){i=null;break n}}i="F!"===(a=i.data)||"F"===a?i:null}if(i){ii=bu(i.nextSibling),r="F!"===i.data;break e}}ci(r)}r=!1}r&&(n=t[0])}}return(t=Da()).memoizedState=t.baseState=n,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:so,lastRenderedState:n},t.queue=r,t=Fo.bind(null,va,r),r.dispatch=t,r=Ja(!1),a=Uo.bind(null,va,!1,r.queue),i={state:n,dispatch:null,action:e,pending:null},(r=Da()).queue=i,t=no.bind(null,va,i,a,t),i.dispatch=t,r.memoizedState=e,[n,t,!1]}function co(e){return uo(Fa(),wa,e)}function uo(e,n,t){if(n=qa(e,n,so)[0],e=Va(Ba)[0],"object"===typeof n&&null!==n&&"function"===typeof n.then)try{var r=Ha(n)}catch(o){if(o===qi)throw Gi;throw o}else r=n;var i=(n=Fa()).queue,a=i.dispatch;return t!==n.memoizedState&&(va.flags|=2048,go(9,{destroy:void 0,resource:void 0},fo.bind(null,i,t),null)),[r,a,e]}function fo(e,n){e.action=n}function po(e){var n=Fa(),t=wa;if(null!==t)return uo(n,t,e);Fa(),n=n.memoizedState;var r=(t=Fa()).queue.dispatch;return t.memoizedState=e,[n,r,!1]}function go(e,n,t,r){return e={tag:e,create:t,deps:r,inst:n,next:null},null===(n=va.updateQueue)&&(n={lastEffect:null,events:null,stores:null,memoCache:null},va.updateQueue=n),null===(t=n.lastEffect)?n.lastEffect=e.next=e:(r=t.next,t.next=e,e.next=r,n.lastEffect=e),e}function mo(){return Fa().memoizedState}function ho(e,n,t,r){var i=Da();r=void 0===r?null:r,va.flags|=e,i.memoizedState=go(1|n,{destroy:void 0,resource:void 0},t,r)}function bo(e,n,t,r){var i=Fa();r=void 0===r?null:r;var a=i.memoizedState.inst;null!==wa&&null!==r&&_a(r,wa.memoizedState.deps)?i.memoizedState=go(n,a,t,r):(va.flags|=e,i.memoizedState=go(1|n,a,t,r))}function yo(e,n){ho(8390656,8,e,n)}function vo(e,n){bo(2048,8,e,n)}function wo(e,n){return bo(4,2,e,n)}function ko(e,n){return bo(4,4,e,n)}function xo(e,n){if("function"===typeof n){e=e();var t=n(e);return function(){"function"===typeof t?t():n(null)}}if(null!==n&&void 0!==n)return e=e(),n.current=e,function(){n.current=null}}function So(e,n,t){t=null!==t&&void 0!==t?t.concat([e]):null,bo(4,4,xo.bind(null,n,e),t)}function Eo(){}function Co(e,n){var t=Fa();n=void 0===n?null:n;var r=t.memoizedState;return null!==n&&_a(n,r[1])?r[0]:(t.memoizedState=[e,n],e)}function zo(e,n){var t=Fa();n=void 0===n?null:n;var r=t.memoizedState;if(null!==n&&_a(n,r[1]))return r[0];if(r=e(),Ea){fe(!0);try{e()}finally{fe(!1)}}return t.memoizedState=[r,n],r}function jo(e,n,t){return void 0===t||0!==(1073741824&ya)?e.memoizedState=n:(e.memoizedState=t,e=Lc(),va.lanes|=e,pc|=e,t)}function Po(e,n,t,r){return Qt(t,n)?t:null!==pa.current?(e=jo(e,t,r),Qt(e,n)||(js=!0),e):0===(42&ya)?(js=!0,e.memoizedState=t):(e=Lc(),va.lanes|=e,pc|=e,n)}function Ro(e,n,t,r,i){var a=L.p;L.p=0!==a&&8>a?a:8;var o=N.T,s={};N.T=s,Uo(e,!1,n,t);try{var l=i(),c=N.S;if(null!==c&&c(s,l),null!==l&&"object"===typeof l&&"function"===typeof l.then)Ho(e,n,function(e,n){var t=[],r={status:"pending",value:null,reason:null,then:function(e){t.push(e)}};return e.then(function(){r.status="fulfilled",r.value=n;for(var e=0;e<t.length;e++)(0,t[e])(n)},function(e){for(r.status="rejected",r.reason=e,e=0;e<t.length;e++)(0,t[e])(void 0)}),r}(l,r),Nc());else Ho(e,n,r,Nc())}catch(d){Ho(e,n,{then:function(){},status:"rejected",reason:d},Nc())}finally{L.p=a,N.T=o}}function _o(){}function Oo(e,n,t,r){if(5!==e.tag)throw Error(o(476));var i=To(e).queue;Ro(e,i,n,M,null===t?_o:function(){return Ao(e),t(r)})}function To(e){var n=e.memoizedState;if(null!==n)return n;var t={};return(n={memoizedState:M,baseState:M,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ba,lastRenderedState:M},next:null}).next={memoizedState:t,baseState:t,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ba,lastRenderedState:t},next:null},e.memoizedState=n,null!==(e=e.alternate)&&(e.memoizedState=n),n}function Ao(e){Ho(e,To(e).next.queue,{},Nc())}function No(){return zi(Ku)}function Lo(){return Fa().memoizedState}function Mo(){return Fa().memoizedState}function Io(e){for(var n=e.return;null!==n;){switch(n.tag){case 24:case 3:var t=Nc(),r=aa(n,e=ia(t),t);return null!==r&&(Mc(r,n,t),oa(r,n,t)),n={cache:Ai()},void(e.payload=n)}n=n.return}}function Do(e,n,t){var r=Nc();t={lane:r,revertLane:0,action:t,hasEagerState:!1,eagerState:null,next:null},Wo(e)?Bo(n,t):null!==(t=_r(e,n,t,r))&&(Mc(t,e,r),Vo(t,n,r))}function Fo(e,n,t){Ho(e,n,t,Nc())}function Ho(e,n,t,r){var i={lane:r,revertLane:0,action:t,hasEagerState:!1,eagerState:null,next:null};if(Wo(e))Bo(n,i);else{var a=e.alternate;if(0===e.lanes&&(null===a||0===a.lanes)&&null!==(a=n.lastRenderedReducer))try{var o=n.lastRenderedState,s=a(o,t);if(i.hasEagerState=!0,i.eagerState=s,Qt(s,o))return Rr(e,n,i,0),null===rc&&Pr(),!1}catch(l){}if(null!==(t=_r(e,n,i,r)))return Mc(t,e,r),Vo(t,n,r),!0}return!1}function Uo(e,n,t,r){if(r={lane:2,revertLane:Rd(),action:r,hasEagerState:!1,eagerState:null,next:null},Wo(e)){if(n)throw Error(o(479))}else null!==(n=_r(e,t,r,2))&&Mc(n,e,2)}function Wo(e){var n=e.alternate;return e===va||null!==n&&n===va}function Bo(e,n){Sa=xa=!0;var t=e.pending;null===t?n.next=n:(n.next=t.next,t.next=n),e.pending=n}function Vo(e,n,t){if(0!==(4194048&t)){var r=n.lanes;t|=r&=e.pendingLanes,n.lanes=t,je(e,t)}}var qo={readContext:zi,use:Ua,useCallback:Ra,useContext:Ra,useEffect:Ra,useImperativeHandle:Ra,useLayoutEffect:Ra,useInsertionEffect:Ra,useMemo:Ra,useReducer:Ra,useRef:Ra,useState:Ra,useDebugValue:Ra,useDeferredValue:Ra,useTransition:Ra,useSyncExternalStore:Ra,useId:Ra,useHostTransitionStatus:Ra,useFormState:Ra,useActionState:Ra,useOptimistic:Ra,useMemoCache:Ra,useCacheRefresh:Ra},Yo={readContext:zi,use:Ua,useCallback:function(e,n){return Da().memoizedState=[e,void 0===n?null:n],e},useContext:zi,useEffect:yo,useImperativeHandle:function(e,n,t){t=null!==t&&void 0!==t?t.concat([e]):null,ho(4194308,4,xo.bind(null,n,e),t)},useLayoutEffect:function(e,n){return ho(4194308,4,e,n)},useInsertionEffect:function(e,n){ho(4,2,e,n)},useMemo:function(e,n){var t=Da();n=void 0===n?null:n;var r=e();if(Ea){fe(!0);try{e()}finally{fe(!1)}}return t.memoizedState=[r,n],r},useReducer:function(e,n,t){var r=Da();if(void 0!==t){var i=t(n);if(Ea){fe(!0);try{t(n)}finally{fe(!1)}}}else i=n;return r.memoizedState=r.baseState=i,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:i},r.queue=e,e=e.dispatch=Do.bind(null,va,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Da().memoizedState=e},useState:function(e){var n=(e=Ja(e)).queue,t=Fo.bind(null,va,n);return n.dispatch=t,[e.memoizedState,t]},useDebugValue:Eo,useDeferredValue:function(e,n){return jo(Da(),e,n)},useTransition:function(){var e=Ja(!1);return e=Ro.bind(null,va,e.queue,!0,!1),Da().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,n,t){var r=va,i=Da();if(ai){if(void 0===t)throw Error(o(407));t=t()}else{if(t=n(),null===rc)throw Error(o(349));0!==(124&ac)||Ka(r,n,t)}i.memoizedState=t;var a={value:t,getSnapshot:n};return i.queue=a,yo($a.bind(null,r,a,e),[e]),r.flags|=2048,go(9,{destroy:void 0,resource:void 0},Qa.bind(null,r,a,t,n),null),t},useId:function(){var e=Da(),n=rc.identifierPrefix;if(ai){var t=Xr;n="\xab"+n+"R"+(t=(Zr&~(1<<32-pe(Zr)-1)).toString(32)+t),0<(t=Ca++)&&(n+="H"+t.toString(32)),n+="\xbb"}else n="\xab"+n+"r"+(t=Pa++).toString(32)+"\xbb";return e.memoizedState=n},useHostTransitionStatus:No,useFormState:lo,useActionState:lo,useOptimistic:function(e){var n=Da();n.memoizedState=n.baseState=e;var t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return n.queue=t,n=Uo.bind(null,va,!0,t),t.dispatch=n,[e,n]},useMemoCache:Wa,useCacheRefresh:function(){return Da().memoizedState=Io.bind(null,va)}},Go={readContext:zi,use:Ua,useCallback:Co,useContext:zi,useEffect:vo,useImperativeHandle:So,useInsertionEffect:wo,useLayoutEffect:ko,useMemo:zo,useReducer:Va,useRef:mo,useState:function(){return Va(Ba)},useDebugValue:Eo,useDeferredValue:function(e,n){return Po(Fa(),wa.memoizedState,e,n)},useTransition:function(){var e=Va(Ba)[0],n=Fa().memoizedState;return["boolean"===typeof e?e:Ha(e),n]},useSyncExternalStore:Ga,useId:Lo,useHostTransitionStatus:No,useFormState:co,useActionState:co,useOptimistic:function(e,n){return eo(Fa(),0,e,n)},useMemoCache:Wa,useCacheRefresh:Mo},Ko={readContext:zi,use:Ua,useCallback:Co,useContext:zi,useEffect:vo,useImperativeHandle:So,useInsertionEffect:wo,useLayoutEffect:ko,useMemo:zo,useReducer:Ya,useRef:mo,useState:function(){return Ya(Ba)},useDebugValue:Eo,useDeferredValue:function(e,n){var t=Fa();return null===wa?jo(t,e,n):Po(t,wa.memoizedState,e,n)},useTransition:function(){var e=Ya(Ba)[0],n=Fa().memoizedState;return["boolean"===typeof e?e:Ha(e),n]},useSyncExternalStore:Ga,useId:Lo,useHostTransitionStatus:No,useFormState:po,useActionState:po,useOptimistic:function(e,n){var t=Fa();return null!==wa?eo(t,0,e,n):(t.baseState=e,[e,t.queue.dispatch])},useMemoCache:Wa,useCacheRefresh:Mo},Qo=null,$o=0;function Zo(e){var n=$o;return $o+=1,null===Qo&&(Qo=[]),Zi(Qo,e,n)}function Xo(e,n){n=n.props.ref,e.ref=void 0!==n?n:null}function Jo(e,n){if(n.$$typeof===p)throw Error(o(525));throw e=Object.prototype.toString.call(n),Error(o(31,"[object Object]"===e?"object with keys {"+Object.keys(n).join(", ")+"}":e))}function es(e){return(0,e._init)(e._payload)}function ns(e){function n(n,t){if(e){var r=n.deletions;null===r?(n.deletions=[t],n.flags|=16):r.push(t)}}function t(t,r){if(!e)return null;for(;null!==r;)n(t,r),r=r.sibling;return null}function r(e){for(var n=new Map;null!==e;)null!==e.key?n.set(e.key,e):n.set(e.index,e),e=e.sibling;return n}function i(e,n){return(e=Dr(e,n)).index=0,e.sibling=null,e}function a(n,t,r){return n.index=r,e?null!==(r=n.alternate)?(r=r.index)<t?(n.flags|=67108866,t):r:(n.flags|=67108866,t):(n.flags|=1048576,t)}function s(n){return e&&null===n.alternate&&(n.flags|=67108866),n}function l(e,n,t,r){return null===n||6!==n.tag?((n=Wr(t,e.mode,r)).return=e,n):((n=i(n,t)).return=e,n)}function c(e,n,t,r){var a=t.type;return a===h?u(e,n,t.props.children,r,t.key):null!==n&&(n.elementType===a||"object"===typeof a&&null!==a&&a.$$typeof===z&&es(a)===n.type)?(Xo(n=i(n,t.props),t),n.return=e,n):(Xo(n=Hr(t.type,t.key,t.props,null,e.mode,r),t),n.return=e,n)}function d(e,n,t,r){return null===n||4!==n.tag||n.stateNode.containerInfo!==t.containerInfo||n.stateNode.implementation!==t.implementation?((n=Br(t,e.mode,r)).return=e,n):((n=i(n,t.children||[])).return=e,n)}function u(e,n,t,r,a){return null===n||7!==n.tag?((n=Ur(t,e.mode,r,a)).return=e,n):((n=i(n,t)).return=e,n)}function f(e,n,t){if("string"===typeof n&&""!==n||"number"===typeof n||"bigint"===typeof n)return(n=Wr(""+n,e.mode,t)).return=e,n;if("object"===typeof n&&null!==n){switch(n.$$typeof){case g:return Xo(t=Hr(n.type,n.key,n.props,null,e.mode,t),n),t.return=e,t;case m:return(n=Br(n,e.mode,t)).return=e,n;case z:return f(e,n=(0,n._init)(n._payload),t)}if(A(n)||_(n))return(n=Ur(n,e.mode,t,null)).return=e,n;if("function"===typeof n.then)return f(e,Zo(n),t);if(n.$$typeof===k)return f(e,ji(e,n),t);Jo(e,n)}return null}function p(e,n,t,r){var i=null!==n?n.key:null;if("string"===typeof t&&""!==t||"number"===typeof t||"bigint"===typeof t)return null!==i?null:l(e,n,""+t,r);if("object"===typeof t&&null!==t){switch(t.$$typeof){case g:return t.key===i?c(e,n,t,r):null;case m:return t.key===i?d(e,n,t,r):null;case z:return p(e,n,t=(i=t._init)(t._payload),r)}if(A(t)||_(t))return null!==i?null:u(e,n,t,r,null);if("function"===typeof t.then)return p(e,n,Zo(t),r);if(t.$$typeof===k)return p(e,n,ji(e,t),r);Jo(e,t)}return null}function b(e,n,t,r,i){if("string"===typeof r&&""!==r||"number"===typeof r||"bigint"===typeof r)return l(n,e=e.get(t)||null,""+r,i);if("object"===typeof r&&null!==r){switch(r.$$typeof){case g:return c(n,e=e.get(null===r.key?t:r.key)||null,r,i);case m:return d(n,e=e.get(null===r.key?t:r.key)||null,r,i);case z:return b(e,n,t,r=(0,r._init)(r._payload),i)}if(A(r)||_(r))return u(n,e=e.get(t)||null,r,i,null);if("function"===typeof r.then)return b(e,n,t,Zo(r),i);if(r.$$typeof===k)return b(e,n,t,ji(n,r),i);Jo(n,r)}return null}function y(l,c,d,u){if("object"===typeof d&&null!==d&&d.type===h&&null===d.key&&(d=d.props.children),"object"===typeof d&&null!==d){switch(d.$$typeof){case g:e:{for(var v=d.key;null!==c;){if(c.key===v){if((v=d.type)===h){if(7===c.tag){t(l,c.sibling),(u=i(c,d.props.children)).return=l,l=u;break e}}else if(c.elementType===v||"object"===typeof v&&null!==v&&v.$$typeof===z&&es(v)===c.type){t(l,c.sibling),Xo(u=i(c,d.props),d),u.return=l,l=u;break e}t(l,c);break}n(l,c),c=c.sibling}d.type===h?((u=Ur(d.props.children,l.mode,u,d.key)).return=l,l=u):(Xo(u=Hr(d.type,d.key,d.props,null,l.mode,u),d),u.return=l,l=u)}return s(l);case m:e:{for(v=d.key;null!==c;){if(c.key===v){if(4===c.tag&&c.stateNode.containerInfo===d.containerInfo&&c.stateNode.implementation===d.implementation){t(l,c.sibling),(u=i(c,d.children||[])).return=l,l=u;break e}t(l,c);break}n(l,c),c=c.sibling}(u=Br(d,l.mode,u)).return=l,l=u}return s(l);case z:return y(l,c,d=(v=d._init)(d._payload),u)}if(A(d))return function(i,o,s,l){for(var c=null,d=null,u=o,g=o=0,m=null;null!==u&&g<s.length;g++){u.index>g?(m=u,u=null):m=u.sibling;var h=p(i,u,s[g],l);if(null===h){null===u&&(u=m);break}e&&u&&null===h.alternate&&n(i,u),o=a(h,o,g),null===d?c=h:d.sibling=h,d=h,u=m}if(g===s.length)return t(i,u),ai&&Jr(i,g),c;if(null===u){for(;g<s.length;g++)null!==(u=f(i,s[g],l))&&(o=a(u,o,g),null===d?c=u:d.sibling=u,d=u);return ai&&Jr(i,g),c}for(u=r(u);g<s.length;g++)null!==(m=b(u,i,g,s[g],l))&&(e&&null!==m.alternate&&u.delete(null===m.key?g:m.key),o=a(m,o,g),null===d?c=m:d.sibling=m,d=m);return e&&u.forEach(function(e){return n(i,e)}),ai&&Jr(i,g),c}(l,c,d,u);if(_(d)){if("function"!==typeof(v=_(d)))throw Error(o(150));return function(i,s,l,c){if(null==l)throw Error(o(151));for(var d=null,u=null,g=s,m=s=0,h=null,y=l.next();null!==g&&!y.done;m++,y=l.next()){g.index>m?(h=g,g=null):h=g.sibling;var v=p(i,g,y.value,c);if(null===v){null===g&&(g=h);break}e&&g&&null===v.alternate&&n(i,g),s=a(v,s,m),null===u?d=v:u.sibling=v,u=v,g=h}if(y.done)return t(i,g),ai&&Jr(i,m),d;if(null===g){for(;!y.done;m++,y=l.next())null!==(y=f(i,y.value,c))&&(s=a(y,s,m),null===u?d=y:u.sibling=y,u=y);return ai&&Jr(i,m),d}for(g=r(g);!y.done;m++,y=l.next())null!==(y=b(g,i,m,y.value,c))&&(e&&null!==y.alternate&&g.delete(null===y.key?m:y.key),s=a(y,s,m),null===u?d=y:u.sibling=y,u=y);return e&&g.forEach(function(e){return n(i,e)}),ai&&Jr(i,m),d}(l,c,d=v.call(d),u)}if("function"===typeof d.then)return y(l,c,Zo(d),u);if(d.$$typeof===k)return y(l,c,ji(l,d),u);Jo(l,d)}return"string"===typeof d&&""!==d||"number"===typeof d||"bigint"===typeof d?(d=""+d,null!==c&&6===c.tag?(t(l,c.sibling),(u=i(c,d)).return=l,l=u):(t(l,c),(u=Wr(d,l.mode,u)).return=l,l=u),s(l)):t(l,c)}return function(e,n,t,r){try{$o=0;var i=y(e,n,t,r);return Qo=null,i}catch(o){if(o===qi||o===Gi)throw o;var a=Mr(29,o,null,e.mode);return a.lanes=r,a.return=e,a}}}var ts=ns(!0),rs=ns(!1),is=F(null),as=null;function os(e){var n=e.alternate;U(ds,1&ds.current),U(is,e),null===as&&(null===n||null!==pa.current||null!==n.memoizedState)&&(as=e)}function ss(e){if(22===e.tag){if(U(ds,ds.current),U(is,e),null===as){var n=e.alternate;null!==n&&null!==n.memoizedState&&(as=e)}}else ls()}function ls(){U(ds,ds.current),U(is,is.current)}function cs(e){H(is),as===e&&(as=null),H(ds)}var ds=F(0);function us(e){for(var n=e;null!==n;){if(13===n.tag){var t=n.memoizedState;if(null!==t&&(null===(t=t.dehydrated)||"$?"===t.data||hu(t)))return n}else if(19===n.tag&&void 0!==n.memoizedProps.revealOrder){if(0!==(128&n.flags))return n}else if(null!==n.child){n.child.return=n,n=n.child;continue}if(n===e)break;for(;null===n.sibling;){if(null===n.return||n.return===e)return null;n=n.return}n.sibling.return=n.return,n=n.sibling}return null}function fs(e,n,t,r){t=null===(t=t(r,n=e.memoizedState))||void 0===t?n:f({},n,t),e.memoizedState=t,0===e.lanes&&(e.updateQueue.baseState=t)}var ps={enqueueSetState:function(e,n,t){e=e._reactInternals;var r=Nc(),i=ia(r);i.payload=n,void 0!==t&&null!==t&&(i.callback=t),null!==(n=aa(e,i,r))&&(Mc(n,e,r),oa(n,e,r))},enqueueReplaceState:function(e,n,t){e=e._reactInternals;var r=Nc(),i=ia(r);i.tag=1,i.payload=n,void 0!==t&&null!==t&&(i.callback=t),null!==(n=aa(e,i,r))&&(Mc(n,e,r),oa(n,e,r))},enqueueForceUpdate:function(e,n){e=e._reactInternals;var t=Nc(),r=ia(t);r.tag=2,void 0!==n&&null!==n&&(r.callback=n),null!==(n=aa(e,r,t))&&(Mc(n,e,t),oa(n,e,t))}};function gs(e,n,t,r,i,a,o){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,a,o):!n.prototype||!n.prototype.isPureReactComponent||(!$t(t,r)||!$t(i,a))}function ms(e,n,t,r){e=n.state,"function"===typeof n.componentWillReceiveProps&&n.componentWillReceiveProps(t,r),"function"===typeof n.UNSAFE_componentWillReceiveProps&&n.UNSAFE_componentWillReceiveProps(t,r),n.state!==e&&ps.enqueueReplaceState(n,n.state,null)}function hs(e,n){var t=n;if("ref"in n)for(var r in t={},n)"ref"!==r&&(t[r]=n[r]);if(e=e.defaultProps)for(var i in t===n&&(t=f({},t)),e)void 0===t[i]&&(t[i]=e[i]);return t}var bs="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var n=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(n))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function ys(e){bs(e)}function vs(e){console.error(e)}function ws(e){bs(e)}function ks(e,n){try{(0,e.onUncaughtError)(n.value,{componentStack:n.stack})}catch(t){setTimeout(function(){throw t})}}function xs(e,n,t){try{(0,e.onCaughtError)(t.value,{componentStack:t.stack,errorBoundary:1===n.tag?n.stateNode:null})}catch(r){setTimeout(function(){throw r})}}function Ss(e,n,t){return(t=ia(t)).tag=3,t.payload={element:null},t.callback=function(){ks(e,n)},t}function Es(e){return(e=ia(e)).tag=3,e}function Cs(e,n,t,r){var i=t.type.getDerivedStateFromError;if("function"===typeof i){var a=r.value;e.payload=function(){return i(a)},e.callback=function(){xs(n,t,r)}}var o=t.stateNode;null!==o&&"function"===typeof o.componentDidCatch&&(e.callback=function(){xs(n,t,r),"function"!==typeof i&&(null===Ec?Ec=new Set([this]):Ec.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var zs=Error(o(461)),js=!1;function Ps(e,n,t,r){n.child=null===e?rs(n,null,t,r):ts(n,e.child,t,r)}function Rs(e,n,t,r,i){t=t.render;var a=n.ref;if("ref"in r){var o={};for(var s in r)"ref"!==s&&(o[s]=r[s])}else o=r;return Ci(n),r=Oa(e,n,t,o,a,i),s=La(),null===e||js?(ai&&s&&ni(n),n.flags|=1,Ps(e,n,r,i),n.child):(Ma(e,n,i),Qs(e,n,i))}function _s(e,n,t,r,i){if(null===e){var a=t.type;return"function"!==typeof a||Ir(a)||void 0!==a.defaultProps||null!==t.compare?((e=Hr(t.type,null,r,n,n.mode,i)).ref=n.ref,e.return=n,n.child=e):(n.tag=15,n.type=a,Os(e,n,a,r,i))}if(a=e.child,!$s(e,i)){var o=a.memoizedProps;if((t=null!==(t=t.compare)?t:$t)(o,r)&&e.ref===n.ref)return Qs(e,n,i)}return n.flags|=1,(e=Dr(a,r)).ref=n.ref,e.return=n,n.child=e}function Os(e,n,t,r,i){if(null!==e){var a=e.memoizedProps;if($t(a,r)&&e.ref===n.ref){if(js=!1,n.pendingProps=r=a,!$s(e,i))return n.lanes=e.lanes,Qs(e,n,i);0!==(131072&e.flags)&&(js=!0)}}return Ls(e,n,t,r,i)}function Ts(e,n,t){var r=n.pendingProps,i=r.children,a=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(0!==(128&n.flags)){if(r=null!==a?a.baseLanes|t:t,null!==e){for(i=n.child=e.child,a=0;null!==i;)a=a|i.lanes|i.childLanes,i=i.sibling;n.childLanes=a&~r}else n.childLanes=0,n.child=null;return As(e,n,r,t)}if(0===(536870912&t))return n.lanes=n.childLanes=536870912,As(e,n,null!==a?a.baseLanes|t:t,t);n.memoizedState={baseLanes:0,cachePool:null},null!==e&&Bi(0,null!==a?a.cachePool:null),null!==a?ma(n,a):ha(),ss(n)}else null!==a?(Bi(0,a.cachePool),ma(n,a),ls(),n.memoizedState=null):(null!==e&&Bi(0,null),ha(),ls());return Ps(e,n,i,t),n.child}function As(e,n,t,r){var i=Wi();return i=null===i?null:{parent:Ti._currentValue,pool:i},n.memoizedState={baseLanes:t,cachePool:i},null!==e&&Bi(0,null),ha(),ss(n),null!==e&&Si(e,n,r,!0),null}function Ns(e,n){var t=n.ref;if(null===t)null!==e&&null!==e.ref&&(n.flags|=4194816);else{if("function"!==typeof t&&"object"!==typeof t)throw Error(o(284));null!==e&&e.ref===t||(n.flags|=4194816)}}function Ls(e,n,t,r,i){return Ci(n),t=Oa(e,n,t,r,void 0,i),r=La(),null===e||js?(ai&&r&&ni(n),n.flags|=1,Ps(e,n,t,i),n.child):(Ma(e,n,i),Qs(e,n,i))}function Ms(e,n,t,r,i,a){return Ci(n),n.updateQueue=null,t=Aa(n,r,t,i),Ta(e),r=La(),null===e||js?(ai&&r&&ni(n),n.flags|=1,Ps(e,n,t,a),n.child):(Ma(e,n,a),Qs(e,n,a))}function Is(e,n,t,r,i){if(Ci(n),null===n.stateNode){var a=Nr,o=t.contextType;"object"===typeof o&&null!==o&&(a=zi(o)),a=new t(r,a),n.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,a.updater=ps,n.stateNode=a,a._reactInternals=n,(a=n.stateNode).props=r,a.state=n.memoizedState,a.refs={},ta(n),o=t.contextType,a.context="object"===typeof o&&null!==o?zi(o):Nr,a.state=n.memoizedState,"function"===typeof(o=t.getDerivedStateFromProps)&&(fs(n,t,o,r),a.state=n.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(o=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),o!==a.state&&ps.enqueueReplaceState(a,a.state,null),da(n,r,a,i),ca(),a.state=n.memoizedState),"function"===typeof a.componentDidMount&&(n.flags|=4194308),r=!0}else if(null===e){a=n.stateNode;var s=n.memoizedProps,l=hs(t,s);a.props=l;var c=a.context,d=t.contextType;o=Nr,"object"===typeof d&&null!==d&&(o=zi(d));var u=t.getDerivedStateFromProps;d="function"===typeof u||"function"===typeof a.getSnapshotBeforeUpdate,s=n.pendingProps!==s,d||"function"!==typeof a.UNSAFE_componentWillReceiveProps&&"function"!==typeof a.componentWillReceiveProps||(s||c!==o)&&ms(n,a,r,o),na=!1;var f=n.memoizedState;a.state=f,da(n,r,a,i),ca(),c=n.memoizedState,s||f!==c||na?("function"===typeof u&&(fs(n,t,u,r),c=n.memoizedState),(l=na||gs(n,t,l,r,f,c,o))?(d||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||("function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount()),"function"===typeof a.componentDidMount&&(n.flags|=4194308)):("function"===typeof a.componentDidMount&&(n.flags|=4194308),n.memoizedProps=r,n.memoizedState=c),a.props=r,a.state=c,a.context=o,r=l):("function"===typeof a.componentDidMount&&(n.flags|=4194308),r=!1)}else{a=n.stateNode,ra(e,n),d=hs(t,o=n.memoizedProps),a.props=d,u=n.pendingProps,f=a.context,c=t.contextType,l=Nr,"object"===typeof c&&null!==c&&(l=zi(c)),(c="function"===typeof(s=t.getDerivedStateFromProps)||"function"===typeof a.getSnapshotBeforeUpdate)||"function"!==typeof a.UNSAFE_componentWillReceiveProps&&"function"!==typeof a.componentWillReceiveProps||(o!==u||f!==l)&&ms(n,a,r,l),na=!1,f=n.memoizedState,a.state=f,da(n,r,a,i),ca();var p=n.memoizedState;o!==u||f!==p||na||null!==e&&null!==e.dependencies&&Ei(e.dependencies)?("function"===typeof s&&(fs(n,t,s,r),p=n.memoizedState),(d=na||gs(n,t,d,r,f,p,l)||null!==e&&null!==e.dependencies&&Ei(e.dependencies))?(c||"function"!==typeof a.UNSAFE_componentWillUpdate&&"function"!==typeof a.componentWillUpdate||("function"===typeof a.componentWillUpdate&&a.componentWillUpdate(r,p,l),"function"===typeof a.UNSAFE_componentWillUpdate&&a.UNSAFE_componentWillUpdate(r,p,l)),"function"===typeof a.componentDidUpdate&&(n.flags|=4),"function"===typeof a.getSnapshotBeforeUpdate&&(n.flags|=1024)):("function"!==typeof a.componentDidUpdate||o===e.memoizedProps&&f===e.memoizedState||(n.flags|=4),"function"!==typeof a.getSnapshotBeforeUpdate||o===e.memoizedProps&&f===e.memoizedState||(n.flags|=1024),n.memoizedProps=r,n.memoizedState=p),a.props=r,a.state=p,a.context=l,r=d):("function"!==typeof a.componentDidUpdate||o===e.memoizedProps&&f===e.memoizedState||(n.flags|=4),"function"!==typeof a.getSnapshotBeforeUpdate||o===e.memoizedProps&&f===e.memoizedState||(n.flags|=1024),r=!1)}return a=r,Ns(e,n),r=0!==(128&n.flags),a||r?(a=n.stateNode,t=r&&"function"!==typeof t.getDerivedStateFromError?null:a.render(),n.flags|=1,null!==e&&r?(n.child=ts(n,e.child,null,i),n.child=ts(n,null,t,i)):Ps(e,n,t,i),n.memoizedState=a.state,e=n.child):e=Qs(e,n,i),e}function Ds(e,n,t,r){return pi(),n.flags|=256,Ps(e,n,t,r),n.child}var Fs={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Hs(e){return{baseLanes:e,cachePool:Vi()}}function Us(e,n,t){return e=null!==e?e.childLanes&~t:0,n&&(e|=hc),e}function Ws(e,n,t){var r,i=n.pendingProps,a=!1,s=0!==(128&n.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&0!==(2&ds.current)),r&&(a=!0,n.flags&=-129),r=0!==(32&n.flags),n.flags&=-33,null===e){if(ai){if(a?os(n):ls(),ai){var l,c=ii;if(l=c){e:{for(l=c,c=si;8!==l.nodeType;){if(!c){c=null;break e}if(null===(l=bu(l.nextSibling))){c=null;break e}}c=l}null!==c?(n.memoizedState={dehydrated:c,treeContext:null!==$r?{id:Zr,overflow:Xr}:null,retryLane:536870912,hydrationErrors:null},(l=Mr(18,null,null,0)).stateNode=c,l.return=n,n.child=l,ri=n,ii=null,l=!0):l=!1}l||ci(n)}if(null!==(c=n.memoizedState)&&null!==(c=c.dehydrated))return hu(c)?n.lanes=32:n.lanes=536870912,null;cs(n)}return c=i.children,i=i.fallback,a?(ls(),c=Vs({mode:"hidden",children:c},a=n.mode),i=Ur(i,a,t,null),c.return=n,i.return=n,c.sibling=i,n.child=c,(a=n.child).memoizedState=Hs(t),a.childLanes=Us(e,r,t),n.memoizedState=Fs,i):(os(n),Bs(n,c))}if(null!==(l=e.memoizedState)&&null!==(c=l.dehydrated)){if(s)256&n.flags?(os(n),n.flags&=-257,n=qs(e,n,t)):null!==n.memoizedState?(ls(),n.child=e.child,n.flags|=128,n=null):(ls(),a=i.fallback,c=n.mode,i=Vs({mode:"visible",children:i.children},c),(a=Ur(a,c,t,null)).flags|=2,i.return=n,a.return=n,i.sibling=a,n.child=i,ts(n,e.child,null,t),(i=n.child).memoizedState=Hs(t),i.childLanes=Us(e,r,t),n.memoizedState=Fs,n=a);else if(os(n),hu(c)){if(r=c.nextSibling&&c.nextSibling.dataset)var d=r.dgst;r=d,(i=Error(o(419))).stack="",i.digest=r,mi({value:i,source:null,stack:null}),n=qs(e,n,t)}else if(js||Si(e,n,t,!1),r=0!==(t&e.childLanes),js||r){if(null!==(r=rc)&&(0!==(i=0!==((i=0!==(42&(i=t&-t))?1:Pe(i))&(r.suspendedLanes|t))?0:i)&&i!==l.retryLane))throw l.retryLane=i,Or(e,i),Mc(r,e,i),zs;"$?"===c.data||Gc(),n=qs(e,n,t)}else"$?"===c.data?(n.flags|=192,n.child=e.child,n=null):(e=l.treeContext,ii=bu(c.nextSibling),ri=n,ai=!0,oi=null,si=!1,null!==e&&(Kr[Qr++]=Zr,Kr[Qr++]=Xr,Kr[Qr++]=$r,Zr=e.id,Xr=e.overflow,$r=n),(n=Bs(n,i.children)).flags|=4096);return n}return a?(ls(),a=i.fallback,c=n.mode,d=(l=e.child).sibling,(i=Dr(l,{mode:"hidden",children:i.children})).subtreeFlags=65011712&l.subtreeFlags,null!==d?a=Dr(d,a):(a=Ur(a,c,t,null)).flags|=2,a.return=n,i.return=n,i.sibling=a,n.child=i,i=a,a=n.child,null===(c=e.child.memoizedState)?c=Hs(t):(null!==(l=c.cachePool)?(d=Ti._currentValue,l=l.parent!==d?{parent:d,pool:d}:l):l=Vi(),c={baseLanes:c.baseLanes|t,cachePool:l}),a.memoizedState=c,a.childLanes=Us(e,r,t),n.memoizedState=Fs,i):(os(n),e=(t=e.child).sibling,(t=Dr(t,{mode:"visible",children:i.children})).return=n,t.sibling=null,null!==e&&(null===(r=n.deletions)?(n.deletions=[e],n.flags|=16):r.push(e)),n.child=t,n.memoizedState=null,t)}function Bs(e,n){return(n=Vs({mode:"visible",children:n},e.mode)).return=e,e.child=n}function Vs(e,n){return(e=Mr(22,e,null,n)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function qs(e,n,t){return ts(n,e.child,null,t),(e=Bs(n,n.pendingProps.children)).flags|=2,n.memoizedState=null,e}function Ys(e,n,t){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n),ki(e.return,n,t)}function Gs(e,n,t,r,i){var a=e.memoizedState;null===a?e.memoizedState={isBackwards:n,rendering:null,renderingStartTime:0,last:r,tail:t,tailMode:i}:(a.isBackwards=n,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=t,a.tailMode=i)}function Ks(e,n,t){var r=n.pendingProps,i=r.revealOrder,a=r.tail;if(Ps(e,n,r.children,t),0!==(2&(r=ds.current)))r=1&r|2,n.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=n.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Ys(e,t,n);else if(19===e.tag)Ys(e,t,n);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===n)break e;for(;null===e.sibling;){if(null===e.return||e.return===n)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(U(ds,r),i){case"forwards":for(t=n.child,i=null;null!==t;)null!==(e=t.alternate)&&null===us(e)&&(i=t),t=t.sibling;null===(t=i)?(i=n.child,n.child=null):(i=t.sibling,t.sibling=null),Gs(n,!1,i,t,a);break;case"backwards":for(t=null,i=n.child,n.child=null;null!==i;){if(null!==(e=i.alternate)&&null===us(e)){n.child=i;break}e=i.sibling,i.sibling=t,t=i,i=e}Gs(n,!0,t,null,a);break;case"together":Gs(n,!1,null,null,void 0);break;default:n.memoizedState=null}return n.child}function Qs(e,n,t){if(null!==e&&(n.dependencies=e.dependencies),pc|=n.lanes,0===(t&n.childLanes)){if(null===e)return null;if(Si(e,n,t,!1),0===(t&n.childLanes))return null}if(null!==e&&n.child!==e.child)throw Error(o(153));if(null!==n.child){for(t=Dr(e=n.child,e.pendingProps),n.child=t,t.return=n;null!==e.sibling;)e=e.sibling,(t=t.sibling=Dr(e,e.pendingProps)).return=n;t.sibling=null}return n.child}function $s(e,n){return 0!==(e.lanes&n)||!(null===(e=e.dependencies)||!Ei(e))}function Zs(e,n,t){if(null!==e)if(e.memoizedProps!==n.pendingProps)js=!0;else{if(!$s(e,t)&&0===(128&n.flags))return js=!1,function(e,n,t){switch(n.tag){case 3:Y(n,n.stateNode.containerInfo),vi(0,Ti,e.memoizedState.cache),pi();break;case 27:case 5:K(n);break;case 4:Y(n,n.stateNode.containerInfo);break;case 10:vi(0,n.type,n.memoizedProps.value);break;case 13:var r=n.memoizedState;if(null!==r)return null!==r.dehydrated?(os(n),n.flags|=128,null):0!==(t&n.child.childLanes)?Ws(e,n,t):(os(n),null!==(e=Qs(e,n,t))?e.sibling:null);os(n);break;case 19:var i=0!==(128&e.flags);if((r=0!==(t&n.childLanes))||(Si(e,n,t,!1),r=0!==(t&n.childLanes)),i){if(r)return Ks(e,n,t);n.flags|=128}if(null!==(i=n.memoizedState)&&(i.rendering=null,i.tail=null,i.lastEffect=null),U(ds,ds.current),r)break;return null;case 22:case 23:return n.lanes=0,Ts(e,n,t);case 24:vi(0,Ti,e.memoizedState.cache)}return Qs(e,n,t)}(e,n,t);js=0!==(131072&e.flags)}else js=!1,ai&&0!==(1048576&n.flags)&&ei(n,Gr,n.index);switch(n.lanes=0,n.tag){case 16:e:{e=n.pendingProps;var r=n.elementType,i=r._init;if(r=i(r._payload),n.type=r,"function"!==typeof r){if(void 0!==r&&null!==r){if((i=r.$$typeof)===x){n.tag=11,n=Rs(null,n,r,e,t);break e}if(i===C){n.tag=14,n=_s(null,n,r,e,t);break e}}throw n=T(r)||r,Error(o(306,n,""))}Ir(r)?(e=hs(r,e),n.tag=1,n=Is(null,n,r,e,t)):(n.tag=0,n=Ls(null,n,r,e,t))}return n;case 0:return Ls(e,n,n.type,n.pendingProps,t);case 1:return Is(e,n,r=n.type,i=hs(r,n.pendingProps),t);case 3:e:{if(Y(n,n.stateNode.containerInfo),null===e)throw Error(o(387));r=n.pendingProps;var a=n.memoizedState;i=a.element,ra(e,n),da(n,r,null,t);var s=n.memoizedState;if(r=s.cache,vi(0,Ti,r),r!==a.cache&&xi(n,[Ti],t,!0),ca(),r=s.element,a.isDehydrated){if(a={element:r,isDehydrated:!1,cache:s.cache},n.updateQueue.baseState=a,n.memoizedState=a,256&n.flags){n=Ds(e,n,r,t);break e}if(r!==i){mi(i=Er(Error(o(424)),n)),n=Ds(e,n,r,t);break e}if(9===(e=n.stateNode.containerInfo).nodeType)e=e.body;else e="HTML"===e.nodeName?e.ownerDocument.body:e;for(ii=bu(e.firstChild),ri=n,ai=!0,oi=null,si=!0,t=rs(n,null,r,t),n.child=t;t;)t.flags=-3&t.flags|4096,t=t.sibling}else{if(pi(),r===i){n=Qs(e,n,t);break e}Ps(e,n,r,t)}n=n.child}return n;case 26:return Ns(e,n),null===e?(t=Pu(n.type,null,n.pendingProps,null))?n.memoizedState=t:ai||(t=n.type,e=n.pendingProps,(r=ru(V.current).createElement(t))[Te]=n,r[Ae]=e,eu(r,t,e),qe(r),n.stateNode=r):n.memoizedState=Pu(n.type,e.memoizedProps,n.pendingProps,e.memoizedState),null;case 27:return K(n),null===e&&ai&&(r=n.stateNode=wu(n.type,n.pendingProps,V.current),ri=n,si=!0,i=ii,pu(n.type)?(yu=i,ii=bu(r.firstChild)):ii=i),Ps(e,n,n.pendingProps.children,t),Ns(e,n),null===e&&(n.flags|=4194304),n.child;case 5:return null===e&&ai&&((i=r=ii)&&(null!==(r=function(e,n,t,r){for(;1===e.nodeType;){var i=t;if(e.nodeName.toLowerCase()!==n.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[Fe])switch(n){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(a=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(a!==i.rel||e.getAttribute("href")!==(null==i.href||""===i.href?null:i.href)||e.getAttribute("crossorigin")!==(null==i.crossOrigin?null:i.crossOrigin)||e.getAttribute("title")!==(null==i.title?null:i.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((a=e.getAttribute("src"))!==(null==i.src?null:i.src)||e.getAttribute("type")!==(null==i.type?null:i.type)||e.getAttribute("crossorigin")!==(null==i.crossOrigin?null:i.crossOrigin))&&a&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==n||"hidden"!==e.type)return e;var a=null==i.name?null:""+i.name;if("hidden"===i.type&&e.getAttribute("name")===a)return e}if(null===(e=bu(e.nextSibling)))break}return null}(r,n.type,n.pendingProps,si))?(n.stateNode=r,ri=n,ii=bu(r.firstChild),si=!1,i=!0):i=!1),i||ci(n)),K(n),i=n.type,a=n.pendingProps,s=null!==e?e.memoizedProps:null,r=a.children,ou(i,a)?r=null:null!==s&&ou(i,s)&&(n.flags|=32),null!==n.memoizedState&&(i=Oa(e,n,Na,null,null,t),Ku._currentValue=i),Ns(e,n),Ps(e,n,r,t),n.child;case 6:return null===e&&ai&&((e=t=ii)&&(null!==(t=function(e,n,t){if(""===n)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!t)return null;if(null===(e=bu(e.nextSibling)))return null}return e}(t,n.pendingProps,si))?(n.stateNode=t,ri=n,ii=null,e=!0):e=!1),e||ci(n)),null;case 13:return Ws(e,n,t);case 4:return Y(n,n.stateNode.containerInfo),r=n.pendingProps,null===e?n.child=ts(n,null,r,t):Ps(e,n,r,t),n.child;case 11:return Rs(e,n,n.type,n.pendingProps,t);case 7:return Ps(e,n,n.pendingProps,t),n.child;case 8:case 12:return Ps(e,n,n.pendingProps.children,t),n.child;case 10:return r=n.pendingProps,vi(0,n.type,r.value),Ps(e,n,r.children,t),n.child;case 9:return i=n.type._context,r=n.pendingProps.children,Ci(n),r=r(i=zi(i)),n.flags|=1,Ps(e,n,r,t),n.child;case 14:return _s(e,n,n.type,n.pendingProps,t);case 15:return Os(e,n,n.type,n.pendingProps,t);case 19:return Ks(e,n,t);case 31:return r=n.pendingProps,t=n.mode,r={mode:r.mode,children:r.children},null===e?((t=Vs(r,t)).ref=n.ref,n.child=t,t.return=n,n=t):((t=Dr(e.child,r)).ref=n.ref,n.child=t,t.return=n,n=t),n;case 22:return Ts(e,n,t);case 24:return Ci(n),r=zi(Ti),null===e?(null===(i=Wi())&&(i=rc,a=Ai(),i.pooledCache=a,a.refCount++,null!==a&&(i.pooledCacheLanes|=t),i=a),n.memoizedState={parent:r,cache:i},ta(n),vi(0,Ti,i)):(0!==(e.lanes&t)&&(ra(e,n),da(n,null,null,t),ca()),i=e.memoizedState,a=n.memoizedState,i.parent!==r?(i={parent:r,cache:r},n.memoizedState=i,0===n.lanes&&(n.memoizedState=n.updateQueue.baseState=i),vi(0,Ti,r)):(r=a.cache,vi(0,Ti,r),r!==i.cache&&xi(n,[Ti],t,!0))),Ps(e,n,n.pendingProps.children,t),n.child;case 29:throw n.pendingProps}throw Error(o(156,n.tag))}function Xs(e){e.flags|=4}function Js(e,n){if("stylesheet"!==n.type||0!==(4&n.state.loading))e.flags&=-16777217;else if(e.flags|=16777216,!Uu(n)){if(null!==(n=is.current)&&((4194048&ac)===ac?null!==as:(62914560&ac)!==ac&&0===(536870912&ac)||n!==as))throw Xi=Ki,Yi;e.flags|=8192}}function el(e,n){null!==n&&(e.flags|=4),16384&e.flags&&(n=22!==e.tag?Se():536870912,e.lanes|=n,bc|=n)}function nl(e,n){if(!ai)switch(e.tailMode){case"hidden":n=e.tail;for(var t=null;null!==n;)null!==n.alternate&&(t=n),n=n.sibling;null===t?e.tail=null:t.sibling=null;break;case"collapsed":t=e.tail;for(var r=null;null!==t;)null!==t.alternate&&(r=t),t=t.sibling;null===r?n||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function tl(e){var n=null!==e.alternate&&e.alternate.child===e.child,t=0,r=0;if(n)for(var i=e.child;null!==i;)t|=i.lanes|i.childLanes,r|=65011712&i.subtreeFlags,r|=65011712&i.flags,i.return=e,i=i.sibling;else for(i=e.child;null!==i;)t|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=t,n}function rl(e,n,t){var r=n.pendingProps;switch(ti(n),n.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return tl(n),null;case 3:return t=n.stateNode,r=null,null!==e&&(r=e.memoizedState.cache),n.memoizedState.cache!==r&&(n.flags|=2048),wi(Ti),G(),t.pendingContext&&(t.context=t.pendingContext,t.pendingContext=null),null!==e&&null!==e.child||(fi(n)?Xs(n):null===e||e.memoizedState.isDehydrated&&0===(256&n.flags)||(n.flags|=1024,gi())),tl(n),null;case 26:return t=n.memoizedState,null===e?(Xs(n),null!==t?(tl(n),Js(n,t)):(tl(n),n.flags&=-16777217)):t?t!==e.memoizedState?(Xs(n),tl(n),Js(n,t)):(tl(n),n.flags&=-16777217):(e.memoizedProps!==r&&Xs(n),tl(n),n.flags&=-16777217),null;case 27:Q(n),t=V.current;var i=n.type;if(null!==e&&null!=n.stateNode)e.memoizedProps!==r&&Xs(n);else{if(!r){if(null===n.stateNode)throw Error(o(166));return tl(n),null}e=W.current,fi(n)?di(n):(e=wu(i,r,t),n.stateNode=e,Xs(n))}return tl(n),null;case 5:if(Q(n),t=n.type,null!==e&&null!=n.stateNode)e.memoizedProps!==r&&Xs(n);else{if(!r){if(null===n.stateNode)throw Error(o(166));return tl(n),null}if(e=W.current,fi(n))di(n);else{switch(i=ru(V.current),e){case 1:e=i.createElementNS("http://www.w3.org/2000/svg",t);break;case 2:e=i.createElementNS("http://www.w3.org/1998/Math/MathML",t);break;default:switch(t){case"svg":e=i.createElementNS("http://www.w3.org/2000/svg",t);break;case"math":e=i.createElementNS("http://www.w3.org/1998/Math/MathML",t);break;case"script":(e=i.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"===typeof r.is?i.createElement("select",{is:r.is}):i.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e="string"===typeof r.is?i.createElement(t,{is:r.is}):i.createElement(t)}}e[Te]=n,e[Ae]=r;e:for(i=n.child;null!==i;){if(5===i.tag||6===i.tag)e.appendChild(i.stateNode);else if(4!==i.tag&&27!==i.tag&&null!==i.child){i.child.return=i,i=i.child;continue}if(i===n)break e;for(;null===i.sibling;){if(null===i.return||i.return===n)break e;i=i.return}i.sibling.return=i.return,i=i.sibling}n.stateNode=e;e:switch(eu(e,t,r),t){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Xs(n)}}return tl(n),n.flags&=-16777217,null;case 6:if(e&&null!=n.stateNode)e.memoizedProps!==r&&Xs(n);else{if("string"!==typeof r&&null===n.stateNode)throw Error(o(166));if(e=V.current,fi(n)){if(e=n.stateNode,t=n.memoizedProps,r=null,null!==(i=ri))switch(i.tag){case 27:case 5:r=i.memoizedProps}e[Te]=n,(e=!!(e.nodeValue===t||null!==r&&!0===r.suppressHydrationWarning||$d(e.nodeValue,t)))||ci(n)}else(e=ru(e).createTextNode(r))[Te]=n,n.stateNode=e}return tl(n),null;case 13:if(r=n.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(i=fi(n),null!==r&&null!==r.dehydrated){if(null===e){if(!i)throw Error(o(318));if(!(i=null!==(i=n.memoizedState)?i.dehydrated:null))throw Error(o(317));i[Te]=n}else pi(),0===(128&n.flags)&&(n.memoizedState=null),n.flags|=4;tl(n),i=!1}else i=gi(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=i),i=!0;if(!i)return 256&n.flags?(cs(n),n):(cs(n),null)}if(cs(n),0!==(128&n.flags))return n.lanes=t,n;if(t=null!==r,e=null!==e&&null!==e.memoizedState,t){i=null,null!==(r=n.child).alternate&&null!==r.alternate.memoizedState&&null!==r.alternate.memoizedState.cachePool&&(i=r.alternate.memoizedState.cachePool.pool);var a=null;null!==r.memoizedState&&null!==r.memoizedState.cachePool&&(a=r.memoizedState.cachePool.pool),a!==i&&(r.flags|=2048)}return t!==e&&t&&(n.child.flags|=8192),el(n,n.updateQueue),tl(n),null;case 4:return G(),null===e&&Hd(n.stateNode.containerInfo),tl(n),null;case 10:return wi(n.type),tl(n),null;case 19:if(H(ds),null===(i=n.memoizedState))return tl(n),null;if(r=0!==(128&n.flags),null===(a=i.rendering))if(r)nl(i,!1);else{if(0!==fc||null!==e&&0!==(128&e.flags))for(e=n.child;null!==e;){if(null!==(a=us(e))){for(n.flags|=128,nl(i,!1),e=a.updateQueue,n.updateQueue=e,el(n,e),n.subtreeFlags=0,e=t,t=n.child;null!==t;)Fr(t,e),t=t.sibling;return U(ds,1&ds.current|2),n.child}e=e.sibling}null!==i.tail&&ne()>xc&&(n.flags|=128,r=!0,nl(i,!1),n.lanes=4194304)}else{if(!r)if(null!==(e=us(a))){if(n.flags|=128,r=!0,e=e.updateQueue,n.updateQueue=e,el(n,e),nl(i,!0),null===i.tail&&"hidden"===i.tailMode&&!a.alternate&&!ai)return tl(n),null}else 2*ne()-i.renderingStartTime>xc&&536870912!==t&&(n.flags|=128,r=!0,nl(i,!1),n.lanes=4194304);i.isBackwards?(a.sibling=n.child,n.child=a):(null!==(e=i.last)?e.sibling=a:n.child=a,i.last=a)}return null!==i.tail?(n=i.tail,i.rendering=n,i.tail=n.sibling,i.renderingStartTime=ne(),n.sibling=null,e=ds.current,U(ds,r?1&e|2:1&e),n):(tl(n),null);case 22:case 23:return cs(n),ba(),r=null!==n.memoizedState,null!==e?null!==e.memoizedState!==r&&(n.flags|=8192):r&&(n.flags|=8192),r?0!==(536870912&t)&&0===(128&n.flags)&&(tl(n),6&n.subtreeFlags&&(n.flags|=8192)):tl(n),null!==(t=n.updateQueue)&&el(n,t.retryQueue),t=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(t=e.memoizedState.cachePool.pool),r=null,null!==n.memoizedState&&null!==n.memoizedState.cachePool&&(r=n.memoizedState.cachePool.pool),r!==t&&(n.flags|=2048),null!==e&&H(Ui),null;case 24:return t=null,null!==e&&(t=e.memoizedState.cache),n.memoizedState.cache!==t&&(n.flags|=2048),wi(Ti),tl(n),null;case 25:case 30:return null}throw Error(o(156,n.tag))}function il(e,n){switch(ti(n),n.tag){case 1:return 65536&(e=n.flags)?(n.flags=-65537&e|128,n):null;case 3:return wi(Ti),G(),0!==(65536&(e=n.flags))&&0===(128&e)?(n.flags=-65537&e|128,n):null;case 26:case 27:case 5:return Q(n),null;case 13:if(cs(n),null!==(e=n.memoizedState)&&null!==e.dehydrated){if(null===n.alternate)throw Error(o(340));pi()}return 65536&(e=n.flags)?(n.flags=-65537&e|128,n):null;case 19:return H(ds),null;case 4:return G(),null;case 10:return wi(n.type),null;case 22:case 23:return cs(n),ba(),null!==e&&H(Ui),65536&(e=n.flags)?(n.flags=-65537&e|128,n):null;case 24:return wi(Ti),null;default:return null}}function al(e,n){switch(ti(n),n.tag){case 3:wi(Ti),G();break;case 26:case 27:case 5:Q(n);break;case 4:G();break;case 13:cs(n);break;case 19:H(ds);break;case 10:wi(n.type);break;case 22:case 23:cs(n),ba(),null!==e&&H(Ui);break;case 24:wi(Ti)}}function ol(e,n){try{var t=n.updateQueue,r=null!==t?t.lastEffect:null;if(null!==r){var i=r.next;t=i;do{if((t.tag&e)===e){r=void 0;var a=t.create,o=t.inst;r=a(),o.destroy=r}t=t.next}while(t!==i)}}catch(s){dd(n,n.return,s)}}function sl(e,n,t){try{var r=n.updateQueue,i=null!==r?r.lastEffect:null;if(null!==i){var a=i.next;r=a;do{if((r.tag&e)===e){var o=r.inst,s=o.destroy;if(void 0!==s){o.destroy=void 0,i=n;var l=t,c=s;try{c()}catch(d){dd(i,l,d)}}}r=r.next}while(r!==a)}}catch(d){dd(n,n.return,d)}}function ll(e){var n=e.updateQueue;if(null!==n){var t=e.stateNode;try{fa(n,t)}catch(r){dd(e,e.return,r)}}}function cl(e,n,t){t.props=hs(e.type,e.memoizedProps),t.state=e.memoizedState;try{t.componentWillUnmount()}catch(r){dd(e,n,r)}}function dl(e,n){try{var t=e.ref;if(null!==t){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;default:r=e.stateNode}"function"===typeof t?e.refCleanup=t(r):t.current=r}}catch(i){dd(e,n,i)}}function ul(e,n){var t=e.ref,r=e.refCleanup;if(null!==t)if("function"===typeof r)try{r()}catch(i){dd(e,n,i)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"===typeof t)try{t(null)}catch(a){dd(e,n,a)}else t.current=null}function fl(e){var n=e.type,t=e.memoizedProps,r=e.stateNode;try{e:switch(n){case"button":case"input":case"select":case"textarea":t.autoFocus&&r.focus();break e;case"img":t.src?r.src=t.src:t.srcSet&&(r.srcset=t.srcSet)}}catch(i){dd(e,e.return,i)}}function pl(e,n,t){try{var r=e.stateNode;!function(e,n,t,r){switch(n){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,a=null,s=null,l=null,c=null,d=null,u=null;for(g in t){var f=t[g];if(t.hasOwnProperty(g)&&null!=f)switch(g){case"checked":case"value":break;case"defaultValue":c=f;default:r.hasOwnProperty(g)||Xd(e,n,g,null,r,f)}}for(var p in r){var g=r[p];if(f=t[p],r.hasOwnProperty(p)&&(null!=g||null!=f))switch(p){case"type":a=g;break;case"name":i=g;break;case"checked":d=g;break;case"defaultChecked":u=g;break;case"value":s=g;break;case"defaultValue":l=g;break;case"children":case"dangerouslySetInnerHTML":if(null!=g)throw Error(o(137,n));break;default:g!==f&&Xd(e,n,p,g,r,f)}}return void bn(e,s,l,c,d,u,a,i);case"select":for(a in g=s=l=p=null,t)if(c=t[a],t.hasOwnProperty(a)&&null!=c)switch(a){case"value":break;case"multiple":g=c;default:r.hasOwnProperty(a)||Xd(e,n,a,null,r,c)}for(i in r)if(a=r[i],c=t[i],r.hasOwnProperty(i)&&(null!=a||null!=c))switch(i){case"value":p=a;break;case"defaultValue":l=a;break;case"multiple":s=a;default:a!==c&&Xd(e,n,i,a,r,c)}return n=l,t=s,r=g,void(null!=p?wn(e,!!t,p,!1):!!r!==!!t&&(null!=n?wn(e,!!t,n,!0):wn(e,!!t,t?[]:"",!1)));case"textarea":for(l in g=p=null,t)if(i=t[l],t.hasOwnProperty(l)&&null!=i&&!r.hasOwnProperty(l))switch(l){case"value":case"children":break;default:Xd(e,n,l,null,r,i)}for(s in r)if(i=r[s],a=t[s],r.hasOwnProperty(s)&&(null!=i||null!=a))switch(s){case"value":p=i;break;case"defaultValue":g=i;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=i)throw Error(o(91));break;default:i!==a&&Xd(e,n,s,i,r,a)}return void kn(e,p,g);case"option":for(var m in t)if(p=t[m],t.hasOwnProperty(m)&&null!=p&&!r.hasOwnProperty(m))if("selected"===m)e.selected=!1;else Xd(e,n,m,null,r,p);for(c in r)if(p=r[c],g=t[c],r.hasOwnProperty(c)&&p!==g&&(null!=p||null!=g))if("selected"===c)e.selected=p&&"function"!==typeof p&&"symbol"!==typeof p;else Xd(e,n,c,p,r,g);return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var h in t)p=t[h],t.hasOwnProperty(h)&&null!=p&&!r.hasOwnProperty(h)&&Xd(e,n,h,null,r,p);for(d in r)if(p=r[d],g=t[d],r.hasOwnProperty(d)&&p!==g&&(null!=p||null!=g))switch(d){case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(o(137,n));break;default:Xd(e,n,d,p,r,g)}return;default:if(jn(n)){for(var b in t)p=t[b],t.hasOwnProperty(b)&&void 0!==p&&!r.hasOwnProperty(b)&&Jd(e,n,b,void 0,r,p);for(u in r)p=r[u],g=t[u],!r.hasOwnProperty(u)||p===g||void 0===p&&void 0===g||Jd(e,n,u,p,r,g);return}}for(var y in t)p=t[y],t.hasOwnProperty(y)&&null!=p&&!r.hasOwnProperty(y)&&Xd(e,n,y,null,r,p);for(f in r)p=r[f],g=t[f],!r.hasOwnProperty(f)||p===g||null==p&&null==g||Xd(e,n,f,p,r,g)}(r,e.type,t,n),r[Ae]=n}catch(i){dd(e,e.return,i)}}function gl(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&pu(e.type)||4===e.tag}function ml(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||gl(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&pu(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function hl(e,n,t){var r=e.tag;if(5===r||6===r)e=e.stateNode,n?(9===t.nodeType?t.body:"HTML"===t.nodeName?t.ownerDocument.body:t).insertBefore(e,n):((n=9===t.nodeType?t.body:"HTML"===t.nodeName?t.ownerDocument.body:t).appendChild(e),null!==(t=t._reactRootContainer)&&void 0!==t||null!==n.onclick||(n.onclick=Zd));else if(4!==r&&(27===r&&pu(e.type)&&(t=e.stateNode,n=null),null!==(e=e.child)))for(hl(e,n,t),e=e.sibling;null!==e;)hl(e,n,t),e=e.sibling}function bl(e,n,t){var r=e.tag;if(5===r||6===r)e=e.stateNode,n?t.insertBefore(e,n):t.appendChild(e);else if(4!==r&&(27===r&&pu(e.type)&&(t=e.stateNode),null!==(e=e.child)))for(bl(e,n,t),e=e.sibling;null!==e;)bl(e,n,t),e=e.sibling}function yl(e){var n=e.stateNode,t=e.memoizedProps;try{for(var r=e.type,i=n.attributes;i.length;)n.removeAttributeNode(i[0]);eu(n,r,t),n[Te]=e,n[Ae]=t}catch(a){dd(e,e.return,a)}}var vl=!1,wl=!1,kl=!1,xl="function"===typeof WeakSet?WeakSet:Set,Sl=null;function El(e,n,t){var r=t.flags;switch(t.tag){case 0:case 11:case 15:Il(e,t),4&r&&ol(5,t);break;case 1:if(Il(e,t),4&r)if(e=t.stateNode,null===n)try{e.componentDidMount()}catch(o){dd(t,t.return,o)}else{var i=hs(t.type,n.memoizedProps);n=n.memoizedState;try{e.componentDidUpdate(i,n,e.__reactInternalSnapshotBeforeUpdate)}catch(s){dd(t,t.return,s)}}64&r&&ll(t),512&r&&dl(t,t.return);break;case 3:if(Il(e,t),64&r&&null!==(e=t.updateQueue)){if(n=null,null!==t.child)switch(t.child.tag){case 27:case 5:case 1:n=t.child.stateNode}try{fa(e,n)}catch(o){dd(t,t.return,o)}}break;case 27:null===n&&4&r&&yl(t);case 26:case 5:Il(e,t),null===n&&4&r&&fl(t),512&r&&dl(t,t.return);break;case 12:Il(e,t);break;case 13:Il(e,t),4&r&&_l(e,t),64&r&&(null!==(e=t.memoizedState)&&(null!==(e=e.dehydrated)&&function(e,n){var t=e.ownerDocument;if("$?"!==e.data||"complete"===t.readyState)n();else{var r=function(){n(),t.removeEventListener("DOMContentLoaded",r)};t.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}(e,t=gd.bind(null,t))));break;case 22:if(!(r=null!==t.memoizedState||vl)){n=null!==n&&null!==n.memoizedState||wl,i=vl;var a=wl;vl=r,(wl=n)&&!a?Fl(e,t,0!==(8772&t.subtreeFlags)):Il(e,t),vl=i,wl=a}break;case 30:break;default:Il(e,t)}}function Cl(e){var n=e.alternate;null!==n&&(e.alternate=null,Cl(n)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(n=e.stateNode)&&He(n)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var zl=null,jl=!1;function Pl(e,n,t){for(t=t.child;null!==t;)Rl(e,n,t),t=t.sibling}function Rl(e,n,t){if(ue&&"function"===typeof ue.onCommitFiberUnmount)try{ue.onCommitFiberUnmount(de,t)}catch(a){}switch(t.tag){case 26:wl||ul(t,n),Pl(e,n,t),t.memoizedState?t.memoizedState.count--:t.stateNode&&(t=t.stateNode).parentNode.removeChild(t);break;case 27:wl||ul(t,n);var r=zl,i=jl;pu(t.type)&&(zl=t.stateNode,jl=!1),Pl(e,n,t),ku(t.stateNode),zl=r,jl=i;break;case 5:wl||ul(t,n);case 6:if(r=zl,i=jl,zl=null,Pl(e,n,t),jl=i,null!==(zl=r))if(jl)try{(9===zl.nodeType?zl.body:"HTML"===zl.nodeName?zl.ownerDocument.body:zl).removeChild(t.stateNode)}catch(o){dd(t,n,o)}else try{zl.removeChild(t.stateNode)}catch(o){dd(t,n,o)}break;case 18:null!==zl&&(jl?(gu(9===(e=zl).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,t.stateNode),Pf(e)):gu(zl,t.stateNode));break;case 4:r=zl,i=jl,zl=t.stateNode.containerInfo,jl=!0,Pl(e,n,t),zl=r,jl=i;break;case 0:case 11:case 14:case 15:wl||sl(2,t,n),wl||sl(4,t,n),Pl(e,n,t);break;case 1:wl||(ul(t,n),"function"===typeof(r=t.stateNode).componentWillUnmount&&cl(t,n,r)),Pl(e,n,t);break;case 21:Pl(e,n,t);break;case 22:wl=(r=wl)||null!==t.memoizedState,Pl(e,n,t),wl=r;break;default:Pl(e,n,t)}}function _l(e,n){if(null===n.memoizedState&&(null!==(e=n.alternate)&&(null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))))try{Pf(e)}catch(t){dd(n,n.return,t)}}function Ol(e,n){var t=function(e){switch(e.tag){case 13:case 19:var n=e.stateNode;return null===n&&(n=e.stateNode=new xl),n;case 22:return null===(n=(e=e.stateNode)._retryCache)&&(n=e._retryCache=new xl),n;default:throw Error(o(435,e.tag))}}(e);n.forEach(function(n){var r=md.bind(null,e,n);t.has(n)||(t.add(n),n.then(r,r))})}function Tl(e,n){var t=n.deletions;if(null!==t)for(var r=0;r<t.length;r++){var i=t[r],a=e,s=n,l=s;e:for(;null!==l;){switch(l.tag){case 27:if(pu(l.type)){zl=l.stateNode,jl=!1;break e}break;case 5:zl=l.stateNode,jl=!1;break e;case 3:case 4:zl=l.stateNode.containerInfo,jl=!0;break e}l=l.return}if(null===zl)throw Error(o(160));Rl(a,s,i),zl=null,jl=!1,null!==(a=i.alternate)&&(a.return=null),i.return=null}if(13878&n.subtreeFlags)for(n=n.child;null!==n;)Nl(n,e),n=n.sibling}var Al=null;function Nl(e,n){var t=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Tl(n,e),Ll(e),4&r&&(sl(3,e,e.return),ol(3,e),sl(5,e,e.return));break;case 1:Tl(n,e),Ll(e),512&r&&(wl||null===t||ul(t,t.return)),64&r&&vl&&(null!==(e=e.updateQueue)&&(null!==(r=e.callbacks)&&(t=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===t?r:t.concat(r))));break;case 26:var i=Al;if(Tl(n,e),Ll(e),512&r&&(wl||null===t||ul(t,t.return)),4&r){var a=null!==t?t.memoizedState:null;if(r=e.memoizedState,null===t)if(null===r)if(null===e.stateNode){e:{r=e.type,t=e.memoizedProps,i=i.ownerDocument||i;n:switch(r){case"title":(!(a=i.getElementsByTagName("title")[0])||a[Fe]||a[Te]||"http://www.w3.org/2000/svg"===a.namespaceURI||a.hasAttribute("itemprop"))&&(a=i.createElement(r),i.head.insertBefore(a,i.querySelector("head > title"))),eu(a,r,t),a[Te]=e,qe(a),r=a;break e;case"link":var s=Fu("link","href",i).get(r+(t.href||""));if(s)for(var l=0;l<s.length;l++)if((a=s[l]).getAttribute("href")===(null==t.href||""===t.href?null:t.href)&&a.getAttribute("rel")===(null==t.rel?null:t.rel)&&a.getAttribute("title")===(null==t.title?null:t.title)&&a.getAttribute("crossorigin")===(null==t.crossOrigin?null:t.crossOrigin)){s.splice(l,1);break n}eu(a=i.createElement(r),r,t),i.head.appendChild(a);break;case"meta":if(s=Fu("meta","content",i).get(r+(t.content||"")))for(l=0;l<s.length;l++)if((a=s[l]).getAttribute("content")===(null==t.content?null:""+t.content)&&a.getAttribute("name")===(null==t.name?null:t.name)&&a.getAttribute("property")===(null==t.property?null:t.property)&&a.getAttribute("http-equiv")===(null==t.httpEquiv?null:t.httpEquiv)&&a.getAttribute("charset")===(null==t.charSet?null:t.charSet)){s.splice(l,1);break n}eu(a=i.createElement(r),r,t),i.head.appendChild(a);break;default:throw Error(o(468,r))}a[Te]=e,qe(a),r=a}e.stateNode=r}else Hu(i,e.type,e.stateNode);else e.stateNode=Nu(i,r,e.memoizedProps);else a!==r?(null===a?null!==t.stateNode&&(t=t.stateNode).parentNode.removeChild(t):a.count--,null===r?Hu(i,e.type,e.stateNode):Nu(i,r,e.memoizedProps)):null===r&&null!==e.stateNode&&pl(e,e.memoizedProps,t.memoizedProps)}break;case 27:Tl(n,e),Ll(e),512&r&&(wl||null===t||ul(t,t.return)),null!==t&&4&r&&pl(e,e.memoizedProps,t.memoizedProps);break;case 5:if(Tl(n,e),Ll(e),512&r&&(wl||null===t||ul(t,t.return)),32&e.flags){i=e.stateNode;try{Sn(i,"")}catch(g){dd(e,e.return,g)}}4&r&&null!=e.stateNode&&pl(e,i=e.memoizedProps,null!==t?t.memoizedProps:i),1024&r&&(kl=!0);break;case 6:if(Tl(n,e),Ll(e),4&r){if(null===e.stateNode)throw Error(o(162));r=e.memoizedProps,t=e.stateNode;try{t.nodeValue=r}catch(g){dd(e,e.return,g)}}break;case 3:if(Du=null,i=Al,Al=Eu(n.containerInfo),Tl(n,e),Al=i,Ll(e),4&r&&null!==t&&t.memoizedState.isDehydrated)try{Pf(n.containerInfo)}catch(g){dd(e,e.return,g)}kl&&(kl=!1,Ml(e));break;case 4:r=Al,Al=Eu(e.stateNode.containerInfo),Tl(n,e),Ll(e),Al=r;break;case 12:default:Tl(n,e),Ll(e);break;case 13:Tl(n,e),Ll(e),8192&e.child.flags&&null!==e.memoizedState!==(null!==t&&null!==t.memoizedState)&&(kc=ne()),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,Ol(e,r)));break;case 22:i=null!==e.memoizedState;var c=null!==t&&null!==t.memoizedState,d=vl,u=wl;if(vl=d||i,wl=u||c,Tl(n,e),wl=u,vl=d,Ll(e),8192&r)e:for(n=e.stateNode,n._visibility=i?-2&n._visibility:1|n._visibility,i&&(null===t||c||vl||wl||Dl(e)),t=null,n=e;;){if(5===n.tag||26===n.tag){if(null===t){c=t=n;try{if(a=c.stateNode,i)"function"===typeof(s=a.style).setProperty?s.setProperty("display","none","important"):s.display="none";else{l=c.stateNode;var f=c.memoizedProps.style,p=void 0!==f&&null!==f&&f.hasOwnProperty("display")?f.display:null;l.style.display=null==p||"boolean"===typeof p?"":(""+p).trim()}}catch(g){dd(c,c.return,g)}}}else if(6===n.tag){if(null===t){c=n;try{c.stateNode.nodeValue=i?"":c.memoizedProps}catch(g){dd(c,c.return,g)}}}else if((22!==n.tag&&23!==n.tag||null===n.memoizedState||n===e)&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===e)break e;for(;null===n.sibling;){if(null===n.return||n.return===e)break e;t===n&&(t=null),n=n.return}t===n&&(t=null),n.sibling.return=n.return,n=n.sibling}4&r&&(null!==(r=e.updateQueue)&&(null!==(t=r.retryQueue)&&(r.retryQueue=null,Ol(e,t))));break;case 19:Tl(n,e),Ll(e),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,Ol(e,r)));case 30:case 21:}}function Ll(e){var n=e.flags;if(2&n){try{for(var t,r=e.return;null!==r;){if(gl(r)){t=r;break}r=r.return}if(null==t)throw Error(o(160));switch(t.tag){case 27:var i=t.stateNode;bl(e,ml(e),i);break;case 5:var a=t.stateNode;32&t.flags&&(Sn(a,""),t.flags&=-33),bl(e,ml(e),a);break;case 3:case 4:var s=t.stateNode.containerInfo;hl(e,ml(e),s);break;default:throw Error(o(161))}}catch(l){dd(e,e.return,l)}e.flags&=-3}4096&n&&(e.flags&=-4097)}function Ml(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var n=e;Ml(n),5===n.tag&&1024&n.flags&&n.stateNode.reset(),e=e.sibling}}function Il(e,n){if(8772&n.subtreeFlags)for(n=n.child;null!==n;)El(e,n.alternate,n),n=n.sibling}function Dl(e){for(e=e.child;null!==e;){var n=e;switch(n.tag){case 0:case 11:case 14:case 15:sl(4,n,n.return),Dl(n);break;case 1:ul(n,n.return);var t=n.stateNode;"function"===typeof t.componentWillUnmount&&cl(n,n.return,t),Dl(n);break;case 27:ku(n.stateNode);case 26:case 5:ul(n,n.return),Dl(n);break;case 22:null===n.memoizedState&&Dl(n);break;default:Dl(n)}e=e.sibling}}function Fl(e,n,t){for(t=t&&0!==(8772&n.subtreeFlags),n=n.child;null!==n;){var r=n.alternate,i=e,a=n,o=a.flags;switch(a.tag){case 0:case 11:case 15:Fl(i,a,t),ol(4,a);break;case 1:if(Fl(i,a,t),"function"===typeof(i=(r=a).stateNode).componentDidMount)try{i.componentDidMount()}catch(c){dd(r,r.return,c)}if(null!==(i=(r=a).updateQueue)){var s=r.stateNode;try{var l=i.shared.hiddenCallbacks;if(null!==l)for(i.shared.hiddenCallbacks=null,i=0;i<l.length;i++)ua(l[i],s)}catch(c){dd(r,r.return,c)}}t&&64&o&&ll(a),dl(a,a.return);break;case 27:yl(a);case 26:case 5:Fl(i,a,t),t&&null===r&&4&o&&fl(a),dl(a,a.return);break;case 12:Fl(i,a,t);break;case 13:Fl(i,a,t),t&&4&o&&_l(i,a);break;case 22:null===a.memoizedState&&Fl(i,a,t),dl(a,a.return);break;case 30:break;default:Fl(i,a,t)}n=n.sibling}}function Hl(e,n){var t=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(t=e.memoizedState.cachePool.pool),e=null,null!==n.memoizedState&&null!==n.memoizedState.cachePool&&(e=n.memoizedState.cachePool.pool),e!==t&&(null!=e&&e.refCount++,null!=t&&Ni(t))}function Ul(e,n){e=null,null!==n.alternate&&(e=n.alternate.memoizedState.cache),(n=n.memoizedState.cache)!==e&&(n.refCount++,null!=e&&Ni(e))}function Wl(e,n,t,r){if(10256&n.subtreeFlags)for(n=n.child;null!==n;)Bl(e,n,t,r),n=n.sibling}function Bl(e,n,t,r){var i=n.flags;switch(n.tag){case 0:case 11:case 15:Wl(e,n,t,r),2048&i&&ol(9,n);break;case 1:case 13:default:Wl(e,n,t,r);break;case 3:Wl(e,n,t,r),2048&i&&(e=null,null!==n.alternate&&(e=n.alternate.memoizedState.cache),(n=n.memoizedState.cache)!==e&&(n.refCount++,null!=e&&Ni(e)));break;case 12:if(2048&i){Wl(e,n,t,r),e=n.stateNode;try{var a=n.memoizedProps,o=a.id,s=a.onPostCommit;"function"===typeof s&&s(o,null===n.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(l){dd(n,n.return,l)}}else Wl(e,n,t,r);break;case 23:break;case 22:a=n.stateNode,o=n.alternate,null!==n.memoizedState?2&a._visibility?Wl(e,n,t,r):ql(e,n):2&a._visibility?Wl(e,n,t,r):(a._visibility|=2,Vl(e,n,t,r,0!==(10256&n.subtreeFlags))),2048&i&&Hl(o,n);break;case 24:Wl(e,n,t,r),2048&i&&Ul(n.alternate,n)}}function Vl(e,n,t,r,i){for(i=i&&0!==(10256&n.subtreeFlags),n=n.child;null!==n;){var a=e,o=n,s=t,l=r,c=o.flags;switch(o.tag){case 0:case 11:case 15:Vl(a,o,s,l,i),ol(8,o);break;case 23:break;case 22:var d=o.stateNode;null!==o.memoizedState?2&d._visibility?Vl(a,o,s,l,i):ql(a,o):(d._visibility|=2,Vl(a,o,s,l,i)),i&&2048&c&&Hl(o.alternate,o);break;case 24:Vl(a,o,s,l,i),i&&2048&c&&Ul(o.alternate,o);break;default:Vl(a,o,s,l,i)}n=n.sibling}}function ql(e,n){if(10256&n.subtreeFlags)for(n=n.child;null!==n;){var t=e,r=n,i=r.flags;switch(r.tag){case 22:ql(t,r),2048&i&&Hl(r.alternate,r);break;case 24:ql(t,r),2048&i&&Ul(r.alternate,r);break;default:ql(t,r)}n=n.sibling}}var Yl=8192;function Gl(e){if(e.subtreeFlags&Yl)for(e=e.child;null!==e;)Kl(e),e=e.sibling}function Kl(e){switch(e.tag){case 26:Gl(e),e.flags&Yl&&null!==e.memoizedState&&function(e,n,t){if(null===Wu)throw Error(o(475));var r=Wu;if("stylesheet"===n.type&&("string"!==typeof t.media||!1!==matchMedia(t.media).matches)&&0===(4&n.state.loading)){if(null===n.instance){var i=Ru(t.href),a=e.querySelector(_u(i));if(a)return null!==(e=a._p)&&"object"===typeof e&&"function"===typeof e.then&&(r.count++,r=Vu.bind(r),e.then(r,r)),n.state.loading|=4,n.instance=a,void qe(a);a=e.ownerDocument||e,t=Ou(t),(i=xu.get(i))&&Mu(t,i),qe(a=a.createElement("link"));var s=a;s._p=new Promise(function(e,n){s.onload=e,s.onerror=n}),eu(a,"link",t),n.instance=a}null===r.stylesheets&&(r.stylesheets=new Map),r.stylesheets.set(n,e),(e=n.state.preload)&&0===(3&n.state.loading)&&(r.count++,n=Vu.bind(r),e.addEventListener("load",n),e.addEventListener("error",n))}}(Al,e.memoizedState,e.memoizedProps);break;case 5:default:Gl(e);break;case 3:case 4:var n=Al;Al=Eu(e.stateNode.containerInfo),Gl(e),Al=n;break;case 22:null===e.memoizedState&&(null!==(n=e.alternate)&&null!==n.memoizedState?(n=Yl,Yl=16777216,Gl(e),Yl=n):Gl(e))}}function Ql(e){var n=e.alternate;if(null!==n&&null!==(e=n.child)){n.child=null;do{n=e.sibling,e.sibling=null,e=n}while(null!==e)}}function $l(e){var n=e.deletions;if(0!==(16&e.flags)){if(null!==n)for(var t=0;t<n.length;t++){var r=n[t];Sl=r,Jl(r,e)}Ql(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Zl(e),e=e.sibling}function Zl(e){switch(e.tag){case 0:case 11:case 15:$l(e),2048&e.flags&&sl(9,e,e.return);break;case 3:case 12:default:$l(e);break;case 22:var n=e.stateNode;null!==e.memoizedState&&2&n._visibility&&(null===e.return||13!==e.return.tag)?(n._visibility&=-3,Xl(e)):$l(e)}}function Xl(e){var n=e.deletions;if(0!==(16&e.flags)){if(null!==n)for(var t=0;t<n.length;t++){var r=n[t];Sl=r,Jl(r,e)}Ql(e)}for(e=e.child;null!==e;){switch((n=e).tag){case 0:case 11:case 15:sl(8,n,n.return),Xl(n);break;case 22:2&(t=n.stateNode)._visibility&&(t._visibility&=-3,Xl(n));break;default:Xl(n)}e=e.sibling}}function Jl(e,n){for(;null!==Sl;){var t=Sl;switch(t.tag){case 0:case 11:case 15:sl(8,t,n);break;case 23:case 22:if(null!==t.memoizedState&&null!==t.memoizedState.cachePool){var r=t.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:Ni(t.memoizedState.cache)}if(null!==(r=t.child))r.return=t,Sl=r;else e:for(t=e;null!==Sl;){var i=(r=Sl).sibling,a=r.return;if(Cl(r),r===t){Sl=null;break e}if(null!==i){i.return=a,Sl=i;break e}Sl=a}}}var ec={getCacheForType:function(e){var n=zi(Ti),t=n.data.get(e);return void 0===t&&(t=e(),n.data.set(e,t)),t}},nc="function"===typeof WeakMap?WeakMap:Map,tc=0,rc=null,ic=null,ac=0,oc=0,sc=null,lc=!1,cc=!1,dc=!1,uc=0,fc=0,pc=0,gc=0,mc=0,hc=0,bc=0,yc=null,vc=null,wc=!1,kc=0,xc=1/0,Sc=null,Ec=null,Cc=0,zc=null,jc=null,Pc=0,Rc=0,_c=null,Oc=null,Tc=0,Ac=null;function Nc(){if(0!==(2&tc)&&0!==ac)return ac&-ac;if(null!==N.T){return 0!==Ii?Ii:Rd()}return _e()}function Lc(){0===hc&&(hc=0===(536870912&ac)||ai?xe():536870912);var e=is.current;return null!==e&&(e.flags|=32),hc}function Mc(e,n,t){(e!==rc||2!==oc&&9!==oc)&&null===e.cancelPendingCommit||(Bc(e,0),Hc(e,ac,hc,!1)),Ce(e,t),0!==(2&tc)&&e===rc||(e===rc&&(0===(2&tc)&&(gc|=t),4===fc&&Hc(e,ac,hc,!1)),xd(e))}function Ic(e,n,t){if(0!==(6&tc))throw Error(o(327));for(var r=!t&&0===(124&n)&&0===(n&e.expiredLanes)||we(e,n),i=r?function(e,n){var t=tc;tc|=2;var r=qc(),i=Yc();rc!==e||ac!==n?(Sc=null,xc=ne()+500,Bc(e,n)):cc=we(e,n);e:for(;;)try{if(0!==oc&&null!==ic){n=ic;var a=sc;n:switch(oc){case 1:oc=0,sc=null,Jc(e,n,a,1);break;case 2:case 9:if(Qi(a)){oc=0,sc=null,Xc(n);break}n=function(){2!==oc&&9!==oc||rc!==e||(oc=7),xd(e)},a.then(n,n);break e;case 3:oc=7;break e;case 4:oc=5;break e;case 7:Qi(a)?(oc=0,sc=null,Xc(n)):(oc=0,sc=null,Jc(e,n,a,7));break;case 5:var s=null;switch(ic.tag){case 26:s=ic.memoizedState;case 5:case 27:var l=ic;if(!s||Uu(s)){oc=0,sc=null;var c=l.sibling;if(null!==c)ic=c;else{var d=l.return;null!==d?(ic=d,ed(d)):ic=null}break n}}oc=0,sc=null,Jc(e,n,a,5);break;case 6:oc=0,sc=null,Jc(e,n,a,6);break;case 8:Wc(),fc=6;break e;default:throw Error(o(462))}}$c();break}catch(u){Vc(e,u)}return yi=bi=null,N.H=r,N.A=i,tc=t,null!==ic?0:(rc=null,ac=0,Pr(),fc)}(e,n):Kc(e,n,!0),a=r;;){if(0===i){cc&&!r&&Hc(e,n,0,!1);break}if(t=e.current.alternate,!a||Fc(t)){if(2===i){if(a=n,e.errorRecoveryDisabledLanes&a)var s=0;else s=0!==(s=-536870913&e.pendingLanes)?s:536870912&s?536870912:0;if(0!==s){n=s;e:{var l=e;i=yc;var c=l.current.memoizedState.isDehydrated;if(c&&(Bc(l,s).flags|=256),2!==(s=Kc(l,s,!1))){if(dc&&!c){l.errorRecoveryDisabledLanes|=a,gc|=a,i=4;break e}a=vc,vc=i,null!==a&&(null===vc?vc=a:vc.push.apply(vc,a))}i=s}if(a=!1,2!==i)continue}}if(1===i){Bc(e,0),Hc(e,n,0,!0);break}e:{switch(r=e,a=i){case 0:case 1:throw Error(o(345));case 4:if((4194048&n)!==n)break;case 6:Hc(r,n,hc,!lc);break e;case 2:vc=null;break;case 3:case 5:break;default:throw Error(o(329))}if((62914560&n)===n&&10<(i=kc+300-ne())){if(Hc(r,n,hc,!lc),0!==ve(r,0,!0))break e;r.timeoutHandle=lu(Dc.bind(null,r,t,vc,Sc,wc,n,hc,gc,bc,lc,a,2,-0,0),i)}else Dc(r,t,vc,Sc,wc,n,hc,gc,bc,lc,a,0,-0,0)}break}i=Kc(e,n,!1),a=!1}xd(e)}function Dc(e,n,t,r,i,a,s,l,c,d,u,f,p,g){if(e.timeoutHandle=-1,(8192&(f=n.subtreeFlags)||16785408===(16785408&f))&&(Wu={stylesheets:null,count:0,unsuspend:Bu},Kl(n),null!==(f=function(){if(null===Wu)throw Error(o(475));var e=Wu;return e.stylesheets&&0===e.count&&Yu(e,e.stylesheets),0<e.count?function(n){var t=setTimeout(function(){if(e.stylesheets&&Yu(e,e.stylesheets),e.unsuspend){var n=e.unsuspend;e.unsuspend=null,n()}},6e4);return e.unsuspend=n,function(){e.unsuspend=null,clearTimeout(t)}}:null}())))return e.cancelPendingCommit=f(td.bind(null,e,n,a,t,r,i,s,l,c,u,1,p,g)),void Hc(e,a,s,!d);td(e,n,a,t,r,i,s,l,c)}function Fc(e){for(var n=e;;){var t=n.tag;if((0===t||11===t||15===t)&&16384&n.flags&&(null!==(t=n.updateQueue)&&null!==(t=t.stores)))for(var r=0;r<t.length;r++){var i=t[r],a=i.getSnapshot;i=i.value;try{if(!Qt(a(),i))return!1}catch(o){return!1}}if(t=n.child,16384&n.subtreeFlags&&null!==t)t.return=n,n=t;else{if(n===e)break;for(;null===n.sibling;){if(null===n.return||n.return===e)return!0;n=n.return}n.sibling.return=n.return,n=n.sibling}}return!0}function Hc(e,n,t,r){n&=~mc,n&=~gc,e.suspendedLanes|=n,e.pingedLanes&=~n,r&&(e.warmLanes|=n),r=e.expirationTimes;for(var i=n;0<i;){var a=31-pe(i),o=1<<a;r[a]=-1,i&=~o}0!==t&&ze(e,t,n)}function Uc(){return 0!==(6&tc)||(Sd(0,!1),!1)}function Wc(){if(null!==ic){if(0===oc)var e=ic.return;else yi=bi=null,Ia(e=ic),Qo=null,$o=0,e=ic;for(;null!==e;)al(e.alternate,e),e=e.return;ic=null}}function Bc(e,n){var t=e.timeoutHandle;-1!==t&&(e.timeoutHandle=-1,cu(t)),null!==(t=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,t()),Wc(),rc=e,ic=t=Dr(e.current,null),ac=n,oc=0,sc=null,lc=!1,cc=we(e,n),dc=!1,bc=hc=mc=gc=pc=fc=0,vc=yc=null,wc=!1,0!==(8&n)&&(n|=32&n);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=n;0<r;){var i=31-pe(r),a=1<<i;n|=e[i],r&=~a}return uc=n,Pr(),t}function Vc(e,n){va=null,N.H=qo,n===qi||n===Gi?(n=Ji(),oc=3):n===Yi?(n=Ji(),oc=4):oc=n===zs?8:null!==n&&"object"===typeof n&&"function"===typeof n.then?6:1,sc=n,null===ic&&(fc=1,ks(e,Er(n,e.current)))}function qc(){var e=N.H;return N.H=qo,null===e?qo:e}function Yc(){var e=N.A;return N.A=ec,e}function Gc(){fc=4,lc||(4194048&ac)!==ac&&null!==is.current||(cc=!0),0===(134217727&pc)&&0===(134217727&gc)||null===rc||Hc(rc,ac,hc,!1)}function Kc(e,n,t){var r=tc;tc|=2;var i=qc(),a=Yc();rc===e&&ac===n||(Sc=null,Bc(e,n)),n=!1;var o=fc;e:for(;;)try{if(0!==oc&&null!==ic){var s=ic,l=sc;switch(oc){case 8:Wc(),o=6;break e;case 3:case 2:case 9:case 6:null===is.current&&(n=!0);var c=oc;if(oc=0,sc=null,Jc(e,s,l,c),t&&cc){o=0;break e}break;default:c=oc,oc=0,sc=null,Jc(e,s,l,c)}}Qc(),o=fc;break}catch(d){Vc(e,d)}return n&&e.shellSuspendCounter++,yi=bi=null,tc=r,N.H=i,N.A=a,null===ic&&(rc=null,ac=0,Pr()),o}function Qc(){for(;null!==ic;)Zc(ic)}function $c(){for(;null!==ic&&!J();)Zc(ic)}function Zc(e){var n=Zs(e.alternate,e,uc);e.memoizedProps=e.pendingProps,null===n?ed(e):ic=n}function Xc(e){var n=e,t=n.alternate;switch(n.tag){case 15:case 0:n=Ms(t,n,n.pendingProps,n.type,void 0,ac);break;case 11:n=Ms(t,n,n.pendingProps,n.type.render,n.ref,ac);break;case 5:Ia(n);default:al(t,n),n=Zs(t,n=ic=Fr(n,uc),uc)}e.memoizedProps=e.pendingProps,null===n?ed(e):ic=n}function Jc(e,n,t,r){yi=bi=null,Ia(n),Qo=null,$o=0;var i=n.return;try{if(function(e,n,t,r,i){if(t.flags|=32768,null!==r&&"object"===typeof r&&"function"===typeof r.then){if(null!==(n=t.alternate)&&Si(n,t,i,!0),null!==(t=is.current)){switch(t.tag){case 13:return null===as?Gc():null===t.alternate&&0===fc&&(fc=3),t.flags&=-257,t.flags|=65536,t.lanes=i,r===Ki?t.flags|=16384:(null===(n=t.updateQueue)?t.updateQueue=new Set([r]):n.add(r),ud(e,r,i)),!1;case 22:return t.flags|=65536,r===Ki?t.flags|=16384:(null===(n=t.updateQueue)?(n={transitions:null,markerInstances:null,retryQueue:new Set([r])},t.updateQueue=n):null===(t=n.retryQueue)?n.retryQueue=new Set([r]):t.add(r),ud(e,r,i)),!1}throw Error(o(435,t.tag))}return ud(e,r,i),Gc(),!1}if(ai)return null!==(n=is.current)?(0===(65536&n.flags)&&(n.flags|=256),n.flags|=65536,n.lanes=i,r!==li&&mi(Er(e=Error(o(422),{cause:r}),t))):(r!==li&&mi(Er(n=Error(o(423),{cause:r}),t)),(e=e.current.alternate).flags|=65536,i&=-i,e.lanes|=i,r=Er(r,t),sa(e,i=Ss(e.stateNode,r,i)),4!==fc&&(fc=2)),!1;var a=Error(o(520),{cause:r});if(a=Er(a,t),null===yc?yc=[a]:yc.push(a),4!==fc&&(fc=2),null===n)return!0;r=Er(r,t),t=n;do{switch(t.tag){case 3:return t.flags|=65536,e=i&-i,t.lanes|=e,sa(t,e=Ss(t.stateNode,r,e)),!1;case 1:if(n=t.type,a=t.stateNode,0===(128&t.flags)&&("function"===typeof n.getDerivedStateFromError||null!==a&&"function"===typeof a.componentDidCatch&&(null===Ec||!Ec.has(a))))return t.flags|=65536,i&=-i,t.lanes|=i,Cs(i=Es(i),e,t,r),sa(t,i),!1}t=t.return}while(null!==t);return!1}(e,i,n,t,ac))return fc=1,ks(e,Er(t,e.current)),void(ic=null)}catch(a){if(null!==i)throw ic=i,a;return fc=1,ks(e,Er(t,e.current)),void(ic=null)}32768&n.flags?(ai||1===r?e=!0:cc||0!==(536870912&ac)?e=!1:(lc=e=!0,(2===r||9===r||3===r||6===r)&&(null!==(r=is.current)&&13===r.tag&&(r.flags|=16384))),nd(n,e)):ed(n)}function ed(e){var n=e;do{if(0!==(32768&n.flags))return void nd(n,lc);e=n.return;var t=rl(n.alternate,n,uc);if(null!==t)return void(ic=t);if(null!==(n=n.sibling))return void(ic=n);ic=n=e}while(null!==n);0===fc&&(fc=5)}function nd(e,n){do{var t=il(e.alternate,e);if(null!==t)return t.flags&=32767,void(ic=t);if(null!==(t=e.return)&&(t.flags|=32768,t.subtreeFlags=0,t.deletions=null),!n&&null!==(e=e.sibling))return void(ic=e);ic=e=t}while(null!==e);fc=6,ic=null}function td(e,n,t,r,i,a,s,l,c){e.cancelPendingCommit=null;do{sd()}while(0!==Cc);if(0!==(6&tc))throw Error(o(327));if(null!==n){if(n===e.current)throw Error(o(177));if(a=n.lanes|n.childLanes,function(e,n,t,r,i,a){var o=e.pendingLanes;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=t,e.entangledLanes&=t,e.errorRecoveryDisabledLanes&=t,e.shellSuspendCounter=0;var s=e.entanglements,l=e.expirationTimes,c=e.hiddenUpdates;for(t=o&~t;0<t;){var d=31-pe(t),u=1<<d;s[d]=0,l[d]=-1;var f=c[d];if(null!==f)for(c[d]=null,d=0;d<f.length;d++){var p=f[d];null!==p&&(p.lane&=-536870913)}t&=~u}0!==r&&ze(e,r,0),0!==a&&0===i&&0!==e.tag&&(e.suspendedLanes|=a&~(o&~n))}(e,t,a|=jr,s,l,c),e===rc&&(ic=rc=null,ac=0),jc=n,zc=e,Pc=t,Rc=a,_c=i,Oc=r,0!==(10256&n.subtreeFlags)||0!==(10256&n.flags)?(e.callbackNode=null,e.callbackPriority=0,Z(ae,function(){return ld(),null})):(e.callbackNode=null,e.callbackPriority=0),r=0!==(13878&n.flags),0!==(13878&n.subtreeFlags)||r){r=N.T,N.T=null,i=L.p,L.p=2,s=tc,tc|=4;try{!function(e,n){if(e=e.containerInfo,nu=tf,nr(e=er(e))){if("selectionStart"in e)var t={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(t=(t=e.ownerDocument)&&t.defaultView||window).getSelection&&t.getSelection();if(r&&0!==r.rangeCount){t=r.anchorNode;var i=r.anchorOffset,a=r.focusNode;r=r.focusOffset;try{t.nodeType,a.nodeType}catch(h){t=null;break e}var s=0,l=-1,c=-1,d=0,u=0,f=e,p=null;n:for(;;){for(var g;f!==t||0!==i&&3!==f.nodeType||(l=s+i),f!==a||0!==r&&3!==f.nodeType||(c=s+r),3===f.nodeType&&(s+=f.nodeValue.length),null!==(g=f.firstChild);)p=f,f=g;for(;;){if(f===e)break n;if(p===t&&++d===i&&(l=s),p===a&&++u===r&&(c=s),null!==(g=f.nextSibling))break;p=(f=p).parentNode}f=g}t=-1===l||-1===c?null:{start:l,end:c}}else t=null}t=t||{start:0,end:0}}else t=null;for(tu={focusedElem:e,selectionRange:t},tf=!1,Sl=n;null!==Sl;)if(e=(n=Sl).child,0!==(1024&n.subtreeFlags)&&null!==e)e.return=n,Sl=e;else for(;null!==Sl;){switch(a=(n=Sl).alternate,e=n.flags,n.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(0!==(1024&e)&&null!==a){e=void 0,t=n,i=a.memoizedProps,a=a.memoizedState,r=t.stateNode;try{var m=hs(t.type,i,(t.elementType,t.type));e=r.getSnapshotBeforeUpdate(m,a),r.__reactInternalSnapshotBeforeUpdate=e}catch(b){dd(t,t.return,b)}}break;case 3:if(0!==(1024&e))if(9===(t=(e=n.stateNode.containerInfo).nodeType))mu(e);else if(1===t)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":mu(e);break;default:e.textContent=""}break;default:if(0!==(1024&e))throw Error(o(163))}if(null!==(e=n.sibling)){e.return=n.return,Sl=e;break}Sl=n.return}}(e,n)}finally{tc=s,L.p=i,N.T=r}}Cc=1,rd(),id(),ad()}}function rd(){if(1===Cc){Cc=0;var e=zc,n=jc,t=0!==(13878&n.flags);if(0!==(13878&n.subtreeFlags)||t){t=N.T,N.T=null;var r=L.p;L.p=2;var i=tc;tc|=4;try{Nl(n,e);var a=tu,o=er(e.containerInfo),s=a.focusedElem,l=a.selectionRange;if(o!==s&&s&&s.ownerDocument&&Jt(s.ownerDocument.documentElement,s)){if(null!==l&&nr(s)){var c=l.start,d=l.end;if(void 0===d&&(d=c),"selectionStart"in s)s.selectionStart=c,s.selectionEnd=Math.min(d,s.value.length);else{var u=s.ownerDocument||document,f=u&&u.defaultView||window;if(f.getSelection){var p=f.getSelection(),g=s.textContent.length,m=Math.min(l.start,g),h=void 0===l.end?m:Math.min(l.end,g);!p.extend&&m>h&&(o=h,h=m,m=o);var b=Xt(s,m),y=Xt(s,h);if(b&&y&&(1!==p.rangeCount||p.anchorNode!==b.node||p.anchorOffset!==b.offset||p.focusNode!==y.node||p.focusOffset!==y.offset)){var v=u.createRange();v.setStart(b.node,b.offset),p.removeAllRanges(),m>h?(p.addRange(v),p.extend(y.node,y.offset)):(v.setEnd(y.node,y.offset),p.addRange(v))}}}}for(u=[],p=s;p=p.parentNode;)1===p.nodeType&&u.push({element:p,left:p.scrollLeft,top:p.scrollTop});for("function"===typeof s.focus&&s.focus(),s=0;s<u.length;s++){var w=u[s];w.element.scrollLeft=w.left,w.element.scrollTop=w.top}}tf=!!nu,tu=nu=null}finally{tc=i,L.p=r,N.T=t}}e.current=n,Cc=2}}function id(){if(2===Cc){Cc=0;var e=zc,n=jc,t=0!==(8772&n.flags);if(0!==(8772&n.subtreeFlags)||t){t=N.T,N.T=null;var r=L.p;L.p=2;var i=tc;tc|=4;try{El(e,n.alternate,n)}finally{tc=i,L.p=r,N.T=t}}Cc=3}}function ad(){if(4===Cc||3===Cc){Cc=0,ee();var e=zc,n=jc,t=Pc,r=Oc;0!==(10256&n.subtreeFlags)||0!==(10256&n.flags)?Cc=5:(Cc=0,jc=zc=null,od(e,e.pendingLanes));var i=e.pendingLanes;if(0===i&&(Ec=null),Re(t),n=n.stateNode,ue&&"function"===typeof ue.onCommitFiberRoot)try{ue.onCommitFiberRoot(de,n,void 0,128===(128&n.current.flags))}catch(l){}if(null!==r){n=N.T,i=L.p,L.p=2,N.T=null;try{for(var a=e.onRecoverableError,o=0;o<r.length;o++){var s=r[o];a(s.value,{componentStack:s.stack})}}finally{N.T=n,L.p=i}}0!==(3&Pc)&&sd(),xd(e),i=e.pendingLanes,0!==(4194090&t)&&0!==(42&i)?e===Ac?Tc++:(Tc=0,Ac=e):Tc=0,Sd(0,!1)}}function od(e,n){0===(e.pooledCacheLanes&=n)&&(null!=(n=e.pooledCache)&&(e.pooledCache=null,Ni(n)))}function sd(e){return rd(),id(),ad(),ld()}function ld(){if(5!==Cc)return!1;var e=zc,n=Rc;Rc=0;var t=Re(Pc),r=N.T,i=L.p;try{L.p=32>t?32:t,N.T=null,t=_c,_c=null;var a=zc,s=Pc;if(Cc=0,jc=zc=null,Pc=0,0!==(6&tc))throw Error(o(331));var l=tc;if(tc|=4,Zl(a.current),Bl(a,a.current,s,t),tc=l,Sd(0,!1),ue&&"function"===typeof ue.onPostCommitFiberRoot)try{ue.onPostCommitFiberRoot(de,a)}catch(c){}return!0}finally{L.p=i,N.T=r,od(e,n)}}function cd(e,n,t){n=Er(t,n),null!==(e=aa(e,n=Ss(e.stateNode,n,2),2))&&(Ce(e,2),xd(e))}function dd(e,n,t){if(3===e.tag)cd(e,e,t);else for(;null!==n;){if(3===n.tag){cd(n,e,t);break}if(1===n.tag){var r=n.stateNode;if("function"===typeof n.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Ec||!Ec.has(r))){e=Er(t,e),null!==(r=aa(n,t=Es(2),2))&&(Cs(t,r,n,e),Ce(r,2),xd(r));break}}n=n.return}}function ud(e,n,t){var r=e.pingCache;if(null===r){r=e.pingCache=new nc;var i=new Set;r.set(n,i)}else void 0===(i=r.get(n))&&(i=new Set,r.set(n,i));i.has(t)||(dc=!0,i.add(t),e=fd.bind(null,e,n,t),n.then(e,e))}function fd(e,n,t){var r=e.pingCache;null!==r&&r.delete(n),e.pingedLanes|=e.suspendedLanes&t,e.warmLanes&=~t,rc===e&&(ac&t)===t&&(4===fc||3===fc&&(62914560&ac)===ac&&300>ne()-kc?0===(2&tc)&&Bc(e,0):mc|=t,bc===ac&&(bc=0)),xd(e)}function pd(e,n){0===n&&(n=Se()),null!==(e=Or(e,n))&&(Ce(e,n),xd(e))}function gd(e){var n=e.memoizedState,t=0;null!==n&&(t=n.retryLane),pd(e,t)}function md(e,n){var t=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;null!==i&&(t=i.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(o(314))}null!==r&&r.delete(n),pd(e,t)}var hd=null,bd=null,yd=!1,vd=!1,wd=!1,kd=0;function xd(e){e!==bd&&null===e.next&&(null===bd?hd=bd=e:bd=bd.next=e),vd=!0,yd||(yd=!0,uu(function(){0!==(6&tc)?Z(re,Ed):Cd()}))}function Sd(e,n){if(!wd&&vd){wd=!0;do{for(var t=!1,r=hd;null!==r;){if(!n)if(0!==e){var i=r.pendingLanes;if(0===i)var a=0;else{var o=r.suspendedLanes,s=r.pingedLanes;a=(1<<31-pe(42|e)+1)-1,a=201326741&(a&=i&~(o&~s))?201326741&a|1:a?2|a:0}0!==a&&(t=!0,Pd(r,a))}else a=ac,0===(3&(a=ve(r,r===rc?a:0,null!==r.cancelPendingCommit||-1!==r.timeoutHandle)))||we(r,a)||(t=!0,Pd(r,a));r=r.next}}while(t);wd=!1}}function Ed(){Cd()}function Cd(){vd=yd=!1;var e=0;0!==kd&&(function(){var e=window.event;if(e&&"popstate"===e.type)return e!==su&&(su=e,!0);return su=null,!1}()&&(e=kd),kd=0);for(var n=ne(),t=null,r=hd;null!==r;){var i=r.next,a=zd(r,n);0===a?(r.next=null,null===t?hd=i:t.next=i,null===i&&(bd=t)):(t=r,(0!==e||0!==(3&a))&&(vd=!0)),r=i}Sd(e,!1)}function zd(e,n){for(var t=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,a=-62914561&e.pendingLanes;0<a;){var o=31-pe(a),s=1<<o,l=i[o];-1===l?0!==(s&t)&&0===(s&r)||(i[o]=ke(s,n)):l<=n&&(e.expiredLanes|=s),a&=~s}if(t=ac,t=ve(e,e===(n=rc)?t:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),r=e.callbackNode,0===t||e===n&&(2===oc||9===oc)||null!==e.cancelPendingCommit)return null!==r&&null!==r&&X(r),e.callbackNode=null,e.callbackPriority=0;if(0===(3&t)||we(e,t)){if((n=t&-t)===e.callbackPriority)return n;switch(null!==r&&X(r),Re(t)){case 2:case 8:t=ie;break;case 32:default:t=ae;break;case 268435456:t=se}return r=jd.bind(null,e),t=Z(t,r),e.callbackPriority=n,e.callbackNode=t,n}return null!==r&&null!==r&&X(r),e.callbackPriority=2,e.callbackNode=null,2}function jd(e,n){if(0!==Cc&&5!==Cc)return e.callbackNode=null,e.callbackPriority=0,null;var t=e.callbackNode;if(sd()&&e.callbackNode!==t)return null;var r=ac;return 0===(r=ve(e,e===rc?r:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(Ic(e,r,n),zd(e,ne()),null!=e.callbackNode&&e.callbackNode===t?jd.bind(null,e):null)}function Pd(e,n){if(sd())return null;Ic(e,n,!0)}function Rd(){return 0===kd&&(kd=xe()),kd}function _d(e){return null==e||"symbol"===typeof e||"boolean"===typeof e?null:"function"===typeof e?e:_n(""+e)}function Od(e,n){var t=n.ownerDocument.createElement("input");return t.name=n.name,t.value=n.value,e.id&&t.setAttribute("form",e.id),n.parentNode.insertBefore(t,n),e=new FormData(e),t.parentNode.removeChild(t),e}for(var Td=0;Td<kr.length;Td++){var Ad=kr[Td];xr(Ad.toLowerCase(),"on"+(Ad[0].toUpperCase()+Ad.slice(1)))}xr(pr,"onAnimationEnd"),xr(gr,"onAnimationIteration"),xr(mr,"onAnimationStart"),xr("dblclick","onDoubleClick"),xr("focusin","onFocus"),xr("focusout","onBlur"),xr(hr,"onTransitionRun"),xr(br,"onTransitionStart"),xr(yr,"onTransitionCancel"),xr(vr,"onTransitionEnd"),Qe("onMouseEnter",["mouseout","mouseover"]),Qe("onMouseLeave",["mouseout","mouseover"]),Qe("onPointerEnter",["pointerout","pointerover"]),Qe("onPointerLeave",["pointerout","pointerover"]),Ke("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ke("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ke("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ke("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ke("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ke("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Nd="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ld=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Nd));function Md(e,n){n=0!==(4&n);for(var t=0;t<e.length;t++){var r=e[t],i=r.event;r=r.listeners;e:{var a=void 0;if(n)for(var o=r.length-1;0<=o;o--){var s=r[o],l=s.instance,c=s.currentTarget;if(s=s.listener,l!==a&&i.isPropagationStopped())break e;a=s,i.currentTarget=c;try{a(i)}catch(d){bs(d)}i.currentTarget=null,a=l}else for(o=0;o<r.length;o++){if(l=(s=r[o]).instance,c=s.currentTarget,s=s.listener,l!==a&&i.isPropagationStopped())break e;a=s,i.currentTarget=c;try{a(i)}catch(d){bs(d)}i.currentTarget=null,a=l}}}}function Id(e,n){var t=n[Le];void 0===t&&(t=n[Le]=new Set);var r=e+"__bubble";t.has(r)||(Ud(n,e,2,!1),t.add(r))}function Dd(e,n,t){var r=0;n&&(r|=4),Ud(t,e,r,n)}var Fd="_reactListening"+Math.random().toString(36).slice(2);function Hd(e){if(!e[Fd]){e[Fd]=!0,Ye.forEach(function(n){"selectionchange"!==n&&(Ld.has(n)||Dd(n,!1,e),Dd(n,!0,e))});var n=9===e.nodeType?e:e.ownerDocument;null===n||n[Fd]||(n[Fd]=!0,Dd("selectionchange",!1,n))}}function Ud(e,n,t,r){switch(df(n)){case 2:var i=rf;break;case 8:i=af;break;default:i=of}t=i.bind(null,n,t,e),i=void 0,!Hn||"touchstart"!==n&&"touchmove"!==n&&"wheel"!==n||(i=!0),r?void 0!==i?e.addEventListener(n,t,{capture:!0,passive:i}):e.addEventListener(n,t,!0):void 0!==i?e.addEventListener(n,t,{passive:i}):e.addEventListener(n,t,!1)}function Wd(e,n,t,r,i){var a=r;if(0===(1&n)&&0===(2&n)&&null!==r)e:for(;;){if(null===r)return;var o=r.tag;if(3===o||4===o){var s=r.stateNode.containerInfo;if(s===i)break;if(4===o)for(o=r.return;null!==o;){var c=o.tag;if((3===c||4===c)&&o.stateNode.containerInfo===i)return;o=o.return}for(;null!==s;){if(null===(o=Ue(s)))return;if(5===(c=o.tag)||6===c||26===c||27===c){r=a=o;continue e}s=s.parentNode}}r=r.return}In(function(){var r=a,i=Tn(t),o=[];e:{var s=wr.get(e);if(void 0!==s){var c=et,d=e;switch(e){case"keypress":if(0===Yn(t))break e;case"keydown":case"keyup":c=mt;break;case"focusin":d="focus",c=ot;break;case"focusout":d="blur",c=ot;break;case"beforeblur":case"afterblur":c=ot;break;case"click":if(2===t.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":c=it;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":c=at;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":c=bt;break;case pr:case gr:case mr:c=st;break;case vr:c=yt;break;case"scroll":case"scrollend":c=tt;break;case"wheel":c=vt;break;case"copy":case"cut":case"paste":c=lt;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":c=ht;break;case"toggle":case"beforetoggle":c=wt}var u=0!==(4&n),f=!u&&("scroll"===e||"scrollend"===e),p=u?null!==s?s+"Capture":null:s;u=[];for(var g,m=r;null!==m;){var h=m;if(g=h.stateNode,5!==(h=h.tag)&&26!==h&&27!==h||null===g||null===p||null!=(h=Dn(m,p))&&u.push(Bd(m,h,g)),f)break;m=m.return}0<u.length&&(s=new c(s,d,null,t,i),o.push({event:s,listeners:u}))}}if(0===(7&n)){if(c="mouseout"===e||"pointerout"===e,(!(s="mouseover"===e||"pointerover"===e)||t===On||!(d=t.relatedTarget||t.fromElement)||!Ue(d)&&!d[Ne])&&(c||s)&&(s=i.window===i?i:(s=i.ownerDocument)?s.defaultView||s.parentWindow:window,c?(c=r,null!==(d=(d=t.relatedTarget||t.toElement)?Ue(d):null)&&(f=l(d),u=d.tag,d!==f||5!==u&&27!==u&&6!==u)&&(d=null)):(c=null,d=r),c!==d)){if(u=it,h="onMouseLeave",p="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(u=ht,h="onPointerLeave",p="onPointerEnter",m="pointer"),f=null==c?s:Be(c),g=null==d?s:Be(d),(s=new u(h,m+"leave",c,t,i)).target=f,s.relatedTarget=g,h=null,Ue(i)===r&&((u=new u(p,m+"enter",d,t,i)).target=g,u.relatedTarget=f,h=u),f=h,c&&d)e:{for(p=d,m=0,g=u=c;g;g=qd(g))m++;for(g=0,h=p;h;h=qd(h))g++;for(;0<m-g;)u=qd(u),m--;for(;0<g-m;)p=qd(p),g--;for(;m--;){if(u===p||null!==p&&u===p.alternate)break e;u=qd(u),p=qd(p)}u=null}else u=null;null!==c&&Yd(o,s,c,u,!1),null!==d&&null!==f&&Yd(o,f,d,u,!0)}if("select"===(c=(s=r?Be(r):window).nodeName&&s.nodeName.toLowerCase())||"input"===c&&"file"===s.type)var b=Dt;else if(Tt(s))if(Ft)b=Kt;else{b=Yt;var y=qt}else!(c=s.nodeName)||"input"!==c.toLowerCase()||"checkbox"!==s.type&&"radio"!==s.type?r&&jn(r.elementType)&&(b=Dt):b=Gt;switch(b&&(b=b(e,r))?At(o,b,t,i):(y&&y(e,s,r),"focusout"===e&&r&&"number"===s.type&&null!=r.memoizedProps.value&&vn(s,"number",s.value)),y=r?Be(r):window,e){case"focusin":(Tt(y)||"true"===y.contentEditable)&&(rr=y,ir=r,ar=null);break;case"focusout":ar=ir=rr=null;break;case"mousedown":or=!0;break;case"contextmenu":case"mouseup":case"dragend":or=!1,sr(o,t,i);break;case"selectionchange":if(tr)break;case"keydown":case"keyup":sr(o,t,i)}var v;if(xt)e:{switch(e){case"compositionstart":var w="onCompositionStart";break e;case"compositionend":w="onCompositionEnd";break e;case"compositionupdate":w="onCompositionUpdate";break e}w=void 0}else _t?Pt(e,t)&&(w="onCompositionEnd"):"keydown"===e&&229===t.keyCode&&(w="onCompositionStart");w&&(Ct&&"ko"!==t.locale&&(_t||"onCompositionStart"!==w?"onCompositionEnd"===w&&_t&&(v=qn()):(Bn="value"in(Wn=i)?Wn.value:Wn.textContent,_t=!0)),0<(y=Vd(r,w)).length&&(w=new ct(w,e,null,t,i),o.push({event:w,listeners:y}),v?w.data=v:null!==(v=Rt(t))&&(w.data=v))),(v=Et?function(e,n){switch(e){case"compositionend":return Rt(n);case"keypress":return 32!==n.which?null:(jt=!0,zt);case"textInput":return(e=n.data)===zt&&jt?null:e;default:return null}}(e,t):function(e,n){if(_t)return"compositionend"===e||!xt&&Pt(e,n)?(e=qn(),Vn=Bn=Wn=null,_t=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(n.ctrlKey||n.altKey||n.metaKey)||n.ctrlKey&&n.altKey){if(n.char&&1<n.char.length)return n.char;if(n.which)return String.fromCharCode(n.which)}return null;case"compositionend":return Ct&&"ko"!==n.locale?null:n.data}}(e,t))&&(0<(w=Vd(r,"onBeforeInput")).length&&(y=new ct("onBeforeInput","beforeinput",null,t,i),o.push({event:y,listeners:w}),y.data=v)),function(e,n,t,r,i){if("submit"===n&&t&&t.stateNode===i){var a=_d((i[Ae]||null).action),o=r.submitter;o&&null!==(n=(n=o[Ae]||null)?_d(n.formAction):o.getAttribute("formAction"))&&(a=n,o=null);var s=new et("action","action",null,r,i);e.push({event:s,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==kd){var e=o?Od(i,o):new FormData(i);Oo(t,{pending:!0,data:e,method:i.method,action:a},null,e)}}else"function"===typeof a&&(s.preventDefault(),e=o?Od(i,o):new FormData(i),Oo(t,{pending:!0,data:e,method:i.method,action:a},a,e))},currentTarget:i}]})}}(o,e,r,t,i)}Md(o,n)})}function Bd(e,n,t){return{instance:e,listener:n,currentTarget:t}}function Vd(e,n){for(var t=n+"Capture",r=[];null!==e;){var i=e,a=i.stateNode;if(5!==(i=i.tag)&&26!==i&&27!==i||null===a||(null!=(i=Dn(e,t))&&r.unshift(Bd(e,i,a)),null!=(i=Dn(e,n))&&r.push(Bd(e,i,a))),3===e.tag)return r;e=e.return}return[]}function qd(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function Yd(e,n,t,r,i){for(var a=n._reactName,o=[];null!==t&&t!==r;){var s=t,l=s.alternate,c=s.stateNode;if(s=s.tag,null!==l&&l===r)break;5!==s&&26!==s&&27!==s||null===c||(l=c,i?null!=(c=Dn(t,a))&&o.unshift(Bd(t,c,l)):i||null!=(c=Dn(t,a))&&o.push(Bd(t,c,l))),t=t.return}0!==o.length&&e.push({event:n,listeners:o})}var Gd=/\r\n?/g,Kd=/\u0000|\uFFFD/g;function Qd(e){return("string"===typeof e?e:""+e).replace(Gd,"\n").replace(Kd,"")}function $d(e,n){return n=Qd(n),Qd(e)===n}function Zd(){}function Xd(e,n,t,r,i,a){switch(t){case"children":"string"===typeof r?"body"===n||"textarea"===n&&""===r||Sn(e,r):("number"===typeof r||"bigint"===typeof r)&&"body"!==n&&Sn(e,""+r);break;case"className":tn(e,"class",r);break;case"tabIndex":tn(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":tn(e,t,r);break;case"style":zn(e,r,a);break;case"data":if("object"!==n){tn(e,"data",r);break}case"src":case"href":if(""===r&&("a"!==n||"href"!==t)){e.removeAttribute(t);break}if(null==r||"function"===typeof r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(t);break}r=_n(""+r),e.setAttribute(t,r);break;case"action":case"formAction":if("function"===typeof r){e.setAttribute(t,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"===typeof a&&("formAction"===t?("input"!==n&&Xd(e,n,"name",i.name,i,null),Xd(e,n,"formEncType",i.formEncType,i,null),Xd(e,n,"formMethod",i.formMethod,i,null),Xd(e,n,"formTarget",i.formTarget,i,null)):(Xd(e,n,"encType",i.encType,i,null),Xd(e,n,"method",i.method,i,null),Xd(e,n,"target",i.target,i,null))),null==r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(t);break}r=_n(""+r),e.setAttribute(t,r);break;case"onClick":null!=r&&(e.onclick=Zd);break;case"onScroll":null!=r&&Id("scroll",e);break;case"onScrollEnd":null!=r&&Id("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(o(61));if(null!=(t=r.__html)){if(null!=i.children)throw Error(o(60));e.innerHTML=t}}break;case"multiple":e.multiple=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"muted":e.muted=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==r||"function"===typeof r||"boolean"===typeof r||"symbol"===typeof r){e.removeAttribute("xlink:href");break}t=_n(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",t);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(t,""+r):e.removeAttribute(t);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(t,""):e.removeAttribute(t);break;case"capture":case"download":!0===r?e.setAttribute(t,""):!1!==r&&null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(t,r):e.removeAttribute(t);break;case"cols":case"rows":case"size":case"span":null!=r&&"function"!==typeof r&&"symbol"!==typeof r&&!isNaN(r)&&1<=r?e.setAttribute(t,r):e.removeAttribute(t);break;case"rowSpan":case"start":null==r||"function"===typeof r||"symbol"===typeof r||isNaN(r)?e.removeAttribute(t):e.setAttribute(t,r);break;case"popover":Id("beforetoggle",e),Id("toggle",e),nn(e,"popover",r);break;case"xlinkActuate":rn(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":rn(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":rn(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":rn(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":rn(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":rn(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":rn(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":rn(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":rn(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":nn(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&nn(e,t=Pn.get(t)||t,r)}}function Jd(e,n,t,r,i,a){switch(t){case"style":zn(e,r,a);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(o(61));if(null!=(t=r.__html)){if(null!=i.children)throw Error(o(60));e.innerHTML=t}}break;case"children":"string"===typeof r?Sn(e,r):("number"===typeof r||"bigint"===typeof r)&&Sn(e,""+r);break;case"onScroll":null!=r&&Id("scroll",e);break;case"onScrollEnd":null!=r&&Id("scrollend",e);break;case"onClick":null!=r&&(e.onclick=Zd);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Ge.hasOwnProperty(t)||("o"!==t[0]||"n"!==t[1]||(i=t.endsWith("Capture"),n=t.slice(2,i?t.length-7:void 0),"function"===typeof(a=null!=(a=e[Ae]||null)?a[t]:null)&&e.removeEventListener(n,a,i),"function"!==typeof r)?t in e?e[t]=r:!0===r?e.setAttribute(t,""):nn(e,t,r):("function"!==typeof a&&null!==a&&(t in e?e[t]=null:e.hasAttribute(t)&&e.removeAttribute(t)),e.addEventListener(n,r,i)))}}function eu(e,n,t){switch(n){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Id("error",e),Id("load",e);var r,i=!1,a=!1;for(r in t)if(t.hasOwnProperty(r)){var s=t[r];if(null!=s)switch(r){case"src":i=!0;break;case"srcSet":a=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,n));default:Xd(e,n,r,s,t,null)}}return a&&Xd(e,n,"srcSet",t.srcSet,t,null),void(i&&Xd(e,n,"src",t.src,t,null));case"input":Id("invalid",e);var l=r=s=a=null,c=null,d=null;for(i in t)if(t.hasOwnProperty(i)){var u=t[i];if(null!=u)switch(i){case"name":a=u;break;case"type":s=u;break;case"checked":c=u;break;case"defaultChecked":d=u;break;case"value":r=u;break;case"defaultValue":l=u;break;case"children":case"dangerouslySetInnerHTML":if(null!=u)throw Error(o(137,n));break;default:Xd(e,n,i,u,t,null)}}return yn(e,r,l,c,d,s,a,!1),void fn(e);case"select":for(a in Id("invalid",e),i=s=r=null,t)if(t.hasOwnProperty(a)&&null!=(l=t[a]))switch(a){case"value":r=l;break;case"defaultValue":s=l;break;case"multiple":i=l;default:Xd(e,n,a,l,t,null)}return n=r,t=s,e.multiple=!!i,void(null!=n?wn(e,!!i,n,!1):null!=t&&wn(e,!!i,t,!0));case"textarea":for(s in Id("invalid",e),r=a=i=null,t)if(t.hasOwnProperty(s)&&null!=(l=t[s]))switch(s){case"value":i=l;break;case"defaultValue":a=l;break;case"children":r=l;break;case"dangerouslySetInnerHTML":if(null!=l)throw Error(o(91));break;default:Xd(e,n,s,l,t,null)}return xn(e,i,a,r),void fn(e);case"option":for(c in t)if(t.hasOwnProperty(c)&&null!=(i=t[c]))if("selected"===c)e.selected=i&&"function"!==typeof i&&"symbol"!==typeof i;else Xd(e,n,c,i,t,null);return;case"dialog":Id("beforetoggle",e),Id("toggle",e),Id("cancel",e),Id("close",e);break;case"iframe":case"object":Id("load",e);break;case"video":case"audio":for(i=0;i<Nd.length;i++)Id(Nd[i],e);break;case"image":Id("error",e),Id("load",e);break;case"details":Id("toggle",e);break;case"embed":case"source":case"link":Id("error",e),Id("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(d in t)if(t.hasOwnProperty(d)&&null!=(i=t[d]))switch(d){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,n));default:Xd(e,n,d,i,t,null)}return;default:if(jn(n)){for(u in t)t.hasOwnProperty(u)&&(void 0!==(i=t[u])&&Jd(e,n,u,i,t,void 0));return}}for(l in t)t.hasOwnProperty(l)&&(null!=(i=t[l])&&Xd(e,n,l,i,t,null))}var nu=null,tu=null;function ru(e){return 9===e.nodeType?e:e.ownerDocument}function iu(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function au(e,n){if(0===e)switch(n){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===n?0:e}function ou(e,n){return"textarea"===e||"noscript"===e||"string"===typeof n.children||"number"===typeof n.children||"bigint"===typeof n.children||"object"===typeof n.dangerouslySetInnerHTML&&null!==n.dangerouslySetInnerHTML&&null!=n.dangerouslySetInnerHTML.__html}var su=null;var lu="function"===typeof setTimeout?setTimeout:void 0,cu="function"===typeof clearTimeout?clearTimeout:void 0,du="function"===typeof Promise?Promise:void 0,uu="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof du?function(e){return du.resolve(null).then(e).catch(fu)}:lu;function fu(e){setTimeout(function(){throw e})}function pu(e){return"head"===e}function gu(e,n){var t=n,r=0,i=0;do{var a=t.nextSibling;if(e.removeChild(t),a&&8===a.nodeType)if("/$"===(t=a.data)){if(0<r&&8>r){t=r;var o=e.ownerDocument;if(1&t&&ku(o.documentElement),2&t&&ku(o.body),4&t)for(ku(t=o.head),o=t.firstChild;o;){var s=o.nextSibling,l=o.nodeName;o[Fe]||"SCRIPT"===l||"STYLE"===l||"LINK"===l&&"stylesheet"===o.rel.toLowerCase()||t.removeChild(o),o=s}}if(0===i)return e.removeChild(a),void Pf(n);i--}else"$"===t||"$?"===t||"$!"===t?i++:r=t.charCodeAt(0)-48;else r=0;t=a}while(t);Pf(n)}function mu(e){var n=e.firstChild;for(n&&10===n.nodeType&&(n=n.nextSibling);n;){var t=n;switch(n=n.nextSibling,t.nodeName){case"HTML":case"HEAD":case"BODY":mu(t),He(t);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===t.rel.toLowerCase())continue}e.removeChild(t)}}function hu(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function bu(e){for(;null!=e;e=e.nextSibling){var n=e.nodeType;if(1===n||3===n)break;if(8===n){if("$"===(n=e.data)||"$!"===n||"$?"===n||"F!"===n||"F"===n)break;if("/$"===n)return null}}return e}var yu=null;function vu(e){e=e.previousSibling;for(var n=0;e;){if(8===e.nodeType){var t=e.data;if("$"===t||"$!"===t||"$?"===t){if(0===n)return e;n--}else"/$"===t&&n++}e=e.previousSibling}return null}function wu(e,n,t){switch(n=ru(t),e){case"html":if(!(e=n.documentElement))throw Error(o(452));return e;case"head":if(!(e=n.head))throw Error(o(453));return e;case"body":if(!(e=n.body))throw Error(o(454));return e;default:throw Error(o(451))}}function ku(e){for(var n=e.attributes;n.length;)e.removeAttributeNode(n[0]);He(e)}var xu=new Map,Su=new Set;function Eu(e){return"function"===typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var Cu=L.d;L.d={f:function(){var e=Cu.f(),n=Uc();return e||n},r:function(e){var n=We(e);null!==n&&5===n.tag&&"form"===n.type?Ao(n):Cu.r(e)},D:function(e){Cu.D(e),ju("dns-prefetch",e,null)},C:function(e,n){Cu.C(e,n),ju("preconnect",e,n)},L:function(e,n,t){Cu.L(e,n,t);var r=zu;if(r&&e&&n){var i='link[rel="preload"][as="'+hn(n)+'"]';"image"===n&&t&&t.imageSrcSet?(i+='[imagesrcset="'+hn(t.imageSrcSet)+'"]',"string"===typeof t.imageSizes&&(i+='[imagesizes="'+hn(t.imageSizes)+'"]')):i+='[href="'+hn(e)+'"]';var a=i;switch(n){case"style":a=Ru(e);break;case"script":a=Tu(e)}xu.has(a)||(e=f({rel:"preload",href:"image"===n&&t&&t.imageSrcSet?void 0:e,as:n},t),xu.set(a,e),null!==r.querySelector(i)||"style"===n&&r.querySelector(_u(a))||"script"===n&&r.querySelector(Au(a))||(eu(n=r.createElement("link"),"link",e),qe(n),r.head.appendChild(n)))}},m:function(e,n){Cu.m(e,n);var t=zu;if(t&&e){var r=n&&"string"===typeof n.as?n.as:"script",i='link[rel="modulepreload"][as="'+hn(r)+'"][href="'+hn(e)+'"]',a=i;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":a=Tu(e)}if(!xu.has(a)&&(e=f({rel:"modulepreload",href:e},n),xu.set(a,e),null===t.querySelector(i))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(t.querySelector(Au(a)))return}eu(r=t.createElement("link"),"link",e),qe(r),t.head.appendChild(r)}}},X:function(e,n){Cu.X(e,n);var t=zu;if(t&&e){var r=Ve(t).hoistableScripts,i=Tu(e),a=r.get(i);a||((a=t.querySelector(Au(i)))||(e=f({src:e,async:!0},n),(n=xu.get(i))&&Iu(e,n),qe(a=t.createElement("script")),eu(a,"link",e),t.head.appendChild(a)),a={type:"script",instance:a,count:1,state:null},r.set(i,a))}},S:function(e,n,t){Cu.S(e,n,t);var r=zu;if(r&&e){var i=Ve(r).hoistableStyles,a=Ru(e);n=n||"default";var o=i.get(a);if(!o){var s={loading:0,preload:null};if(o=r.querySelector(_u(a)))s.loading=5;else{e=f({rel:"stylesheet",href:e,"data-precedence":n},t),(t=xu.get(a))&&Mu(e,t);var l=o=r.createElement("link");qe(l),eu(l,"link",e),l._p=new Promise(function(e,n){l.onload=e,l.onerror=n}),l.addEventListener("load",function(){s.loading|=1}),l.addEventListener("error",function(){s.loading|=2}),s.loading|=4,Lu(o,n,r)}o={type:"stylesheet",instance:o,count:1,state:s},i.set(a,o)}}},M:function(e,n){Cu.M(e,n);var t=zu;if(t&&e){var r=Ve(t).hoistableScripts,i=Tu(e),a=r.get(i);a||((a=t.querySelector(Au(i)))||(e=f({src:e,async:!0,type:"module"},n),(n=xu.get(i))&&Iu(e,n),qe(a=t.createElement("script")),eu(a,"link",e),t.head.appendChild(a)),a={type:"script",instance:a,count:1,state:null},r.set(i,a))}}};var zu="undefined"===typeof document?null:document;function ju(e,n,t){var r=zu;if(r&&"string"===typeof n&&n){var i=hn(n);i='link[rel="'+e+'"][href="'+i+'"]',"string"===typeof t&&(i+='[crossorigin="'+t+'"]'),Su.has(i)||(Su.add(i),e={rel:e,crossOrigin:t,href:n},null===r.querySelector(i)&&(eu(n=r.createElement("link"),"link",e),qe(n),r.head.appendChild(n)))}}function Pu(e,n,t,r){var i,a,s,l,c=(c=V.current)?Eu(c):null;if(!c)throw Error(o(446));switch(e){case"meta":case"title":return null;case"style":return"string"===typeof t.precedence&&"string"===typeof t.href?(n=Ru(t.href),(r=(t=Ve(c).hoistableStyles).get(n))||(r={type:"style",instance:null,count:0,state:null},t.set(n,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===t.rel&&"string"===typeof t.href&&"string"===typeof t.precedence){e=Ru(t.href);var d=Ve(c).hoistableStyles,u=d.get(e);if(u||(c=c.ownerDocument||c,u={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},d.set(e,u),(d=c.querySelector(_u(e)))&&!d._p&&(u.instance=d,u.state.loading=5),xu.has(e)||(t={rel:"preload",as:"style",href:t.href,crossOrigin:t.crossOrigin,integrity:t.integrity,media:t.media,hrefLang:t.hrefLang,referrerPolicy:t.referrerPolicy},xu.set(e,t),d||(i=c,a=e,s=t,l=u.state,i.querySelector('link[rel="preload"][as="style"]['+a+"]")?l.loading=1:(a=i.createElement("link"),l.preload=a,a.addEventListener("load",function(){return l.loading|=1}),a.addEventListener("error",function(){return l.loading|=2}),eu(a,"link",s),qe(a),i.head.appendChild(a))))),n&&null===r)throw Error(o(528,""));return u}if(n&&null!==r)throw Error(o(529,""));return null;case"script":return n=t.async,"string"===typeof(t=t.src)&&n&&"function"!==typeof n&&"symbol"!==typeof n?(n=Tu(t),(r=(t=Ve(c).hoistableScripts).get(n))||(r={type:"script",instance:null,count:0,state:null},t.set(n,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,e))}}function Ru(e){return'href="'+hn(e)+'"'}function _u(e){return'link[rel="stylesheet"]['+e+"]"}function Ou(e){return f({},e,{"data-precedence":e.precedence,precedence:null})}function Tu(e){return'[src="'+hn(e)+'"]'}function Au(e){return"script[async]"+e}function Nu(e,n,t){if(n.count++,null===n.instance)switch(n.type){case"style":var r=e.querySelector('style[data-href~="'+hn(t.href)+'"]');if(r)return n.instance=r,qe(r),r;var i=f({},t,{"data-href":t.href,"data-precedence":t.precedence,href:null,precedence:null});return qe(r=(e.ownerDocument||e).createElement("style")),eu(r,"style",i),Lu(r,t.precedence,e),n.instance=r;case"stylesheet":i=Ru(t.href);var a=e.querySelector(_u(i));if(a)return n.state.loading|=4,n.instance=a,qe(a),a;r=Ou(t),(i=xu.get(i))&&Mu(r,i),qe(a=(e.ownerDocument||e).createElement("link"));var s=a;return s._p=new Promise(function(e,n){s.onload=e,s.onerror=n}),eu(a,"link",r),n.state.loading|=4,Lu(a,t.precedence,e),n.instance=a;case"script":return a=Tu(t.src),(i=e.querySelector(Au(a)))?(n.instance=i,qe(i),i):(r=t,(i=xu.get(a))&&Iu(r=f({},t),i),qe(i=(e=e.ownerDocument||e).createElement("script")),eu(i,"link",r),e.head.appendChild(i),n.instance=i);case"void":return null;default:throw Error(o(443,n.type))}else"stylesheet"===n.type&&0===(4&n.state.loading)&&(r=n.instance,n.state.loading|=4,Lu(r,t.precedence,e));return n.instance}function Lu(e,n,t){for(var r=t.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=r.length?r[r.length-1]:null,a=i,o=0;o<r.length;o++){var s=r[o];if(s.dataset.precedence===n)a=s;else if(a!==i)break}a?a.parentNode.insertBefore(e,a.nextSibling):(n=9===t.nodeType?t.head:t).insertBefore(e,n.firstChild)}function Mu(e,n){null==e.crossOrigin&&(e.crossOrigin=n.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=n.referrerPolicy),null==e.title&&(e.title=n.title)}function Iu(e,n){null==e.crossOrigin&&(e.crossOrigin=n.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=n.referrerPolicy),null==e.integrity&&(e.integrity=n.integrity)}var Du=null;function Fu(e,n,t){if(null===Du){var r=new Map,i=Du=new Map;i.set(t,r)}else(r=(i=Du).get(t))||(r=new Map,i.set(t,r));if(r.has(e))return r;for(r.set(e,null),t=t.getElementsByTagName(e),i=0;i<t.length;i++){var a=t[i];if(!(a[Fe]||a[Te]||"link"===e&&"stylesheet"===a.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==a.namespaceURI){var o=a.getAttribute(n)||"";o=e+o;var s=r.get(o);s?s.push(a):r.set(o,[a])}}return r}function Hu(e,n,t){(e=e.ownerDocument||e).head.insertBefore(t,"title"===n?e.querySelector("head > title"):null)}function Uu(e){return"stylesheet"!==e.type||0!==(3&e.state.loading)}var Wu=null;function Bu(){}function Vu(){if(this.count--,0===this.count)if(this.stylesheets)Yu(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var qu=null;function Yu(e,n){e.stylesheets=null,null!==e.unsuspend&&(e.count++,qu=new Map,n.forEach(Gu,e),qu=null,Vu.call(e))}function Gu(e,n){if(!(4&n.state.loading)){var t=qu.get(e);if(t)var r=t.get(null);else{t=new Map,qu.set(e,t);for(var i=e.querySelectorAll("link[data-precedence],style[data-precedence]"),a=0;a<i.length;a++){var o=i[a];"LINK"!==o.nodeName&&"not all"===o.getAttribute("media")||(t.set(o.dataset.precedence,o),r=o)}r&&t.set(null,r)}o=(i=n.instance).getAttribute("data-precedence"),(a=t.get(o)||r)===r&&t.set(null,i),t.set(o,i),this.count++,r=Vu.bind(this),i.addEventListener("load",r),i.addEventListener("error",r),a?a.parentNode.insertBefore(i,a.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(i,e.firstChild),n.state.loading|=4}}var Ku={$$typeof:k,Provider:null,Consumer:null,_currentValue:M,_currentValue2:M,_threadCount:0};function Qu(e,n,t,r,i,a,o,s){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Ee(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ee(0),this.hiddenUpdates=Ee(null),this.identifierPrefix=r,this.onUncaughtError=i,this.onCaughtError=a,this.onRecoverableError=o,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=s,this.incompleteTransitions=new Map}function $u(e,n,t,r,i,a,o,s,l,c,d,u){return e=new Qu(e,n,t,o,s,l,c,u),n=1,!0===a&&(n|=24),a=Mr(3,null,null,n),e.current=a,a.stateNode=e,(n=Ai()).refCount++,e.pooledCache=n,n.refCount++,a.memoizedState={element:r,isDehydrated:t,cache:n},ta(a),e}function Zu(e){return e?e=Nr:Nr}function Xu(e,n,t,r,i,a){i=Zu(i),null===r.context?r.context=i:r.pendingContext=i,(r=ia(n)).payload={element:t},null!==(a=void 0===a?null:a)&&(r.callback=a),null!==(t=aa(e,r,n))&&(Mc(t,0,n),oa(t,e,n))}function Ju(e,n){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var t=e.retryLane;e.retryLane=0!==t&&t<n?t:n}}function ef(e,n){Ju(e,n),(e=e.alternate)&&Ju(e,n)}function nf(e){if(13===e.tag){var n=Or(e,67108864);null!==n&&Mc(n,0,67108864),ef(e,67108864)}}var tf=!0;function rf(e,n,t,r){var i=N.T;N.T=null;var a=L.p;try{L.p=2,of(e,n,t,r)}finally{L.p=a,N.T=i}}function af(e,n,t,r){var i=N.T;N.T=null;var a=L.p;try{L.p=8,of(e,n,t,r)}finally{L.p=a,N.T=i}}function of(e,n,t,r){if(tf){var i=sf(r);if(null===i)Wd(e,n,r,lf,t),vf(e,r);else if(function(e,n,t,r,i){switch(n){case"focusin":return ff=wf(ff,e,n,t,r,i),!0;case"dragenter":return pf=wf(pf,e,n,t,r,i),!0;case"mouseover":return gf=wf(gf,e,n,t,r,i),!0;case"pointerover":var a=i.pointerId;return mf.set(a,wf(mf.get(a)||null,e,n,t,r,i)),!0;case"gotpointercapture":return a=i.pointerId,hf.set(a,wf(hf.get(a)||null,e,n,t,r,i)),!0}return!1}(i,e,n,t,r))r.stopPropagation();else if(vf(e,r),4&n&&-1<yf.indexOf(e)){for(;null!==i;){var a=We(i);if(null!==a)switch(a.tag){case 3:if((a=a.stateNode).current.memoizedState.isDehydrated){var o=ye(a.pendingLanes);if(0!==o){var s=a;for(s.pendingLanes|=2,s.entangledLanes|=2;o;){var l=1<<31-pe(o);s.entanglements[1]|=l,o&=~l}xd(a),0===(6&tc)&&(xc=ne()+500,Sd(0,!1))}}break;case 13:null!==(s=Or(a,2))&&Mc(s,0,2),Uc(),ef(a,2)}if(null===(a=sf(r))&&Wd(e,n,r,lf,t),a===i)break;i=a}null!==i&&r.stopPropagation()}else Wd(e,n,r,null,t)}}function sf(e){return cf(e=Tn(e))}var lf=null;function cf(e){if(lf=null,null!==(e=Ue(e))){var n=l(e);if(null===n)e=null;else{var t=n.tag;if(13===t){if(null!==(e=c(n)))return e;e=null}else if(3===t){if(n.stateNode.current.memoizedState.isDehydrated)return 3===n.tag?n.stateNode.containerInfo:null;e=null}else n!==e&&(e=null)}}return lf=e,null}function df(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(te()){case re:return 2;case ie:return 8;case ae:case oe:return 32;case se:return 268435456;default:return 32}default:return 32}}var uf=!1,ff=null,pf=null,gf=null,mf=new Map,hf=new Map,bf=[],yf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function vf(e,n){switch(e){case"focusin":case"focusout":ff=null;break;case"dragenter":case"dragleave":pf=null;break;case"mouseover":case"mouseout":gf=null;break;case"pointerover":case"pointerout":mf.delete(n.pointerId);break;case"gotpointercapture":case"lostpointercapture":hf.delete(n.pointerId)}}function wf(e,n,t,r,i,a){return null===e||e.nativeEvent!==a?(e={blockedOn:n,domEventName:t,eventSystemFlags:r,nativeEvent:a,targetContainers:[i]},null!==n&&(null!==(n=We(n))&&nf(n)),e):(e.eventSystemFlags|=r,n=e.targetContainers,null!==i&&-1===n.indexOf(i)&&n.push(i),e)}function kf(e){var n=Ue(e.target);if(null!==n){var t=l(n);if(null!==t)if(13===(n=t.tag)){if(null!==(n=c(t)))return e.blockedOn=n,void function(e,n){var t=L.p;try{return L.p=e,n()}finally{L.p=t}}(e.priority,function(){if(13===t.tag){var e=Nc();e=Pe(e);var n=Or(t,e);null!==n&&Mc(n,0,e),ef(t,e)}})}else if(3===n&&t.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===t.tag?t.stateNode.containerInfo:null)}e.blockedOn=null}function xf(e){if(null!==e.blockedOn)return!1;for(var n=e.targetContainers;0<n.length;){var t=sf(e.nativeEvent);if(null!==t)return null!==(n=We(t))&&nf(n),e.blockedOn=t,!1;var r=new(t=e.nativeEvent).constructor(t.type,t);On=r,t.target.dispatchEvent(r),On=null,n.shift()}return!0}function Sf(e,n,t){xf(e)&&t.delete(n)}function Ef(){uf=!1,null!==ff&&xf(ff)&&(ff=null),null!==pf&&xf(pf)&&(pf=null),null!==gf&&xf(gf)&&(gf=null),mf.forEach(Sf),hf.forEach(Sf)}function Cf(e,n){e.blockedOn===n&&(e.blockedOn=null,uf||(uf=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,Ef)))}var zf=null;function jf(e){zf!==e&&(zf=e,r.unstable_scheduleCallback(r.unstable_NormalPriority,function(){zf===e&&(zf=null);for(var n=0;n<e.length;n+=3){var t=e[n],r=e[n+1],i=e[n+2];if("function"!==typeof r){if(null===cf(r||t))continue;break}var a=We(t);null!==a&&(e.splice(n,3),n-=3,Oo(a,{pending:!0,data:i,method:t.method,action:r},r,i))}}))}function Pf(e){function n(n){return Cf(n,e)}null!==ff&&Cf(ff,e),null!==pf&&Cf(pf,e),null!==gf&&Cf(gf,e),mf.forEach(n),hf.forEach(n);for(var t=0;t<bf.length;t++){var r=bf[t];r.blockedOn===e&&(r.blockedOn=null)}for(;0<bf.length&&null===(t=bf[0]).blockedOn;)kf(t),null===t.blockedOn&&bf.shift();if(null!=(t=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<t.length;r+=3){var i=t[r],a=t[r+1],o=i[Ae]||null;if("function"===typeof a)o||jf(t);else if(o){var s=null;if(a&&a.hasAttribute("formAction")){if(i=a,o=a[Ae]||null)s=o.formAction;else if(null!==cf(i))continue}else s=o.action;"function"===typeof s?t[r+1]=s:(t.splice(r,3),r-=3),jf(t)}}}function Rf(e){this._internalRoot=e}function _f(e){this._internalRoot=e}_f.prototype.render=Rf.prototype.render=function(e){var n=this._internalRoot;if(null===n)throw Error(o(409));Xu(n.current,Nc(),e,n,null,null)},_f.prototype.unmount=Rf.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var n=e.containerInfo;Xu(e.current,2,null,e,null,null),Uc(),n[Ne]=null}},_f.prototype.unstable_scheduleHydration=function(e){if(e){var n=_e();e={blockedOn:null,target:e,priority:n};for(var t=0;t<bf.length&&0!==n&&n<bf[t].priority;t++);bf.splice(t,0,e),0===t&&kf(e)}};var Of=i.version;if("19.1.0"!==Of)throw Error(o(527,Of,"19.1.0"));L.findDOMNode=function(e){var n=e._reactInternals;if(void 0===n){if("function"===typeof e.render)throw Error(o(188));throw e=Object.keys(e).join(","),Error(o(268,e))}return e=function(e){var n=e.alternate;if(!n){if(null===(n=l(e)))throw Error(o(188));return n!==e?null:e}for(var t=e,r=n;;){var i=t.return;if(null===i)break;var a=i.alternate;if(null===a){if(null!==(r=i.return)){t=r;continue}break}if(i.child===a.child){for(a=i.child;a;){if(a===t)return d(i),e;if(a===r)return d(i),n;a=a.sibling}throw Error(o(188))}if(t.return!==r.return)t=i,r=a;else{for(var s=!1,c=i.child;c;){if(c===t){s=!0,t=i,r=a;break}if(c===r){s=!0,r=i,t=a;break}c=c.sibling}if(!s){for(c=a.child;c;){if(c===t){s=!0,t=a,r=i;break}if(c===r){s=!0,r=a,t=i;break}c=c.sibling}if(!s)throw Error(o(189))}}if(t.alternate!==r)throw Error(o(190))}if(3!==t.tag)throw Error(o(188));return t.stateNode.current===t?e:n}(n),e=null===(e=null!==e?u(e):null)?null:e.stateNode};var Tf={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:N,reconcilerVersion:"19.1.0"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Af=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Af.isDisabled&&Af.supportsFiber)try{de=Af.inject(Tf),ue=Af}catch(Lf){}}n.createRoot=function(e,n){if(!s(e))throw Error(o(299));var t=!1,r="",i=ys,a=vs,l=ws;return null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(t=!0),void 0!==n.identifierPrefix&&(r=n.identifierPrefix),void 0!==n.onUncaughtError&&(i=n.onUncaughtError),void 0!==n.onCaughtError&&(a=n.onCaughtError),void 0!==n.onRecoverableError&&(l=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks),n=$u(e,1,!1,null,0,t,r,i,a,l,0,null),e[Ne]=n.current,Hd(e),new Rf(n)},n.hydrateRoot=function(e,n,t){if(!s(e))throw Error(o(299));var r=!1,i="",a=ys,l=vs,c=ws,d=null;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(r=!0),void 0!==t.identifierPrefix&&(i=t.identifierPrefix),void 0!==t.onUncaughtError&&(a=t.onUncaughtError),void 0!==t.onCaughtError&&(l=t.onCaughtError),void 0!==t.onRecoverableError&&(c=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks,void 0!==t.formState&&(d=t.formState)),(n=$u(e,1,!0,n,0,r,i,a,l,c,0,d)).context=Zu(null),t=n.current,(i=ia(r=Pe(r=Nc()))).callback=null,aa(t,i,r),t=r,n.current.lanes=t,Ce(n,t),xd(n),e[Ne]=n.current,Hd(e),new _f(n)},n.version="19.1.0"},29:function(e,n,t){var r;r=function(e){return function(e){var n={};function t(r){if(n[r])return n[r].exports;var i=n[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,t),i.l=!0,i.exports}return t.m=e,t.c=n,t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:r})},t.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,n){if(1&n&&(e=t(e)),8&n)return e;if(4&n&&"object"===typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(t.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var i in e)t.d(r,i,function(n){return e[n]}.bind(null,i));return r},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},t.p="",t(t.s="./src/react-webcam.tsx")}({"./src/react-webcam.tsx":function(e,n,t){"use strict";t.r(n);var r=t("react"),i=function(){var e=function(n,t){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,n){e.__proto__=n}||function(e,n){for(var t in n)n.hasOwnProperty(t)&&(e[t]=n[t])},e(n,t)};return function(n,t){function r(){this.constructor=n}e(n,t),n.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}}(),a=function(){return a=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var i in n=arguments[t])Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i]);return e},a.apply(this,arguments)},o=function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(e);i<r.length;i++)n.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(t[r[i]]=e[r[i]])}return t};function s(){return!(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)}"undefined"!==typeof window&&(void 0===navigator.mediaDevices&&(navigator.mediaDevices={}),void 0===navigator.mediaDevices.getUserMedia&&(navigator.mediaDevices.getUserMedia=function(e){var n=navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.msGetUserMedia;return n?new Promise(function(t,r){n.call(navigator,e,t,r)}):Promise.reject(new Error("getUserMedia is not implemented in this browser"))}));var l=function(e){function n(n){var t=e.call(this,n)||this;return t.canvas=null,t.ctx=null,t.requestUserMediaId=0,t.unmounted=!1,t.state={hasUserMedia:!1},t}return i(n,e),n.prototype.componentDidMount=function(){var e=this.state,n=this.props;this.unmounted=!1,s()?(e.hasUserMedia||this.requestUserMedia(),n.children&&"function"!=typeof n.children&&console.warn("children must be a function")):n.onUserMediaError("getUserMedia not supported")},n.prototype.componentDidUpdate=function(e){var n=this.props;if(s()){var t=JSON.stringify(e.audioConstraints)!==JSON.stringify(n.audioConstraints),r=JSON.stringify(e.videoConstraints)!==JSON.stringify(n.videoConstraints),i=e.minScreenshotWidth!==n.minScreenshotWidth,a=e.minScreenshotHeight!==n.minScreenshotHeight;(r||i||a)&&(this.canvas=null,this.ctx=null),(t||r)&&(this.stopAndCleanup(),this.requestUserMedia())}else n.onUserMediaError("getUserMedia not supported")},n.prototype.componentWillUnmount=function(){this.unmounted=!0,this.stopAndCleanup()},n.stopMediaStream=function(e){e&&(e.getVideoTracks&&e.getAudioTracks?(e.getVideoTracks().map(function(n){e.removeTrack(n),n.stop()}),e.getAudioTracks().map(function(n){e.removeTrack(n),n.stop()})):e.stop())},n.prototype.stopAndCleanup=function(){var e=this.state;e.hasUserMedia&&(n.stopMediaStream(this.stream),e.src&&window.URL.revokeObjectURL(e.src))},n.prototype.getScreenshot=function(e){var n=this.state,t=this.props;if(!n.hasUserMedia)return null;var r=this.getCanvas(e);return r&&r.toDataURL(t.screenshotFormat,t.screenshotQuality)},n.prototype.getCanvas=function(e){var n=this.state,t=this.props;if(!this.video)return null;if(!n.hasUserMedia||!this.video.videoHeight)return null;if(!this.ctx){var r=this.video.videoWidth,i=this.video.videoHeight;if(!this.props.forceScreenshotSourceSize){var a=r/i;i=(r=t.minScreenshotWidth||this.video.clientWidth)/a,t.minScreenshotHeight&&i<t.minScreenshotHeight&&(r=(i=t.minScreenshotHeight)*a)}this.canvas=document.createElement("canvas"),this.canvas.width=(null===e||void 0===e?void 0:e.width)||r,this.canvas.height=(null===e||void 0===e?void 0:e.height)||i,this.ctx=this.canvas.getContext("2d")}var o=this.ctx,s=this.canvas;return o&&s&&(s.width=(null===e||void 0===e?void 0:e.width)||s.width,s.height=(null===e||void 0===e?void 0:e.height)||s.height,t.mirrored&&(o.translate(s.width,0),o.scale(-1,1)),o.imageSmoothingEnabled=t.imageSmoothing,o.drawImage(this.video,0,0,(null===e||void 0===e?void 0:e.width)||s.width,(null===e||void 0===e?void 0:e.height)||s.height),t.mirrored&&(o.scale(-1,1),o.translate(-s.width,0))),s},n.prototype.requestUserMedia=function(){var e=this,t=this.props,r=function(r,i){var a={video:"undefined"===typeof i||i};t.audio&&(a.audio="undefined"===typeof r||r),e.requestUserMediaId++;var o=e.requestUserMediaId;navigator.mediaDevices.getUserMedia(a).then(function(t){e.unmounted||o!==e.requestUserMediaId?n.stopMediaStream(t):e.handleUserMedia(null,t)}).catch(function(n){e.handleUserMedia(n)})};if("mediaDevices"in navigator)r(t.audioConstraints,t.videoConstraints);else{var i=function(e){return{optional:[{sourceId:e}]}},a=function(e){var n=e.deviceId;return"string"===typeof n?n:Array.isArray(n)&&n.length>0?n[0]:"object"===typeof n&&n.ideal?n.ideal:null};MediaStreamTrack.getSources(function(e){var n=null,o=null;e.forEach(function(e){"audio"===e.kind?n=e.id:"video"===e.kind&&(o=e.id)});var s=a(t.audioConstraints);s&&(n=s);var l=a(t.videoConstraints);l&&(o=l),r(i(n),i(o))})}},n.prototype.handleUserMedia=function(e,n){var t=this.props;if(e||!n)return this.setState({hasUserMedia:!1}),void t.onUserMediaError(e);this.stream=n;try{this.video&&(this.video.srcObject=n),this.setState({hasUserMedia:!0})}catch(r){this.setState({hasUserMedia:!0,src:window.URL.createObjectURL(n)})}t.onUserMedia(n)},n.prototype.render=function(){var e=this,n=this.state,t=this.props,i=t.audio,s=(t.forceScreenshotSourceSize,t.disablePictureInPicture),l=(t.onUserMedia,t.onUserMediaError,t.screenshotFormat,t.screenshotQuality,t.minScreenshotWidth,t.minScreenshotHeight,t.audioConstraints,t.videoConstraints,t.imageSmoothing,t.mirrored),c=t.style,d=void 0===c?{}:c,u=t.children,f=o(t,["audio","forceScreenshotSourceSize","disablePictureInPicture","onUserMedia","onUserMediaError","screenshotFormat","screenshotQuality","minScreenshotWidth","minScreenshotHeight","audioConstraints","videoConstraints","imageSmoothing","mirrored","style","children"]),p=l?a(a({},d),{transform:(d.transform||"")+" scaleX(-1)"}):d,g={getScreenshot:this.getScreenshot.bind(this)};return r.createElement(r.Fragment,null,r.createElement("video",a({autoPlay:!0,disablePictureInPicture:s,src:n.src,muted:!i,playsInline:!0,ref:function(n){e.video=n},style:p},f)),u&&u(g))},n.defaultProps={audio:!1,disablePictureInPicture:!1,forceScreenshotSourceSize:!1,imageSmoothing:!0,mirrored:!1,onUserMedia:function(){},onUserMediaError:function(){},screenshotFormat:"image/webp",screenshotQuality:.92},n}(r.Component);n.default=l},react:function(n,t){n.exports=e}}).default},e.exports=r(t(43))},43:(e,n,t)=>{"use strict";e.exports=t(288)},288:(e,n)=>{"use strict";var t=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),s=Symbol.for("react.consumer"),l=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),u=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var g={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,h={};function b(e,n,t){this.props=e,this.context=n,this.refs=h,this.updater=t||g}function y(){}function v(e,n,t){this.props=e,this.context=n,this.refs=h,this.updater=t||g}b.prototype.isReactComponent={},b.prototype.setState=function(e,n){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,n,"setState")},b.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=b.prototype;var w=v.prototype=new y;w.constructor=v,m(w,b.prototype),w.isPureReactComponent=!0;var k=Array.isArray,x={H:null,A:null,T:null,S:null,V:null},S=Object.prototype.hasOwnProperty;function E(e,n,r,i,a,o){return r=o.ref,{$$typeof:t,type:e,key:n,ref:void 0!==r?r:null,props:o}}function C(e){return"object"===typeof e&&null!==e&&e.$$typeof===t}var z=/\/+/g;function j(e,n){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var n={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return n[e]})}(""+e.key):n.toString(36)}function P(){}function R(e,n,i,a,o){var s=typeof e;"undefined"!==s&&"boolean"!==s||(e=null);var l,c,d=!1;if(null===e)d=!0;else switch(s){case"bigint":case"string":case"number":d=!0;break;case"object":switch(e.$$typeof){case t:case r:d=!0;break;case f:return R((d=e._init)(e._payload),n,i,a,o)}}if(d)return o=o(e),d=""===a?"."+j(e,0):a,k(o)?(i="",null!=d&&(i=d.replace(z,"$&/")+"/"),R(o,n,i,"",function(e){return e})):null!=o&&(C(o)&&(l=o,c=i+(null==o.key||e&&e.key===o.key?"":(""+o.key).replace(z,"$&/")+"/")+d,o=E(l.type,c,void 0,0,0,l.props)),n.push(o)),1;d=0;var u,g=""===a?".":a+":";if(k(e))for(var m=0;m<e.length;m++)d+=R(a=e[m],n,i,s=g+j(a,m),o);else if("function"===typeof(m=null===(u=e)||"object"!==typeof u?null:"function"===typeof(u=p&&u[p]||u["@@iterator"])?u:null))for(e=m.call(e),m=0;!(a=e.next()).done;)d+=R(a=a.value,n,i,s=g+j(a,m++),o);else if("object"===s){if("function"===typeof e.then)return R(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"===typeof e.status?e.then(P,P):(e.status="pending",e.then(function(n){"pending"===e.status&&(e.status="fulfilled",e.value=n)},function(n){"pending"===e.status&&(e.status="rejected",e.reason=n)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(e),n,i,a,o);throw n=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===n?"object with keys {"+Object.keys(e).join(", ")+"}":n)+"). If you meant to render a collection of children, use an array instead.")}return d}function _(e,n,t){if(null==e)return e;var r=[],i=0;return R(e,r,"","",function(e){return n.call(t,e,i++)}),r}function O(e){if(-1===e._status){var n=e._result;(n=n()).then(function(n){0!==e._status&&-1!==e._status||(e._status=1,e._result=n)},function(n){0!==e._status&&-1!==e._status||(e._status=2,e._result=n)}),-1===e._status&&(e._status=0,e._result=n)}if(1===e._status)return e._result.default;throw e._result}var T="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var n=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(n))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function A(){}n.Children={map:_,forEach:function(e,n,t){_(e,function(){n.apply(this,arguments)},t)},count:function(e){var n=0;return _(e,function(){n++}),n},toArray:function(e){return _(e,function(e){return e})||[]},only:function(e){if(!C(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},n.Component=b,n.Fragment=i,n.Profiler=o,n.PureComponent=v,n.StrictMode=a,n.Suspense=d,n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=x,n.__COMPILER_RUNTIME={__proto__:null,c:function(e){return x.H.useMemoCache(e)}},n.cache=function(e){return function(){return e.apply(null,arguments)}},n.cloneElement=function(e,n,t){if(null===e||void 0===e)throw Error("The argument must be a React element, but you passed "+e+".");var r=m({},e.props),i=e.key;if(null!=n)for(a in void 0!==n.ref&&void 0,void 0!==n.key&&(i=""+n.key),n)!S.call(n,a)||"key"===a||"__self"===a||"__source"===a||"ref"===a&&void 0===n.ref||(r[a]=n[a]);var a=arguments.length-2;if(1===a)r.children=t;else if(1<a){for(var o=Array(a),s=0;s<a;s++)o[s]=arguments[s+2];r.children=o}return E(e.type,i,void 0,0,0,r)},n.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:s,_context:e},e},n.createElement=function(e,n,t){var r,i={},a=null;if(null!=n)for(r in void 0!==n.key&&(a=""+n.key),n)S.call(n,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(i[r]=n[r]);var o=arguments.length-2;if(1===o)i.children=t;else if(1<o){for(var s=Array(o),l=0;l<o;l++)s[l]=arguments[l+2];i.children=s}if(e&&e.defaultProps)for(r in o=e.defaultProps)void 0===i[r]&&(i[r]=o[r]);return E(e,a,void 0,0,0,i)},n.createRef=function(){return{current:null}},n.forwardRef=function(e){return{$$typeof:c,render:e}},n.isValidElement=C,n.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:O}},n.memo=function(e,n){return{$$typeof:u,type:e,compare:void 0===n?null:n}},n.startTransition=function(e){var n=x.T,t={};x.T=t;try{var r=e(),i=x.S;null!==i&&i(t,r),"object"===typeof r&&null!==r&&"function"===typeof r.then&&r.then(A,T)}catch(a){T(a)}finally{x.T=n}},n.unstable_useCacheRefresh=function(){return x.H.useCacheRefresh()},n.use=function(e){return x.H.use(e)},n.useActionState=function(e,n,t){return x.H.useActionState(e,n,t)},n.useCallback=function(e,n){return x.H.useCallback(e,n)},n.useContext=function(e){return x.H.useContext(e)},n.useDebugValue=function(){},n.useDeferredValue=function(e,n){return x.H.useDeferredValue(e,n)},n.useEffect=function(e,n,t){var r=x.H;if("function"===typeof t)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,n)},n.useId=function(){return x.H.useId()},n.useImperativeHandle=function(e,n,t){return x.H.useImperativeHandle(e,n,t)},n.useInsertionEffect=function(e,n){return x.H.useInsertionEffect(e,n)},n.useLayoutEffect=function(e,n){return x.H.useLayoutEffect(e,n)},n.useMemo=function(e,n){return x.H.useMemo(e,n)},n.useOptimistic=function(e,n){return x.H.useOptimistic(e,n)},n.useReducer=function(e,n,t){return x.H.useReducer(e,n,t)},n.useRef=function(e){return x.H.useRef(e)},n.useState=function(e){return x.H.useState(e)},n.useSyncExternalStore=function(e,n,t){return x.H.useSyncExternalStore(e,n,t)},n.useTransition=function(){return x.H.useTransition()},n.version="19.1.0"},324:e=>{e.exports=function(e,n,t,r){var i=t?t.call(r,e,n):void 0;if(void 0!==i)return!!i;if(e===n)return!0;if("object"!==typeof e||!e||"object"!==typeof n||!n)return!1;var a=Object.keys(e),o=Object.keys(n);if(a.length!==o.length)return!1;for(var s=Object.prototype.hasOwnProperty.bind(n),l=0;l<a.length;l++){var c=a[l];if(!s(c))return!1;var d=e[c],u=n[c];if(!1===(i=t?t.call(r,d,u,c):void 0)||void 0===i&&d!==u)return!1}return!0}},391:(e,n,t)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(n){console.error(n)}}(),e.exports=t(4)},579:(e,n,t)=>{"use strict";e.exports=t(799)},672:(e,n,t)=>{"use strict";var r=t(43);function i(e){var n="https://react.dev/errors/"+e;if(1<arguments.length){n+="?args[]="+encodeURIComponent(arguments[1]);for(var t=2;t<arguments.length;t++)n+="&args[]="+encodeURIComponent(arguments[t])}return"Minified React error #"+e+"; visit "+n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function a(){}var o={d:{f:a,r:function(){throw Error(i(522))},D:a,C:a,L:a,m:a,X:a,S:a,M:a},p:0,findDOMNode:null},s=Symbol.for("react.portal");var l=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function c(e,n){return"font"===e?"":"string"===typeof n?"use-credentials"===n?n:"":void 0}n.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,n.createPortal=function(e,n){var t=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!n||1!==n.nodeType&&9!==n.nodeType&&11!==n.nodeType)throw Error(i(299));return function(e,n,t){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:s,key:null==r?null:""+r,children:e,containerInfo:n,implementation:t}}(e,n,null,t)},n.flushSync=function(e){var n=l.T,t=o.p;try{if(l.T=null,o.p=2,e)return e()}finally{l.T=n,o.p=t,o.d.f()}},n.preconnect=function(e,n){"string"===typeof e&&(n?n="string"===typeof(n=n.crossOrigin)?"use-credentials"===n?n:"":void 0:n=null,o.d.C(e,n))},n.prefetchDNS=function(e){"string"===typeof e&&o.d.D(e)},n.preinit=function(e,n){if("string"===typeof e&&n&&"string"===typeof n.as){var t=n.as,r=c(t,n.crossOrigin),i="string"===typeof n.integrity?n.integrity:void 0,a="string"===typeof n.fetchPriority?n.fetchPriority:void 0;"style"===t?o.d.S(e,"string"===typeof n.precedence?n.precedence:void 0,{crossOrigin:r,integrity:i,fetchPriority:a}):"script"===t&&o.d.X(e,{crossOrigin:r,integrity:i,fetchPriority:a,nonce:"string"===typeof n.nonce?n.nonce:void 0})}},n.preinitModule=function(e,n){if("string"===typeof e)if("object"===typeof n&&null!==n){if(null==n.as||"script"===n.as){var t=c(n.as,n.crossOrigin);o.d.M(e,{crossOrigin:t,integrity:"string"===typeof n.integrity?n.integrity:void 0,nonce:"string"===typeof n.nonce?n.nonce:void 0})}}else null==n&&o.d.M(e)},n.preload=function(e,n){if("string"===typeof e&&"object"===typeof n&&null!==n&&"string"===typeof n.as){var t=n.as,r=c(t,n.crossOrigin);o.d.L(e,t,{crossOrigin:r,integrity:"string"===typeof n.integrity?n.integrity:void 0,nonce:"string"===typeof n.nonce?n.nonce:void 0,type:"string"===typeof n.type?n.type:void 0,fetchPriority:"string"===typeof n.fetchPriority?n.fetchPriority:void 0,referrerPolicy:"string"===typeof n.referrerPolicy?n.referrerPolicy:void 0,imageSrcSet:"string"===typeof n.imageSrcSet?n.imageSrcSet:void 0,imageSizes:"string"===typeof n.imageSizes?n.imageSizes:void 0,media:"string"===typeof n.media?n.media:void 0})}},n.preloadModule=function(e,n){if("string"===typeof e)if(n){var t=c(n.as,n.crossOrigin);o.d.m(e,{as:"string"===typeof n.as&&"script"!==n.as?n.as:void 0,crossOrigin:t,integrity:"string"===typeof n.integrity?n.integrity:void 0})}else o.d.m(e)},n.requestFormReset=function(e){o.d.r(e)},n.unstable_batchedUpdates=function(e,n){return e(n)},n.useFormState=function(e,n,t){return l.H.useFormState(e,n,t)},n.useFormStatus=function(){return l.H.useHostTransitionStatus()},n.version="19.1.0"},799:(e,n)=>{"use strict";var t=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function i(e,n,r){var i=null;if(void 0!==r&&(i=""+r),void 0!==n.key&&(i=""+n.key),"key"in n)for(var a in r={},n)"key"!==a&&(r[a]=n[a]);else r=n;return n=r.ref,{$$typeof:t,type:e,key:i,ref:void 0!==n?n:null,props:r}}n.Fragment=r,n.jsx=i,n.jsxs=i},853:(e,n,t)=>{"use strict";e.exports=t(896)},896:(e,n)=>{"use strict";function t(e,n){var t=e.length;e.push(n);e:for(;0<t;){var r=t-1>>>1,i=e[r];if(!(0<a(i,n)))break e;e[r]=n,e[t]=i,t=r}}function r(e){return 0===e.length?null:e[0]}function i(e){if(0===e.length)return null;var n=e[0],t=e.pop();if(t!==n){e[0]=t;e:for(var r=0,i=e.length,o=i>>>1;r<o;){var s=2*(r+1)-1,l=e[s],c=s+1,d=e[c];if(0>a(l,t))c<i&&0>a(d,l)?(e[r]=d,e[c]=t,r=c):(e[r]=l,e[s]=t,r=s);else{if(!(c<i&&0>a(d,t)))break e;e[r]=d,e[c]=t,r=c}}}return n}function a(e,n){var t=e.sortIndex-n.sortIndex;return 0!==t?t:e.id-n.id}if(n.unstable_now=void 0,"object"===typeof performance&&"function"===typeof performance.now){var o=performance;n.unstable_now=function(){return o.now()}}else{var s=Date,l=s.now();n.unstable_now=function(){return s.now()-l}}var c=[],d=[],u=1,f=null,p=3,g=!1,m=!1,h=!1,b=!1,y="function"===typeof setTimeout?setTimeout:null,v="function"===typeof clearTimeout?clearTimeout:null,w="undefined"!==typeof setImmediate?setImmediate:null;function k(e){for(var n=r(d);null!==n;){if(null===n.callback)i(d);else{if(!(n.startTime<=e))break;i(d),n.sortIndex=n.expirationTime,t(c,n)}n=r(d)}}function x(e){if(h=!1,k(e),!m)if(null!==r(c))m=!0,E||(E=!0,S());else{var n=r(d);null!==n&&T(x,n.startTime-e)}}var S,E=!1,C=-1,z=5,j=-1;function P(){return!!b||!(n.unstable_now()-j<z)}function R(){if(b=!1,E){var e=n.unstable_now();j=e;var t=!0;try{e:{m=!1,h&&(h=!1,v(C),C=-1),g=!0;var a=p;try{n:{for(k(e),f=r(c);null!==f&&!(f.expirationTime>e&&P());){var o=f.callback;if("function"===typeof o){f.callback=null,p=f.priorityLevel;var s=o(f.expirationTime<=e);if(e=n.unstable_now(),"function"===typeof s){f.callback=s,k(e),t=!0;break n}f===r(c)&&i(c),k(e)}else i(c);f=r(c)}if(null!==f)t=!0;else{var l=r(d);null!==l&&T(x,l.startTime-e),t=!1}}break e}finally{f=null,p=a,g=!1}t=void 0}}finally{t?S():E=!1}}}if("function"===typeof w)S=function(){w(R)};else if("undefined"!==typeof MessageChannel){var _=new MessageChannel,O=_.port2;_.port1.onmessage=R,S=function(){O.postMessage(null)}}else S=function(){y(R,0)};function T(e,t){C=y(function(){e(n.unstable_now())},t)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(e){e.callback=null},n.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):z=0<e?Math.floor(1e3/e):5},n.unstable_getCurrentPriorityLevel=function(){return p},n.unstable_next=function(e){switch(p){case 1:case 2:case 3:var n=3;break;default:n=p}var t=p;p=n;try{return e()}finally{p=t}},n.unstable_requestPaint=function(){b=!0},n.unstable_runWithPriority=function(e,n){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var t=p;p=e;try{return n()}finally{p=t}},n.unstable_scheduleCallback=function(e,i,a){var o=n.unstable_now();switch("object"===typeof a&&null!==a?a="number"===typeof(a=a.delay)&&0<a?o+a:o:a=o,e){case 1:var s=-1;break;case 2:s=250;break;case 5:s=1073741823;break;case 4:s=1e4;break;default:s=5e3}return e={id:u++,callback:i,priorityLevel:e,startTime:a,expirationTime:s=a+s,sortIndex:-1},a>o?(e.sortIndex=a,t(d,e),null===r(c)&&e===r(d)&&(h?(v(C),C=-1):h=!0,T(x,a-o))):(e.sortIndex=s,t(c,e),m||g||(m=!0,E||(E=!0,S()))),e},n.unstable_shouldYield=P,n.unstable_wrapCallback=function(e){var n=p;return function(){var t=p;p=n;try{return e.apply(this,arguments)}finally{p=t}}}},950:(e,n,t)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(n){console.error(n)}}(),e.exports=t(672)}},n={};function t(r){var i=n[r];if(void 0!==i)return i.exports;var a=n[r]={exports:{}};return e[r].call(a.exports,a,a.exports,t),a.exports}t.m=e,t.n=e=>{var n=e&&e.__esModule?()=>e.default:()=>e;return t.d(n,{a:n}),n},t.d=(e,n)=>{for(var r in n)t.o(n,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:n[r]})},t.f={},t.e=e=>Promise.all(Object.keys(t.f).reduce((n,r)=>(t.f[r](e,n),n),[])),t.u=e=>"static/js/"+e+".a21126ec.chunk.js",t.miniCssF=e=>{},t.o=(e,n)=>Object.prototype.hasOwnProperty.call(e,n),(()=>{var e={},n="training-frontend:";t.l=(r,i,a,o)=>{if(e[r])e[r].push(i);else{var s,l;if(void 0!==a)for(var c=document.getElementsByTagName("script"),d=0;d<c.length;d++){var u=c[d];if(u.getAttribute("src")==r||u.getAttribute("data-webpack")==n+a){s=u;break}}s||(l=!0,(s=document.createElement("script")).charset="utf-8",s.timeout=120,t.nc&&s.setAttribute("nonce",t.nc),s.setAttribute("data-webpack",n+a),s.src=r),e[r]=[i];var f=(n,t)=>{s.onerror=s.onload=null,clearTimeout(p);var i=e[r];if(delete e[r],s.parentNode&&s.parentNode.removeChild(s),i&&i.forEach(e=>e(t)),n)return n(t)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=f.bind(null,s.onerror),s.onload=f.bind(null,s.onload),l&&document.head.appendChild(s)}}})(),t.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.p="/",(()=>{var e={792:0};t.f.j=(n,r)=>{var i=t.o(e,n)?e[n]:void 0;if(0!==i)if(i)r.push(i[2]);else{var a=new Promise((t,r)=>i=e[n]=[t,r]);r.push(i[2]=a);var o=t.p+t.u(n),s=new Error;t.l(o,r=>{if(t.o(e,n)&&(0!==(i=e[n])&&(e[n]=void 0),i)){var a=r&&("load"===r.type?"missing":r.type),o=r&&r.target&&r.target.src;s.message="Loading chunk "+n+" failed.\n("+a+": "+o+")",s.name="ChunkLoadError",s.type=a,s.request=o,i[1](s)}},"chunk-"+n,n)}};var n=(n,r)=>{var i,a,o=r[0],s=r[1],l=r[2],c=0;if(o.some(n=>0!==e[n])){for(i in s)t.o(s,i)&&(t.m[i]=s[i]);if(l)l(t)}for(n&&n(r);c<o.length;c++)a=o[c],t.o(e,a)&&e[a]&&e[a][0](),e[a]=0},r=self.webpackChunktraining_frontend=self.webpackChunktraining_frontend||[];r.forEach(n.bind(null,0)),r.push=n.bind(null,r.push.bind(r))})(),t.nc=void 0,(()=>{"use strict";var e=t(43),n=t(391);function r(e,n){return n||(n=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(n)}}))}var i=function(){return i=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var i in n=arguments[t])Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i]);return e},i.apply(this,arguments)};Object.create;function a(e,n,t){if(t||2===arguments.length)for(var r,i=0,a=n.length;i<a;i++)!r&&i in n||(r||(r=Array.prototype.slice.call(n,0,i)),r[i]=n[i]);return e.concat(r||Array.prototype.slice.call(n))}Object.create;"function"===typeof SuppressedError&&SuppressedError;var o=t(324),s=t.n(o),l="-ms-",c="-moz-",d="-webkit-",u="comm",f="rule",p="decl",g="@keyframes",m=Math.abs,h=String.fromCharCode,b=Object.assign;function y(e){return e.trim()}function v(e,n){return(e=n.exec(e))?e[0]:e}function w(e,n,t){return e.replace(n,t)}function k(e,n,t){return e.indexOf(n,t)}function x(e,n){return 0|e.charCodeAt(n)}function S(e,n,t){return e.slice(n,t)}function E(e){return e.length}function C(e){return e.length}function z(e,n){return n.push(e),e}function j(e,n){return e.filter(function(e){return!v(e,n)})}var P=1,R=1,_=0,O=0,T=0,A="";function N(e,n,t,r,i,a,o,s){return{value:e,root:n,parent:t,type:r,props:i,children:a,line:P,column:R,length:o,return:"",siblings:s}}function L(e,n){return b(N("",null,null,"",null,null,0,e.siblings),e,{length:-e.length},n)}function M(e){for(;e.root;)e=L(e.root,{children:[e]});z(e,e.siblings)}function I(){return T=O>0?x(A,--O):0,R--,10===T&&(R=1,P--),T}function D(){return T=O<_?x(A,O++):0,R++,10===T&&(R=1,P++),T}function F(){return x(A,O)}function H(){return O}function U(e,n){return S(A,e,n)}function W(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function B(e){return P=R=1,_=E(A=e),O=0,[]}function V(e){return A="",e}function q(e){return y(U(O-1,K(91===e?e+2:40===e?e+1:e)))}function Y(e){for(;(T=F())&&T<33;)D();return W(e)>2||W(T)>3?"":" "}function G(e,n){for(;--n&&D()&&!(T<48||T>102||T>57&&T<65||T>70&&T<97););return U(e,H()+(n<6&&32==F()&&32==D()))}function K(e){for(;D();)switch(T){case e:return O;case 34:case 39:34!==e&&39!==e&&K(T);break;case 40:41===e&&K(e);break;case 92:D()}return O}function Q(e,n){for(;D()&&e+T!==57&&(e+T!==84||47!==F()););return"/*"+U(n,O-1)+"*"+h(47===e?e:D())}function $(e){for(;!W(F());)D();return U(e,O)}function Z(e,n){for(var t="",r=0;r<e.length;r++)t+=n(e[r],r,e,n)||"";return t}function X(e,n,t,r){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case p:return e.return=e.return||e.value;case u:return"";case g:return e.return=e.value+"{"+Z(e.children,r)+"}";case f:if(!E(e.value=e.props.join(",")))return""}return E(t=Z(e.children,r))?e.return=e.value+"{"+t+"}":""}function J(e,n,t){switch(function(e,n){return 45^x(e,0)?(((n<<2^x(e,0))<<2^x(e,1))<<2^x(e,2))<<2^x(e,3):0}(e,n)){case 5103:return d+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return d+e+e;case 4789:return c+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return d+e+c+e+l+e+e;case 5936:switch(x(e,n+11)){case 114:return d+e+l+w(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return d+e+l+w(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return d+e+l+w(e,/[svh]\w+-[tblr]{2}/,"lr")+e}case 6828:case 4268:case 2903:return d+e+l+e+e;case 6165:return d+e+l+"flex-"+e+e;case 5187:return d+e+w(e,/(\w+).+(:[^]+)/,d+"box-$1$2"+l+"flex-$1$2")+e;case 5443:return d+e+l+"flex-item-"+w(e,/flex-|-self/g,"")+(v(e,/flex-|baseline/)?"":l+"grid-row-"+w(e,/flex-|-self/g,""))+e;case 4675:return d+e+l+"flex-line-pack"+w(e,/align-content|flex-|-self/g,"")+e;case 5548:return d+e+l+w(e,"shrink","negative")+e;case 5292:return d+e+l+w(e,"basis","preferred-size")+e;case 6060:return d+"box-"+w(e,"-grow","")+d+e+l+w(e,"grow","positive")+e;case 4554:return d+w(e,/([^-])(transform)/g,"$1"+d+"$2")+e;case 6187:return w(w(w(e,/(zoom-|grab)/,d+"$1"),/(image-set)/,d+"$1"),e,"")+e;case 5495:case 3959:return w(e,/(image-set\([^]*)/,d+"$1$`$1");case 4968:return w(w(e,/(.+:)(flex-)?(.*)/,d+"box-pack:$3"+l+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+d+e+e;case 4200:if(!v(e,/flex-|baseline/))return l+"grid-column-align"+S(e,n)+e;break;case 2592:case 3360:return l+w(e,"template-","")+e;case 4384:case 3616:return t&&t.some(function(e,t){return n=t,v(e.props,/grid-\w+-end/)})?~k(e+(t=t[n].value),"span",0)?e:l+w(e,"-start","")+e+l+"grid-row-span:"+(~k(t,"span",0)?v(t,/\d+/):+v(t,/\d+/)-+v(e,/\d+/))+";":l+w(e,"-start","")+e;case 4896:case 4128:return t&&t.some(function(e){return v(e.props,/grid-\w+-start/)})?e:l+w(w(e,"-end","-span"),"span ","")+e;case 4095:case 3583:case 4068:case 2532:return w(e,/(.+)-inline(.+)/,d+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(E(e)-1-n>6)switch(x(e,n+1)){case 109:if(45!==x(e,n+4))break;case 102:return w(e,/(.+:)(.+)-([^]+)/,"$1"+d+"$2-$3$1"+c+(108==x(e,n+3)?"$3":"$2-$3"))+e;case 115:return~k(e,"stretch",0)?J(w(e,"stretch","fill-available"),n,t)+e:e}break;case 5152:case 5920:return w(e,/(.+?):(\d+)(\s*\/\s*(span)?\s*(\d+))?(.*)/,function(n,t,r,i,a,o,s){return l+t+":"+r+s+(i?l+t+"-span:"+(a?o:+o-+r)+s:"")+e});case 4949:if(121===x(e,n+6))return w(e,":",":"+d)+e;break;case 6444:switch(x(e,45===x(e,14)?18:11)){case 120:return w(e,/(.+:)([^;\s!]+)(;|(\s+)?!.+)?/,"$1"+d+(45===x(e,14)?"inline-":"")+"box$3$1"+d+"$2$3$1"+l+"$2box$3")+e;case 100:return w(e,":",":"+l)+e}break;case 5719:case 2647:case 2135:case 3927:case 2391:return w(e,"scroll-","scroll-snap-")+e}return e}function ee(e,n,t,r){if(e.length>-1&&!e.return)switch(e.type){case p:return void(e.return=J(e.value,e.length,t));case g:return Z([L(e,{value:w(e.value,"@","@"+d)})],r);case f:if(e.length)return function(e,n){return e.map(n).join("")}(t=e.props,function(n){switch(v(n,r=/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":M(L(e,{props:[w(n,/:(read-\w+)/,":-moz-$1")]})),M(L(e,{props:[n]})),b(e,{props:j(t,r)});break;case"::placeholder":M(L(e,{props:[w(n,/:(plac\w+)/,":"+d+"input-$1")]})),M(L(e,{props:[w(n,/:(plac\w+)/,":-moz-$1")]})),M(L(e,{props:[w(n,/:(plac\w+)/,l+"input-$1")]})),M(L(e,{props:[n]})),b(e,{props:j(t,r)})}return""})}}function ne(e){return V(te("",null,null,null,[""],e=B(e),0,[0],e))}function te(e,n,t,r,i,a,o,s,l){for(var c=0,d=0,u=o,f=0,p=0,g=0,b=1,y=1,v=1,S=0,C="",j=i,P=a,R=r,_=C;y;)switch(g=S,S=D()){case 40:if(108!=g&&58==x(_,u-1)){-1!=k(_+=w(q(S),"&","&\f"),"&\f",m(c?s[c-1]:0))&&(v=-1);break}case 34:case 39:case 91:_+=q(S);break;case 9:case 10:case 13:case 32:_+=Y(g);break;case 92:_+=G(H()-1,7);continue;case 47:switch(F()){case 42:case 47:z(ie(Q(D(),H()),n,t,l),l);break;default:_+="/"}break;case 123*b:s[c++]=E(_)*v;case 125*b:case 59:case 0:switch(S){case 0:case 125:y=0;case 59+d:-1==v&&(_=w(_,/\f/g,"")),p>0&&E(_)-u&&z(p>32?ae(_+";",r,t,u-1,l):ae(w(_," ","")+";",r,t,u-2,l),l);break;case 59:_+=";";default:if(z(R=re(_,n,t,c,d,i,s,C,j=[],P=[],u,a),a),123===S)if(0===d)te(_,n,R,R,j,a,u,s,P);else switch(99===f&&110===x(_,3)?100:f){case 100:case 108:case 109:case 115:te(e,R,R,r&&z(re(e,R,R,0,0,i,s,C,i,j=[],u,P),P),i,P,u,s,r?j:P);break;default:te(_,R,R,R,[""],P,0,s,P)}}c=d=p=0,b=v=1,C=_="",u=o;break;case 58:u=1+E(_),p=g;default:if(b<1)if(123==S)--b;else if(125==S&&0==b++&&125==I())continue;switch(_+=h(S),S*b){case 38:v=d>0?1:(_+="\f",-1);break;case 44:s[c++]=(E(_)-1)*v,v=1;break;case 64:45===F()&&(_+=q(D())),f=F(),d=u=E(C=_+=$(H())),S++;break;case 45:45===g&&2==E(_)&&(b=0)}}return a}function re(e,n,t,r,i,a,o,s,l,c,d,u){for(var p=i-1,g=0===i?a:[""],h=C(g),b=0,v=0,k=0;b<r;++b)for(var x=0,E=S(e,p+1,p=m(v=o[b])),z=e;x<h;++x)(z=y(v>0?g[x]+" "+E:w(E,/&\f/g,g[x])))&&(l[k++]=z);return N(e,n,t,0===i?f:s,l,c,d,u)}function ie(e,n,t,r){return N(e,n,t,u,h(T),S(e,2,-2),0,r)}function ae(e,n,t,r,i){return N(e,n,t,p,S(e,0,r),S(e,r+1,-1),r,i)}var oe={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},se="undefined"!=typeof process&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_BACKEND_URL:"http://localhost:8000",REACT_APP_WEBSOCKET_URL:"ws://localhost:8000/ws/detect"}&&({NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_BACKEND_URL:"http://localhost:8000",REACT_APP_WEBSOCKET_URL:"ws://localhost:8000/ws/detect"}.REACT_APP_SC_ATTR||{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_BACKEND_URL:"http://localhost:8000",REACT_APP_WEBSOCKET_URL:"ws://localhost:8000/ws/detect"}.SC_ATTR)||"data-styled",le="active",ce="data-styled-version",de="6.1.19",ue="/*!sc*/\n",fe="undefined"!=typeof window&&"undefined"!=typeof document,pe=Boolean("boolean"==typeof SC_DISABLE_SPEEDY?SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_BACKEND_URL:"http://localhost:8000",REACT_APP_WEBSOCKET_URL:"ws://localhost:8000/ws/detect"}&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_BACKEND_URL:"http://localhost:8000",REACT_APP_WEBSOCKET_URL:"ws://localhost:8000/ws/detect"}.REACT_APP_SC_DISABLE_SPEEDY&&""!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_BACKEND_URL:"http://localhost:8000",REACT_APP_WEBSOCKET_URL:"ws://localhost:8000/ws/detect"}.REACT_APP_SC_DISABLE_SPEEDY?"false"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_BACKEND_URL:"http://localhost:8000",REACT_APP_WEBSOCKET_URL:"ws://localhost:8000/ws/detect"}.REACT_APP_SC_DISABLE_SPEEDY&&{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_BACKEND_URL:"http://localhost:8000",REACT_APP_WEBSOCKET_URL:"ws://localhost:8000/ws/detect"}.REACT_APP_SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_BACKEND_URL:"http://localhost:8000",REACT_APP_WEBSOCKET_URL:"ws://localhost:8000/ws/detect"}&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_BACKEND_URL:"http://localhost:8000",REACT_APP_WEBSOCKET_URL:"ws://localhost:8000/ws/detect"}.SC_DISABLE_SPEEDY&&""!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_BACKEND_URL:"http://localhost:8000",REACT_APP_WEBSOCKET_URL:"ws://localhost:8000/ws/detect"}.SC_DISABLE_SPEEDY&&("false"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_BACKEND_URL:"http://localhost:8000",REACT_APP_WEBSOCKET_URL:"ws://localhost:8000/ws/detect"}.SC_DISABLE_SPEEDY&&{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_BACKEND_URL:"http://localhost:8000",REACT_APP_WEBSOCKET_URL:"ws://localhost:8000/ws/detect"}.SC_DISABLE_SPEEDY)),ge=(new Set,Object.freeze([])),me=Object.freeze({});function he(e,n,t){return void 0===t&&(t=me),e.theme!==t.theme&&e.theme||n||t.theme}var be=new Set(["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","track","u","ul","use","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"]),ye=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,ve=/(^-|-$)/g;function we(e){return e.replace(ye,"-").replace(ve,"")}var ke=/(a)(d)/gi,xe=function(e){return String.fromCharCode(e+(e>25?39:97))};function Se(e){var n,t="";for(n=Math.abs(e);n>52;n=n/52|0)t=xe(n%52)+t;return(xe(n%52)+t).replace(ke,"$1-$2")}var Ee,Ce=function(e,n){for(var t=n.length;t;)e=33*e^n.charCodeAt(--t);return e},ze=function(e){return Ce(5381,e)};function je(e){return Se(ze(e)>>>0)}function Pe(e){return e.displayName||e.name||"Component"}function Re(e){return"string"==typeof e&&!0}var _e="function"==typeof Symbol&&Symbol.for,Oe=_e?Symbol.for("react.memo"):60115,Te=_e?Symbol.for("react.forward_ref"):60112,Ae={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},Ne={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},Le={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},Me=((Ee={})[Te]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},Ee[Oe]=Le,Ee);function Ie(e){return("type"in(n=e)&&n.type.$$typeof)===Oe?Le:"$$typeof"in e?Me[e.$$typeof]:Ae;var n}var De=Object.defineProperty,Fe=Object.getOwnPropertyNames,He=Object.getOwnPropertySymbols,Ue=Object.getOwnPropertyDescriptor,We=Object.getPrototypeOf,Be=Object.prototype;function Ve(e,n,t){if("string"!=typeof n){if(Be){var r=We(n);r&&r!==Be&&Ve(e,r,t)}var i=Fe(n);He&&(i=i.concat(He(n)));for(var a=Ie(e),o=Ie(n),s=0;s<i.length;++s){var l=i[s];if(!(l in Ne||t&&t[l]||o&&l in o||a&&l in a)){var c=Ue(n,l);try{De(e,l,c)}catch(e){}}}}return e}function qe(e){return"function"==typeof e}function Ye(e){return"object"==typeof e&&"styledComponentId"in e}function Ge(e,n){return e&&n?"".concat(e," ").concat(n):e||n||""}function Ke(e,n){if(0===e.length)return"";for(var t=e[0],r=1;r<e.length;r++)t+=n?n+e[r]:e[r];return t}function Qe(e){return null!==e&&"object"==typeof e&&e.constructor.name===Object.name&&!("props"in e&&e.$$typeof)}function $e(e,n,t){if(void 0===t&&(t=!1),!t&&!Qe(e)&&!Array.isArray(e))return n;if(Array.isArray(n))for(var r=0;r<n.length;r++)e[r]=$e(e[r],n[r]);else if(Qe(n))for(var r in n)e[r]=$e(e[r],n[r]);return e}function Ze(e,n){Object.defineProperty(e,"toString",{value:n})}function Xe(e){for(var n=[],t=1;t<arguments.length;t++)n[t-1]=arguments[t];return new Error("An error occurred. See https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#".concat(e," for more information.").concat(n.length>0?" Args: ".concat(n.join(", ")):""))}var Je=function(){function e(e){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=e}return e.prototype.indexOfGroup=function(e){for(var n=0,t=0;t<e;t++)n+=this.groupSizes[t];return n},e.prototype.insertRules=function(e,n){if(e>=this.groupSizes.length){for(var t=this.groupSizes,r=t.length,i=r;e>=i;)if((i<<=1)<0)throw Xe(16,"".concat(e));this.groupSizes=new Uint32Array(i),this.groupSizes.set(t),this.length=i;for(var a=r;a<i;a++)this.groupSizes[a]=0}for(var o=this.indexOfGroup(e+1),s=(a=0,n.length);a<s;a++)this.tag.insertRule(o,n[a])&&(this.groupSizes[e]++,o++)},e.prototype.clearGroup=function(e){if(e<this.length){var n=this.groupSizes[e],t=this.indexOfGroup(e),r=t+n;this.groupSizes[e]=0;for(var i=t;i<r;i++)this.tag.deleteRule(t)}},e.prototype.getGroup=function(e){var n="";if(e>=this.length||0===this.groupSizes[e])return n;for(var t=this.groupSizes[e],r=this.indexOfGroup(e),i=r+t,a=r;a<i;a++)n+="".concat(this.tag.getRule(a)).concat(ue);return n},e}(),en=new Map,nn=new Map,tn=1,rn=function(e){if(en.has(e))return en.get(e);for(;nn.has(tn);)tn++;var n=tn++;return en.set(e,n),nn.set(n,e),n},an=function(e,n){tn=n+1,en.set(e,n),nn.set(n,e)},on="style[".concat(se,"][").concat(ce,'="').concat(de,'"]'),sn=new RegExp("^".concat(se,'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)')),ln=function(e,n,t){for(var r,i=t.split(","),a=0,o=i.length;a<o;a++)(r=i[a])&&e.registerName(n,r)},cn=function(e,n){for(var t,r=(null!==(t=n.textContent)&&void 0!==t?t:"").split(ue),i=[],a=0,o=r.length;a<o;a++){var s=r[a].trim();if(s){var l=s.match(sn);if(l){var c=0|parseInt(l[1],10),d=l[2];0!==c&&(an(d,c),ln(e,d,l[3]),e.getTag().insertRules(c,i)),i.length=0}else i.push(s)}}},dn=function(e){for(var n=document.querySelectorAll(on),t=0,r=n.length;t<r;t++){var i=n[t];i&&i.getAttribute(se)!==le&&(cn(e,i),i.parentNode&&i.parentNode.removeChild(i))}};function un(){return t.nc}var fn=function(e){var n=document.head,t=e||n,r=document.createElement("style"),i=function(e){var n=Array.from(e.querySelectorAll("style[".concat(se,"]")));return n[n.length-1]}(t),a=void 0!==i?i.nextSibling:null;r.setAttribute(se,le),r.setAttribute(ce,de);var o=un();return o&&r.setAttribute("nonce",o),t.insertBefore(r,a),r},pn=function(){function e(e){this.element=fn(e),this.element.appendChild(document.createTextNode("")),this.sheet=function(e){if(e.sheet)return e.sheet;for(var n=document.styleSheets,t=0,r=n.length;t<r;t++){var i=n[t];if(i.ownerNode===e)return i}throw Xe(17)}(this.element),this.length=0}return e.prototype.insertRule=function(e,n){try{return this.sheet.insertRule(n,e),this.length++,!0}catch(e){return!1}},e.prototype.deleteRule=function(e){this.sheet.deleteRule(e),this.length--},e.prototype.getRule=function(e){var n=this.sheet.cssRules[e];return n&&n.cssText?n.cssText:""},e}(),gn=function(){function e(e){this.element=fn(e),this.nodes=this.element.childNodes,this.length=0}return e.prototype.insertRule=function(e,n){if(e<=this.length&&e>=0){var t=document.createTextNode(n);return this.element.insertBefore(t,this.nodes[e]||null),this.length++,!0}return!1},e.prototype.deleteRule=function(e){this.element.removeChild(this.nodes[e]),this.length--},e.prototype.getRule=function(e){return e<this.length?this.nodes[e].textContent:""},e}(),mn=function(){function e(e){this.rules=[],this.length=0}return e.prototype.insertRule=function(e,n){return e<=this.length&&(this.rules.splice(e,0,n),this.length++,!0)},e.prototype.deleteRule=function(e){this.rules.splice(e,1),this.length--},e.prototype.getRule=function(e){return e<this.length?this.rules[e]:""},e}(),hn=fe,bn={isServer:!fe,useCSSOMInjection:!pe},yn=function(){function e(e,n,t){void 0===e&&(e=me),void 0===n&&(n={});var r=this;this.options=i(i({},bn),e),this.gs=n,this.names=new Map(t),this.server=!!e.isServer,!this.server&&fe&&hn&&(hn=!1,dn(this)),Ze(this,function(){return function(e){for(var n=e.getTag(),t=n.length,r="",i=function(t){var i=function(e){return nn.get(e)}(t);if(void 0===i)return"continue";var a=e.names.get(i),o=n.getGroup(t);if(void 0===a||!a.size||0===o.length)return"continue";var s="".concat(se,".g").concat(t,'[id="').concat(i,'"]'),l="";void 0!==a&&a.forEach(function(e){e.length>0&&(l+="".concat(e,","))}),r+="".concat(o).concat(s,'{content:"').concat(l,'"}').concat(ue)},a=0;a<t;a++)i(a);return r}(r)})}return e.registerId=function(e){return rn(e)},e.prototype.rehydrate=function(){!this.server&&fe&&dn(this)},e.prototype.reconstructWithOptions=function(n,t){return void 0===t&&(t=!0),new e(i(i({},this.options),n),this.gs,t&&this.names||void 0)},e.prototype.allocateGSInstance=function(e){return this.gs[e]=(this.gs[e]||0)+1},e.prototype.getTag=function(){return this.tag||(this.tag=(e=function(e){var n=e.useCSSOMInjection,t=e.target;return e.isServer?new mn(t):n?new pn(t):new gn(t)}(this.options),new Je(e)));var e},e.prototype.hasNameForId=function(e,n){return this.names.has(e)&&this.names.get(e).has(n)},e.prototype.registerName=function(e,n){if(rn(e),this.names.has(e))this.names.get(e).add(n);else{var t=new Set;t.add(n),this.names.set(e,t)}},e.prototype.insertRules=function(e,n,t){this.registerName(e,n),this.getTag().insertRules(rn(e),t)},e.prototype.clearNames=function(e){this.names.has(e)&&this.names.get(e).clear()},e.prototype.clearRules=function(e){this.getTag().clearGroup(rn(e)),this.clearNames(e)},e.prototype.clearTag=function(){this.tag=void 0},e}(),vn=/&/g,wn=/^\s*\/\/.*$/gm;function kn(e,n){return e.map(function(e){return"rule"===e.type&&(e.value="".concat(n," ").concat(e.value),e.value=e.value.replaceAll(",",",".concat(n," ")),e.props=e.props.map(function(e){return"".concat(n," ").concat(e)})),Array.isArray(e.children)&&"@keyframes"!==e.type&&(e.children=kn(e.children,n)),e})}function xn(e){var n,t,r,i=void 0===e?me:e,a=i.options,o=void 0===a?me:a,s=i.plugins,l=void 0===s?ge:s,c=function(e,r,i){return i.startsWith(t)&&i.endsWith(t)&&i.replaceAll(t,"").length>0?".".concat(n):e},d=l.slice();d.push(function(e){e.type===f&&e.value.includes("&")&&(e.props[0]=e.props[0].replace(vn,t).replace(r,c))}),o.prefix&&d.push(ee),d.push(X);var u=function(e,i,a,s){void 0===i&&(i=""),void 0===a&&(a=""),void 0===s&&(s="&"),n=s,t=i,r=new RegExp("\\".concat(t,"\\b"),"g");var l=e.replace(wn,""),c=ne(a||i?"".concat(a," ").concat(i," { ").concat(l," }"):l);o.namespace&&(c=kn(c,o.namespace));var u,f=[];return Z(c,function(e){var n=C(e);return function(t,r,i,a){for(var o="",s=0;s<n;s++)o+=e[s](t,r,i,a)||"";return o}}(d.concat((u=function(e){return f.push(e)},function(e){e.root||(e=e.return)&&u(e)})))),f};return u.hash=l.length?l.reduce(function(e,n){return n.name||Xe(15),Ce(e,n.name)},5381).toString():"",u}var Sn=new yn,En=xn(),Cn=e.createContext({shouldForwardProp:void 0,styleSheet:Sn,stylis:En}),zn=(Cn.Consumer,e.createContext(void 0));function jn(){return(0,e.useContext)(Cn)}function Pn(n){var t=(0,e.useState)(n.stylisPlugins),r=t[0],i=t[1],a=jn().styleSheet,o=(0,e.useMemo)(function(){var e=a;return n.sheet?e=n.sheet:n.target&&(e=e.reconstructWithOptions({target:n.target},!1)),n.disableCSSOMInjection&&(e=e.reconstructWithOptions({useCSSOMInjection:!1})),e},[n.disableCSSOMInjection,n.sheet,n.target,a]),l=(0,e.useMemo)(function(){return xn({options:{namespace:n.namespace,prefix:n.enableVendorPrefixes},plugins:r})},[n.enableVendorPrefixes,n.namespace,r]);(0,e.useEffect)(function(){s()(r,n.stylisPlugins)||i(n.stylisPlugins)},[n.stylisPlugins]);var c=(0,e.useMemo)(function(){return{shouldForwardProp:n.shouldForwardProp,styleSheet:o,stylis:l}},[n.shouldForwardProp,o,l]);return e.createElement(Cn.Provider,{value:c},e.createElement(zn.Provider,{value:l},n.children))}var Rn=function(){function e(e,n){var t=this;this.inject=function(e,n){void 0===n&&(n=En);var r=t.name+n.hash;e.hasNameForId(t.id,r)||e.insertRules(t.id,r,n(t.rules,r,"@keyframes"))},this.name=e,this.id="sc-keyframes-".concat(e),this.rules=n,Ze(this,function(){throw Xe(12,String(t.name))})}return e.prototype.getName=function(e){return void 0===e&&(e=En),this.name+e.hash},e}(),_n=function(e){return e>="A"&&e<="Z"};function On(e){for(var n="",t=0;t<e.length;t++){var r=e[t];if(1===t&&"-"===r&&"-"===e[0])return e;_n(r)?n+="-"+r.toLowerCase():n+=r}return n.startsWith("ms-")?"-"+n:n}var Tn=function(e){return null==e||!1===e||""===e},An=function(e){var n,t,r=[];for(var i in e){var o=e[i];e.hasOwnProperty(i)&&!Tn(o)&&(Array.isArray(o)&&o.isCss||qe(o)?r.push("".concat(On(i),":"),o,";"):Qe(o)?r.push.apply(r,a(a(["".concat(i," {")],An(o),!1),["}"],!1)):r.push("".concat(On(i),": ").concat((n=i,null==(t=o)||"boolean"==typeof t||""===t?"":"number"!=typeof t||0===t||n in oe||n.startsWith("--")?String(t).trim():"".concat(t,"px")),";")))}return r};function Nn(e,n,t,r){return Tn(e)?[]:Ye(e)?[".".concat(e.styledComponentId)]:qe(e)?!qe(i=e)||i.prototype&&i.prototype.isReactComponent||!n?[e]:Nn(e(n),n,t,r):e instanceof Rn?t?(e.inject(t,r),[e.getName(r)]):[e]:Qe(e)?An(e):Array.isArray(e)?Array.prototype.concat.apply(ge,e.map(function(e){return Nn(e,n,t,r)})):[e.toString()];var i}function Ln(e){for(var n=0;n<e.length;n+=1){var t=e[n];if(qe(t)&&!Ye(t))return!1}return!0}var Mn=ze(de),In=function(){function e(e,n,t){this.rules=e,this.staticRulesId="",this.isStatic=(void 0===t||t.isStatic)&&Ln(e),this.componentId=n,this.baseHash=Ce(Mn,n),this.baseStyle=t,yn.registerId(n)}return e.prototype.generateAndInjectStyles=function(e,n,t){var r=this.baseStyle?this.baseStyle.generateAndInjectStyles(e,n,t):"";if(this.isStatic&&!t.hash)if(this.staticRulesId&&n.hasNameForId(this.componentId,this.staticRulesId))r=Ge(r,this.staticRulesId);else{var i=Ke(Nn(this.rules,e,n,t)),a=Se(Ce(this.baseHash,i)>>>0);if(!n.hasNameForId(this.componentId,a)){var o=t(i,".".concat(a),void 0,this.componentId);n.insertRules(this.componentId,a,o)}r=Ge(r,a),this.staticRulesId=a}else{for(var s=Ce(this.baseHash,t.hash),l="",c=0;c<this.rules.length;c++){var d=this.rules[c];if("string"==typeof d)l+=d;else if(d){var u=Ke(Nn(d,e,n,t));s=Ce(s,u+c),l+=u}}if(l){var f=Se(s>>>0);n.hasNameForId(this.componentId,f)||n.insertRules(this.componentId,f,t(l,".".concat(f),void 0,this.componentId)),r=Ge(r,f)}}return r},e}(),Dn=e.createContext(void 0);Dn.Consumer;var Fn={};new Set;function Hn(n,t,r){var a=Ye(n),o=n,s=!Re(n),l=t.attrs,c=void 0===l?ge:l,d=t.componentId,u=void 0===d?function(e,n){var t="string"!=typeof e?"sc":we(e);Fn[t]=(Fn[t]||0)+1;var r="".concat(t,"-").concat(je(de+t+Fn[t]));return n?"".concat(n,"-").concat(r):r}(t.displayName,t.parentComponentId):d,f=t.displayName,p=void 0===f?function(e){return Re(e)?"styled.".concat(e):"Styled(".concat(Pe(e),")")}(n):f,g=t.displayName&&t.componentId?"".concat(we(t.displayName),"-").concat(t.componentId):t.componentId||u,m=a&&o.attrs?o.attrs.concat(c).filter(Boolean):c,h=t.shouldForwardProp;if(a&&o.shouldForwardProp){var b=o.shouldForwardProp;if(t.shouldForwardProp){var y=t.shouldForwardProp;h=function(e,n){return b(e,n)&&y(e,n)}}else h=b}var v=new In(r,g,a?o.componentStyle:void 0);function w(n,t){return function(n,t,r){var a=n.attrs,o=n.componentStyle,s=n.defaultProps,l=n.foldedComponentIds,c=n.styledComponentId,d=n.target,u=e.useContext(Dn),f=jn(),p=n.shouldForwardProp||f.shouldForwardProp,g=he(t,u,s)||me,m=function(e,n,t){for(var r,a=i(i({},n),{className:void 0,theme:t}),o=0;o<e.length;o+=1){var s=qe(r=e[o])?r(a):r;for(var l in s)a[l]="className"===l?Ge(a[l],s[l]):"style"===l?i(i({},a[l]),s[l]):s[l]}return n.className&&(a.className=Ge(a.className,n.className)),a}(a,t,g),h=m.as||d,b={};for(var y in m)void 0===m[y]||"$"===y[0]||"as"===y||"theme"===y&&m.theme===g||("forwardedAs"===y?b.as=m.forwardedAs:p&&!p(y,h)||(b[y]=m[y]));var v=function(e,n){var t=jn();return e.generateAndInjectStyles(n,t.styleSheet,t.stylis)}(o,m),w=Ge(l,c);return v&&(w+=" "+v),m.className&&(w+=" "+m.className),b[Re(h)&&!be.has(h)?"class":"className"]=w,r&&(b.ref=r),(0,e.createElement)(h,b)}(k,n,t)}w.displayName=p;var k=e.forwardRef(w);return k.attrs=m,k.componentStyle=v,k.displayName=p,k.shouldForwardProp=h,k.foldedComponentIds=a?Ge(o.foldedComponentIds,o.styledComponentId):"",k.styledComponentId=g,k.target=a?o.target:n,Object.defineProperty(k,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(e){this._foldedDefaultProps=a?function(e){for(var n=[],t=1;t<arguments.length;t++)n[t-1]=arguments[t];for(var r=0,i=n;r<i.length;r++)$e(e,i[r],!0);return e}({},o.defaultProps,e):e}}),Ze(k,function(){return".".concat(k.styledComponentId)}),s&&Ve(k,n,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0}),k}function Un(e,n){for(var t=[e[0]],r=0,i=n.length;r<i;r+=1)t.push(n[r],e[r+1]);return t}var Wn=function(e){return Object.assign(e,{isCss:!0})};function Bn(e){for(var n=[],t=1;t<arguments.length;t++)n[t-1]=arguments[t];if(qe(e)||Qe(e))return Wn(Nn(Un(ge,a([e],n,!0))));var r=e;return 0===n.length&&1===r.length&&"string"==typeof r[0]?Nn(r):Wn(Nn(Un(r,n)))}function Vn(e,n,t){if(void 0===t&&(t=me),!n)throw Xe(1,n);var r=function(r){for(var i=[],o=1;o<arguments.length;o++)i[o-1]=arguments[o];return e(n,t,Bn.apply(void 0,a([r],i,!1)))};return r.attrs=function(r){return Vn(e,n,i(i({},t),{attrs:Array.prototype.concat(t.attrs,r).filter(Boolean)}))},r.withConfig=function(r){return Vn(e,n,i(i({},t),r))},r}var qn=function(e){return Vn(Hn,e)},Yn=qn;be.forEach(function(e){Yn[e]=qn(e)});!function(){function e(e,n){this.rules=e,this.componentId=n,this.isStatic=Ln(e),yn.registerId(this.componentId+1)}e.prototype.createStyles=function(e,n,t,r){var i=r(Ke(Nn(this.rules,n,t,r)),""),a=this.componentId+e;t.insertRules(a,a,i)},e.prototype.removeStyles=function(e,n){n.clearRules(this.componentId+e)},e.prototype.renderStyles=function(e,n,t,r){e>2&&yn.registerId(this.componentId+e),this.removeStyles(e,t),this.createStyles(e,n,t,r)}}();function Gn(e){for(var n=[],t=1;t<arguments.length;t++)n[t-1]=arguments[t];var r=Ke(Bn.apply(void 0,a([e],n,!1))),i=je(r);return new Rn(i,r)}(function(){function n(){var n=this;this._emitSheetCSS=function(){var e=n.instance.toString();if(!e)return"";var t=un(),r=Ke([t&&'nonce="'.concat(t,'"'),"".concat(se,'="true"'),"".concat(ce,'="').concat(de,'"')].filter(Boolean)," ");return"<style ".concat(r,">").concat(e,"</style>")},this.getStyleTags=function(){if(n.sealed)throw Xe(2);return n._emitSheetCSS()},this.getStyleElement=function(){var t;if(n.sealed)throw Xe(2);var r=n.instance.toString();if(!r)return[];var a=((t={})[se]="",t[ce]=de,t.dangerouslySetInnerHTML={__html:r},t),o=un();return o&&(a.nonce=o),[e.createElement("style",i({},a,{key:"sc-0-0"}))]},this.seal=function(){n.sealed=!0},this.instance=new yn({isServer:!0}),this.sealed=!1}n.prototype.collectStyles=function(n){if(this.sealed)throw Xe(2);return e.createElement(Pn,{sheet:this.instance},n)},n.prototype.interleaveWithNodeStream=function(e){throw Xe(3)}})(),"__sc-".concat(se,"__");function Kn(e){return Kn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Kn(e)}function Qn(e){var n=function(e,n){if("object"!=Kn(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,n||"default");if("object"!=Kn(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}(e,"string");return"symbol"==Kn(n)?n:n+""}function $n(e,n,t){return(n=Qn(n))in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function Zn(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),t.push.apply(t,r)}return t}function Xn(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?Zn(Object(t),!0).forEach(function(n){$n(e,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Zn(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})}return e}function Jn(e,n){if(null==e)return{};var t,r,i=function(e,n){if(null==e)return{};var t={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==n.indexOf(r))continue;t[r]=e[r]}return t}(e,n);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)t=a[r],-1===n.indexOf(t)&&{}.propertyIsEnumerable.call(e,t)&&(i[t]=e[t])}return i}const et=e=>{const n=(e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,n,t)=>t?t.toUpperCase():n.toLowerCase()))(e);return n.charAt(0).toUpperCase()+n.slice(1)},nt=function(){for(var e=arguments.length,n=new Array(e),t=0;t<e;t++)n[t]=arguments[t];return n.filter((e,n,t)=>Boolean(e)&&""!==e.trim()&&t.indexOf(e)===n).join(" ").trim()};var tt={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const rt=["color","size","strokeWidth","absoluteStrokeWidth","className","children","iconNode"],it=(0,e.forwardRef)((n,t)=>{let{color:r="currentColor",size:i=24,strokeWidth:a=2,absoluteStrokeWidth:o,className:s="",children:l,iconNode:c}=n,d=Jn(n,rt);return(0,e.createElement)("svg",Xn(Xn(Xn({ref:t},tt),{},{width:i,height:i,stroke:r,strokeWidth:o?24*Number(a)/Number(i):a,className:nt("lucide",s)},!l&&!(e=>{for(const n in e)if(n.startsWith("aria-")||"role"===n||"title"===n)return!0})(d)&&{"aria-hidden":"true"}),d),[...c.map(n=>{let[t,r]=n;return(0,e.createElement)(t,r)}),...Array.isArray(l)?l:[l]])}),at=["className"],ot=(n,t)=>{const r=(0,e.forwardRef)((r,i)=>{let{className:a}=r,o=Jn(r,at);return(0,e.createElement)(it,Xn({ref:i,iconNode:t,className:nt("lucide-".concat((s=et(n),s.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase())),"lucide-".concat(n),a)},o));var s});return r.displayName=et(n),r},st=ot("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]),lt=ot("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),ct=ot("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]),dt=ot("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]),ut=ot("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]),ft=ot("cpu",[["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M17 20v2",key:"1rnc9c"}],["path",{d:"M17 2v2",key:"11trls"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M2 17h2",key:"7oei6x"}],["path",{d:"M2 7h2",key:"asdhe0"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"M20 17h2",key:"1fpfkl"}],["path",{d:"M20 7h2",key:"1o8tra"}],["path",{d:"M7 20v2",key:"4gnj0m"}],["path",{d:"M7 2v2",key:"1i4yhu"}],["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"8",y:"8",width:"8",height:"8",rx:"1",key:"z9xiuo"}]]),pt=ot("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),gt=ot("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]),mt=ot("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),ht=ot("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);var bt,yt,vt,wt,kt,xt,St,Et,Ct,zt,jt,Pt,Rt,_t,Ot,Tt,At,Nt,Lt,Mt,It,Dt,Ft,Ht,Ut,Wt,Bt,Vt,qt,Yt=t(579);const Gt=Yn.div(bt||(bt=r(["\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 40% 60%, rgba(16, 185, 129, 0.02) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n"]))),Kt=Yn.nav(yt||(yt=r(["\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: var(--bg-glass);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-neural);\n  padding: var(--space-4) 0;\n  transition: var(--transition-normal);\n\n  @media (max-width: 768px) {\n    padding: var(--space-3) 0;\n  }\n"]))),Qt=Yn.div(vt||(vt=r(["\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  @media (max-width: 768px) {\n    padding: 0 var(--space-4);\n  }\n"]))),$t=Yn.div(wt||(wt=r(["\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n    gap: var(--space-2);\n  }\n"]))),Zt=Yn.div(kt||(kt=r(["\n  width: 40px;\n  height: 40px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    width: 36px;\n    height: 36px;\n  }\n"]))),Xt=Yn.div(xt||(xt=r(["\n  display: flex;\n  align-items: center;\n  gap: var(--space-8);\n\n  @media (max-width: 768px) {\n    gap: var(--space-4);\n  }\n"]))),Jt=Yn.a(St||(St=r(["\n  color: var(--text-secondary);\n  text-decoration: none;\n  font-weight: 500;\n  font-size: 0.9rem;\n  transition: var(--transition-fast);\n  position: relative;\n\n  &:hover {\n    color: var(--text-accent);\n  }\n\n  &::after {\n    content: '';\n    position: absolute;\n    bottom: -4px;\n    left: 0;\n    width: 0;\n    height: 2px;\n    background: var(--bg-neural);\n    transition: var(--transition-normal);\n  }\n\n  &:hover::after {\n    width: 100%;\n  }\n\n  @media (max-width: 768px) {\n    font-size: 0.85rem;\n  }\n"]))),er=Yn.section(Et||(Et=r(["\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: var(--space-24) var(--space-6) var(--space-20);\n  text-align: center;\n  position: relative;\n  z-index: 1;\n\n  @media (max-width: 768px) {\n    padding: var(--space-20) var(--space-4) var(--space-16);\n    min-height: calc(100vh - 80px);\n  }\n"]))),nr=Yn.div(Ct||(Ct=r(["\n  max-width: 900px;\n  width: 100%;\n  position: relative;\n  z-index: 2;\n"]))),tr=Yn.div(zt||(zt=r(["\n  display: inline-flex;\n  align-items: center;\n  gap: var(--space-2);\n  background: var(--bg-glass);\n  border: 1px solid var(--border-neural);\n  border-radius: var(--radius-full);\n  padding: var(--space-2) var(--space-4);\n  margin-bottom: var(--space-6);\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: var(--text-accent);\n  backdrop-filter: blur(10px);\n  box-shadow: var(--shadow-glow);\n\n  @media (max-width: 768px) {\n    font-size: 0.8rem;\n    padding: var(--space-2) var(--space-3);\n  }\n"]))),rr=Yn.h1(jt||(jt=r(["\n  font-family: var(--font-primary);\n  font-size: 3.5rem;\n  font-weight: 800;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin-bottom: var(--space-6);\n  line-height: 1.1;\n  letter-spacing: -0.02em;\n\n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n    margin-bottom: var(--space-4);\n  }\n\n  @media (max-width: 480px) {\n    font-size: 2rem;\n  }\n"]))),ir=Yn.p(Pt||(Pt=r(["\n  font-size: 1.25rem;\n  font-weight: 400;\n  color: var(--text-secondary);\n  margin-bottom: var(--space-8);\n  line-height: 1.6;\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    margin-bottom: var(--space-6);\n  }\n\n  @media (max-width: 480px) {\n    font-size: 1rem;\n  }\n"]))),ar=Yn.p(Rt||(Rt=r(["\n  font-size: 1rem;\n  line-height: 1.7;\n  color: var(--text-tertiary);\n  margin-bottom: var(--space-10);\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n\n  @media (max-width: 768px) {\n    font-size: 0.95rem;\n    margin-bottom: var(--space-8);\n    line-height: 1.6;\n  }\n"]))),or=Yn.div(_t||(_t=r(["\n  display: flex;\n  gap: var(--space-5);\n  justify-content: center;\n  align-items: center;\n  flex-wrap: wrap;\n  margin-bottom: var(--space-16);\n\n  @media (max-width: 768px) {\n    gap: var(--space-4);\n    margin-bottom: var(--space-12);\n    flex-direction: column;\n  }\n"]))),sr=Yn.button(Ot||(Ot=r(["\n  background: var(--bg-neural);\n  color: white;\n  border: none;\n  padding: var(--space-4) var(--space-10);\n  border-radius: var(--radius-xl);\n  font-size: 1.125rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  box-shadow: var(--shadow-neural);\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: -100%;\n    width: 100%;\n    height: 100%;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n    transition: var(--transition-slow);\n  }\n\n  &:hover {\n    transform: translateY(-3px);\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n\n    &::before {\n      left: 100%;\n    }\n  }\n\n  &:active {\n    transform: translateY(-1px);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-4) var(--space-8);\n    font-size: 1rem;\n    width: 100%;\n    max-width: 300px;\n  }\n"]))),lr=Yn.button(Tt||(Tt=r(["\n  background: var(--bg-glass);\n  color: var(--text-primary);\n  border: 2px solid var(--border-neural);\n  padding: var(--space-4) var(--space-8);\n  border-radius: var(--radius-xl);\n  font-size: 1.125rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  backdrop-filter: blur(10px);\n\n  &:hover {\n    border-color: var(--primary-600);\n    color: var(--primary-600);\n    background: var(--primary-50);\n    transform: translateY(-2px);\n    box-shadow: var(--shadow-lg);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-4) var(--space-6);\n    font-size: 1rem;\n    width: 100%;\n    max-width: 300px;\n  }\n"]))),cr=Yn.section(At||(At=r(["\n  padding: var(--space-24) var(--space-6);\n  background: var(--bg-tertiary);\n  position: relative;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),\n      radial-gradient(circle at 70% 70%, rgba(147, 51, 234, 0.05) 0%, transparent 50%);\n    pointer-events: none;\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-20) var(--space-4);\n  }\n"]))),dr=Yn.div(Nt||(Nt=r(["\n  max-width: 1400px;\n  margin: 0 auto;\n  text-align: center;\n  position: relative;\n  z-index: 1;\n"]))),ur=Yn.h2(Lt||(Lt=r(["\n  font-family: var(--font-primary);\n  font-size: 2.75rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin-bottom: var(--space-6);\n  letter-spacing: -0.02em;\n\n  @media (max-width: 768px) {\n    font-size: 2rem;\n  }\n"]))),fr=Yn.p(Mt||(Mt=r(["\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  margin-bottom: var(--space-16);\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n\n  @media (max-width: 768px) {\n    font-size: 1rem;\n    margin-bottom: var(--space-12);\n  }\n"]))),pr=Yn.div(It||(It=r(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));\n  gap: var(--space-8);\n  margin-bottom: var(--space-20);\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-6);\n  }\n"]))),gr=Yn.div(Dt||(Dt=r(["\n  background: var(--bg-glass);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-10);\n  border: 1px solid var(--border-neural);\n  transition: var(--transition-normal);\n  text-align: left;\n  position: relative;\n  overflow: hidden;\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-lg);\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 4px;\n    background: var(--bg-neural);\n    transform: scaleX(0);\n    transition: var(--transition-normal);\n  }\n\n  &:hover {\n    transform: translateY(-6px);\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n    border-color: var(--primary-300);\n\n    &::before {\n      transform: scaleX(1);\n    }\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-8);\n    text-align: center;\n  }\n"]))),mr=Yn.div(Ft||(Ft=r(["\n  width: 64px;\n  height: 64px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-xl);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: var(--space-6);\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    margin: 0 auto var(--space-6);\n  }\n"]))),hr=Yn.h3(Ht||(Ht=r(["\n  font-size: 1.5rem;\n  margin-bottom: var(--space-4);\n  color: var(--text-primary);\n  font-weight: 700;\n  font-family: var(--font-primary);\n\n  @media (max-width: 768px) {\n    text-align: center;\n  }\n"]))),br=Yn.p(Ut||(Ut=r(["\n  font-size: 1rem;\n  color: var(--text-secondary);\n  line-height: 1.7;\n  font-weight: 400;\n\n  @media (max-width: 768px) {\n    text-align: center;\n  }\n"]))),yr=Yn.div(Wt||(Wt=r(["\n  display: flex;\n  justify-content: center;\n  gap: var(--space-8);\n  margin-top: var(--space-8);\n\n  @media (max-width: 768px) {\n    gap: var(--space-6);\n    flex-wrap: wrap;\n  }\n"]))),vr=Yn.div(Bt||(Bt=r(["\n  text-align: center;\n"]))),wr=Yn.div(Vt||(Vt=r(["\n  font-size: 2rem;\n  font-weight: 700;\n  color: var(--primary-600);\n  font-family: var(--font-primary);\n\n  @media (max-width: 768px) {\n    font-size: 1.5rem;\n  }\n"]))),kr=Yn.div(qt||(qt=r(["\n  font-size: 0.875rem;\n  color: var(--text-tertiary);\n  font-weight: 500;\n  margin-top: var(--space-1);\n"]))),xr=e=>{let{onStartTraining:n,onNavigateToAbout:t,onNavigateToContact:r}=e;return(0,Yt.jsxs)(Gt,{children:[(0,Yt.jsx)(Kt,{children:(0,Yt.jsxs)(Qt,{children:[(0,Yt.jsxs)($t,{children:[(0,Yt.jsx)(Zt,{children:(0,Yt.jsx)(st,{size:24})}),"ASL Neural"]}),(0,Yt.jsxs)(Xt,{children:[(0,Yt.jsx)(Jt,{href:"#features",children:"Features"}),(0,Yt.jsx)(Jt,{onClick:t,style:{cursor:"pointer"},children:"About"}),(0,Yt.jsx)(Jt,{onClick:r,style:{cursor:"pointer"},children:"Contact"})]})]})}),(0,Yt.jsx)(er,{children:(0,Yt.jsxs)(nr,{children:[(0,Yt.jsxs)(tr,{children:[(0,Yt.jsx)(lt,{size:16}),"AI-Powered Learning Platform"]}),(0,Yt.jsx)(rr,{children:"Master Sign Language with Neural Intelligence"}),(0,Yt.jsx)(ir,{children:"Revolutionary AI platform that transforms sign language learning through real-time computer vision and adaptive neural networks"}),(0,Yt.jsx)(ar,{children:"Experience the future of accessibility education. Our advanced machine learning algorithms provide instant feedback while contributing to breakthrough AI research for the deaf and hard-of-hearing community."}),(0,Yt.jsxs)(or,{children:[(0,Yt.jsxs)(sr,{onClick:n,children:[(0,Yt.jsx)(ct,{size:20}),"Start Neural Training"]}),(0,Yt.jsxs)(lr,{onClick:t,children:[(0,Yt.jsx)(dt,{size:20}),"Explore Technology"]})]}),(0,Yt.jsxs)(yr,{children:[(0,Yt.jsxs)(vr,{children:[(0,Yt.jsx)(wr,{children:"100K+"}),(0,Yt.jsx)(kr,{children:"Neural Sessions"})]}),(0,Yt.jsxs)(vr,{children:[(0,Yt.jsx)(wr,{children:"250+"}),(0,Yt.jsx)(kr,{children:"Sign Patterns"})]}),(0,Yt.jsxs)(vr,{children:[(0,Yt.jsx)(wr,{children:"+88.7%"}),(0,Yt.jsx)(kr,{children:"AI Accuracy"})]})]})]})}),(0,Yt.jsx)(cr,{id:"features",children:(0,Yt.jsxs)(dr,{children:[(0,Yt.jsx)(ur,{children:"Neural Network Capabilities"}),(0,Yt.jsx)(fr,{children:"Discover how our advanced AI technology revolutionizes sign language learning through cutting-edge computer vision and machine learning"}),(0,Yt.jsxs)(pr,{children:[(0,Yt.jsxs)(gr,{children:[(0,Yt.jsx)(mr,{children:(0,Yt.jsx)(ut,{size:28,color:"white"})}),(0,Yt.jsx)(hr,{children:"Real-time Computer Vision"}),(0,Yt.jsx)(br,{children:"Advanced neural networks analyze your hand movements in real-time, providing instant feedback with 98.7% accuracy using state-of-the-art pose estimation algorithms"})]}),(0,Yt.jsxs)(gr,{children:[(0,Yt.jsx)(mr,{children:(0,Yt.jsx)(ft,{size:28,color:"white"})}),(0,Yt.jsx)(hr,{children:"Adaptive AI Learning"}),(0,Yt.jsx)(br,{children:"Our deep learning models continuously adapt to your learning style, creating personalized training paths that optimize skill acquisition and retention rates"})]}),(0,Yt.jsxs)(gr,{children:[(0,Yt.jsx)(mr,{children:(0,Yt.jsx)(pt,{size:28,color:"white"})}),(0,Yt.jsx)(hr,{children:"Global Impact Network"}),(0,Yt.jsx)(br,{children:"Join a worldwide community contributing to breakthrough AI research that advances accessibility technology for millions in the deaf and hard-of-hearing community"})]}),(0,Yt.jsxs)(gr,{children:[(0,Yt.jsx)(mr,{children:(0,Yt.jsx)(gt,{size:28,color:"white"})}),(0,Yt.jsx)(hr,{children:"Cross-Platform Intelligence"}),(0,Yt.jsx)(br,{children:"Seamless AI-powered experience across all devices with cloud-synchronized progress and edge computing for lightning-fast response times"})]}),(0,Yt.jsxs)(gr,{children:[(0,Yt.jsx)(mr,{children:(0,Yt.jsx)(mt,{size:28,color:"white"})}),(0,Yt.jsx)(hr,{children:"Precision Learning Analytics"}),(0,Yt.jsx)(br,{children:"Advanced analytics track micro-movements and learning patterns, providing data-driven insights to accelerate your mastery of sign language"})]}),(0,Yt.jsxs)(gr,{children:[(0,Yt.jsx)(mr,{children:(0,Yt.jsx)(ht,{size:28,color:"white"})}),(0,Yt.jsx)(hr,{children:"Privacy-First Architecture"}),(0,Yt.jsx)(br,{children:"Enterprise-grade security with local processing ensures your data remains private while contributing anonymized insights to advance AI research"})]})]})]})})]})};var Sr=t(29),Er=t.n(Sr);const Cr=ot("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),zr=ot("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),jr=ot("square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]),Pr=ot("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),Rr=ot("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]),_r=ot("wifi-off",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),Or=ot("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]),Tr={BACKEND_URL:"http://localhost:8000",WEBSOCKET_URL:"ws://localhost:8000/ws/detect",API_ENDPOINTS:{HEALTH:"/health",SIGNS:"/signs",RECORDINGS:"/recordings",TRAIN:"/train",TRAINING_STATUS:"/training/status"},WS_ENDPOINTS:{DETECT:"/ws/detect"},APP:{NAME:"ASL Sign Language Trainer",VERSION:"1.0.0",DESCRIPTION:"Real-time American Sign Language detection and training"},DETECTION:{CONFIDENCE_THRESHOLD:.5,FRAME_INTERVAL:100,MAX_PREDICTION_HISTORY:5},RECORDING:{SESSION_DURATION:3e3,MIN_FRAMES:30}},Ar=()=>{const[n,t]=(0,e.useState)(!1),[r,i]=(0,e.useState)(null),[a,o]=(0,e.useState)(null),[s,l]=(0,e.useState)(!1),[c,d]=(0,e.useState)(""),[u,f]=(0,e.useState)(null),[p,g]=(0,e.useState)(!1),[m,h]=(0,e.useState)(""),[b,y]=(0,e.useState)([]),[v,w]=(0,e.useState)(null),k=(0,e.useRef)(null),x=(0,e.useRef)(null),S=(0,e.useRef)(null),E=(0,e.useCallback)(()=>{try{k.current=new WebSocket(Tr.WEBSOCKET_URL),k.current.onopen=()=>{console.log("Connected to sign detection backend"),t(!0),d("")},k.current.onmessage=e=>{try{const n=JSON.parse(e.data);switch(console.log("Received from backend:",n.type,n.prediction),n.type){case"frame_processed":f(n.processed_frame),g(n.sign_matched||!1),h(n.target_sign||""),n.prediction?(i(n.prediction),o(n.prediction),y(e=>[n.prediction,...e.slice(0,4)]),console.log("Detected: ".concat(n.prediction.sign," (").concat(Math.round(100*n.prediction.confidence),"%)")),n.keypoints&&w(n.keypoints),n.should_start_session?d("\ud83c\udfac Started recording session for ".concat(n.target_sign," (3 seconds)")):n.is_in_session&&n.sign_matched&&d("\ud83d\udcf9 Recording session active for ".concat(n.target_sign,"..."))):a&&i(Xn(Xn({},a),{},{confidence:.7*a.confidence,isStale:!0}));break;case"recording_started":l(!0),d("Ready to record: ".concat(n.target_sign," (will auto-record for 3s when detected)"));break;case"recording_stopped":if(l(!1),n.result){const e="auto_stop_session_complete"===n.reason?"Session completed":"Manual stop";d("\u2705 Recording saved: ".concat(n.result.frame_count," frames (").concat(e,")"))}else d("Recording stopped");break;default:console.log("Unknown message type:",n.type)}}catch(n){console.error("Error parsing WebSocket message:",n)}},k.current.onclose=()=>{console.log("Disconnected from sign detection backend"),t(!1),d("Connection lost - Click retry to reconnect"),x.current=setTimeout(()=>{console.log("Attempting to reconnect..."),E()},3e3)},k.current.onerror=e=>{console.error("WebSocket error:",e),d("Connection error")}}catch(e){console.error("Error connecting to WebSocket:",e),d("Failed to connect to backend")}},[]),C=(0,e.useCallback)(()=>{x.current&&clearTimeout(x.current),S.current&&clearInterval(S.current),k.current&&(k.current.close(),k.current=null),t(!1)},[]),z=(0,e.useCallback)(e=>{if(k.current&&k.current.readyState===WebSocket.OPEN){const n={type:"frame",frame:e};k.current.send(JSON.stringify(n))}},[]),j=(0,e.useCallback)(e=>{if(k.current&&k.current.readyState===WebSocket.OPEN){const n={type:"set_level",level:e};k.current.send(JSON.stringify(n)),console.log("Setting detection level to ".concat(e))}},[]),P=(0,e.useCallback)(function(e){let n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(k.current&&k.current.readyState===WebSocket.OPEN){const t={type:"start_recording",target_sign:e,start_session:n};k.current.send(JSON.stringify(t)),h(e)}},[]),R=(0,e.useCallback)(()=>{if(k.current&&k.current.readyState===WebSocket.OPEN){const e={type:"stop_recording"};k.current.send(JSON.stringify(e))}},[]),_=(0,e.useCallback)(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100;S.current&&clearInterval(S.current),S.current=setInterval(()=>{if(e.current&&n){const n=e.current.getScreenshot();n&&(console.log("Sending frame to backend"),z(n))}},t)},[n,z]),O=(0,e.useCallback)(()=>{S.current&&(clearInterval(S.current),S.current=null)},[]);(0,e.useEffect)(()=>(E(),()=>{C()}),[E,C]);const T=(0,e.useCallback)(()=>{d("Attempting to reconnect..."),C(),setTimeout(()=>{E()},1e3)},[E,C]);return{isConnected:n,prediction:r,lastPrediction:a,predictionHistory:b,isRecording:s,recordingStatus:c,processedFrame:u,signMatched:p,targetSign:m,currentKeypoints:v,connect:E,disconnect:C,retryConnection:T,sendFrame:z,startRecording:P,stopRecording:R,startFrameCapture:_,stopFrameCapture:O,setLevel:j}},Nr=ot("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),Lr=ot("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),Mr=ot("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),Ir=ot("trophy",[["path",{d:"M10 14.66v1.626a2 2 0 0 1-.976 1.696A5 5 0 0 0 7 21.978",key:"1n3hpd"}],["path",{d:"M14 14.66v1.626a2 2 0 0 0 .976 1.696A5 5 0 0 1 17 21.978",key:"rfe1zi"}],["path",{d:"M18 9h1.5a1 1 0 0 0 0-5H18",key:"7xy6bh"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M6 9a6 6 0 0 0 12 0V3a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1z",key:"1mhfuq"}],["path",{d:"M6 9H4.5a1 1 0 0 1 0-5H6",key:"tex48p"}]]),Dr={1:{name:"Basic Greetings & Family",description:"Start with essential greetings and family members",theme:"\ud83d\udc4b Greetings & Family",signs:[{key:"hello",name:"Hello",gif:"https://media.giphy.com/media/3o7TKNKOfKlIhbD3gY/giphy.gif",description:"Basic greeting sign"},{key:"bye",name:"Bye",gif:"https://c.tenor.com/vME77PObDN8AAAAC/asl-bye-asl-goodbye.gif",description:"Sign for goodbye"},{key:"please",name:"Please",gif:"https://lifeprint.com/asl101/gifs-animated/pleasecloseup.gif",description:"Polite request sign"},{key:"yes",name:"Yes",gif:"https://media.tenor.com/oYIirlyIih0AAAAC/yes-asl.gif",description:"Affirmative response"},{key:"no",name:"No",gif:"https://lifeprint.com/asl101/gifs/n/no-2-movement.gif",description:"Negative response"},{key:"mom",name:"Mom",gif:"https://lifeprint.com/asl101/gifs/m/mom.gif",description:"Sign for mother"},{key:"dad",name:"Dad",gif:"https://media.giphy.com/media/l0MYQcLKwtl5v6H1S/giphy.gif",description:"Sign for father"},{key:"brother",name:"Brother",gif:"https://lifeprint.com/asl101/gifs/b/brother.gif",description:"Sign for brother"},{key:"sister",name:"Sister",gif:"https://lifeprint.com/asl101/gifs/s/sister.gif",description:"Sign for sister"},{key:"grandma",name:"Grandma",gif:"https://www.lifeprint.com/asl101/gifs/g/grandma.gif",description:"Sign for grandmother"},{key:"grandpa",name:"Grandpa",gif:"https://th.bing.com/th/id/OIP.yyLPc-rWg0PMNbrwjeQQngHaE-?w=238&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for grandfather"},{key:"aunt",name:"Aunt",gif:"https://th.bing.com/th/id/OIP.Yz5UUZdNTrVWXf72we_N6wHaHa?rs=1&pid=ImgDetMain",description:"Sign for aunt"},{key:"uncle",name:"Uncle",gif:"https://lifeprint.com/asl101/gifs/u/uncle.gif",description:"Sign for uncle"},{key:"family",name:"Family",gif:"https://lifeprint.com/asl101/gifs/f/family.gif",description:"Sign for family"},{key:"friend",name:"Friend",gif:"https://lifeprint.com/asl101/gifs/f/friend.gif",description:"Sign for friend"},{key:"boy",name:"Boy",gif:"https://lifeprint.com/asl101/gifs/b/boy.gif",description:"Sign for boy"},{key:"girl",name:"Girl",gif:"https://th.bing.com/th/id/R.********************************?rik=yDsGUPEaDyeSlA&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fgirl.gif&ehk=zdVxVSayRBDn67vVCpMhUH6UmzUQE8vaY7%2bv8jedvs8%3d&risl=&pid=ImgRaw&r=0",description:"Sign for girl"},{key:"child",name:"Child",gif:"https://lifeprint.com/asl101/gifs/c/child.gif",description:"Sign for child"},{key:"man",name:"Man",gif:"https://lifeprint.com/asl101/gifs/m/man.gif",description:"Sign for man"},{key:"person",name:"Person",gif:"https://lifeprint.com/asl101/gifs/p/person.gif",description:"Sign for person"}]},2:{name:"Colors & Basic Objects",description:"Learn essential colors and everyday objects",theme:"\ud83c\udfa8 Colors & Objects",signs:[{key:"red",name:"Red",gif:"https://lifeprint.com/asl101/gifs-animated/red.gif",description:"Color red"},{key:"blue",name:"Blue",gif:"https://lifeprint.com/asl101/gifs/b/blue-1.gif",description:"Color blue"},{key:"green",name:"Green",gif:"https://i.pinimg.com/originals/cb/7f/75/cb7f757ffb79cb3d1309c9ad785e83a1.gif",description:"Color green"},{key:"yellow",name:"Yellow",gif:"https://lifeprint.com/asl101/gifs/y/yellow.gif",description:"Color yellow"},{key:"black",name:"Black",gif:"https://th.bing.com/th/id/R.********************************?rik=52tGw7%2fGcx2HtwMm9rZGVkejE2N2RhMWp0NTY3Z2pwNHBqZXFlOTF3ZGd0cG1zZSZlcD12MV9naWZzX3NlYXJjaCZjdD1n/l0MYL34CVEqLK27yU/giphy.gif",description:"Color black"},{key:"white",name:"White",gif:"https://lifeprint.com/asl101/gifs/w/white.gif",description:"Color white"},{key:"brown",name:"Brown",gif:"https://lifeprint.com/asl101/gifs/b/brown.gif",description:"Color brown"},{key:"orange",name:"Orange",gif:"https://lifeprint.com/asl101/gifs/o/orange.gif",description:"Color orange"},{key:"book",name:"Book",gif:"https://media.giphy.com/media/l0MYL43dl4pQEn3uE/giphy.gif",description:"Reading material"},{key:"chair",name:"Chair",gif:"https://th.bing.com/th/id/OIP.5kr1MkVLnuN2Z9Jkw-0QpAHaE-?w=237&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Furniture for sitting"},{key:"table",name:"Table",gif:"https://lifeprint.com/asl101/gifs/t/table.gif",description:"Furniture surface"},{key:"bed",name:"Bed",gif:"https://lifeprint.com/asl101/gifs/b/bed-1.gif",description:"Sleeping furniture"},{key:"door",name:"Door",gif:"https://lifeprint.com/asl101/gifs/d/door.gif",description:"Entry/exit"},{key:"window",name:"Window",gif:"https://lifeprint.com/asl101/gifs/w/window.gif",description:"Glass opening"},{key:"car",name:"Car",gif:"https://th.bing.com/th/id/OIP.wxw32OaIdqFt8f_ucHVoRgHaEH?w=308&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Vehicle"},{key:"house",name:"House",gif:"https://lifeprint.com/asl101/gifs/h/house.gif",description:"Home building"},{key:"school",name:"School",gif:"https://lifeprint.com/asl101/gifs/s/school.gif",description:"Educational institution"},{key:"water",name:"Water",gif:"https://lifeprint.com/asl101/gifs/w/water-2.gif",description:"Clear liquid"},{key:"food",name:"Food",gif:"https://i.pinimg.com/originals/cc/bb/0c/ccbb0c143db0b51e9947a5966db42fd8.gif",description:"Nourishment"},{key:"milk",name:"Milk",gif:"https://lifeprint.com/asl101/gifs/m/milk.gif",description:"Dairy beverage"}]},3:{name:"Animals & Nature",description:"Discover animals and nature signs",theme:"\ud83d\udc3e Animals & Nature",signs:[{key:"cat",name:"Cat",gif:"https://lifeprint.com/asl101/gifs-animated/cat-02.gif",description:"Feline pet"},{key:"dog",name:"Dog",gif:"https://th.bing.com/th/id/R.********************************?rik=uVshGsOHXgldoQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fd%2fdog1.gif&ehk=Xnfrvg0xwy1u%2fae9vWdKYMT25%2bF6qoteW2FThCbVrOA%3d&risl=&pid=ImgRaw&r=0",description:"Canine pet"},{key:"bird",name:"Bird",gif:"https://lifeprint.com/asl101/gifs/b/bird.gif",description:"Flying animal"},{key:"fish",name:"Fish",gif:"https://th.bing.com/th/id/OIP.Lzhd7lIIa-V4H3faS1d3mQHaHa?rs=1&pid=ImgDetMain",description:"Aquatic animal"},{key:"cow",name:"Cow",gif:"https://lifeprint.com/asl101/gifs/c/cow.gif",description:"Farm animal"},{key:"horse",name:"Horse",gif:"https://media.giphy.com/media/l0HlM5HffraiQaHUk/giphy.gif",description:"Riding animal"},{key:"pig",name:"Pig",gif:"https://lifeprint.com/asl101/gifs/p/pig.gif",description:"Farm animal"},{key:"chicken",name:"Chicken",gif:"https://lifeprint.com/asl101/gifs/c/chicken.gif",description:"Poultry"},{key:"rabbit",name:"Rabbit",gif:"https://lifeprint.com/asl101/gifs/r/rabbit.gif",description:"Small mammal"},{key:"bear",name:"Bear",gif:"https://lifeprint.com/asl101/gifs/b/bear.gif",description:"Large mammal"},{key:"tree",name:"Tree",gif:"https://lifeprint.com/asl101/gifs-animated/tree.gif",description:"Tall plant"},{key:"flower",name:"Flower",gif:"https://media.giphy.com/media/3o7TKGkqPpLUdFiFPy/giphy.gif",description:"Blooming plant"},{key:"grass",name:"Grass",gif:"https://th.bing.com/th/id/R.********************************?rik=uGZNVzt6tISwHA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs-animated%2fgrass.gif&ehk=VKQd9cvBrglo47EhogWYL9rOiZZsEJ7Yqt%2bgJ8N99yQ%3d&risl=&pid=ImgRaw&r=0",description:"Ground cover"},{key:"sun",name:"Sun",gif:"https://media.giphy.com/media/3o6Zt7merN2zxEtNRK/giphy.gif",description:"Solar star"},{key:"moon",name:"Moon",gif:"https://th.bing.com/th/id/R.********************************?rik=XbVhBJtkANrG9g&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fm%2fmoon.gif&ehk=YSDvFeUSTa9X1BEJhDjdnLC4c7zWn8z7Hj%2fMkkLUyFE%3d&risl=&pid=ImgRaw&r=0",description:"Night satellite"},{key:"star",name:"Star",gif:"https://lifeprint.com/asl101/gifs/s/star.gif",description:"Celestial body"},{key:"cloud",name:"Cloud",gif:"https://th.bing.com/th/id/OIP.hMO89bV2zwVcIVIa7FOT5QHaEc?rs=1&pid=ImgDetMain",description:"Sky formation"},{key:"rain",name:"Rain",gif:"https://lifeprint.com/asl101/gifs/r/rain.gif",description:"Water precipitation"},{key:"snow",name:"Snow",gif:"https://lifeprint.com/asl101/gifs/s/snow.gif",description:"Frozen precipitation"},{key:"wind",name:"Wind",gif:"https://lifeprint.com/asl101/gifs/w/wind.gif",description:"Air movement"}]},4:{name:"Body Parts & Actions",description:"Learn body parts and common actions",theme:"\ud83d\udc64 Body & Actions",signs:[{key:"head",name:"Head",gif:"https://th.bing.com/th/id/R.********************************?rik=OcbJdRbpEFsWXQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fsignjpegs%2fh%2fhead-1.jpg&ehk=RPBV45fSrLDEWYiZvRuZs2c1JNrL4WzdqLSNMFIF3Rs%3d&risl=&pid=ImgRaw&r=0",description:"Top body part"},{key:"face",name:"Face",gif:"https://lifeprint.com/asl101/gifs/f/face.gif",description:"Front of head"},{key:"eye",name:"Eye",gif:"https://lifeprint.com/asl101/gifs/e/eyes.gif",description:"Vision organ"},{key:"nose",name:"Nose",gif:"https://lifeprint.com/asl101/signjpegs/n/nose.h1.jpg",description:"Smell organ"},{key:"mouth",name:"Mouth",gif:"https://lifeprint.com/asl101/gifs-animated/mouth.gif",description:"Eating/speaking organ"},{key:"ear",name:"Ear",gif:"https://lifeprint.com/asl101/signjpegs/e/ears.h3.jpg",description:"Hearing organ"},{key:"hand",name:"Hand",gif:"https://lifeprint.com/asl101/gifs/h/hand.gif",description:"Grasping appendage"},{key:"arm",name:"Arm",gif:"https://th.bing.com/th/id/OIP.KkJLqfbp6XEHiIe-jOUb2AHaEc?w=251&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Upper limb"},{key:"leg",name:"Leg",gif:"https://lifeprint.com/asl101/gifs/l/leg.gif",description:"Lower limb"},{key:"foot",name:"Foot",gif:"https://lifeprint.com/asl101/gifs/f/foot.gif",description:"Walking appendage"},{key:"walk",name:"Walk",gif:"https://lifeprint.com/asl101/gifs/w/walk.gif",description:"Move on foot"},{key:"run",name:"Run",gif:"https://lifeprint.com/asl101/gifs/r/run.gif",description:"Move quickly"},{key:"sit",name:"Sit",gif:"https://lifeprint.com/asl101/gifs/s/sit.gif",description:"Rest position"},{key:"stand",name:"Stand",gif:"https://lifeprint.com/asl101/gifs/s/stand.gif",description:"Upright position"},{key:"sleep",name:"Sleep",gif:"https://media4.giphy.com/media/3o7TKnRuBdakLslcaI/200.gif?cid=790b76110d8f185a9713f36dd65a0df801576e01b403c95c&rid=200.gif&ct=g",description:"Rest state"},{key:"eat",name:"Eat",gif:"https://lifeprint.com/asl101/gifs/e/eat.gif",description:"Consume food"},{key:"drink",name:"Drink",gif:"https://www.lifeprint.com/asl101/gifs/d/drink-c.gif",description:"Consume liquid"},{key:"see",name:"See",gif:"https://lifeprint.com/asl101/gifs/l/look-at-2.gif",description:"Use vision"},{key:"hear",name:"Hear",gif:"https://www.lifeprint.com/asl101/signjpegs/h/hear.h4.jpg",description:"Use hearing"},{key:"touch",name:"Touch",gif:"https://th.bing.com/th/id/OIP.imGRfqjCtcHhof6Lc_0QJQHaE-?w=230&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Physical contact"}]},5:{name:"Numbers & Time",description:"Master numbers and time concepts",theme:"\ud83d\udd22 Numbers & Time",signs:[{key:"one",name:"One",gif:"https://lifeprint.com/asl101/gifs/numbers/1.gif",description:"Number 1"},{key:"two",name:"Two",gif:"https://lifeprint.com/asl101/gifs/numbers/2.gif",description:"Number 2"},{key:"three",name:"Three",gif:"https://lifeprint.com/asl101/gifs/numbers/3.gif",description:"Number 3"},{key:"four",name:"Four",gif:"https://lifeprint.com/asl101/gifs/numbers/4.gif",description:"Number 4"},{key:"five",name:"Five",gif:"https://lifeprint.com/asl101/gifs/numbers/5.gif",description:"Number 5"},{key:"six",name:"Six",gif:"https://lifeprint.com/asl101/gifs/numbers/6.gif",description:"Number 6"},{key:"seven",name:"Seven",gif:"https://lifeprint.com/asl101/gifs/numbers/7.gif",description:"Number 7"},{key:"eight",name:"Eight",gif:"https://lifeprint.com/asl101/gifs/numbers/8.gif",description:"Number 8"},{key:"nine",name:"Nine",gif:"https://lifeprint.com/asl101/gifs/numbers/9.gif",description:"Number 9"},{key:"ten",name:"Ten",gif:"https://lifeprint.com/asl101/gifs/numbers/10.gif",description:"Number 10"},{key:"today",name:"Today",gif:"https://lifeprint.com/asl101/gifs/t/today.gif",description:"Current day"},{key:"tomorrow",name:"Tomorrow",gif:"https://lifeprint.com/asl101/gifs/t/tomorrow.gif",description:"Next day"},{key:"yesterday",name:"Yesterday",gif:"https://lifeprint.com/asl101/gifs/y/yesterday.gif",description:"Previous day"},{key:"morning",name:"Morning",gif:"https://media0.giphy.com/media/3o6ZtrcJ9GCXGGw0ww/source.gif",description:"Early day"},{key:"afternoon",name:"Afternoon",gif:"https://lifeprint.com/asl101/gifs/a/afternoon.gif",description:"Mid day"},{key:"night",name:"Night",gif:"https://lifeprint.com/asl101/gifs/n/night.gif",description:"Dark time"},{key:"week",name:"Week",gif:"https://lifeprint.com/asl101/gifs/w/week.gif",description:"7-day period"},{key:"month",name:"Month",gif:"https://lifeprint.com/asl101/gifs/m/month.gif",description:"Calendar period"},{key:"year",name:"Year",gif:"https://lifeprint.com/asl101/gifs/y/year.gif",description:"Annual period"},{key:"time",name:"Time",gif:"https://lifeprint.com/asl101/gifs/t/time-1.gif",description:"Clock measurement"}]},6:{name:"Emotions & Feelings",description:"Express emotions and feelings",theme:"\ud83d\ude0a Emotions & Feelings",signs:[{key:"happy",name:"Happy",gif:"https://media0.giphy.com/media/3o7TKFpahYpUp4g0N2/giphy.gif?cid=790b7611bca188a92e2e759876756ef62ba95b7cdd337c77&rid=giphy.gif&ct=g",description:"Expression of joy"},{key:"sad",name:"Sad",gif:"https://lifeprint.com/asl101/gifs/s/sad.gif",description:"Expression of sadness"},{key:"mad",name:"Mad",gif:"https://lifeprint.com/asl101/gifs/m/mad.gif",description:"Expression of anger"},{key:"love",name:"Love",gif:"https://lifeprint.com/asl101/gifs/l/love.gif",description:"Expression of love"},{key:"like",name:"Like",gif:"https://lifeprint.com/asl101/gifs/l/like.gif",description:"Expression of preference"},{key:"hate",name:"Hate",gif:"https://media.giphy.com/media/l0MYPiNw8l2LAPJXW/giphy.gif",description:"Expression of dislike"},{key:"good",name:"Good",gif:"https://lifeprint.com/asl101/gifs/g/good.gif",description:"Positive evaluation"},{key:"bad",name:"Bad",gif:"https://media.giphy.com/media/v1.Y2lkPTc5MGI3NjExeThwMm9rZGVkejE2N2RhMWp0NTY3Z2pwNHBqZXFlOTF3ZGd0cG1zZSZlcD12MV9naWZzX3NlYXJjaCZjdD1n/l0MYL34CVEqLK27yU/giphy.gif",description:"Negative evaluation"},{key:"better",name:"Better",gif:"https://lifeprint.com/asl101/gifs/b/better.gif",description:"Comparative improvement"},{key:"fine",name:"Fine",gif:"https://th.bing.com/th/id/R.********************************?rik=Qpm%2bw3fHTAWj1A&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2ff%2ffine.gif&ehk=mGMZf4l%2bLZMq4atRomNJSvrSjYgFe%2bRVCm1dYLh5J3I%3d&risl=&pid=ImgRaw&r=0",description:"Feeling okay"},{key:"cry",name:"Cry",gif:"https://www.lifeprint.com/asl101/gifs/c/cry-tears.gif",description:"Expression of sadness"},{key:"smile",name:"Smile",gif:"https://th.bing.com/th/id/OIP.dpce-bMAh-1jorUrPQFW4AHaE-?w=263&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Happy expression"},{key:"cute",name:"Cute",gif:"https://lifeprint.com/asl101/gifs/c/cute-sugar.gif",description:"Adorable quality"},{key:"pretty",name:"Pretty",gif:"https://lifeprint.com/asl101/gifs/b/beautiful.gif",description:"Attractive quality"},{key:"old",name:"Old",gif:"https://lifeprint.com/asl101/gifs/o/old.gif",description:"Advanced age"},{key:"sleepy",name:"Sleepy",gif:"https://th.bing.com/th/id/R.********************************?rik=zdWvzvABcDHTdw&riu=http%3a%2f%2fwww.lifeprint.com%2fasl101%2fgifs-animated%2fsleepy.gif&ehk=zLqDFJMAs2nqG02RbbR6mEMvux4h85JGzls4uwgrePQ%3d&risl=&pid=ImgRaw&r=0",description:"Need for rest"},{key:"hungry",name:"Hungry",gif:"https://media.giphy.com/media/l3vR0xkdFEz4tnfTq/giphy.gif",description:"Need for food"},{key:"thirsty",name:"Thirsty",gif:"https://media.giphy.com/media/l3vR0sYheBulL1P7W/giphy.gif",description:"Need for drink"},{key:"sick",name:"Sick",gif:"https://lifeprint.com/asl101/gifs/s/sick.gif",description:"Not feeling well"},{key:"owie",name:"Owie",gif:"https://lifeprint.com/asl101/gifs/o/owie.gif",description:"Expression of pain"}]},7:{name:"Food & Drinks",description:"Learn food and beverage signs",theme:"\ud83c\udf7d\ufe0f Food & Drinks",signs:[{key:"apple",name:"Apple",gif:"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif",description:"Red fruit"},{key:"carrot",name:"Carrot",gif:"https://media.giphy.com/media/l0HlDdvqxs1jsRtiU/giphy.gif",description:"Orange vegetable"},{key:"chocolate",name:"Chocolate",gif:"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fi.pinimg.com%2foriginals%2f9f%2fa2%2fb5%2f9fa2b5064a72b5e46202d20848f1bf21.gif&ehk=izvOlFp25%2fx5NVTCmqVz0UOnZNOWy%2fAJJtzAhkZ8nTg%3d",description:"Sweet treat"},{key:"cereal",name:"Cereal",gif:"https://th.bing.com/th/id/R.********************************?rik=wPMg%2fK1dYTfR%2bw&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fcereal.gif&ehk=RpDS3wWZM4eryawaxA1wAvWwM0EM%2fdGgJkWY2ce1KFs%3d&risl=&pid=ImgRaw&r=0",description:"Breakfast food"},{key:"frenchfries",name:"French Fries",gif:"https://www.lifeprint.com/asl101/gifs/f/french-fries.gif",description:"Fried potatoes"},{key:"icecream",name:"Ice Cream",gif:"https://media.giphy.com/media/3o7TKp6yVibVMhBSLu/giphy.gif",description:"Frozen dessert"},{key:"pizza",name:"Pizza",gif:"https://lifeprint.com/asl101/gifs/p/pizza.gif",description:"Italian food"},{key:"snack",name:"Snack",gif:"https://media.giphy.com/media/26ybw1E1GTKzLuKDS/giphy.gif",description:"Small food"},{key:"nuts",name:"Nuts",gif:"https://th.bing.com/th/id/OIP.wRnQjn9j2vfFfzAnRR205QHaE-?w=276&h=185&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Edible seeds"},{key:"gum",name:"Gum",gif:"https://lifeprint.com/asl101/gifs/g/gum.gif",description:"Chewing gum"},{key:"napkin",name:"Napkin",gif:"https://lifeprint.com/asl101/gifs/n/napkin.gif",description:"Table cloth"},{key:"taste",name:"Taste",gif:"https://lifeprint.com/asl101/gifs/t/taste.gif",description:"Flavor sense"},{key:"hot",name:"Hot",gif:"https://media.giphy.com/media/3o6Zt99k5aDok347bG/giphy.gif",description:"High temperature"},{key:"wet",name:"Wet",gif:"https://www.babysignlanguage.com/signs/wet.gif",description:"Covered in liquid"},{key:"dry",name:"Dry",gif:"https://th.bing.com/th/id/OIP.A0oQgM0IGtwZjfz1Caj-AgHaE-?w=268&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Not wet"},{key:"sticky",name:"Sticky",gif:"https://th.bing.com/th/id/OIP.fffIgrX_DBAjxGMkskvTvQHaE-?w=240&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Adhesive quality"},{key:"dirty",name:"Dirty",gif:"https://th.bing.com/th/id/OIP.wRA7r1OPPUuEoLL4Hds9jAHaHa?rs=1&pid=ImgDetMain",description:"Not clean"},{key:"clean",name:"Clean",gif:"https://media.giphy.com/media/3o7TKoturrdpf5Muwo/giphy.gif",description:"Not dirty"},{key:"refrigerator",name:"Refrigerator",gif:"https://lifeprint.com/asl101/gifs/r/refrigerator-r-e-f.gif",description:"Cold storage"},{key:"toothbrush",name:"Toothbrush",gif:"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia.giphy.com%2fmedia%2fl3vR0Rq2HVL2KHLUI%2fgiphy.gif&ehk=eC0Sq9sHjrrOrkyJvOogQbXVkTOL5OPCeyVymejL0RU%3d",description:"Dental hygiene"}]},8:{name:"Clothing & Accessories",description:"Learn clothing and accessory signs",theme:"\ud83d\udc55 Clothing & Accessories",signs:[{key:"shirt",name:"Shirt",gif:"https://lifeprint.com/asl101/gifs/s/shirt-volunteer.gif",description:"Upper garment"},{key:"pants",name:"Pants",gif:"https://lifeprint.com/asl101/gifs/p/pants.gif",description:"Lower garment"},{key:"shoes",name:"Shoes",gif:"https://media.giphy.com/media/3o7TKC4StpZKa6d2y4/giphy.gif",description:"Footwear"},{key:"hat",name:"Hat",gif:"https://th.bing.com/th/id/OIP.QyFdqn-0ZqUwNfE6jbzKWAHaE-?w=258&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Head covering"},{key:"socks",name:"Socks",gif:"https://lifeprint.com/asl101/gifs/s/socks.gif",description:"Foot covering"},{key:"jacket",name:"Jacket",gif:"https://www.lifeprint.com/asl101/gifs/c/coat.gif",description:"Outer garment"},{key:"jeans",name:"Jeans",gif:"https://lifeprint.com/asl101/gifs/j/jeans.gif",description:"Denim pants"},{key:"pajamas",name:"Pajamas",gif:"https://lifeprint.com/asl101/gifs/p/pajamas.gif",description:"Sleep clothing"},{key:"underwear",name:"Underwear",gif:"https://th.bing.com/th/id/OIP.c8g9T_lOhbZWRvKAA12J8wHaEO?pid=ImgDet&w=310&h=177&rs=1",description:"Under garments"},{key:"mitten",name:"Mitten",gif:"https://lifeprint.com/asl101/gifs-animated/mittens.gif",description:"Hand covering"},{key:"hair",name:"Hair",gif:"https://www.lifeprint.com/asl101/gifs/h/hair-g-version.gif",description:"Head covering"},{key:"lips",name:"Lips",gif:"https://lifeprint.com/asl101/gifs/l/lips.gif",description:"Mouth feature"},{key:"chin",name:"Chin",gif:"https://lifeprint.com/asl101/gifs/c/chin.gif",description:"Face feature"},{key:"cheek",name:"Cheek",gif:"https://lifeprint.com/asl101/gifs/c/cheek.gif",description:"Face feature"},{key:"tongue",name:"Tongue",gif:"https://th.bing.com/th/id/R.********************************?rik=ZJJ2Ixdj0l0b5A&riu=http%3a%2f%2fwww.aslsearch.com%2fsigns%2fimages%2ftongue.jpg&ehk=MxZVUjfqPa3klIauPGpReg%2fYgnJUyIjlxOOvCYYG0hc%3d&risl=&pid=ImgRaw&r=0",description:"Mouth organ"},{key:"tooth",name:"Tooth",gif:"https://th.bing.com/th/id/R.********************************?rik=ZF%2fsFUXvt5czGA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fsignjpegs%2ft%2fteeth1.jpg&ehk=vI5eDlD4HZWXhK1PQOQz4nA5e6oguHgeXqDo%2fcdcWg4%3d&risl=&pid=ImgRaw&r=0",description:"Dental feature"},{key:"finger",name:"Finger",gif:"https://lifeprint.com/asl101/gifs/f/finger.gif",description:"Hand digit"},{key:"feet",name:"Feet",gif:"https://th.bing.com/th/id/OIP.RaYFj5lvSS6NeIna8NtmZQHaE-?w=247&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Foot organs"},{key:"finger",name:"Finger",gif:"https://lifeprint.com/asl101/gifs/f/finger.gif",description:"Hand digit"},{key:"feet",name:"Feet",gif:"https://th.bing.com/th/id/OIP.RaYFj5lvSS6NeIna8NtmZQHaE-?w=247&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Foot organs"}]},9:{name:"Transportation & Travel",description:"Learn transportation and travel signs",theme:"\ud83d\ude97 Transportation & Travel",signs:[{key:"airplane",name:"Airplane",gif:"https://www.lifeprint.com/asl101/gifs/a/airplane-flying.gif",description:"Flying vehicle"},{key:"boat",name:"Boat",gif:"https://lifeprint.com/asl101/gifs/b/boat-ship.gif",description:"Water vehicle"},{key:"helicopter",name:"Helicopter",gif:"https://th.bing.com/th/id/R.********************************?rik=5uhWxBaByliWA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhelicopter.gif&ehk=mwAyT82RBoeYDe7yaHA1jL3%2f30dUksltmv4dF7YGf%2bU%3d&risl=&pid=ImgRaw&r=0",description:"Rotor aircraft"},{key:"ride",name:"Ride",gif:"https://lifeprint.com/asl101/gifs/r/ride.gif",description:"Travel on vehicle"},{key:"go",name:"Go",gif:"https://media.giphy.com/media/l3vRdVMMN9VsW5a0w/giphy.gif",description:"Move forward"},{key:"up",name:"Up",gif:"https://www.babysignlanguage.com/signs/up.gif",description:"Direction upward"},{key:"down",name:"Down",gif:"https://th.bing.com/th/id/OIP.CZlW6IpZUdgspxVpW6PZ8QHaE-?w=250&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Direction downward"},{key:"stairs",name:"Stairs",gif:"https://th.bing.com/th/id/OIP.8BtYhPXXDQHRqodMyyy3HgHaEc?w=288&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Steps"},{key:"fast",name:"Fast",gif:"https://th.bing.com/th/id/OIP.YX_BqT1FjGm8HeM4k4WFAgAAAA?rs=1&pid=ImgDetMain",description:"Quick movement"},{key:"high",name:"High",gif:"https://lifeprint.com/asl101/gifs/h/high.gif",description:"Elevated position"},{key:"outside",name:"Outside",gif:"https://lifeprint.com/asl101/gifs/o/outside.gif",description:"Exterior location"},{key:"home",name:"Home",gif:"https://th.bing.com/th/id/R.********************************?rik=%2bnBd%2foQjxnoPfg&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhome-2.gif&ehk=7yD%2f%2fh6JN1Y4D4BOrUjgKW4Jccy2Y4GVYLf%2fzyk%2b5YY%3d&risl=&pid=ImgRaw&r=0",description:"Residence"},{key:"room",name:"Room",gif:"https://lifeprint.com/asl101/gifs/r/room-box.gif",description:"Enclosed space"},{key:"bedroom",name:"Bedroom",gif:"https://lifeprint.com/asl101/gifs/b/bedroom.gif",description:"Sleeping room"},{key:"closet",name:"Closet",gif:"https://lifeprint.com/asl101/gifs/c/closet.gif",description:"Storage space"},{key:"drawer",name:"Drawer",gif:"https://th.bing.com/th/id/OIP.8yooqOFFixqki7j28PVpYQHaE-?w=234&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Storage compartment"},{key:"backyard",name:"Backyard",gif:"https://lifeprint.com/asl101/gifs/b/backyard.gif",description:"Rear yard"},{key:"store",name:"Store",gif:"https://th.bing.com/th/id/R.********************************?rik=x7oUPJGckc7QDg&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fstore.gif&ehk=P7beooAyFUst%2bbVtqIqINeQGP0%2bIUlNSPXc1Du5zWfQ%3d&risl=&pid=ImgRaw&r=0",description:"Shopping place"},{key:"pool",name:"Pool",gif:"https://th.bing.com/th/id/OIP.dhcMKyW2psDcA5uwsRaRagHaEc?w=276&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Swimming area"}]},10:{name:"Activities & Actions",description:"Learn action and activity signs",theme:"\ud83c\udfaf Activities & Actions",signs:[{key:"play",name:"Play",gif:"https://lifeprint.com/asl101/gifs/p/play.gif",description:"Recreational activity"},{key:"dance",name:"Dance",gif:"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia.giphy.com%2fmedia%2f3o7TKMspYQjQTbOz2U%2fgiphy.gif&ehk=h%2bdBHCxuoOT89ovSy5uTk6MCL9acaBEV6ld9lrVDRF4%3d",description:"Rhythmic movement"},{key:"jump",name:"Jump",gif:"https://lifeprint.com/asl101/gifs-animated/jump.gif",description:"Leap upward"},{key:"fall",name:"Fall",gif:"https://lifeprint.com/asl101/gifs/f/fall.gif",description:"Drop down"},{key:"drop",name:"Drop",gif:"https://th.bing.com/th/id/OIP.XQJn0tOccOUmG8OZHz8X9gHaE-?w=247&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Let fall"},{key:"cut",name:"Cut",gif:"https://th.bing.com/th/id/OIP.ZtKu3hlJ6pduArqfgEcyUgHaE-?w=248&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Divide with tool"},{key:"open",name:"Open",gif:"https://th.bing.com/th/id/OIP.BeMiGXQFuYk_6ZrgG3iqzQHaE-?w=264&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Unclose"},{key:"close",name:"Close",gif:"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia2.giphy.com%2fmedia%2fl4JyZuXNGxS3Yydeo%2fgiphy.gif%3fcid%3d790b7611318eb5b864ad67b3cecb35b9d81240a50d251bb0%26rid%3dgiphy.gif%26ct%3dg&ehk=A6wfp3Afm3rFCPLWSjgQd6JVjmRSBNBlk9vd0jVNgJc%3d",description:"Shut"},{key:"find",name:"Find",gif:"https://www.lifeprint.com/asl101/gifs/f/find-pick.gif",description:"Locate"},{key:"hide",name:"Hide",gif:"https://lifeprint.com/asl101/gifs/h/hide.gif",description:"Conceal"},{key:"look",name:"Look",gif:"https://th.bing.com/th/id/R.********************************?rik=pYhzip7LqNs7qw&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fl%2flook-at-1.gif&ehk=rFJ7dBrMGFDK0nHLzrOPAzROVE7yqyDEcb%2btLqKqYOI%3d&risl=&pid=ImgRaw&r=0",description:"Direct gaze"},{key:"listen",name:"Listen",gif:"https://th.bing.com/th/id/OIP.VjsXAad6abRwkCla83kbZQHaEc?w=284&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Pay attention"},{key:"talk",name:"Talk",gif:"https://lifeprint.com/asl101/gifs/t/talk.gif",description:"Communicate"},{key:"read",name:"Read",gif:"https://lifeprint.com/asl101/gifs/r/read.gif",description:"Process text"},{key:"write",name:"Write",gif:"https://lifeprint.com/asl101/gifs/w/write.gif",description:"Create text"},{key:"draw",name:"Draw",gif:"https://lifeprint.com/asl101/gifs/d/draw.gif",description:"Create picture"},{key:"paint",name:"Paint",gif:"https://lifeprint.com/asl101/gifs/p/paint.gif",description:"Apply color"},{key:"build",name:"Build",gif:"https://lifeprint.com/asl101/gifs/b/build.gif",description:"Construct"},{key:"fix",name:"Fix",gif:"https://lifeprint.com/asl101/gifs/f/fix.gif",description:"Repair"},{key:"help",name:"Help",gif:"https://lifeprint.com/asl101/gifs/h/help.gif",description:"Assist"}]},11:{name:"Weather & Environment",description:"Learn weather and environmental signs",theme:"\ud83c\udf24\ufe0f Weather & Environment",signs:[{key:"sun",name:"Sun",gif:"https://media.giphy.com/media/3o6Zt7merN2zxEtNRK/giphy.gif",description:"Solar star"},{key:"moon",name:"Moon",gif:"https://th.bing.com/th/id/R.********************************?rik=XbVhBJtkANrG9g&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fm%2fmoon.gif&ehk=YSDvFeUSTa9X1BEJhDjdnLC4c7zWn8z7Hj%2fMkkLUyFE%3d&risl=&pid=ImgRaw&r=0",description:"Night satellite"},{key:"star",name:"Star",gif:"https://lifeprint.com/asl101/gifs/s/star.gif",description:"Celestial body"},{key:"cloud",name:"Cloud",gif:"https://th.bing.com/th/id/OIP.hMO89bV2zwVcIVIa7FOT5QHaEc?rs=1&pid=ImgDetMain",description:"Sky formation"},{key:"rain",name:"Rain",gif:"https://lifeprint.com/asl101/gifs/r/rain.gif",description:"Water precipitation"},{key:"snow",name:"Snow",gif:"https://lifeprint.com/asl101/gifs/s/snow.gif",description:"Frozen precipitation"},{key:"wind",name:"Wind",gif:"https://lifeprint.com/asl101/gifs/w/wind.gif",description:"Air movement"},{key:"hot",name:"Hot",gif:"https://media.giphy.com/media/3o6Zt99k5aDok347bG/giphy.gif",description:"High temperature"},{key:"cold",name:"Cold",gif:"https://lifeprint.com/asl101/gifs/c/cold.gif",description:"Low temperature"},{key:"warm",name:"Warm",gif:"https://lifeprint.com/asl101/gifs/w/warm.gif",description:"Moderate temperature"},{key:"wet",name:"Wet",gif:"https://www.babysignlanguage.com/signs/wet.gif",description:"Covered in liquid"},{key:"dry",name:"Dry",gif:"https://th.bing.com/th/id/OIP.A0oQgM0IGtwZjfz1Caj-AgHaE-?w=268&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Not wet"},{key:"loud",name:"Loud",gif:"https://lifeprint.com/asl101/gifs-animated/loud.gif",description:"High volume"},{key:"quiet",name:"Quiet",gif:"https://lifeprint.com/asl101/gifs-animated/quiet-03.gif",description:"Low volume"},{key:"noisy",name:"Noisy",gif:"https://lifeprint.com/asl101/gifs/n/noisy.gif",description:"Making sound"},{key:"shhh",name:"Shhh",gif:"https://lifeprint.com/asl101/signjpegs/s/shhh.jpg",description:"Be quiet"},{key:"light",name:"Light",gif:"https://lifeprint.com/asl101/gifs/l/light.gif",description:"Bright illumination"},{key:"dark",name:"Dark",gif:"https://lifeprint.com/asl101/gifs/d/dark.gif",description:"No light"},{key:"big",name:"Big",gif:"https://lifeprint.com/asl101/gifs/b/big.gif",description:"Large size"},{key:"small",name:"Small",gif:"https://lifeprint.com/asl101/gifs/s/small.gif",description:"Little size"},{key:"many",name:"Many",gif:"https://lifeprint.com/asl101/gifs/m/many.gif",description:"Large quantity"}]},12:{name:"Advanced Animals & Nature",description:"Learn more complex animal and nature signs",theme:"\ud83e\udd81 Advanced Animals & Nature",signs:[{key:"elephant",name:"Elephant",gif:"https://lifeprint.com/asl101/gifs-animated/elephant.gif",description:"Large mammal"},{key:"lion",name:"Lion",gif:"https://th.bing.com/th/id/OIP.8sDkvbXdMKVmCNlDV79WpAHaE-?w=247&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"King of jungle"},{key:"tiger",name:"Tiger",gif:"https://lifeprint.com/asl101/gifs/t/tiger.gif",description:"Striped cat"},{key:"bear",name:"Bear",gif:"https://lifeprint.com/asl101/gifs/b/bear.gif",description:"Large mammal"},{key:"wolf",name:"Wolf",gif:"https://lifeprint.com/asl101/gifs/w/wolf-side-view.gif",description:"Wild canine"},{key:"fox",name:"Fox",gif:"https://lifeprint.com/asl101/gifs/f/fox.gif",description:"Small canine"},{key:"deer",name:"Deer",gif:"https://lifeprint.com/asl101/gifs/d/deer.gif",description:"Forest animal"},{key:"squirrel",name:"Squirrel",gif:"https://lifeprint.com/asl101/gifs/s/squirrel.gif",description:"Tree rodent"},{key:"mouse",name:"Mouse",gif:"https://lifeprint.com/asl101/gifs/m/mouse.gif",description:"Small rodent"},{key:"rat",name:"Rat",gif:"https://lifeprint.com/asl101/gifs/r/rat.gif",description:"Large rodent"},{key:"snake",name:"Snake",gif:"https://lifeprint.com/asl101/gifs/s/snake.gif",description:"Reptile"},{key:"turtle",name:"Turtle",gif:"https://lifeprint.com/asl101/gifs/t/turtle.gif",description:"Shell reptile"},{key:"frog",name:"Frog",gif:"https://media.giphy.com/media/l0HlKl64lIvTjZ7QA/giphy.gif",description:"Amphibian"},{key:"duck",name:"Duck",gif:"https://th.bing.com/th/id/R.********************************?rik=ZetjiJ3WOhOXrQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fd%2fduck.gif&ehk=STeui62x5lieai0VcyeZkX2t8rILR%2f8GR5F3x2xJ5tw%3d&risl=&pid=ImgRaw&r=0",description:"Water bird"},{key:"goose",name:"Goose",gif:"https://www.babysignlanguage.com/signs/goose.gif",description:"Large water bird"},{key:"owl",name:"Owl",gif:"https://lifeprint.com/asl101/gifs/o/owl.gif",description:"Night bird"},{key:"bee",name:"Bee",gif:"https://lifeprint.com/asl101/gifs/b/bee.gif",description:"Flying insect"},{key:"bug",name:"Bug",gif:"https://lifeprint.com/asl101/gifs/b/bug.gif",description:"Small insect"},{key:"spider",name:"Spider",gif:"https://lifeprint.com/asl101/gifs/s/spider.gif",description:"Eight-legged creature"},{key:"butterfly",name:"Butterfly",gif:"https://lifeprint.com/asl101/gifs/b/butterfly.gif",description:"Flying insect"}]},13:{name:"Advanced Concepts & Communication",description:"Learn advanced communication and concept signs",theme:"\ud83d\udcad Advanced Concepts & Communication",signs:[{key:"think",name:"Think",gif:"https://lifeprint.com/asl101/gifs/t/think.gif",description:"Mental process"},{key:"know",name:"Know",gif:"https://lifeprint.com/asl101/gifs/k/know.gif",description:"Have knowledge"},{key:"understand",name:"Understand",gif:"https://lifeprint.com/asl101/gifs/u/understand.gif",description:"Comprehend"},{key:"remember",name:"Remember",gif:"https://lifeprint.com/asl101/gifs/r/remember.gif",description:"Recall"},{key:"forget",name:"Forget",gif:"https://lifeprint.com/asl101/gifs/f/forget.gif",description:"Not remember"},{key:"learn",name:"Learn",gif:"https://lifeprint.com/asl101/gifs/l/learn.gif",description:"Acquire knowledge"},{key:"teach",name:"Teach",gif:"https://lifeprint.com/asl101/gifs/t/teach.gif",description:"Impart knowledge"},{key:"explain",name:"Explain",gif:"https://lifeprint.com/asl101/gifs/e/explain.gif",description:"Make clear"},{key:"show",name:"Show",gif:"https://lifeprint.com/asl101/gifs/s/show.gif",description:"Display"},{key:"tell",name:"Tell",gif:"https://lifeprint.com/asl101/gifs/t/tell.gif",description:"Inform"},{key:"ask",name:"Ask",gif:"https://lifeprint.com/asl101/gifs/a/ask.gif",description:"Request information"},{key:"answer",name:"Answer",gif:"https://lifeprint.com/asl101/gifs/a/answer.gif",description:"Respond"},{key:"question",name:"Question",gif:"https://lifeprint.com/asl101/gifs/q/question.gif",description:"Inquiry"},{key:"problem",name:"Problem",gif:"https://lifeprint.com/asl101/gifs/p/problem.gif",description:"Difficulty"},{key:"solution",name:"Solution",gif:"https://lifeprint.com/asl101/gifs/s/solution.gif",description:"Answer to problem"},{key:"idea",name:"Idea",gif:"https://lifeprint.com/asl101/gifs/i/idea.gif",description:"Thought"},{key:"plan",name:"Plan",gif:"https://lifeprint.com/asl101/gifs/p/plan.gif",description:"Strategy"},{key:"decide",name:"Decide",gif:"https://lifeprint.com/asl101/gifs/d/decide.gif",description:"Make choice"},{key:"choose",name:"Choose",gif:"https://lifeprint.com/asl101/gifs/c/choose.gif",description:"Select"},{key:"want",name:"Want",gif:"https://lifeprint.com/asl101/gifs/w/want.gif",description:"Desire"}]}},Fr=()=>Object.keys(Dr).length,Hr={colors:{primary:{50:"#f0f9ff",100:"#e0f2fe",200:"#bae6fd",300:"#7dd3fc",400:"#38bdf8",500:"#0ea5e9",600:"#0284c7",700:"#0369a1",800:"#075985",900:"#0c4a6e"},secondary:{50:"#fdf4ff",100:"#fae8ff",200:"#f5d0fe",300:"#f0abfc",400:"#e879f9",500:"#d946ef",600:"#c026d3",700:"#a21caf",800:"#86198f",900:"#701a75"},success:{50:"#f0fdf4",100:"#dcfce7",200:"#bbf7d0",300:"#86efac",400:"#4ade80",500:"#22c55e",600:"#16a34a",700:"#15803d",800:"#166534",900:"#14532d"},warning:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f"},error:{50:"#fef2f2",100:"#fee2e2",200:"#fecaca",300:"#fca5a5",400:"#f87171",500:"#ef4444",600:"#dc2626",700:"#b91c1c",800:"#991b1b",900:"#7f1d1d"},neutral:{50:"#fafafa",100:"#f5f5f5",200:"#e5e5e5",300:"#d4d4d4",400:"#a3a3a3",500:"#737373",600:"#525252",700:"#404040",800:"#262626",900:"#171717"},background:"#f8fafc",surface:"#fafafa",text:{primary:"#171717",secondary:"#525252",tertiary:"#a3a3a3",inverse:"#ffffff"},gradients:{primary:"linear-gradient(135deg, #0ea5e9 0%, #d946ef 100%)",secondary:"linear-gradient(135deg, #22c55e 0%, #0ea5e9 100%)",success:"linear-gradient(135deg, #22c55e 0%, #16a34a 100%)",warning:"linear-gradient(135deg, #f59e0b 0%, #d97706 100%)",error:"linear-gradient(135deg, #ef4444 0%, #dc2626 100%)",surface:"linear-gradient(135deg, #ffffff 0%, #fafafa 100%)",glass:"linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)"}},typography:{fontFamily:{primary:'"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',mono:'"JetBrains Mono", "Fira Code", Consolas, monospace'},fontSize:{xs:"0.75rem",sm:"0.875rem",base:"1rem",lg:"1.125rem",xl:"1.25rem","2xl":"1.5rem","3xl":"1.875rem","4xl":"2.25rem","5xl":"3rem","6xl":"3.75rem"},fontWeight:{light:300,normal:400,medium:500,semibold:600,bold:700,extrabold:800},lineHeight:{tight:1.25,normal:1.5,relaxed:1.75}},spacing:{0:"0",1:"0.25rem",2:"0.5rem",3:"0.75rem",4:"1rem",5:"1.25rem",6:"1.5rem",8:"2rem",10:"2.5rem",12:"3rem",16:"4rem",20:"5rem",24:"6rem",32:"8rem"},borderRadius:{none:"0",sm:"0.125rem",base:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},shadows:{sm:"0 1px 2px 0 rgba(0, 0, 0, 0.05)",base:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",md:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",lg:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",xl:"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)","2xl":"0 25px 50px -12px rgba(0, 0, 0, 0.25)",inner:"inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)",glow:"0 0 20px rgba(14, 165, 233, 0.3)",glowSuccess:"0 0 20px rgba(34, 197, 94, 0.3)",glowError:"0 0 20px rgba(239, 68, 68, 0.3)"},breakpoints:{xs:"320px",sm:"640px",md:"768px",lg:"1024px",xl:"1280px","2xl":"1536px"},zIndex:{hide:-1,auto:"auto",base:0,docked:10,dropdown:1e3,sticky:1100,banner:1200,overlay:1300,modal:1400,popover:1500,skipLink:1600,toast:1700,tooltip:1800},transitions:{fast:"150ms ease",normal:"300ms ease",slow:"500ms ease",bounce:"300ms cubic-bezier(0.68, -0.55, 0.265, 1.55)"},components:{button:{height:{sm:"2rem",md:"2.5rem",lg:"3rem",xl:"3.5rem"},padding:{sm:"0.5rem 1rem",md:"0.75rem 1.5rem",lg:"1rem 2rem",xl:"1.25rem 2.5rem"}},card:{padding:{sm:"1rem",md:"1.5rem",lg:"2rem",xl:"2.5rem"}},input:{height:{sm:"2rem",md:"2.5rem",lg:"3rem"}}}};var Ur,Wr,Br,Vr,qr,Yr,Gr,Kr,Qr,$r,Zr,Xr,Jr,ei,ni,ti,ri,ii,ai,oi,si,li,ci,di,ui,fi,pi,gi,mi,hi,bi,yi,vi,wi;Gn(Ur||(Ur=r(["\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n"]))),Gn(Wr||(Wr=r(["\n  from {\n    opacity: 0;\n    transform: translateX(100%);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n"]))),Gn(Br||(Br=r(["\n  from {\n    opacity: 0;\n    transform: translateX(-100%);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n"]))),Gn(Vr||(Vr=r(["\n  from {\n    opacity: 0;\n    transform: scale(0.9);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1);\n  }\n"]))),Gn(qr||(qr=r(["\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n"])));const ki=Gn(Yr||(Yr=r(["\n  0% {\n    background-position: -200px 0;\n  }\n  100% {\n    background-position: calc(200px + 100%) 0;\n  }\n"]))),xi=Yn.div(Gr||(Gr=r(["\n  width: 100%;\n  max-width: ",";\n  margin: 0 auto;\n  padding: 0 ",";\n  \n  @media (max-width: ",") {\n    padding: 0 ",";\n  }\n"])),e=>e.maxWidth||"1200px",Hr.spacing[4],Hr.breakpoints.sm,Hr.spacing[3]),Si=Yn.section(Kr||(Kr=r(["\n  padding: "," 0;\n  \n  @media (max-width: ",") {\n    padding: "," 0;\n  }\n  \n  @media (max-width: ",") {\n    padding: "," 0;\n  }\n"])),Hr.spacing[12],Hr.breakpoints.md,Hr.spacing[8],Hr.breakpoints.sm,Hr.spacing[6]),Ei=Yn.div(Qr||(Qr=r(["\n  display: grid;\n  grid-template-columns: ",";\n  gap: ",";\n  \n  @media (max-width: ",") {\n    grid-template-columns: 1fr;\n    gap: ",";\n  }\n"])),e=>e.columns||"repeat(auto-fit, minmax(300px, 1fr))",e=>e.gap||Hr.spacing[6],Hr.breakpoints.md,Hr.spacing[4]),Ci=(Yn.div($r||($r=r(["\n  display: flex;\n  align-items: ",";\n  justify-content: ",";\n  gap: ",";\n  flex-direction: ",";\n  flex-wrap: ",";\n  \n  @media (max-width: ",") {\n    flex-direction: ",";\n    gap: ",";\n  }\n"])),e=>e.align||"center",e=>e.justify||"flex-start",e=>e.gap||Hr.spacing[4],e=>e.direction||"row",e=>e.wrap||"nowrap",Hr.breakpoints.sm,e=>e.mobileDirection||e.direction||"column",Hr.spacing[3]),Yn.div(Zr||(Zr=r(["\n  background: ",";\n  border-radius: ",";\n  box-shadow: ",";\n  padding: ",";\n  transition: all ",";\n  position: relative;\n  overflow: hidden;\n  \n  ","\n  \n  ","\n  \n  ","\n  \n  @media (max-width: ",") {\n    padding: ",";\n    border-radius: ",";\n  }\n"])),Hr.colors.background,Hr.borderRadius["2xl"],Hr.shadows.lg,e=>Hr.components.card.padding[e.size||"md"],Hr.transitions.normal,e=>e.hover&&Bn(Xr||(Xr=r(["\n    &:hover {\n      transform: translateY(-4px);\n      box-shadow: ",";\n    }\n  "])),Hr.shadows.xl),e=>e.glass&&Bn(Jr||(Jr=r(["\n    background: rgba(255, 255, 255, 0.1);\n    backdrop-filter: blur(20px);\n    border: 1px solid rgba(255, 255, 255, 0.2);\n  "]))),e=>e.gradient&&Bn(ei||(ei=r(["\n    background: ",";\n    color: ",";\n  "])),Hr.colors.gradients[e.gradient],Hr.colors.text.inverse),Hr.breakpoints.sm,Hr.components.card.padding.sm,Hr.borderRadius.xl)),zi=(Yn(Ci)(ni||(ni=r(["\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n"]))),Yn.button(ti||(ti=r(["\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  gap: ",";\n  padding: ",";\n  height: ",";\n  border: none;\n  border-radius: ",";\n  font-family: ",";\n  font-weight: ",";\n  font-size: ",";\n  cursor: pointer;\n  transition: all ",";\n  position: relative;\n  overflow: hidden;\n  user-select: none;\n  touch-action: manipulation;\n  \n  /* Variants */\n  ","\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none !important;\n  }\n  \n  /* Mobile optimizations */\n  @media (max-width: ",") {\n    min-height: 44px; /* iOS touch target */\n    padding: "," ",";\n    font-size: ",";\n  }\n  \n  /* Touch feedback */\n  @media (hover: none) {\n    &:active {\n      transform: scale(0.98);\n    }\n  }\n"])),Hr.spacing[2],e=>Hr.components.button.padding[e.size||"md"],e=>Hr.components.button.height[e.size||"md"],Hr.borderRadius.xl,Hr.typography.fontFamily.primary,Hr.typography.fontWeight.semibold,e=>Hr.typography.fontSize[{sm:"sm",md:"base",lg:"lg",xl:"xl"}[e.size]||"base"],Hr.transitions.normal,e=>{switch(e.variant){case"primary":return Bn(ri||(ri=r(["\n          background: ",";\n          color: ",";\n          box-shadow: ",";\n          \n          &:hover:not(:disabled) {\n            transform: translateY(-2px);\n            box-shadow: ",";\n          }\n          \n          &:active {\n            transform: translateY(0);\n          }\n        "])),Hr.colors.gradients.primary,Hr.colors.text.inverse,Hr.shadows.md,Hr.shadows.glow);case"secondary":return Bn(ii||(ii=r(["\n          background: ",";\n          color: ",";\n          border: 1px solid ",";\n          \n          &:hover:not(:disabled) {\n            background: ",";\n            transform: translateY(-1px);\n            box-shadow: ",";\n          }\n        "])),Hr.colors.neutral[100],Hr.colors.text.primary,Hr.colors.neutral[200],Hr.colors.neutral[50],Hr.shadows.md);case"success":return Bn(ai||(ai=r(["\n          background: ",";\n          color: ",";\n          \n          &:hover:not(:disabled) {\n            transform: translateY(-2px);\n            box-shadow: ",";\n          }\n        "])),Hr.colors.gradients.success,Hr.colors.text.inverse,Hr.shadows.glowSuccess);case"error":return Bn(oi||(oi=r(["\n          background: ",";\n          color: ",";\n          \n          &:hover:not(:disabled) {\n            transform: translateY(-2px);\n            box-shadow: ",";\n          }\n        "])),Hr.colors.gradients.error,Hr.colors.text.inverse,Hr.shadows.glowError);case"ghost":return Bn(si||(si=r(["\n          background: transparent;\n          color: ",";\n          \n          &:hover:not(:disabled) {\n            background: ",";\n            color: ",";\n          }\n        "])),Hr.colors.text.secondary,Hr.colors.neutral[100],Hr.colors.text.primary);default:return Bn(li||(li=r(["\n          background: ",";\n          color: ",";\n          \n          &:hover:not(:disabled) {\n            background: ",";\n            transform: translateY(-1px);\n          }\n        "])),Hr.colors.primary[500],Hr.colors.text.inverse,Hr.colors.primary[600])}},Hr.breakpoints.sm,Hr.spacing[3],Hr.spacing[6],Hr.typography.fontSize.base)),ji=(Yn(zi)(ci||(ci=r(["\n  width: ",";\n  height: ",";\n  padding: 0;\n  border-radius: ",";\n  \n  @media (max-width: ",") {\n    width: 44px;\n    height: 44px;\n  }\n"])),e=>Hr.components.button.height[e.size||"md"],e=>Hr.components.button.height[e.size||"md"],Hr.borderRadius.full,Hr.breakpoints.sm),Yn.h1(di||(di=r(["\n  font-family: ",";\n  font-weight: ",";\n  color: ",";\n  line-height: ",";\n  margin: 0;\n  \n  ","\n  \n  ","\n  \n  @media (max-width: ",") {\n    font-size: ",";\n  }\n  \n  @media (max-width: ",") {\n    font-size: ",";\n  }\n"])),Hr.typography.fontFamily.primary,e=>Hr.typography.fontWeight[e.weight||"bold"],e=>e.color||Hr.colors.text.primary,Hr.typography.lineHeight.tight,e=>{const n=e.level||1;return Bn(ui||(ui=r(["\n      font-size: ",";\n    "])),Hr.typography.fontSize[{1:"5xl",2:"4xl",3:"3xl",4:"2xl",5:"xl",6:"lg"}[n]])},e=>e.gradient&&Bn(fi||(fi=r(["\n    background: ",";\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n  "])),Hr.colors.gradients[e.gradient]),Hr.breakpoints.md,e=>{const n=e.level||1;return Hr.typography.fontSize[{1:"4xl",2:"3xl",3:"2xl",4:"xl",5:"lg",6:"base"}[n]]},Hr.breakpoints.sm,e=>{const n=e.level||1;return Hr.typography.fontSize[{1:"3xl",2:"2xl",3:"xl",4:"lg",5:"base",6:"sm"}[n]]})),Pi=Yn.p(pi||(pi=r(["\n  font-family: ",";\n  font-size: ",";\n  font-weight: ",";\n  color: ",";\n  line-height: ",";\n  margin: 0;\n  \n  @media (max-width: ",") {\n    font-size: ",";\n  }\n"])),Hr.typography.fontFamily.primary,e=>Hr.typography.fontSize[e.size||"base"],e=>Hr.typography.fontWeight[e.weight||"normal"],e=>e.color?e.color:e.muted?Hr.colors.text.secondary:Hr.colors.text.primary,Hr.typography.lineHeight.normal,Hr.breakpoints.sm,e=>{const n=e.size||"base";return Hr.typography.fontSize[{"2xl":"xl",xl:"lg",lg:"base",base:"sm",sm:"xs"}[n]||n]}),Ri=Yn.div(gi||(gi=r(["\n  width: 100%;\n  height: ",";\n  background: ",";\n  border-radius: ",";\n  overflow: hidden;\n  position: relative;\n"])),e=>e.height||"8px",Hr.colors.neutral[200],Hr.borderRadius.full),_i=Yn.div(mi||(mi=r(["\n  height: 100%;\n  background: ",";\n  border-radius: ",";\n  transition: width ",";\n  position: relative;\n  \n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(\n      90deg,\n      transparent,\n      rgba(255, 255, 255, 0.3),\n      transparent\n    );\n    animation: "," 2s infinite;\n  }\n"])),e=>Hr.colors.gradients[e.variant||"primary"],Hr.borderRadius.full,Hr.transitions.normal,ki),Oi=Yn.span(hi||(hi=r(["\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: "," ",";\n  border-radius: ",";\n  font-size: ",";\n  font-weight: ",";\n  \n  ","\n"])),Hr.spacing[1],Hr.spacing[3],Hr.borderRadius.full,Hr.typography.fontSize.sm,Hr.typography.fontWeight.medium,e=>{switch(e.variant){case"success":return Bn(bi||(bi=r(["\n          background: ",";\n          color: ",";\n        "])),Hr.colors.success[100],Hr.colors.success[800]);case"warning":return Bn(yi||(yi=r(["\n          background: ",";\n          color: ",";\n        "])),Hr.colors.warning[100],Hr.colors.warning[800]);case"error":return Bn(vi||(vi=r(["\n          background: ",";\n          color: ",";\n        "])),Hr.colors.error[100],Hr.colors.error[800]);default:return Bn(wi||(wi=r(["\n          background: ",";\n          color: ",";\n        "])),Hr.colors.primary[100],Hr.colors.primary[800])}});var Ti,Ai,Ni,Li,Mi,Ii,Di,Fi,Hi,Ui,Wi,Bi,Vi,qi,Yi,Gi,Ki,Qi,$i,Zi,Xi,Ji,ea;const na=Gn(Ti||(Ti=r(["\n  from {\n    opacity: 0;\n    transform: translateY(30px) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n"]))),ta=Gn(Ai||(Ai=r(["\n  0%, 100% {\n    transform: scale(1);\n    box-shadow: ",";\n  }\n  50% {\n    transform: scale(1.02);\n    box-shadow: ",";\n  }\n"])),Hr.shadows.lg,Hr.shadows.glow),ra=Gn(Ni||(Ni=r(["\n  0%, 100% { transform: translateY(0px); }\n  50% { transform: translateY(-10px); }\n"]))),ia=Yn(xi)(Li||(Li=r(["\n  background: var(--bg-primary);\n  min-height: 100vh;\n  padding: "," ",";\n  position: relative;\n  overflow-x: hidden;\n\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n\n  @media (max-width: ",") {\n    padding: "," ",";\n  }\n"])),Hr.spacing[8],Hr.spacing[4],Hr.breakpoints.md,Hr.spacing[6],Hr.spacing[3]),aa=Yn(Si)(Mi||(Mi=r(["\n  text-align: center;\n  padding: "," 0 ",";\n  animation: "," 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);\n\n  @media (max-width: ",") {\n    padding: "," 0 ",";\n  }\n"])),Hr.spacing[8],Hr.spacing[12],na,Hr.breakpoints.md,Hr.spacing[6],Hr.spacing[8]),oa=Yn(ji)(Ii||(Ii=r(["\n  margin-bottom: ",";\n  position: relative;\n\n  &::after {\n    content: '\u2728';\n    position: absolute;\n    top: -10px;\n    right: -20px;\n    font-size: 2rem;\n    animation: "," 3s ease-in-out infinite;\n  }\n\n  @media (max-width: ",") {\n    &::after {\n      display: none;\n    }\n  }\n"])),Hr.spacing[4],ra,Hr.breakpoints.sm),sa=Yn(Pi)(Di||(Di=r(["\n  max-width: 600px;\n  margin: 0 auto ",";\n"])),Hr.spacing[8]),la=Yn(Ei)(Fi||(Fi=r(["\n  margin-bottom: ",";\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\n\n  @media (max-width: ",") {\n    grid-template-columns: 1fr;\n  }\n"])),Hr.spacing[8],Hr.breakpoints.sm),ca=Yn(Ci)(Hi||(Hi=r(["\n  cursor: ",";\n  opacity: ",";\n  animation: "," 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);\n  animation-delay: ","s;\n  animation-fill-mode: both;\n  position: relative;\n  overflow: hidden;\n  border: 2px solid ",";\n\n  &:hover {\n    transform: ",";\n    box-shadow: ",";\n  }\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 6px;\n    background: ",";\n    border-radius: "," "," 0 0;\n  }\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: -50%;\n    left: -50%;\n    width: 200%;\n    height: 200%;\n    background: ",";\n    opacity: 0.5;\n    pointer-events: none;\n    z-index: 0;\n  }\n\n  > * {\n    position: relative;\n    z-index: 1;\n  }\n\n  ","\n\n  ","\n"])),e=>e.isLocked?"not-allowed":"pointer",e=>e.isLocked?.6:1,na,e=>.1*e.index,e=>e.isCompleted?Hr.colors.success[200]:e.isLocked?Hr.colors.neutral[200]:e.isActive?Hr.colors.primary[300]:Hr.colors.neutral[100],e=>e.isLocked?"none":"translateY(-8px) scale(1.02)",e=>e.isLocked?Hr.shadows.lg:e.isCompleted?Hr.shadows.glowSuccess:Hr.shadows.glow,e=>e.isCompleted?Hr.colors.gradients.success:e.isLocked?Hr.colors.gradients.secondary:Hr.colors.gradients.primary,Hr.borderRadius["2xl"],Hr.borderRadius["2xl"],e=>e.isCompleted?"radial-gradient(circle, ".concat(Hr.colors.success[50]," 0%, transparent 70%)"):e.isActive?"radial-gradient(circle, ".concat(Hr.colors.primary[50]," 0%, transparent 70%)"):"transparent",e=>e.isActive&&"\n    animation: ".concat(ta," 2s ease infinite;\n    box-shadow: 0 0 0 4px ").concat(Hr.colors.primary[200],";\n  "),e=>e.isCompleted&&"\n    &::before {\n      background: ".concat(Hr.colors.gradients.success,";\n      box-shadow: 0 2px 10px ").concat(Hr.colors.success[300],";\n    }\n  ")),da=Yn.div(Ui||(Ui=r(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ",";\n"])),Hr.spacing[4]),ua=Yn.div(Wi||(Wi=r(["\n  background: ",";\n  color: ",";\n  width: 60px;\n  height: 60px;\n  border-radius: ",";\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: ",";\n  font-size: ",";\n  box-shadow: ",";\n  position: relative;\n\n  ","\n\n  @media (max-width: ",") {\n    width: 50px;\n    height: 50px;\n    font-size: ",";\n  }\n"])),e=>e.isCompleted?Hr.colors.gradients.success:e.isLocked?Hr.colors.gradients.secondary:Hr.colors.gradients.primary,Hr.colors.text.inverse,Hr.borderRadius.full,Hr.typography.fontWeight.bold,Hr.typography.fontSize.xl,Hr.shadows.md,e=>e.isCompleted&&"\n    &::after {\n      content: '\u2728';\n      position: absolute;\n      top: -5px;\n      right: -5px;\n      font-size: 1rem;\n      animation: ".concat(ra," 2s ease-in-out infinite;\n    }\n  "),Hr.breakpoints.sm,Hr.typography.fontSize.lg),fa=Yn.div(Bi||(Bi=r(["\n  display: flex;\n  align-items: center;\n  gap: ",";\n  color: ",";\n  font-weight: ",";\n"])),Hr.spacing[2],e=>e.isCompleted?Hr.colors.success[600]:e.isLocked?Hr.colors.neutral[400]:Hr.colors.primary[600],Hr.typography.fontWeight.medium),pa=Yn(ji)(Vi||(Vi=r(["\n  margin-bottom: ",";\n  color: ",";\n"])),Hr.spacing[2],Hr.colors.text.primary),ga=Yn.div(qi||(qi=r(["\n  font-size: ",";\n  margin-bottom: ",";\n  text-align: center;\n  padding: ",";\n  background: ",";\n  border-radius: ",";\n  border: 1px solid ",";\n\n  @media (max-width: ",") {\n    font-size: ",";\n  }\n"])),Hr.typography.fontSize.xl,Hr.spacing[3],Hr.spacing[2],Hr.colors.gradients.surface,Hr.borderRadius.lg,Hr.colors.neutral[100],Hr.breakpoints.sm,Hr.typography.fontSize.lg),ma=Yn(Pi)(Yi||(Yi=r(["\n  margin-bottom: ",";\n  text-align: center;\n"])),Hr.spacing[4]),ha=Yn.div(Gi||(Gi=r(["\n  margin-bottom: ",";\n"])),Hr.spacing[5]),ba=Yn.div(Ki||(Ki=r(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ",";\n"])),Hr.spacing[2]),ya=Yn(Pi)(Qi||(Qi=r(["\n  font-weight: ",";\n"])),Hr.typography.fontWeight.medium),va=Yn(Oi)($i||($i=r(["\n  font-weight: ",";\n"])),Hr.typography.fontWeight.bold),wa=Yn(Ri)(Zi||(Zi=r(["\n  height: 12px;\n  background: ",";\n  margin-bottom: ",";\n  position: relative;\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n    animation: shimmer 2s infinite;\n  }\n"])),Hr.colors.neutral[200],Hr.spacing[3]),ka=Yn(_i)(Xi||(Xi=r(["\n  background: ",";\n  position: relative;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\n    animation: shimmer 3s infinite;\n  }\n"])),e=>e.isCompleted?Hr.colors.gradients.success:Hr.colors.gradients.primary),xa=Yn(zi)(Ji||(Ji=r(["\n  width: 100%;\n  height: ",";\n  font-size: ",";\n  font-weight: ",";\n\n  ","\n\n  @media (max-width: ",") {\n    height: ",";\n    font-size: ",";\n  }\n"])),Hr.components.button.height.lg,Hr.typography.fontSize.base,Hr.typography.fontWeight.semibold,e=>e.disabled?"\n        background: ".concat(Hr.colors.neutral[200],";\n        color: ").concat(Hr.colors.neutral[400],";\n        cursor: not-allowed;\n\n        &:hover {\n          transform: none;\n          box-shadow: ").concat(Hr.shadows.base,";\n        }\n      "):e.isCompleted?"\n        variant: success;\n      ":"\n      variant: primary;\n    ",Hr.breakpoints.sm,Hr.components.button.height.xl,Hr.typography.fontSize.lg),Sa=(Gn(ea||(ea=r(["\n  0% { transform: translateX(-100%); }\n  100% { transform: translateX(100%); }\n"]))),e=>{let{currentLevel:n,userProgress:t={},onLevelSelect:r,className:i}=e;Fr();const a=e=>{if(1===e)return!1;const n=t[e-1]||{completed:0,total:20};return n.completed<n.total},o=e=>{const n=t[e]||{completed:0,total:20};return n.completed>=n.total},s=e=>o(e)?(0,Yt.jsx)(Nr,{size:20}):a(e)?(0,Yt.jsx)(Lr,{size:20}):e===n?(0,Yt.jsx)(mt,{size:20}):(0,Yt.jsx)(Mr,{size:20}),l=e=>a(e)?"Locked":o(e)?"Review Level":e===n?"Continue":"Start Level",c=e=>a(e)?(0,Yt.jsx)(Lr,{size:20}):o(e)?(0,Yt.jsx)(Ir,{size:20}):(0,Yt.jsx)(ct,{size:20});return(0,Yt.jsxs)(ia,{className:i,children:[(0,Yt.jsxs)(aa,{children:[(0,Yt.jsx)(oa,{level:1,gradient:"primary",children:"ASL Learning Journey"}),(0,Yt.jsx)(sa,{size:"xl",muted:!0,children:"Master American Sign Language through interactive flash cards. Complete each level to unlock the next challenge!"}),(0,Yt.jsxs)(Oi,{variant:"primary",style:{fontSize:Hr.typography.fontSize.base,padding:"".concat(Hr.spacing[2]," ").concat(Hr.spacing[4])},children:[Fr()," Levels \u2022 260 Signs Total"]})]}),(0,Yt.jsx)(la,{children:Object.entries(Dr).map((e,i)=>{var d;let[u,f]=e;const p=parseInt(u),g=a(p),m=o(p),h=p===n,b=(e=>{const n=t[e]||{completed:0,total:20};return n.completed/n.total*100})(p);return(0,Yt.jsxs)(ca,{index:i,isLocked:g,isCompleted:m,isActive:h,hover:!g,size:"lg",onClick:()=>!g&&r(p),children:[(0,Yt.jsxs)(da,{children:[(0,Yt.jsx)(ua,{isCompleted:m,isLocked:g,children:p}),(0,Yt.jsx)(fa,{isCompleted:m,isLocked:g,children:s(p)})]}),(0,Yt.jsx)(ga,{children:f.theme}),(0,Yt.jsx)(pa,{level:3,children:f.name}),(0,Yt.jsx)(ma,{size:"base",muted:!0,children:f.description}),(0,Yt.jsxs)(ha,{children:[(0,Yt.jsxs)(ba,{children:[(0,Yt.jsxs)(ya,{size:"sm",weight:"medium",children:[(null===(d=t[p])||void 0===d?void 0:d.completed)||0," / 20 signs"]}),(0,Yt.jsxs)(va,{variant:m?"success":"primary",children:[Math.round(b),"%"]})]}),(0,Yt.jsx)(wa,{children:(0,Yt.jsx)(ka,{style:{width:"".concat(b,"%")},isCompleted:m})})]}),(0,Yt.jsxs)(xa,{variant:m?"success":"primary",size:"lg",disabled:g,isCompleted:m,onClick:e=>{e.stopPropagation(),g||r(p)},children:[c(p),l(p)]})]},p)})})]})}),Ea=ot("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),Ca=ot("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),za=ot("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]),ja=ot("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]);var Pa,Ra,_a,Oa,Ta,Aa,Na,La,Ma,Ia,Da,Fa,Ha,Ua,Wa,Ba,Va,qa,Ya,Ga,Ka,Qa;const $a=Gn(Pa||(Pa=r(["\n  from {\n    transform: perspective(600px) rotateY(-90deg);\n    opacity: 0;\n  }\n  to {\n    transform: perspective(600px) rotateY(0deg);\n    opacity: 1;\n  }\n"]))),Za=Gn(Ra||(Ra=r(["\n  from {\n    transform: translateX(100%) scale(0.9);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0) scale(1);\n    opacity: 1;\n  }\n"]))),Xa=Gn(_a||(_a=r(["\n  from {\n    transform: translateX(-100%) scale(0.9);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0) scale(1);\n    opacity: 1;\n  }\n"]))),Ja=Gn(Oa||(Oa=r(["\n  0% {\n    transform: scale(1);\n    box-shadow: ",";\n  }\n  50% {\n    transform: scale(1.02);\n    box-shadow: ",";\n  }\n  100% {\n    transform: scale(1);\n    box-shadow: ",";\n  }\n"])),Hr.shadows.lg,Hr.shadows.glowSuccess,Hr.shadows.lg),eo=Gn(Ta||(Ta=r(["\n  0%, 100% { transform: translateX(0); }\n  25% { transform: translateX(-8px); }\n  75% { transform: translateX(8px); }\n"]))),no=Gn(Aa||(Aa=r(["\n  0%, 100% {\n    transform: scale(0) rotate(0deg);\n    opacity: 0;\n  }\n  50% {\n    transform: scale(1) rotate(180deg);\n    opacity: 1;\n  }\n"]))),to=Yn.div(Na||(Na=r(["\n  position: relative;\n  width: 100%;\n  max-width: 480px;\n  margin: 0 auto;\n  perspective: 1200px;\n\n  @media (max-width: ",") {\n    max-width: 420px;\n  }\n\n  @media (max-width: ",") {\n    max-width: 380px;\n  }\n\n  @media (max-width: ",") {\n    max-width: 100%;\n    padding: 0 ",";\n    margin: 0;\n  }\n"])),Hr.breakpoints.lg,Hr.breakpoints.md,Hr.breakpoints.sm,Hr.spacing[2]),ro=Yn(Ci)(La||(La=r(["\n  background: ",";\n  border-radius: ",";\n  box-shadow: ",";\n  padding: ",";\n  text-align: center;\n  position: relative;\n  overflow: hidden;\n  transition: all ",";\n  border: 1px solid ",";\n  \n  @media (max-width: ",") {\n    padding: ",";\n    border-radius: ",";\n  }\n\n  animation: ",";\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 6px;\n    background: ",";\n    border-radius: "," "," 0 0;\n  }\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: -50%;\n    left: -50%;\n    width: 200%;\n    height: 200%;\n    background: ",";\n    opacity: ",";\n    transition: opacity ",";\n    pointer-events: none;\n    z-index: 0;\n  }\n\n  > * {\n    position: relative;\n    z-index: 1;\n  }\n\n  @media (max-width: ",") {\n    padding: ",";\n    border-radius: ",";\n  }\n\n  @media (max-width: ",") {\n    padding: ",";\n    border-radius: ",";\n  }\n\n  @media (max-width: ",") {\n    padding: ",";\n    border-radius: ",";\n    box-shadow: ",";\n  }\n"])),Hr.colors.background,Hr.borderRadius["2xl"],Hr.shadows.xl,Hr.spacing[6],Hr.transitions.normal,Hr.colors.neutral[100],Hr.breakpoints.sm,Hr.spacing[5],Hr.borderRadius.xl,e=>e.isCorrect?Bn(Ma||(Ma=r([""," 0.8s ease"])),Ja):e.isIncorrect?Bn(Ia||(Ia=r([""," 0.6s ease"])),eo):"right"===e.slideDirection?Bn(Da||(Da=r([""," 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)"])),Za):"left"===e.slideDirection?Bn(Fa||(Fa=r([""," 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)"])),Xa):Bn(Ha||(Ha=r([""," 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)"])),$a),e=>e.isCorrect?Hr.colors.gradients.success:e.isIncorrect?Hr.colors.gradients.error:Hr.colors.gradients.primary,Hr.borderRadius["3xl"],Hr.borderRadius["3xl"],e=>e.isCorrect?"radial-gradient(circle, ".concat(Hr.colors.success[100]," 0%, transparent 70%)"):e.isIncorrect?"radial-gradient(circle, ".concat(Hr.colors.error[100]," 0%, transparent 70%)"):"transparent",e=>e.isCorrect||e.isIncorrect?.3:0,Hr.transitions.normal,Hr.breakpoints.lg,Hr.spacing[6],Hr.borderRadius["2xl"],Hr.breakpoints.md,Hr.spacing[5],Hr.borderRadius.xl,Hr.breakpoints.sm,Hr.spacing[5],Hr.borderRadius.xl,Hr.shadows.lg),io=Yn.div(Ua||(Ua=r(["\n  width: 240px;\n  height: 240px;\n  margin: 0 auto ",";\n  border-radius: ",";\n  overflow: hidden;\n  background: ",";\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  box-shadow: ",";\n  border: 2px solid ",";\n  transition: all ",";\n\n  &:hover {\n    transform: scale(1.02);\n    box-shadow: ",";\n  }\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: ",";\n    transition: transform ",";\n  }\n\n  .fallback {\n    font-size: 5rem;\n    color: ",";\n    filter: grayscale(0.3);\n  }\n\n  @media (max-width: ",") {\n    width: 220px;\n    height: 220px;\n    margin-bottom: ",";\n  }\n\n  @media (max-width: ",") {\n    width: 200px;\n    height: 200px;\n    margin-bottom: ",";\n  }\n\n  @media (max-width: ",") {\n    width: 160px;\n    height: 160px;\n    margin-bottom: ",";\n    border-radius: ",";\n  }\n"])),Hr.spacing[6],Hr.borderRadius["2xl"],Hr.colors.gradients.surface,Hr.shadows.md,Hr.colors.neutral[100],Hr.transitions.normal,Hr.shadows.lg,Hr.borderRadius.xl,Hr.transitions.normal,Hr.colors.text.tertiary,Hr.breakpoints.lg,Hr.spacing[6],Hr.breakpoints.md,Hr.spacing[5],Hr.breakpoints.sm,Hr.spacing[4],Hr.borderRadius.lg),ao=Yn(ji)(Wa||(Wa=r(["\n  margin-bottom: ",";\n  background: ",";\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n"])),Hr.spacing[2],Hr.colors.gradients.primary),oo=Yn(Pi)(Ba||(Ba=r(["\n  margin-bottom: ",";\n  max-width: 300px;\n  margin-left: auto;\n  margin-right: auto;\n\n  @media (max-width: ",") {\n    margin-bottom: ",";\n  }\n"])),Hr.spacing[6],Hr.breakpoints.sm,Hr.spacing[4]),so=Yn.div(Va||(Va=r(["\n  position: absolute;\n  top: ",";\n  right: ",";\n  width: 56px;\n  height: 56px;\n  border-radius: ",";\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: ",";\n  color: ",";\n  transition: all ",";\n  box-shadow: ",";\n  z-index: 10;\n\n  ","\n\n  ","\n\n  @media (max-width: ",") {\n    width: 48px;\n    height: 48px;\n    top: ",";\n    right: ",";\n  }\n"])),Hr.spacing[4],Hr.spacing[4],Hr.borderRadius.full,e=>e.isCorrect?Hr.colors.gradients.success:e.isIncorrect?Hr.colors.gradients.error:e.isDetecting?Hr.colors.gradients.warning:"transparent",Hr.colors.text.inverse,Hr.transitions.normal,e=>e.isCorrect?Hr.shadows.glowSuccess:e.isIncorrect?Hr.shadows.glowError:Hr.shadows.md,e=>e.isDetecting&&Bn(qa||(qa=r(["\n    animation: "," 1.5s ease infinite;\n  "])),Ja),e=>e.isCorrect&&Bn(Ya||(Ya=r(["\n    &::before {\n      content: '';\n      position: absolute;\n      top: -10px;\n      right: -10px;\n      width: 20px;\n      height: 20px;\n      background: ",";\n      border-radius: ",";\n      animation: "," 1s ease;\n    }\n  "])),Hr.colors.warning[400],Hr.borderRadius.full,no),Hr.breakpoints.sm,Hr.spacing[3],Hr.spacing[3]),lo=Yn.div(Ga||(Ga=r(["\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  height: 6px;\n  background: ",";\n  transition: width ",";\n  border-radius: 0 0 "," ",";\n  box-shadow: 0 -2px 10px rgba(34, 197, 94, 0.3);\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\n    animation: shimmer 2s infinite;\n  }\n"])),Hr.colors.gradients.success,Hr.transitions.slow,Hr.borderRadius["3xl"],Hr.borderRadius["3xl"]),co=Yn(Oi)(Ka||(Ka=r(["\n  position: absolute;\n  top: ",";\n  left: ",";\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(10px);\n  color: ",";\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  font-weight: ",";\n  font-size: ",";\n  padding: "," ",";\n  box-shadow: ",";\n\n  @media (max-width: ",") {\n    top: ",";\n    left: ",";\n    font-size: ",";\n    padding: "," ",";\n  }\n"])),Hr.spacing[4],Hr.spacing[4],Hr.colors.primary[700],Hr.typography.fontWeight.semibold,Hr.typography.fontSize.sm,Hr.spacing[2],Hr.spacing[3],Hr.shadows.sm,Hr.breakpoints.sm,Hr.spacing[3],Hr.spacing[3],Hr.typography.fontSize.xs,Hr.spacing[1],Hr.spacing[2]),uo=(Gn(Qa||(Qa=r(["\n  0% { transform: translateX(-100%); }\n  100% { transform: translateX(100%); }\n"]))),n=>{let{sign:t,cardNumber:r,totalCards:i,isCorrect:a,isIncorrect:o,isDetecting:s,slideDirection:l,progress:c=0}=n;const[d,u]=(0,e.useState)(!1),[f,p]=(0,e.useState)(0);(0,e.useEffect)(()=>{p(e=>e+1),u(!1)},[t.key]);return(0,Yt.jsx)(to,{children:(0,Yt.jsxs)(ro,{isCorrect:a,isIncorrect:o,slideDirection:l,children:[(0,Yt.jsxs)(co,{variant:"primary",children:[r," / ",i]}),(0,Yt.jsx)(so,{isCorrect:a,isIncorrect:o,isDetecting:s,children:a?(0,Yt.jsx)(Nr,{size:20}):o?(0,Yt.jsx)(Ea,{size:20}):s?(0,Yt.jsx)(lt,{size:20}):null}),(0,Yt.jsx)(io,{children:d?(0,Yt.jsx)("div",{className:"fallback",children:(0,Yt.jsx)(ja,{size:60})}):(0,Yt.jsx)("img",{src:t.gif,alt:t.name,onError:()=>u(!0)})}),(0,Yt.jsx)(ao,{level:2,gradient:"primary",children:t.name}),(0,Yt.jsx)(oo,{size:"lg",muted:!0,children:t.description}),(0,Yt.jsx)(lo,{style:{width:"".concat(c,"%")}})]},f)})});var fo,po,go,mo,ho,bo,yo,vo,wo,ko,xo,So,Eo,Co,zo,jo,Po,Ro,_o,Oo,To,Ao,No,Lo,Mo,Io,Do,Fo,Ho,Uo,Wo;const Bo=Gn(fo||(fo=r(["\n  from { opacity: 0; transform: translateY(20px); }\n  to { opacity: 1; transform: translateY(0); }\n"]))),Vo=Gn(po||(po=r(["\n  0%, 100% { transform: scale(1) rotate(0deg); }\n  25% { transform: scale(1.1) rotate(-5deg); }\n  75% { transform: scale(1.1) rotate(5deg); }\n"]))),qo=Yn.div(go||(go=r(["\n  min-height: 100vh;\n  background: var(--bg-primary);\n  padding: ",";\n  position: relative;\n  overflow-x: hidden;\n\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n\n  > * {\n    position: relative;\n    z-index: 1;\n  }\n\n  @media (max-width: ",") {\n    padding: ",";\n  }\n\n  @media (max-width: ",") {\n    padding: ",";\n    min-height: 100dvh; /* Use dynamic viewport height for mobile */\n  }\n"])),Hr.spacing[4],Hr.breakpoints.md,Hr.spacing[3],Hr.breakpoints.sm,Hr.spacing[2]),Yo=(Yn.div(mo||(mo=r(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ",";\n  \n  @media (max-width: ",") {\n    flex-direction: column;\n    gap: ",";\n  }\n"])),Hr.spacing[8],Hr.breakpoints.md,Hr.spacing[4]),Yn(Ci)(ho||(ho=r(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ",";\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: ",";\n\n  @media (max-width: ",") {\n    flex-direction: column;\n    gap: ",";\n    text-align: center;\n    margin-bottom: ",";\n  }\n\n  @media (max-width: ",") {\n    margin-bottom: ",";\n    padding: ",";\n  }\n\n  @media (max-width: ",") {\n    margin-bottom: ",";\n  }\n"])),Hr.spacing[6],Hr.shadows.xl,Hr.breakpoints.md,Hr.spacing[4],Hr.spacing[5],Hr.breakpoints.sm,Hr.spacing[4],Hr.spacing[3],Hr.breakpoints.sm,Hr.spacing[4])),Go=Yn(zi)(bo||(bo=r(["\n  background: rgba(59, 130, 246, 0.15);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(59, 130, 246, 0.3);\n  color: #3b82f6;\n  font-weight: 600;\n  transition: all 0.3s ease;\n\n  &:hover:not(:disabled) {\n    background: rgba(59, 130, 246, 0.25);\n    border-color: rgba(59, 130, 246, 0.5);\n    color: #2563eb;\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);\n  }\n\n  &:active:not(:disabled) {\n    transform: translateY(0);\n    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);\n  }\n\n  @media (max-width: ",") {\n    width: 100%;\n  }\n\n  @media (max-width: ",") {\n    padding: 0.75rem 1rem;\n    font-size: 0.875rem;\n  }\n"])),Hr.breakpoints.md,Hr.breakpoints.sm),Ko=Yn.div(yo||(yo=r(["\n  text-align: center;\n  color: ",";\n\n  @media (max-width: ",") {\n    order: -1;\n  }\n"])),Hr.colors.text.primary,Hr.breakpoints.md),Qo=Yn(ji)(vo||(vo=r(["\n  margin-bottom: ",";\n  background: ",";\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n"])),Hr.spacing[1],Hr.colors.gradients.primary),$o=Yn(Pi)(wo||(wo=r(["\n  opacity: 0.8;\n  font-weight: ",";\n"])),Hr.typography.fontWeight.medium),Zo=Yn(Oi)(ko||(ko=r(["\n  background: ",";\n  color: ",";\n  border: 1px solid ",";\n  backdrop-filter: blur(10px);\n  cursor: ",";\n\n  &:hover {\n    background: ",";\n  }\n\n  @media (max-width: ",") {\n    width: 100%;\n    justify-content: center;\n  }\n\n  @media (max-width: ",") {\n    padding: 0.5rem 0.75rem;\n    font-size: 0.75rem;\n    \n    .connection-text {\n      display: none; /* Hide text on mobile, show only icon */\n    }\n  }\n"])),e=>e.isConnected?"rgba(34, 197, 94, 0.15)":"rgba(239, 68, 68, 0.15)",e=>e.isConnected?Hr.colors.success[700]:Hr.colors.error[700],e=>e.isConnected?Hr.colors.success[200]:Hr.colors.error[200],e=>e.isConnected?"default":"pointer",e=>e.isConnected?"rgba(34, 197, 94, 0.2)":"rgba(239, 68, 68, 0.2)",Hr.breakpoints.md,Hr.breakpoints.sm),Xo=Yn.div(xo||(xo=r(["\n  display: grid;\n  grid-template-columns: 1fr 500px;\n  gap: ",";\n  max-width: 1400px;\n  margin: 0 auto;\n  align-items: start;\n\n  @media (max-width: ",") {\n    grid-template-columns: 1fr 450px;\n    gap: ",";\n    max-width: 1200px;\n  }\n\n  @media (max-width: ",") {\n    grid-template-columns: 1fr;\n    gap: ",";\n    max-width: 800px;\n  }\n\n  @media (max-width: ",") {\n    /* On mobile, show flash card first, then camera */\n    display: flex;\n    flex-direction: column;\n    gap: ",";\n    max-width: 100%;\n  }\n"])),Hr.spacing[6],Hr.breakpoints.xl,Hr.spacing[5],Hr.breakpoints.lg,Hr.spacing[6],Hr.breakpoints.sm,Hr.spacing[4]),Jo=Yn.div(So||(So=r(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  animation: "," 0.6s ease;\n  position: sticky;\n  top: ",";\n  \n  @media (max-width: ",") {\n    position: static;\n  }\n"])),Bo,Hr.spacing[8],Hr.breakpoints.lg),es=Yn.div(Eo||(Eo=r(["\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20px;\n  padding: 1rem;\n  margin-bottom: 1.5rem;\n  text-align: center;\n  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  width: 100%;\n  max-width: 480px;\n  margin-left: auto;\n  margin-right: auto;\n  \n  @media (max-width: ",") {\n    max-width: 420px;\n    padding: 0.875rem;\n    margin-bottom: 1.5rem;\n    border-radius: 18px;\n  }\n  \n  @media (max-width: ",") {\n    max-width: 380px;\n    padding: 0.75rem;\n    margin-bottom: 1.5rem;\n    border-radius: 16px;\n  }\n  \n  @media (max-width: ",") {\n    max-width: 100%;\n    padding: 0.75rem;\n    margin-bottom: 1.5rem;\n    border-radius: 16px;\n  }\n"])),Hr.breakpoints.lg,Hr.breakpoints.md,Hr.breakpoints.sm),ns=Yn.h3(Co||(Co=r(["\n  font-size: 1.125rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin-bottom: 0.75rem;\n"]))),ts=Yn.div(zo||(zo=r(["\n  width: 100%;\n  height: 14px;\n  background: #e2e8f0;\n  border-radius: 8px;\n  overflow: hidden;\n  margin-bottom: 0.75rem;\n  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);\n  \n  @media (max-width: ",") {\n    height: 12px;\n    margin-bottom: 0.75rem;\n  }\n  \n  @media (max-width: ",") {\n    height: 10px;\n    margin-bottom: 0.75rem;\n  }\n"])),Hr.breakpoints.lg,Hr.breakpoints.sm),rs=Yn.div(jo||(jo=r(["\n  height: 100%;\n  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #10b981);\n  border-radius: 8px;\n  transition: width 0.5s ease;\n  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);\n  \n  @media (max-width: ",") {\n    border-radius: 7px;\n  }\n  \n  @media (max-width: ",") {\n    border-radius: 6px;\n  }\n"])),Hr.breakpoints.lg,Hr.breakpoints.sm),is=Yn.div(Po||(Po=r(["\n  font-size: 0.875rem;\n  color: #64748b;\n  font-weight: 600;\n"]))),as=Yn.div(Ro||(Ro=r(["\n  display: flex;\n  gap: 1rem;\n  margin-top: 2rem;\n  justify-content: center;\n  \n  @media (max-width: ",") {\n    gap: 0.75rem;\n    margin-top: 1.5rem;\n  }\n  \n  @media (max-width: 768px) {\n    gap: 0.75rem;\n    margin-top: 1.5rem;\n    flex-wrap: wrap;\n  }\n\n  @media (max-width: ",") {\n    gap: 0.5rem;\n    margin-top: 1rem;\n    padding: 0 ",";\n  }\n"])),Hr.breakpoints.lg,Hr.breakpoints.sm,Hr.spacing[2]),os=Yn.button(_o||(_o=r(["\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 1rem 2rem;\n  border: none;\n  border-radius: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  min-height: 48px; /* Minimum touch target size */\n  touch-action: manipulation; /* Optimize for touch */\n  \n  @media (max-width: ",") {\n    padding: 1rem 2rem;\n    font-size: 1rem;\n    min-height: 48px;\n  }\n  \n  @media (max-width: ",") {\n    padding: 0.75rem 1.5rem;\n    font-size: 0.875rem;\n    min-height: 44px;\n  }\n  \n  ","\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none !important;\n  }\n  \n  @media (max-width: 768px) {\n    padding: 0.75rem 1.5rem;\n    font-size: 0.875rem;\n  }\n"])),Hr.breakpoints.lg,Hr.breakpoints.sm,e=>"primary"===e.variant?"\n        background: linear-gradient(135deg, #3b82f6, #8b5cf6);\n        color: white;\n        &:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);\n        }\n      ":"success"===e.variant?"\n        background: linear-gradient(135deg, #10b981, #34d399);\n        color: white;\n        &:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);\n        }\n      ":"\n      background: rgba(255, 255, 255, 0.9);\n      color: #64748b;\n      &:hover {\n        background: white;\n        transform: translateY(-2px);\n      }\n    "),ss=Yn(Ci)(Oo||(Oo=r(["\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\n  border-radius: 20px;\n  padding: 1.5rem;\n\n  @media (max-width: ",") {\n    order: 1; /* Show after flash card on mobile */\n    padding: 1.25rem;\n    border-radius: 18px;\n  }\n\n  @media (max-width: ",") {\n    order: 2; /* Ensure camera comes after flash card on mobile */\n    margin: 0 -","; /* Full width on mobile */\n    border-radius: 16px;\n    padding: 1rem;\n  }\n"])),Hr.breakpoints.lg,Hr.breakpoints.sm,Hr.spacing[2]),ls=Yn.h3(To||(To=r(["\n  font-size: 1.25rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin-bottom: 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  \n  @media (max-width: ",") {\n    font-size: 1.125rem;\n    margin-bottom: 0.75rem;\n    gap: 0.5rem;\n  }\n"])),Hr.breakpoints.lg),cs=Yn.div(Ao||(Ao=r(["\n  position: relative;\n  border-radius: 18px;\n  overflow: hidden;\n  background: #000;\n  margin-bottom: 1rem;\n  aspect-ratio: 4/3; /* Maintain aspect ratio */\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n  border: 2px solid rgba(255, 255, 255, 0.1);\n  \n  @media (max-width: ",") {\n    border-radius: 16px;\n    margin-bottom: 0.75rem;\n  }\n  \n  @media (max-width: ",") {\n    border-radius: 12px;\n    margin: 0 -"," 1rem -","; /* Full width on mobile */\n  }\n"])),Hr.breakpoints.lg,Hr.breakpoints.sm,Hr.spacing[2],Hr.spacing[2]),ds=Yn.div(No||(No=r(["\n  position: absolute;\n  top: 1rem;\n  left: 1rem;\n  right: 1rem;\n  background: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: 0.75rem;\n  border-radius: 8px;\n  font-weight: 600;\n  text-align: center;\n  font-size: 0.875rem;\n  \n  @media (max-width: ",") {\n    top: 0.5rem;\n    left: 0.5rem;\n    right: 0.5rem;\n    padding: 0.5rem;\n    font-size: 0.75rem;\n  }\n"])),Hr.breakpoints.sm),us=Yn.div(Lo||(Lo=r(["\n  background: linear-gradient(135deg, #f8fafc, #f1f5f9);\n  border-radius: 14px;\n  padding: 1.25rem;\n  text-align: center;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);\n  \n  @media (max-width: ",") {\n    padding: 1rem;\n    border-radius: 12px;\n  }\n  \n  @media (max-width: ",") {\n    padding: 0.75rem;\n    margin: 0 ",";\n    border-radius: 12px;\n  }\n"])),Hr.breakpoints.lg,Hr.breakpoints.sm,Hr.spacing[2]),fs=Yn.div(Mo||(Mo=r(["\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin-bottom: 0.75rem;\n  \n  @media (max-width: ",") {\n    font-size: 1.125rem;\n    margin-bottom: 0.5rem;\n  }\n"])),Hr.breakpoints.lg),ps=Yn.div(Io||(Io=r(["\n  font-size: 0.875rem;\n  color: #64748b;\n"]))),gs=Yn.div(Do||(Do=r(["\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  animation: "," 0.3s ease;\n"])),Bo),ms=Yn.div(Fo||(Fo=r(["\n  background: white;\n  border-radius: 24px;\n  padding: 3rem;\n  text-align: center;\n  max-width: 500px;\n  margin: 1rem;\n  animation: "," 0.6s ease;\n"])),Vo),hs=Yn.h2(Ho||(Ho=r(["\n  font-size: 2.5rem;\n  font-weight: 800;\n  color: #1e293b;\n  margin-bottom: 1rem;\n"]))),bs=Yn.p(Uo||(Uo=r(["\n  font-size: 1.125rem;\n  color: #64748b;\n  margin-bottom: 2rem;\n  line-height: 1.6;\n"]))),ys=Yn.div(Wo||(Wo=r(["\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n"]))),vs=n=>{let{level:t,onBack:r,userProgress:i={},onProgressUpdate:a}=n;const o=(0,e.useRef)(null),[s,l]=(0,e.useState)(0),[c,d]=(0,e.useState)(new Set),[u,f]=(0,e.useState)({}),[p,g]=(0,e.useState)(null),[m,h]=(0,e.useState)(!1),[b,y]=(0,e.useState)(!1),v=(e=>Dr[e]||null)(t),w=(e=>{var n;return(null===(n=Dr[e])||void 0===n?void 0:n.signs)||[]})(t),k=w[s],{isConnected:x,prediction:S,isAIRecording:E,recordingStatus:C,signMatched:z,targetSign:j,currentKeypoints:P,startRecording:R,stopRecording:_,startFrameCapture:O,retryConnection:T,setLevel:A}=Ar(),N=c.size/w.length*100,L=(0,e.useCallback)(()=>{o.current&&(y(!0),O(o,100))},[O]),M=(0,e.useCallback)(async(e,n,t)=>{try{console.log("\ud83d\udcbe Saving training data for ".concat(e," with confidence ").concat(t));const r=await fetch("http://localhost:8000/save-training-data",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sign_name:e,keypoints:n,confidence:t,timestamp:(new Date).toISOString()})});if(r.ok){const e=await r.json();return console.log("\u2705 Training data saved: ".concat(e.message)),!0}return console.error("\u274c Failed to save training data"),!1}catch(r){return console.error("\u274c Error saving training data:",r),!1}},[]),I=(0,e.useCallback)(()=>{s<w.length-1&&(g("right"),l(e=>e+1),f(e=>Xn(Xn({},e),{},{[s]:null})),setTimeout(()=>g(null),500))},[s,w.length]);(0,e.useEffect)(()=>{var e;if(console.log("\ud83d\udd0d Debug: signMatched=".concat(z,", currentSign=").concat(null===k||void 0===k?void 0:k.name,", prediction=").concat(null===S||void 0===S?void 0:S.sign,", confidence=").concat(null===S||void 0===S?void 0:S.confidence)),z&&k&&(null===S||void 0===S||null===(e=S.sign)||void 0===e?void 0:e.toLowerCase())===k.name.toLowerCase())if(console.log("\u2705 Sign match confirmed: ".concat(k.name," with confidence ").concat(S.confidence)),c.has(s))console.log("\u26a0\ufe0f Card ".concat(s," already completed"));else{console.log("\ud83c\udfaf Correct sign detected: ".concat(k.name," with confidence ").concat(S.confidence));if((async()=>{if(P&&(null===S||void 0===S?void 0:S.confidence)>=.5){await M(k.name,P,S.confidence)&&console.log("\u2705 Training data saved successfully for ".concat(k.name))}})(),!E&&x&&(console.log("\ud83c\udfac Starting automatic recording for ".concat(k.name,"...")),R(k.name,!0),setTimeout(()=>{_(),console.log("\u2705 Automatic recording completed for: ".concat(k.name))},3e3)),f(e=>Xn(Xn({},e),{},{[s]:"correct"})),d(e=>new Set([...e,s])),a){const e=c.size+1;a(t,e,w.length)}setTimeout(()=>{s<w.length-1?I():(h(!0),a&&a(t,w.length,w.length))},2e3)}},[z,k,S,s,w.length,t,a,E,x,R,_,c,M,P]),(0,e.useEffect)(()=>{x&&t&&A(t)},[x,t,A]),(0,e.useEffect)(()=>{x&&o.current&&!b&&L()},[x,L,b]),(0,e.useEffect)(()=>{x&&k&&(console.log("\ud83c\udfaf Setting target sign to: ".concat(k.name)),R(k.name,!1))},[x,k,R]);const D=(0,e.useCallback)(()=>{s>0&&(g("left"),l(e=>e-1),f(e=>Xn(Xn({},e),{},{[s]:null})),setTimeout(()=>g(null),500))},[s]),F=(0,e.useCallback)(()=>{f(e=>Xn(Xn({},e),{},{[s]:null})),d(e=>{const n=new Set(e);return n.delete(s),n})},[s]);if(!v||!k)return(0,Yt.jsx)(xi,{children:(0,Yt.jsxs)("div",{style:{color:"white",textAlign:"center",padding:"2rem"},children:[(0,Yt.jsx)("h2",{children:"Level not found"}),(0,Yt.jsx)("button",{onClick:r,children:"Go Back"})]})});const H=c.has(s),U=u[s];return(0,Yt.jsxs)(qo,{children:[(0,Yt.jsxs)(Yo,{size:"md",children:[(0,Yt.jsxs)(Go,{variant:"ghost",size:"md",onClick:r,children:[(0,Yt.jsx)(Cr,{size:20}),"Back to Levels"]}),(0,Yt.jsxs)(Ko,{children:[(0,Yt.jsxs)(Qo,{level:3,children:["Level ",t,": ",v.name]}),(0,Yt.jsx)($o,{size:"lg",children:v.theme})]}),(0,Yt.jsxs)(Zo,{isConnected:x,onClick:x?void 0:T,children:[x?(0,Yt.jsx)(Rr,{size:18}):(0,Yt.jsx)(_r,{size:18}),(0,Yt.jsx)("span",{className:"connection-text",children:x?"Connected":"Disconnected"}),!x&&(0,Yt.jsx)(Pr,{size:14})]})]}),(0,Yt.jsxs)(Xo,{children:[(0,Yt.jsxs)(Jo,{children:[(0,Yt.jsxs)(es,{children:[(0,Yt.jsx)(ns,{children:"Level Progress"}),(0,Yt.jsx)(ts,{children:(0,Yt.jsx)(rs,{style:{width:"".concat(N,"%")}})}),(0,Yt.jsxs)(is,{children:[c.size," of ",w.length," signs completed (",Math.round(N),"%)"]})]}),(0,Yt.jsx)(uo,{sign:k,cardNumber:s+1,totalCards:w.length,isCorrect:"correct"===U,isIncorrect:"incorrect"===U,isDetecting:x&&!H,slideDirection:p,progress:s/w.length*100}),(0,Yt.jsxs)(as,{children:[(0,Yt.jsxs)(os,{onClick:D,disabled:0===s,children:[(0,Yt.jsx)(Cr,{size:20}),"Previous"]}),H?(0,Yt.jsxs)(os,{variant:"success",onClick:I,disabled:s===w.length-1,children:[(0,Yt.jsx)(Nr,{size:20}),"Next Card"]}):(0,Yt.jsxs)(os,{onClick:F,disabled:!x,children:[(0,Yt.jsx)(Ea,{size:20}),"Retry"]}),(0,Yt.jsxs)(os,{onClick:I,disabled:s===w.length-1,children:["Next",(0,Yt.jsx)(Ca,{size:20})]})]})]}),(0,Yt.jsxs)(ss,{children:[(0,Yt.jsxs)(ls,{children:[(0,Yt.jsx)(ut,{size:24}),"Camera Feed"]}),(0,Yt.jsxs)(cs,{children:[(0,Yt.jsx)(Er(),{ref:o,audio:!1,width:"100%",height:"auto",screenshotFormat:"image/jpeg",videoConstraints:{width:640,height:480,facingMode:"user"}}),C&&(0,Yt.jsx)(ds,{children:C})]}),(0,Yt.jsxs)(us,{children:[(0,Yt.jsx)(fs,{children:null!==S&&void 0!==S&&S.sign?"Detected: ".concat(S.sign):"Show the sign to get started"}),(null===S||void 0===S?void 0:S.confidence)&&(0,Yt.jsxs)(ps,{children:["Confidence: ",Math.round(100*S.confidence),"%"]})]})]})]}),m&&(0,Yt.jsx)(gs,{children:(0,Yt.jsxs)(ms,{children:[(0,Yt.jsx)(Ir,{size:80,style:{color:"#f59e0b",marginBottom:"1rem"}}),(0,Yt.jsx)(hs,{children:"\ud83c\udf89 Level Complete!"}),(0,Yt.jsxs)(bs,{children:["Congratulations! You've successfully completed Level ",t,": ",v.name,". You've mastered all ",w.length," signs in this level!"]}),(0,Yt.jsxs)(ys,{children:[(0,Yt.jsxs)(os,{onClick:()=>{h(!1),r()},children:[(0,Yt.jsx)(za,{size:20}),"Back to Levels"]}),t<5&&(0,Yt.jsxs)(os,{variant:"primary",onClick:()=>{h(!1),r()},children:[(0,Yt.jsx)(mt,{size:20}),"Next Level"]})]})]})})]})};var ws,ks,xs,Ss,Es,Cs,zs,js,Ps,Rs,_s,Os,Ts,As,Ns,Ls,Ms,Is,Ds,Fs,Hs,Us,Ws,Bs,Vs,qs,Ys,Gs,Ks,Qs,$s,Zs,Xs,Js,el,nl,tl,rl;const il=Yn.div(ws||(ws=r(["\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow-x: hidden;\n\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n"]))),al=Yn.nav(ks||(ks=r(["\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: var(--bg-glass);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-neural);\n  padding: var(--space-4) 0;\n  transition: var(--transition-normal);\n"]))),ol=Yn.div(xs||(xs=r(["\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  @media (max-width: 768px) {\n    padding: 0 var(--space-4);\n  }\n"]))),sl=Yn.div(Ss||(Ss=r(["\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n    gap: var(--space-2);\n  }\n"]))),ll=Yn.div(Es||(Es=r(["\n  width: 40px;\n  height: 40px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    width: 36px;\n    height: 36px;\n  }\n"]))),cl=Yn.button(Cs||(Cs=r(["\n  background: rgba(59, 130, 246, 0.15);\n  color: #3b82f6;\n  border: 1px solid rgba(59, 130, 246, 0.3);\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-xl);\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  backdrop-filter: blur(10px);\n\n  &:hover {\n    background: rgba(59, 130, 246, 0.25);\n    color: #2563eb;\n    border-color: rgba(59, 130, 246, 0.5);\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);\n  }\n\n  &:active {\n    transform: translateY(0);\n    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-2) var(--space-4);\n    font-size: 0.85rem;\n  }\n"]))),dl=Yn.h1(zs||(zs=r(["\n  font-family: var(--font-primary);\n  font-size: 2.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  text-align: center;\n  margin-bottom: var(--space-4);\n  letter-spacing: -0.02em;\n\n  @media (max-width: 768px) {\n    font-size: 2rem;\n  }\n"]))),ul=Yn.p(js||(js=r(["\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  text-align: center;\n  margin-bottom: var(--space-16);\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n\n  @media (max-width: 768px) {\n    margin-bottom: var(--space-12);\n    font-size: 1rem;\n  }\n"]))),fl=Yn.div(Ps||(Ps=r(["\n  display: inline-flex;\n  align-items: center;\n  gap: var(--space-2);\n  background: var(--bg-glass);\n  border: 1px solid var(--border-neural);\n  border-radius: var(--radius-full);\n  padding: var(--space-2) var(--space-4);\n  margin-bottom: var(--space-8);\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: var(--text-accent);\n  backdrop-filter: blur(10px);\n  box-shadow: var(--shadow-glow);\n\n  @media (max-width: 768px) {\n    font-size: 0.8rem;\n    padding: var(--space-2) var(--space-3);\n  }\n"]))),pl=Yn.main(Rs||(Rs=r(["\n  padding: var(--space-20) var(--space-4) var(--space-16);\n  max-width: 1200px;\n  margin: 0 auto;\n\n  @media (max-width: 768px) {\n    padding: var(--space-12) var(--space-3) var(--space-8);\n    max-width: 100%;\n  }\n"]))),gl=Yn.div(_s||(_s=r(['\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--space-8);\n  max-width: 1200px;\n  margin: 0 auto var(--space-12);\n\n  @media (max-width: 1024px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-4);\n  }\n\n  @media (max-width: 768px) {\n    gap: var(--space-3);\n    margin: 0 auto var(--space-6);\n    grid-template-areas: \n      "sign"\n      "camera";\n  }\n']))),ml=Yn.div(Os||(Os=r(["\n  background: var(--bg-glass);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-10);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-lg);\n  transition: var(--transition-normal);\n  position: relative;\n  overflow: hidden;\n\n  @media (max-width: 768px) {\n    padding: var(--space-6);\n    border-radius: var(--radius-xl);\n    grid-area: camera;\n  }\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 4px;\n    background: var(--bg-neural);\n    transform: scaleX(0);\n    transition: var(--transition-normal);\n  }\n\n  &:hover {\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n    border-color: var(--primary-300);\n\n    &::before {\n      transform: scaleX(1);\n    }\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-8);\n  }\n"]))),hl=Yn.h2(Ts||(Ts=r(["\n  font-family: var(--font-primary);\n  font-size: 1.25rem;\n  margin-bottom: var(--space-6);\n  color: var(--text-primary);\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    margin-bottom: var(--space-4);\n  }\n"]))),bl=Yn.div(As||(As=r(["\n  width: 36px;\n  height: 36px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    width: 32px;\n    height: 32px;\n  }\n"]))),yl=Yn.div(Ns||(Ns=r(["\n  position: relative;\n  border-radius: var(--radius-2xl);\n  overflow: hidden;\n  background: var(--neural-100);\n  aspect-ratio: 4/3;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 3px solid var(--border-neural);\n  margin-bottom: var(--space-6);\n  box-shadow: var(--shadow-lg);\n\n  @media (max-width: 768px) {\n    aspect-ratio: 3/4;\n    margin-bottom: var(--space-4);\n    border-radius: var(--radius-xl);\n    border-width: 2px;\n  }\n"]))),vl=Yn(Er())(Ls||(Ls=r(["\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n"]))),wl=Yn.div(Ms||(Ms=r(["\n  position: absolute;\n  top: var(--space-4);\n  right: var(--space-4);\n  background: ",";\n  color: white;\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-full);\n  font-size: 0.9rem;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  box-shadow: var(--shadow-lg);\n  animation: ",";\n\n  @keyframes pulse {\n    0%, 100% { opacity: 1; transform: scale(1); }\n    50% { opacity: 0.8; transform: scale(1.05); }\n  }\n"])),e=>e.isRecording?"var(--error-500)":"var(--neural-600)",e=>e.isRecording?"pulse 1.5s infinite":"none"),kl=Yn.div(Is||(Is=r(["\n  background: var(--bg-primary);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-8);\n  border: 1px solid var(--border-light);\n  box-shadow: var(--shadow-lg);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  transition: all 0.3s ease;\n\n  &:hover {\n    box-shadow: var(--shadow-xl);\n    border-color: var(--primary-200);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-6);\n    grid-area: sign;\n  }\n"]))),xl=Yn.select(Ds||(Ds=r(["\n  width: 100%;\n  padding: var(--space-3) var(--space-4);\n  border: 2px solid var(--border-light);\n  border-radius: var(--radius-lg);\n  background: var(--bg-primary);\n  color: var(--text-primary);\n  font-size: 1rem;\n  font-weight: 500;\n  margin-bottom: var(--space-4);\n  cursor: pointer;\n  transition: var(--transition-normal);\n\n  &:focus {\n    outline: none;\n    border-color: var(--primary-500);\n    box-shadow: 0 0 0 3px var(--primary-100);\n  }\n\n  &:hover {\n    border-color: var(--primary-300);\n  }\n\n  option {\n    padding: var(--space-2);\n    background: var(--bg-primary);\n    color: var(--text-primary);\n  }\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    padding: var(--space-4);\n    margin-bottom: var(--space-3);\n  }\n"]))),Sl=Yn.div(Fs||(Fs=r(["\n  width: 300px;\n  height: 300px;\n  background: var(--primary-50);\n  border-radius: var(--radius-2xl);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: var(--space-6);\n  border: 2px solid var(--primary-200);\n  transition: all 0.3s ease;\n  overflow: hidden;\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: var(--radius-xl);\n  }\n\n  &:hover {\n    transform: scale(1.02);\n    border-color: var(--primary-300);\n  }\n\n  @media (max-width: 768px) {\n    width: 250px;\n    height: 250px;\n  }\n"]))),El=Yn.h3(Hs||(Hs=r(["\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  margin-bottom: var(--space-3);\n  color: var(--text-primary);\n  font-weight: 700;\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n  }\n"]))),Cl=Yn.p(Us||(Us=r(["\n  text-align: center;\n  line-height: 1.6;\n  color: var(--text-secondary);\n  font-size: 0.9rem;\n  font-weight: 400;\n  max-width: 280px;\n"]))),zl=Yn.div(Ws||(Ws=r(["\n  display: flex;\n  justify-content: center;\n  gap: var(--space-4);\n  margin-bottom: var(--space-8);\n  padding: var(--space-4);\n  background: var(--bg-glass);\n  border-radius: var(--radius-xl);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(10px);\n  box-shadow: var(--shadow-lg);\n\n  @media (max-width: 768px) {\n    flex-direction: column;\n    align-items: center;\n    gap: var(--space-3);\n    margin-bottom: var(--space-6);\n    padding: var(--space-3);\n  }\n"]))),jl=Yn.div(Bs||(Bs=r(["\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  padding: var(--space-2) var(--space-3);\n  border-radius: var(--radius-lg);\n  font-size: 0.875rem;\n  font-weight: 500;\n  background: ",";\n  color: ",";\n  border: 1px solid ",";\n  white-space: nowrap;\n\n  @media (max-width: 768px) {\n    font-size: 0.8rem;\n    padding: var(--space-1) var(--space-2);\n  }\n"])),e=>e.isRecording?"var(--error-50)":"var(--success-50)",e=>e.isRecording?"var(--error-700)":"var(--success-700)",e=>e.isRecording?"var(--error-200)":"var(--success-200)"),Pl=Yn.button(Vs||(Vs=r(["\n  background: ",";\n  border: ",";\n  color: ",";\n  padding: ",";\n  border-radius: var(--radius-lg);\n  cursor: pointer;\n  font-size: ",";\n  font-weight: 600;\n  transition: all 0.2s ease;\n  min-width: ",";\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--space-2);\n\n  @media (max-width: 768px) {\n    padding: ",";\n    font-size: ",";\n    min-width: ",";\n    border-radius: var(--radius-xl);\n  }\n  box-shadow: ",";\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: ",";\n    background: ",";\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none;\n  }\n\n  @media (max-width: 768px) {\n    width: 100%;\n    max-width: ",";\n  }\n"])),e=>"primary"===e.variant?"var(--primary-600)":"retry"===e.variant?"var(--warning-500)":"var(--bg-primary)",e=>"primary"===e.variant||"retry"===e.variant?"none":"1px solid var(--border-medium)",e=>"primary"===e.variant||"retry"===e.variant?"white":"var(--text-primary)",e=>e.compact?"var(--space-2) var(--space-4)":"var(--space-3) var(--space-6)",e=>e.compact?"0.8rem":"0.9rem",e=>e.compact?"120px":"160px",e=>e.compact?"var(--space-3) var(--space-5)":"var(--space-4) var(--space-8)",e=>e.compact?"0.9rem":"1rem",e=>e.compact?"140px":"180px",e=>"primary"===e.variant||"retry"===e.variant?"var(--shadow-lg)":"var(--shadow-sm)",e=>"primary"===e.variant||"retry"===e.variant?"var(--shadow-xl)":"var(--shadow-md)",e=>"primary"===e.variant?"var(--primary-700)":"retry"===e.variant?"var(--warning-600)":"var(--gray-50)",e=>e.compact?"200px":"280px"),Rl=Yn.div(qs||(qs=r(["\n  text-align: center;\n  margin-top: var(--space-6);\n  padding: var(--space-4) var(--space-6);\n  border-radius: var(--radius-lg);\n  background: ",";\n  color: white;\n  font-weight: 500;\n  font-size: 0.875rem;\n  max-width: 400px;\n  margin-left: auto;\n  margin-right: auto;\n"])),e=>"success"===e.type?"var(--success-500)":"error"===e.type?"var(--error-500)":"var(--primary-600)"),_l=Yn.div(Ys||(Ys=r(["\n  margin-top: var(--space-16);\n  background: var(--bg-secondary);\n  padding: var(--space-12) var(--space-4);\n  border-radius: var(--radius-2xl);\n  max-width: 1200px;\n  margin-left: auto;\n  margin-right: auto;\n"]))),Ol=Yn.h3(Gs||(Gs=r(["\n  font-family: var(--font-primary);\n  color: var(--text-primary);\n  margin-bottom: var(--space-8);\n  font-size: 1.5rem;\n  font-weight: 600;\n  text-align: center;\n"]))),Tl=Yn.div(Ks||(Ks=r(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: var(--space-6);\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-4);\n  }\n"]))),Al=Yn.div(Qs||(Qs=r(["\n  background: var(--bg-primary);\n  padding: var(--space-6);\n  border-radius: var(--radius-xl);\n  border: 1px solid var(--border-light);\n  box-shadow: var(--shadow-sm);\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: translateY(-2px);\n    border-color: var(--primary-200);\n    box-shadow: var(--shadow-lg);\n  }\n"]))),Nl=Yn.p($s||($s=r(["\n  margin: 0 0 var(--space-2) 0;\n  color: var(--text-primary);\n  font-weight: 600;\n  font-size: 1rem;\n  font-family: var(--font-primary);\n"]))),Ll=Yn.p(Zs||(Zs=r(["\n  margin: 0 0 var(--space-4) 0;\n  font-size: 0.8rem;\n  color: var(--text-tertiary);\n"]))),Ml=Yn.button(Xs||(Xs=r(["\n  background: var(--primary-600);\n  border: none;\n  border-radius: var(--radius-lg);\n  padding: var(--space-2) var(--space-4);\n  color: white;\n  cursor: pointer;\n  font-size: 0.8rem;\n  font-weight: 500;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  margin: 0 auto;\n\n  &:hover {\n    background: var(--primary-700);\n    transform: translateY(-1px);\n  }\n"]))),Il=Yn.div(Js||(Js=r(["\n  background: var(--bg-glass);\n  border: 2px solid ",";\n  border-radius: var(--radius-xl);\n  padding: var(--space-4);\n  margin-bottom: var(--space-4);\n  text-align: center;\n  transition: var(--transition-normal);\n  backdrop-filter: blur(10px);\n  opacity: ",";\n  min-height: 80px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n\n  ","\n\n  ","\n\n  @keyframes pulse {\n    0%, 100% { transform: scale(1); }\n    50% { transform: scale(1.02); }\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-3);\n    margin-bottom: var(--space-3);\n    min-height: 70px;\n  }\n"])),e=>e.matched?"var(--success-400)":e.isStale?"var(--warning-300)":"var(--border-light)",e=>e.isStale?.7:1,e=>e.matched&&"\n    background: var(--success-50);\n    box-shadow: 0 0 20px var(--success-200);\n    animation: pulse 1s ease-in-out;\n  ",e=>e.isStale&&"\n    background: var(--warning-50);\n  "),Dl=Yn.div(el||(el=r(["\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: ",";\n  margin-bottom: var(--space-2);\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n  }\n"])),e=>e.matched?"var(--success-700)":e.isStale?"var(--warning-700)":"var(--text-primary)"),Fl=Yn.div(nl||(nl=r(["\n  width: 100%;\n  height: 8px;\n  background: var(--bg-secondary);\n  border-radius: var(--radius-full);\n  overflow: hidden;\n  margin-top: var(--space-2);\n"]))),Hl=Yn.div(tl||(tl=r(["\n  height: 100%;\n  background: ",";\n  width: ","%;\n  transition: width 0.3s ease;\n"])),e=>e.confidence>.8?"var(--success-500)":e.confidence>.6?"var(--warning-500)":"var(--error-500)",e=>100*e.confidence),Ul=Yn.div(rl||(rl=r(["\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  padding: var(--space-2) var(--space-3);\n  border-radius: var(--radius-lg);\n  font-size: 0.875rem;\n  font-weight: 500;\n  background: ",";\n  color: ",";\n  border: 1px solid ",";\n"])),e=>e.connected?"var(--success-50)":"var(--error-50)",e=>e.connected?"var(--success-700)":"var(--error-700)",e=>e.connected?"var(--success-200)":"var(--error-200)"),Wl={TV:{name:"TV",gif:"https://lifeprint.com/asl101/gifs/t/tv.gif",description:"Sign for TV."},after:{name:"After",gif:"https://lifeprint.com/asl101/gifs/a/after-over-across.gif",description:"Sign for after."},airplane:{name:"Airplane",gif:"https://www.lifeprint.com/asl101/gifs/a/airplane-flying.gif",description:"Sign for airplane."},all:{name:"All",gif:"https://lifeprint.com/asl101/gifs/a/all-whole.gif",description:"Sign for all."},alligator:{name:"Alligator",gif:"https://lifeprint.com/asl101/gifs/a/alligator.gif",description:"Sign for alligator."},animal:{name:"Animal",gif:"https://www.lifeprint.com/asl101/gifs-animated/animal.gif",description:"Sign for animal."},another:{name:"Another",gif:"https://lifeprint.com/asl101/gifs-animated/another.gif",description:"Sign for another."},any:{name:"Any",gif:"https://lifeprint.com/asl101/gifs/a/any.gif",description:"Sign for any."},apple:{name:"Apple",gif:"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif",description:"Sign for apple."},arm:{name:"Arm",gif:"https://th.bing.com/th/id/OIP.KkJLqfbp6XEHiIe-jOUb2AHaEc?w=251&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for arm."},aunt:{name:"Aunt",gif:"https://th.bing.com/th/id/OIP.Yz5UUZdNTrVWXf72we_N6wHaHa?rs=1&pid=ImgDetMain",description:"Sign for aunt."},awake:{name:"Awake",gif:"https://th.bing.com/th/id/OIP.XcgdjGKBo8LynmiAw-tDCQHaE-?w=235&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for awake."},backyard:{name:"Backyard",gif:"https://lifeprint.com/asl101/gifs/b/backyard.gif",description:"Sign for backyard."},bad:{name:"Bad",gif:"https://media.giphy.com/media/v1.Y2lkPTc5MGI3NjExeThwMm9rZGVkejE2N2RhMWp0NTY3Z2pwNHBqZXFlOTF3ZGd0cG1zZSZlcD12MV9naWZzX3NlYXJjaCZjdD1n/l0MYL34CVEqLK27yU/giphy.gif",description:"Sign for bad."},balloon:{name:"Balloon",gif:"https://media.giphy.com/media/26FL9yfajyobRXJde/giphy.gif",description:"Sign for balloon."},bath:{name:"Bath",gif:"https://media.giphy.com/media/l0MYPjjoeJbZVPmNO/giphy.gif",description:"Sign for bath."},because:{name:"Because",gif:"https://lifeprint.com/asl101/gifs-animated/because.gif",description:"Sign for because."},bed:{name:"Bed",gif:"https://lifeprint.com/asl101/gifs/b/bed-1.gif",description:"Sign for bed."},bedroom:{name:"Bedroom",gif:"https://lifeprint.com/asl101/gifs/b/bedroom.gif",description:"Sign for bedroom."},bee:{name:"Bee",gif:"https://lifeprint.com/asl101/gifs/b/bee.gif",description:"Sign for bee."},before:{name:"Before",gif:"https://th.bing.com/th/id/OIP.0EvzUY4jH2cDCa4nNcRw4wHaE-?w=267&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for before."},beside:{name:"Beside",gif:"https://lifeprint.com/asl101/gifs/b/beside.gif",description:"Sign for beside."},better:{name:"Better",gif:"https://lifeprint.com/asl101/gifs/b/better.gif",description:"Sign for better."},bird:{name:"Bird",gif:"https://lifeprint.com/asl101/gifs/b/bird.gif",description:"Sign for bird."},black:{name:"Black",gif:"https://th.bing.com/th/id/R.********************************?rik=52tGw7%2fGcx2HtwMm9rZGVkejE2N2RhMWp0NTY3Z2pwNHBqZXFlOTF3ZGd0cG1zZSZlcD12MV9naWZzX3NlYXJjaCZjdD1n/l0MYL34CVEqLK27yU/giphy.gif",description:"Sign for black."},blow:{name:"Blow",gif:"https://th.bing.com/th/id/OIP.rJg-otMBtvfj1T1HkSKugwHaEc?w=304&h=182&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for blow."},blue:{name:"Blue",gif:"https://lifeprint.com/asl101/gifs/b/blue-1.gif",description:"Sign for blue."},boat:{name:"Boat",gif:"https://lifeprint.com/asl101/gifs/b/boat-ship.gif",description:"Sign for boat."},book:{name:"Book",gif:"https://media.giphy.com/media/l0MYL43dl4pQEn3uE/giphy.gif",description:"Sign for book."},boy:{name:"Boy",gif:"https://lifeprint.com/asl101/gifs/b/boy.gif",description:"Sign for boy."},brother:{name:"Brother",gif:"https://lifeprint.com/asl101/gifs/b/brother.gif",description:"Sign for brother."},brown:{name:"Brown",gif:"https://lifeprint.com/asl101/gifs/b/brown.gif",description:"Sign for brown."},bug:{name:"Bug",gif:"https://lifeprint.com/asl101/gifs/b/bug.gif",description:"Sign for bug."},bye:{name:"Bye",gif:"https://c.tenor.com/vME77PObDN8AAAAC/asl-bye-asl-goodbye.gif",description:"Sign for bye."},callonphone:{name:"Call on phone",gif:"https://www.lifeprint.com/asl101/gifs/c/call-hearing.gif",description:"Sign for call on phone."},can:{name:"Can",gif:"https://lifeprint.com/asl101/gifs/c/can.gif",description:"Sign for can."},car:{name:"Car",gif:"https://th.bing.com/th/id/OIP.wxw32OaIdqFt8f_ucHVoRgHaEH?w=308&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for car."},carrot:{name:"Carrot",gif:"https://media.giphy.com/media/l0HlDdvqxs1jsRtiU/giphy.gif",description:"Sign for carrot."},cat:{name:"Cat",gif:"https://lifeprint.com/asl101/gifs-animated/cat-02.gif",description:"Sign for cat."},cereal:{name:"Cereal",gif:"https://th.bing.com/th/id/R.********************************?rik=wPMg%2fK1dYTfR%2bw&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fcereal.gif&ehk=RpDS3wWZM4eryawaxA1wAvWwM0EM%2fdGgJkWY2ce1KFs%3d&risl=&pid=ImgRaw&r=0",description:"Sign for cereal."},chair:{name:"Chair",gif:"https://th.bing.com/th/id/OIP.5kr1MkVLnuN2Z9Jkw-0QpAHaE-?w=237&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for chair."},cheek:{name:"Cheek",gif:"https://lifeprint.com/asl101/gifs/c/cheek.gif",description:"Sign for cheek."},child:{name:"Child",gif:"https://lifeprint.com/asl101/gifs/c/child.gif",description:"Sign for child."},chin:{name:"Chin",gif:"https://lifeprint.com/asl101/gifs/c/chin.gif",description:"Sign for chin."},chocolate:{name:"Chocolate",gif:"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fi.pinimg.com%2foriginals%2f9f%2fa2%2fb5%2f9fa2b5064a72b5e46202d20848f1bf21.gif&ehk=izvOlFp25%2fx5NVTCmqVz0UOnZNOWy%2fAJJtzAhkZ8nTg%3d",description:"Sign for chocolate."},clean:{name:"Clean",gif:"https://media.giphy.com/media/3o7TKoturrdpf5Muwo/giphy.gif",description:"Sign for clean."},close:{name:"Close",gif:"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia2.giphy.com%2fmedia%2fl4JyZuXNGxS3Yydeo%2fgiphy.gif%3fcid%3d790b7611318eb5b864ad67b3cecb35b9d81240a50d251bb0%26rid%3dgiphy.gif%26ct%3dg&ehk=A6wfp3Afm3rFCPLWSjgQd6JVjmRSBNBlk9vd0jVNgJc%3d",description:"Sign for close."},closet:{name:"Closet",gif:"https://lifeprint.com/asl101/gifs/c/closet.gif",description:"Sign for closet."},cloud:{name:"Cloud",gif:"https://th.bing.com/th/id/OIP.hMO89bV2zwVcIVIa7FOT5QHaEc?rs=1&pid=ImgDetMain",description:"Sign for cloud."},clown:{name:"Clown",gif:"https://th.bing.com/th/id/R.********************************?rik=OPrV3%2b1Zkelr2A&pid=ImgRaw&r=0",description:"Sign for clown."},cow:{name:"Cow",gif:"https://lifeprint.com/asl101/gifs/c/cow.gif",description:"Sign for cow."},cowboy:{name:"Cowboy",gif:"https://lifeprint.com/asl101/gifs/c/cowboy.gif",description:"Sign for cowboy."},cry:{name:"Cry",gif:"https://www.lifeprint.com/asl101/gifs/c/cry-tears.gif",description:"Sign for cry."},cut:{name:"Cut",gif:"https://th.bing.com/th/id/OIP.ZtKu3hlJ6pduArqfgEcyUgHaE-?w=248&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for cut."},cute:{name:"Cute",gif:"https://lifeprint.com/asl101/gifs/c/cute-sugar.gif",description:"Sign for cute."},dad:{name:"Dad",gif:"https://media.giphy.com/media/l0MYQcLKwtl5v6H1S/giphy.gif",description:"Sign for dad."},dance:{name:"Dance",gif:"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia.giphy.com%2fmedia%2f3o7TKMspYQjQTbOz2U%2fgiphy.gif&ehk=h%2bdBHCxuoOT89ovSy5uTk6MCL9acaBEV6ld9lrVDRF4%3d",description:"Sign for dance."},dirty:{name:"Dirty",gif:"https://th.bing.com/th/id/OIP.wRA7r1OPPUuEoLL4Hds9jAHaHa?rs=1&pid=ImgDetMain",description:"Sign for dirty."},dog:{name:"Dog",gif:"https://th.bing.com/th/id/R.********************************?rik=uVshGsOHXgldoQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fd%2fdog1.gif&ehk=Xnfrvg0xwy1u%2fae9vWdKYMT25%2bF6qoteW2FThCbVrOA%3d&risl=&pid=ImgRaw&r=0",description:"Sign for dog."},doll:{name:"Doll",gif:"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fwww.lifeprint.com%2fasl101%2fgifs-animated%2fdoll.gif&ehk=hPI0Fzzl9CGOrgQYS2Z53a5YdYgjxYFeOIGghGAEZYU%3d",description:"Sign for doll."},donkey:{name:"Donkey",gif:"https://www.lifeprint.com/asl101/gifs/d/donkey-1h.gif",description:"Sign for donkey."},down:{name:"Down",gif:"https://th.bing.com/th/id/OIP.CZlW6IpZUdgspxVpW6PZ8QHaE-?w=250&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for down."},drawer:{name:"Drawer",gif:"https://th.bing.com/th/id/OIP.8yooqOFFixqki7j28PVpYQHaE-?w=234&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for drawer."},drink:{name:"Drink",gif:"https://www.lifeprint.com/asl101/gifs/d/drink-c.gif",description:"Sign for drink."},drop:{name:"Drop",gif:"https://th.bing.com/th/id/OIP.XQJn0tOccOUmG8OZHz8X9gHaE-?w=247&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for drop."},dry:{name:"Dry",gif:"https://th.bing.com/th/id/OIP.A0oQgM0IGtwZjfz1Caj-AgHaE-?w=268&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for dry."},dryer:{name:"Dryer",gif:"https://lifeprint.com/asl101/gifs/d/dryer.gif",description:"Sign for dryer."},duck:{name:"Duck",gif:"https://th.bing.com/th/id/R.********************************?rik=ZetjiJ3WOhOXrQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fd%2fduck.gif&ehk=STeui62x5lieai0VcyeZkX2t8rILR%2f8GR5F3x2xJ5tw%3d&risl=&pid=ImgRaw&r=0",description:"Sign for duck."},ear:{name:"Ear",gif:"https://lifeprint.com/asl101/signjpegs/e/ears.h3.jpg",description:"Sign for ear."},elephant:{name:"Elephant",gif:"https://lifeprint.com/asl101/gifs-animated/elephant.gif",description:"Sign for elephant."},empty:{name:"Empty",gif:"https://lifeprint.com/images-signs/empty.gif",description:"Sign for empty."},every:{name:"Every",gif:"https://lifeprint.com/asl101/gifs-animated/every.gif",description:"Sign for every."},eye:{name:"Eye",gif:"https://lifeprint.com/asl101/gifs/e/eyes.gif",description:"Sign for eye."},face:{name:"Face",gif:"https://lifeprint.com/asl101/gifs/f/face.gif",description:"Sign for face."},fall:{name:"Fall",gif:"https://lifeprint.com/asl101/gifs/f/fall.gif",description:"Sign for fall."},farm:{name:"Farm",gif:"https://th.bing.com/th/id/R.********************************?rik=IO%2brRd7xNmCQBQ&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2ffarm.gif&ehk=aOO01Vk8fbE84nLfNNnOVL3kUdyWJtLaTEcwePgbP9A%3d&risl=&pid=ImgRaw&r=0",description:"Sign for farm."},fast:{name:"Fast",gif:"https://th.bing.com/th/id/OIP.YX_BqT1FjGm8HeM4k4WFAgAAAA?rs=1&pid=ImgDetMain",description:"Sign for fast."},feet:{name:"Feet",gif:"https://th.bing.com/th/id/OIP.RaYFj5lvSS6NeIna8NtmZQHaE-?w=247&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for feet."},find:{name:"Find",gif:"https://www.lifeprint.com/asl101/gifs/f/find-pick.gif",description:"Sign for find."},fine:{name:"Fine",gif:"https://th.bing.com/th/id/R.********************************?rik=Qpm%2bw3fHTAWj1A&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2ff%2ffine.gif&ehk=mGMZf4l%2bLZMq4atRomNJSvrSjYgFe%2bRVCm1dYLh5J3I%3d&risl=&pid=ImgRaw&r=0",description:"Sign for fine."},finger:{name:"Finger",gif:"https://lifeprint.com/asl101/gifs/f/finger.gif",description:"Sign for finger."},finish:{name:"Finish",gif:"https://th.bing.com/th/id/R.********************************?rik=34j4pW2f3E5TtQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2ff%2ffinish.gif&ehk=xNk24Jbe3t0moSmcmUftmZzCRgHIxsarq3W9E7kGmPM%3d&risl=&pid=ImgRaw&r=0",description:"Sign for finish."},fireman:{name:"Fireman",gif:"https://lifeprint.com/asl101/gifs/f/fireman-c2.gif",description:"Sign for fireman."},first:{name:"First",gif:"https://lifeprint.com/asl101/gifs/f/first.gif",description:"Sign for first."},fish:{name:"Fish",gif:"https://th.bing.com/th/id/OIP.Lzhd7lIIa-V4H3faS1d3mQHaHa?rs=1&pid=ImgDetMain",description:"Sign for fish."},flag:{name:"Flag",gif:"https://th.bing.com/th/id/OIP.3LqQWEnK4TG0lohgQ3G5uAHaE-?w=263&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for flag."},flower:{name:"Flower",gif:"https://media.giphy.com/media/3o7TKGkqPpLUdFiFPy/giphy.gif",description:"Sign for flower."},food:{name:"Food",gif:"https://i.pinimg.com/originals/cc/bb/0c/ccbb0c143db0b51e9947a5966db42fd8.gif",description:"Sign for food."},for:{name:"For",gif:"https://lifeprint.com/asl101/gifs/f/for.gif",description:"Sign for for."},frenchfries:{name:"French Fries",gif:"https://www.lifeprint.com/asl101/gifs/f/french-fries.gif",description:"Sign for french fries."},frog:{name:"Frog",gif:"https://media.giphy.com/media/l0HlKl64lIvTjZ7QA/giphy.gif",description:"Sign for frog."},garbage:{name:"Garbage",gif:"https://th.bing.com/th/id/R.********************************?rik=78iU%2fDx85Ut9fA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fg%2fgarbage.gif&ehk=lafY%2f1y5WEEfr04p6Uq4waDP9iV7bJB5r2k3RYGOhWY%3d&risl=&pid=ImgRaw&r=0",description:"Sign for garbage."},gift:{name:"Gift",gif:"https://www.babysignlanguage.com/signs/gift.gif",description:"Sign for gift."},giraffe:{name:"Giraffe",gif:"https://www.lifeprint.com/asl101/gifs/g/giraffe.gif",description:"Sign for giraffe."},girl:{name:"Girl",gif:"https://th.bing.com/th/id/R.********************************?rik=yDsGUPEaDyeSlA&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fgirl.gif&ehk=zdVxVSayRBDn67vVCpMhUH6UmzUQE8vaY7%2bv8jedvs8%3d&risl=&pid=ImgRaw&r=0",description:"Sign for girl."},give:{name:"Give",gif:"https://www.lifeprint.com/asl101/gifs/g/give-x-two-handed.gif",description:"Sign for give."},glasswindow:{name:"Glass Window",gif:"https://lifeprint.com/asl101/gifs/g/glass.gif",description:"Sign for glass window."},go:{name:"Go",gif:"https://media.giphy.com/media/l3vRdVMMN9VsW5a0w/giphy.gif",description:"Sign for go."},goose:{name:"Goose",gif:"https://www.babysignlanguage.com/signs/goose.gif",description:"Sign for goose."},grandma:{name:"Grandma",gif:"https://www.lifeprint.com/asl101/gifs/g/grandma.gif",description:"Sign for grandma."},grandpa:{name:"Grandpa",gif:"https://th.bing.com/th/id/OIP.yyLPc-rWg0PMNbrwjeQQngHaE-?w=238&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for grandpa."},grass:{name:"Grass",gif:"https://th.bing.com/th/id/R.********************************?rik=uGZNVzt6tISwHA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs-animated%2fgrass.gif&ehk=VKQd9cvBrglo47EhogWYL9rOiZZsEJ7Yqt%2bgJ8N99yQ%3d&risl=&pid=ImgRaw&r=0",description:"Sign for grass."},green:{name:"Green",gif:"https://i.pinimg.com/originals/cb/7f/75/cb7f757ffb79cb3d1309c9ad785e83a1.gif",description:"Sign for green."},gum:{name:"Gum",gif:"https://lifeprint.com/asl101/gifs/g/gum.gif",description:"Sign for gum."},hair:{name:"Hair",gif:"https://www.lifeprint.com/asl101/gifs/h/hair-g-version.gif",description:"Sign for hair."},happy:{name:"Happy",gif:"https://media0.giphy.com/media/3o7TKFpahYpUp4g0N2/giphy.gif?cid=790b7611bca188a92e2e759876756ef62ba95b7cdd337c77&rid=giphy.gif&ct=g",description:"Sign for happy."},hat:{name:"Hat",gif:"https://th.bing.com/th/id/OIP.QyFdqn-0ZqUwNfE6jbzKWAHaE-?w=258&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for hat."},hate:{name:"Hate",gif:"https://media.giphy.com/media/l0MYPiNw8l2LAPJXW/giphy.gif",description:"Sign for hate."},have:{name:"Have",gif:"https://th.bing.com/th/id/R.********************************?rik=q5Ei%2b7oJb7Uzyw&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhave.gif&ehk=H9yIaJxFVejkfHpkhTUipBRv9CW63KBFy6QW5cdbkKw%3d&risl=&pid=ImgRaw&r=0",description:"Sign for have."},haveto:{name:"Have to",gif:"https://lifeprint.com/asl101/gifs/h/have-to.gif",description:"Sign for have to."},head:{name:"Head",gif:"https://th.bing.com/th/id/R.********************************?rik=OcbJdRbpEFsWXQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fsignjpegs%2fh%2fhead-1.jpg&ehk=RPBV45fSrLDEWYiZvRuZs2c1JNrL4WzdqLSNMFIF3Rs%3d&risl=&pid=ImgRaw&r=0",description:"Sign for head."},hear:{name:"Hear",gif:"https://www.lifeprint.com/asl101/signjpegs/h/hear.h4.jpg",description:"Sign for hear."},helicopter:{name:"Helicopter",gif:"https://th.bing.com/th/id/R.********************************?rik=5uhWxBaByliWA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhelicopter.gif&ehk=mwAyT82RBoeYDe7yaHA1jL3%2f30dUksltmv4dF7YGf%2bU%3d&risl=&pid=ImgRaw&r=0",description:"Sign for helicopter."},hello:{name:"Hello",gif:"https://media.giphy.com/media/3o7TKNKOfKlIhbD3gY/giphy.gif",description:"Sign for hello."},hen:{name:"Hen",gif:"https://media0.giphy.com/media/26hisADhtILiu1J3W/giphy.gif?cid=790b76112d512b94e1647afb111c8d77f92ae31f37864f2&rid=giphy.gif&ct=g",description:"Sign for hen."},hesheit:{name:"He/She/It",gif:"https://lifeprint.com/asl101/gifs/h/he-she-it.gif",description:"Sign for he/she/it."},hide:{name:"Hide",gif:"https://lifeprint.com/asl101/gifs/h/hide.gif",description:"Sign for hide."},high:{name:"High",gif:"https://lifeprint.com/asl101/gifs/h/high.gif",description:"Sign for high."},home:{name:"Home",gif:"https://th.bing.com/th/id/R.********************************?rik=%2bnBd%2foQjxnoPfg&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhome-2.gif&ehk=7yD%2f%2fh6JN1Y4D4BOrUjgKW4Jccy2Y4GVYLf%2fzyk%2b5YY%3d&risl=&pid=ImgRaw&r=0",description:"Sign for home."},horse:{name:"Horse",gif:"https://media.giphy.com/media/l0HlM5HffraiQaHUk/giphy.gif",description:"Sign for horse."},hot:{name:"Hot",gif:"https://media.giphy.com/media/3o6Zt99k5aDok347bG/giphy.gif",description:"Sign for hot."},hungry:{name:"Hungry",gif:"https://media.giphy.com/media/l3vR0xkdFEz4tnfTq/giphy.gif",description:"Sign for hungry."},icecream:{name:"Ice Cream",gif:"https://media.giphy.com/media/3o7TKp6yVibVMhBSLu/giphy.gif",description:"Sign for ice cream."},if:{name:"If",gif:"https://lifeprint.com/asl101/gifs/i/if.gif",description:"Sign for if."},into:{name:"Into",gif:"https://lifeprint.com/asl101/gifs/i/into.gif",description:"Sign for into."},jacket:{name:"Jacket",gif:"https://www.lifeprint.com/asl101/gifs/c/coat.gif",description:"Sign for jacket."},jeans:{name:"Jeans",gif:"https://lifeprint.com/asl101/gifs/j/jeans.gif",description:"Sign for jeans."},jump:{name:"Jump",gif:"https://lifeprint.com/asl101/gifs-animated/jump.gif",description:"Sign for jump."},kiss:{name:"Kiss",gif:"https://i.gifer.com/PxGY.gif",description:"Sign for kiss."},kitty:{name:"Kitty",gif:"https://lifeprint.com/asl101/gifs-animated/cat-02.gif",description:"Sign for kitty."},lamp:{name:"Lamp",gif:"https://lifeprint.com/asl101/gifs/l/lamp.gif",description:"Sign for lamp."},later:{name:"Later",gif:"https://media3.giphy.com/media/l0MYHTyMzMRcikIxi/giphy.gif?cid=790b761128cd39f9baa06dbeb4e099d13e3516763d5f0952&rid=giphy.gif&ct=g",description:"Sign for later."},like:{name:"Like",gif:"https://lifeprint.com/asl101/gifs/l/like.gif",description:"Sign for like."},lion:{name:"Lion",gif:"https://th.bing.com/th/id/OIP.8sDkvbXdMKVmCNlDV79WpAHaE-?w=247&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for lion."},lips:{name:"Lips",gif:"https://lifeprint.com/asl101/gifs/l/lips.gif",description:"Sign for lips."},listen:{name:"Listen",gif:"https://th.bing.com/th/id/OIP.VjsXAad6abRwkCla83kbZQHaEc?w=284&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for listen."},look:{name:"Look",gif:"https://th.bing.com/th/id/R.********************************?rik=pYhzip7LqNs7qw&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fl%2flook-at-1.gif&ehk=rFJ7dBrMGFDK0nHLzrOPAzROVE7yqyDEcb%2btLqKqYOI%3d&risl=&pid=ImgRaw&r=0",description:"Sign for look."},loud:{name:"Loud",gif:"https://lifeprint.com/asl101/gifs-animated/loud.gif",description:"Sign for loud."},mad:{name:"Mad",gif:"https://lifeprint.com/asl101/gifs/m/mad.gif",description:"Sign for mad."},make:{name:"Make",gif:"https://th.bing.com/th/id/OIP.CPz7T2bH107Tu-DBnHvatAHaEc?w=313&h=188&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for make."},man:{name:"Man",gif:"https://lifeprint.com/asl101/gifs/m/man.gif",description:"Sign for man."},many:{name:"Many",gif:"https://lifeprint.com/asl101/gifs/m/many.gif",description:"Sign for many."},milk:{name:"Milk",gif:"https://lifeprint.com/asl101/gifs/m/milk.gif",description:"Sign for milk."},minemy:{name:"Mine/My",gif:"https://th.bing.com/th/id/OIP.VBkNZsR_pK7KUoCNiWYMdgHaEc?w=288&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for mine/my."},mitten:{name:"Mitten",gif:"https://lifeprint.com/asl101/gifs-animated/mittens.gif",description:"Sign for mitten."},mom:{name:"Mom",gif:"https://lifeprint.com/asl101/gifs/m/mom.gif",description:"Sign for mom."},moon:{name:"Moon",gif:"https://th.bing.com/th/id/R.********************************?rik=XbVhBJtkANrG9g&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fm%2fmoon.gif&ehk=YSDvFeUSTa9X1BEJhDjdnLC4c7zWn8z7Hj%2fMkkLUyFE%3d&risl=&pid=ImgRaw&r=0",description:"Sign for moon."},morning:{name:"Morning",gif:"https://media0.giphy.com/media/3o6ZtrcJ9GCXGGw0ww/source.gif",description:"Sign for morning."},mouse:{name:"Mouse",gif:"https://lifeprint.com/asl101/gifs/m/mouse.gif",description:"Sign for mouse."},mouth:{name:"Mouth",gif:"https://lifeprint.com/asl101/gifs-animated/mouth.gif",description:"Sign for mouth."},nap:{name:"Nap",gif:"https://lifeprint.com/asl101/gifs/n/nap.gif",description:"Sign for nap."},napkin:{name:"Napkin",gif:"https://lifeprint.com/asl101/gifs/n/napkin.gif",description:"Sign for napkin."},night:{name:"Night",gif:"https://lifeprint.com/asl101/gifs/n/night.gif",description:"Sign for night."},no:{name:"No",gif:"https://lifeprint.com/asl101/gifs/n/no-2-movement.gif",description:"Sign for no."},noisy:{name:"Noisy",gif:"https://lifeprint.com/asl101/gifs/n/noisy.gif",description:"Sign for noisy."},nose:{name:"Nose",gif:"https://lifeprint.com/asl101/signjpegs/n/nose.h1.jpg",description:"Sign for nose."},not:{name:"Not",gif:"https://th.bing.com/th/id/R.********************************?rik=6%2bbZ2jRA%2famQ4Q&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fn%2fnot-negative.gif&ehk=%2bppuO9P0%2fpdzrrdNO4FXpxdIGs8jgY%2fj%2b1ZCwdbDWO4%3d&risl=&pid=ImgRaw&r=0",description:"Sign for not."},now:{name:"Now",gif:"https://lifeprint.com/asl101/gifs/n/now.gif",description:"Sign for now."},nuts:{name:"Nuts",gif:"https://th.bing.com/th/id/OIP.wRnQjn9j2vfFfzAnRR205QHaE-?w=276&h=185&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for nuts."},old:{name:"Old",gif:"https://lifeprint.com/asl101/gifs/o/old.gif",description:"Sign for old."},on:{name:"On",gif:"https://lifeprint.com/asl101/gifs/o/on-onto.gif",description:"Sign for on."},open:{name:"Open",gif:"https://th.bing.com/th/id/OIP.BeMiGXQFuYk_6ZrgG3iqzQHaE-?w=264&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for open."},orange:{name:"Orange",gif:"https://lifeprint.com/asl101/gifs/o/orange.gif",description:"Sign for orange."},outside:{name:"Outside",gif:"https://lifeprint.com/asl101/gifs/o/outside.gif",description:"Sign for outside."},owie:{name:"Owie",gif:"https://lifeprint.com/asl101/gifs/o/owie.gif",description:"Sign for owie."},owl:{name:"Owl",gif:"https://lifeprint.com/asl101/gifs/o/owl.gif",description:"Sign for owl."},pajamas:{name:"Pajamas",gif:"https://lifeprint.com/asl101/gifs/p/pajamas.gif",description:"Sign for pajamas."},pen:{name:"Pen",gif:"https://lifeprint.com/asl101/gifs/p/pen.gif",description:"Sign for pen."},pencil:{name:"Pencil",gif:"https://lifeprint.com/asl101/gifs/p/pencil-2.gif",description:"Sign for pencil."},penny:{name:"Penny",gif:"https://lifeprint.com/asl101/gifs/p/penny.gif",description:"Sign for penny."},person:{name:"Person",gif:"https://lifeprint.com/asl101/gifs/p/person.gif",description:"Sign for person."},pig:{name:"Pig",gif:"https://lifeprint.com/asl101/gifs/p/pig.gif",description:"Sign for pig."},pizza:{name:"Pizza",gif:"https://lifeprint.com/asl101/gifs/p/pizza.gif",description:"Sign for pizza."},please:{name:"Please",gif:"https://lifeprint.com/asl101/gifs-animated/pleasecloseup.gif",description:"Sign for please."},police:{name:"Police",gif:"https://th.bing.com/th/id/R.********************************?rik=icjjfUg15cqgLw&pid=ImgRaw&r=0",description:"Sign for police."},pool:{name:"Pool",gif:"https://th.bing.com/th/id/OIP.dhcMKyW2psDcA5uwsRaRagHaEc?w=276&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for pool."},potty:{name:"Potty",gif:"https://th.bing.com/th/id/OIP.YcNMUjCg6f95xdgN5rnenwHaE-?w=264&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for potty."},pretend:{name:"Pretend",gif:"https://lifeprint.com/asl101/gifs/p/pretend.gif",description:"Sign for pretend."},pretty:{name:"Pretty",gif:"https://lifeprint.com/asl101/gifs/b/beautiful.gif",description:"Sign for pretty."},puppy:{name:"Puppy",gif:"https://lifeprint.com/asl101/gifs/p/puppy.gif",description:"Sign for puppy."},puzzle:{name:"Puzzle",gif:"https://res.cloudinary.com/spiralyze/image/upload/f_auto,w_auto/BabySignLanguage/DictionaryPages/puzzle.svg",description:"Sign for puzzle."},quiet:{name:"Quiet",gif:"https://lifeprint.com/asl101/gifs-animated/quiet-03.gif",description:"Sign for quiet."},radio:{name:"Radio",gif:"https://i.pinimg.com/originals/6d/5e/5e/6d5e5e2f78f80e9006293df853a2ba3b.gif",description:"Sign for radio."},rain:{name:"Rain",gif:"https://lifeprint.com/asl101/gifs/r/rain.gif",description:"Sign for rain."},read:{name:"Read",gif:"https://lifeprint.com/asl101/gifs/r/read.gif",description:"Sign for read."},red:{name:"Red",gif:"https://lifeprint.com/asl101/gifs-animated/red.gif",description:"Sign for red."},refrigerator:{name:"Refrigerator",gif:"https://lifeprint.com/asl101/gifs/r/refrigerator-r-e-f.gif",description:"Sign for refrigerator."},ride:{name:"Ride",gif:"https://lifeprint.com/asl101/gifs/r/ride.gif",description:"Sign for ride."},room:{name:"Room",gif:"https://lifeprint.com/asl101/gifs/r/room-box.gif",description:"Sign for room."},sad:{name:"Sad",gif:"https://lifeprint.com/asl101/gifs/s/sad.gif",description:"Sign for sad."},same:{name:"Same",gif:"https://lifeprint.com/asl101/gifs/s/same-similar.gif",description:"Sign for same."},say:{name:"Say",gif:"https://asl.signlanguage.io/words/say/say-in-asl-a0a5e00000a44k0.jpg",description:"Sign for say."},scissors:{name:"Scissors",gif:"https://i.makeagif.com/media/4-17-2023/pl4M4F.gif",description:"Sign for scissors."},see:{name:"See",gif:"https://lifeprint.com/asl101/gifs/l/look-at-2.gif",description:"Sign for see."},shhh:{name:"Shhh",gif:"https://lifeprint.com/asl101/signjpegs/s/shhh.jpg",description:"Sign for shhh."},shirt:{name:"Shirt",gif:"https://lifeprint.com/asl101/gifs/s/shirt-volunteer.gif",description:"Sign for shirt."},shoe:{name:"Shoe",gif:"https://media.giphy.com/media/3o7TKC4StpZKa6d2y4/giphy.gif",description:"Sign for shoe."},shower:{name:"Shower",gif:"https://lifeprint.com/asl101/gifs/s/shower.gif",description:"Sign for shower."},sick:{name:"Sick",gif:"https://lifeprint.com/asl101/gifs/s/sick.gif",description:"Sign for sick."},sleep:{name:"Sleep",gif:"https://media4.giphy.com/media/3o7TKnRuBdakLslcaI/200.gif?cid=790b76110d8f185a9713f36dd65a0df801576e01b403c95c&rid=200.gif&ct=g",description:"Sign for sleep."},sleepy:{name:"Sleepy",gif:"https://th.bing.com/th/id/R.********************************?rik=zdWvzvABcDHTdw&riu=http%3a%2f%2fwww.lifeprint.com%2fasl101%2fgifs-animated%2fsleepy.gif&ehk=zLqDFJMAs2nqG02RbbR6mEMvux4h85JGzls4uwgrePQ%3d&risl=&pid=ImgRaw&r=0",description:"Sign for sleepy."},smile:{name:"Smile",gif:"https://th.bing.com/th/id/OIP.dpce-bMAh-1jorUrPQFW4AHaE-?w=263&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for smile."},snack:{name:"Snack",gif:"https://media.giphy.com/media/26ybw1E1GTKzLuKDS/giphy.gif",description:"Sign for snack."},snow:{name:"Snow",gif:"https://lifeprint.com/asl101/gifs/s/snow.gif",description:"Sign for snow."},stairs:{name:"Stairs",gif:"https://th.bing.com/th/id/OIP.8BtYhPXXDQHRqodMyyy3HgHaEc?w=288&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for stairs."},stay:{name:"Stay",gif:"https://i.pinimg.com/originals/f5/29/8e/f5298eaa46b91cd6de2a32bd76aadffc.gif",description:"Sign for stay."},sticky:{name:"Sticky",gif:"https://th.bing.com/th/id/OIP.fffIgrX_DBAjxGMkskvTvQHaE-?w=240&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for sticky."},store:{name:"Store",gif:"https://th.bing.com/th/id/R.********************************?rik=x7oUPJGckc7QDg&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fstore.gif&ehk=P7beooAyFUst%2bbVtqIqINeQGP0%2bIUlNSPXc1Du5zWfQ%3d&risl=&pid=ImgRaw&r=0",description:"Sign for store."},story:{name:"Story",gif:"https://lifeprint.com/asl101/gifs/s/story.gif",description:"Sign for story."},stuck:{name:"Stuck",gif:"https://lifeprint.com/asl101/signjpegs/s/stuck.2.jpg",description:"Sign for stuck."},sun:{name:"Sun",gif:"https://media.giphy.com/media/3o6Zt7merN2zxEtNRK/giphy.gif",description:"Sign for sun."},table:{name:"Table",gif:"https://lifeprint.com/asl101/gifs/t/table.gif",description:"Sign for table."},talk:{name:"Talk",gif:"https://lifeprint.com/asl101/gifs/t/talk.gif",description:"Sign for talk."},taste:{name:"Taste",gif:"https://lifeprint.com/asl101/gifs/t/taste.gif",description:"Sign for taste."},thankyou:{name:"Thank You",gif:"https://lifeprint.com/asl101/gifs/t/thank-you.gif",description:"Sign for thank you."},that:{name:"That",gif:"https://i.ytimg.com/vi/81Wr75AFDnQ/maxresdefault.jpg",description:"Sign for that."},there:{name:"There",gif:"https://lifeprint.com/asl101/gifs-animated/there.gif",description:"Sign for there."},think:{name:"Think",gif:"https://lifeprint.com/asl101/gifs/t/think.gif",description:"Sign for think."},thirsty:{name:"Thirsty",gif:"https://media.giphy.com/media/l3vR0sYheBulL1P7W/giphy.gif",description:"Sign for thirsty."},tiger:{name:"Tiger",gif:"https://lifeprint.com/asl101/gifs/t/tiger.gif",description:"Sign for tiger."},time:{name:"Time",gif:"https://lifeprint.com/asl101/gifs/t/time-1.gif",description:"Sign for time."},tomorrow:{name:"Tomorrow",gif:"https://lifeprint.com/asl101/gifs/t/tomorrow.gif",description:"Sign for tomorrow."},tongue:{name:"Tongue",gif:"https://th.bing.com/th/id/R.********************************?rik=ZJJ2Ixdj0l0b5A&riu=http%3a%2f%2fwww.aslsearch.com%2fsigns%2fimages%2ftongue.jpg&ehk=MxZVUjfqPa3klIauPGpReg%2fYgnJUyIjlxOOvCYYG0hc%3d&risl=&pid=ImgRaw&r=0",description:"Sign for tongue."},tooth:{name:"Tooth",gif:"https://th.bing.com/th/id/R.********************************?rik=ZF%2fsFUXvt5czGA&riu=http%3a%2f%2flifeprint.com%2fasl101%2fsignjpegs%2ft%2fteeth1.jpg&ehk=vI5eDlD4HZWXhK1PQOQz4nA5e6oguHgeXqDo%2fcdcWg4%3d&risl=&pid=ImgRaw&r=0",description:"Sign for tooth."},toothbrush:{name:"Toothbrush",gif:"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia.giphy.com%2fmedia%2fl3vR0Rq2HVL2KHLUI%2fgiphy.gif&ehk=eC0Sq9sHjrrOrkyJvOogQbXVkTOL5OPCeyVymejL0RU%3d",description:"Sign for toothbrush."},touch:{name:"Touch",gif:"https://th.bing.com/th/id/OIP.imGRfqjCtcHhof6Lc_0QJQHaE-?w=230&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Sign for touch."},toy:{name:"Toy",gif:"https://lifeprint.com/asl101/gifs-animated/play-02.gif",description:"Sign for toy."},tree:{name:"Tree",gif:"https://lifeprint.com/asl101/gifs-animated/tree.gif",description:"Sign for tree."},uncle:{name:"Uncle",gif:"https://lifeprint.com/asl101/gifs/u/uncle.gif",description:"Sign for uncle."},underwear:{name:"Underwear",gif:"https://th.bing.com/th/id/OIP.c8g9T_lOhbZWRvKAA12J8wHaEO?pid=ImgDet&w=310&h=177&rs=1",description:"Sign for underwear."},up:{name:"Up",gif:"https://www.babysignlanguage.com/signs/up.gif",description:"Sign for up."},vacuum:{name:"Vacuum",gif:"https://www.babysignlanguage.com/signs/vacuum.gif",description:"Sign for vacuum."},wait:{name:"Wait",gif:"https://lifeprint.com/asl101/gifs/w/wait.gif",description:"Sign for wait."},wake:{name:"Wake",gif:"https://lifeprint.com/asl101/gifs/w/wake-up.gif",description:"Sign for wake."},water:{name:"Water",gif:"https://lifeprint.com/asl101/gifs/w/water-2.gif",description:"Sign for water."},wet:{name:"Wet",gif:"https://www.babysignlanguage.com/signs/wet.gif",description:"Sign for wet."},weus:{name:"We/Us",gif:"https://lifeprint.com/asl101/gifs/w/we-us.gif",description:"Sign for we/us."},where:{name:"Where",gif:"https://lifeprint.com/asl101/gifs/w/where.gif",description:"Sign for where."},white:{name:"White",gif:"https://lifeprint.com/asl101/gifs/w/white.gif",description:"Sign for white."},who:{name:"Who",gif:"https://lifeprint.com/asl101/gifs/w/who.gif",description:"Sign for who."},why:{name:"Why",gif:"https://lifeprint.com/asl101/gifs/w/why.gif",description:"Sign for why."},will:{name:"Will",gif:"https://lifeprint.com/asl101/gifs/f/future.gif",description:"Sign for will."},wolf:{name:"Wolf",gif:"https://lifeprint.com/asl101/gifs/w/wolf-side-view.gif",description:"Sign for wolf."},yellow:{name:"Yellow",gif:"https://lifeprint.com/asl101/gifs/y/yellow.gif",description:"Sign for yellow."},yes:{name:"Yes",gif:"https://media.tenor.com/oYIirlyIih0AAAAC/yes-asl.gif",description:"Sign for yes."},yesterday:{name:"Yesterday",gif:"https://lifeprint.com/asl101/gifs/y/yesterday.gif",description:"Sign for yesterday."},yourself:{name:"Yourself",gif:"https://www.lifeprint.com/asl101/gifs/s/self-myself.gif",description:"Sign for yourself."},yucky:{name:"Yucky",gif:"https://i.pinimg.com/originals/7f/66/7f/7f667f7eeb92c994829dcaf52c5bcf2d.gif",description:"Sign for yucky."},zebra:{name:"Zebra",gif:"https://lifeprint.com/asl101/gifs/z/zebra-stripes-two-hands.gif",description:"Sign for zebra."},zipper:{name:"Zipper",gif:"https://th.bing.com/th/id/R.********************************?rik=qPRTVGd2SzUBxw&riu=http%3a%2f%2fwww.babysignlanguage.com%2fsigns%2fzipper.gif&ehk=IGx68sSokNwU21zu3Z2D%2blmeehKYxpSNhX2VnrvQqYE%3d&risl=&pid=ImgRaw&r=0",description:"Sign for zipper."}},Bl=n=>{let{onBackToHome:t}=n;const[r,i]=(0,e.useState)("levels"),[a,o]=(0,e.useState)(null),[s,l]=(0,e.useState)(()=>{const e=localStorage.getItem("asl-training-progress");return e?JSON.parse(e):{}}),[c,d]=(0,e.useState)("hello"),[u,f]=(0,e.useState)(""),[p,g]=(0,e.useState)(!1),[m,h]=(0,e.useState)(""),[b,y]=(0,e.useState)([]),[v,w]=(0,e.useState)(!1),k=(0,e.useRef)(null),x=(0,e.useRef)(null),S=(0,e.useRef)(0),{isConnected:E,prediction:C,isAIRecording:z,recordingStatus:j,signMatched:P,targetSign:R,startRecording:_,stopRecording:O,startFrameCapture:T,retryConnection:A}=Ar(),N=(0,e.useCallback)(e=>{d(e.target.value),w(!1)},[]),L=(0,e.useCallback)(()=>{k.current?(g(!0),T(k,100),f("AI detection started")):f("Camera not available")},[T]),M=(0,e.useCallback)(()=>{if(!E)return void f("AI backend not connected");if(!k.current)return void f("Camera not available");if(z)return void f("Already recording...");const e=Wl[c].name;f('\ud83c\udfac Starting 3-second recording for "'.concat(e,'"...')),h('\ud83c\udfac Recording "'.concat(e,'"...')),_(e,!0),x.current=setTimeout(()=>{O(),f('\u2705 Recording complete! "'.concat(e,'" saved to recordings folder with landmark data')),h('\u2705 Recording saved: "'.concat(e,'" (3 seconds)'))},3e3),p||L()},[c,E,p,L,z,_,O]),I=(0,e.useCallback)(()=>{z&&(O(),h('\u2705 Recording saved: "'.concat(Wl[c].name,'" (Manual stop)'))),S.current=0,x.current&&clearTimeout(x.current),f("Manual recording stopped")},[O,z,c]);(0,e.useEffect)(()=>{E&&k.current&&!p&&L()},[E,L,p]),(0,e.useEffect)(()=>{j&&j.includes("saved")&&h(j)},[j]),(0,e.useEffect)(()=>{if(!C||!E)return void(S.current=0);const e=C.sign.toLowerCase(),n=Wl[c].name.toLowerCase(),t=C.confidence;return e===n&&t>=.5?(S.current+=1,S.current>=2&&!z&&(f('\ud83c\udfac Auto-recording "'.concat(Wl[c].name,'"... (').concat(Math.round(100*t),"% confidence)")),_(Wl[c].name,!1),x.current=setTimeout(()=>{O(),f('\u2705 Auto-recording complete! "'.concat(Wl[c].name,'" saved to recordings folder with landmark data')),h('\u2705 Auto-recording saved: "'.concat(Wl[c].name,'" (3 seconds)')),S.current=0},3e3))):S.current=0,()=>{x.current&&clearTimeout(x.current)}},[C,c,z,_,O,E]);const D=e=>{o(e),i("training")},F=()=>{i("levels"),o(null)},H=(e,n,t)=>{const r=Xn(Xn({},s),{},{[e]:{completed:n,total:t}});l(r),localStorage.setItem("asl-training-progress",JSON.stringify(r))};return"levels"===r?(0,Yt.jsx)(Sa,{currentLevel:a,userProgress:s,onLevelSelect:D}):"training"===r&&a?(0,Yt.jsx)(vs,{level:a,onBack:F,userProgress:s,onProgressUpdate:H}):(0,Yt.jsxs)(il,{children:[(0,Yt.jsx)(al,{children:(0,Yt.jsxs)(ol,{children:[(0,Yt.jsxs)(sl,{children:[(0,Yt.jsx)(ll,{children:(0,Yt.jsx)(st,{size:24})}),"ASL Neural"]}),(0,Yt.jsxs)(cl,{onClick:t,children:[(0,Yt.jsx)(Cr,{size:18}),"Back to Home"]})]})}),(0,Yt.jsxs)(pl,{children:[(0,Yt.jsx)("div",{style:{textAlign:"center",marginBottom:"var(--space-12)"},children:(0,Yt.jsxs)(fl,{children:[(0,Yt.jsx)(zr,{size:16}),"Neural Vision Active"]})}),(0,Yt.jsx)(dl,{children:"AI Training Session"}),(0,Yt.jsx)(ul,{children:"Experience real-time neural network analysis as our AI learns from your sign language practice"}),(0,Yt.jsx)(zl,{children:(0,Yt.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"var(--space-4)",flexWrap:"wrap",justifyContent:"center"},children:[(0,Yt.jsx)(Pl,{variant:"primary",compact:!0,onClick:z?I:M,children:z?(0,Yt.jsxs)(Yt.Fragment,{children:[(0,Yt.jsx)(jr,{size:16}),"Stop Recording"]}):(0,Yt.jsxs)(Yt.Fragment,{children:[(0,Yt.jsx)(ct,{size:16}),"Record 3s Video"]})}),m&&(0,Yt.jsx)(jl,{isRecording:z,children:m}),!E&&(0,Yt.jsxs)(Pl,{variant:"retry",compact:!0,onClick:A,children:[(0,Yt.jsx)(Pr,{size:16}),"Retry Connection"]})]})}),(0,Yt.jsxs)(gl,{children:[(0,Yt.jsxs)(kl,{children:[(0,Yt.jsxs)(hl,{children:[(0,Yt.jsx)(bl,{children:(0,Yt.jsx)(mt,{size:24})}),"Select a Sign"]}),(0,Yt.jsx)(xl,{value:c,onChange:N,disabled:z,children:Object.keys(Wl).map(e=>(0,Yt.jsx)("option",{value:e,children:Wl[e].name},e))}),(0,Yt.jsx)(Sl,{children:v?(0,Yt.jsx)("div",{style:{display:"flex",fontSize:"3rem",width:"100%",height:"100%",alignItems:"center",justifyContent:"center"},children:"\ud83d\udcf7"}):(0,Yt.jsx)("img",{src:Wl[c].gif,alt:Wl[c].name,onError:()=>w(!0),style:{display:v?"none":"block"}})}),(0,Yt.jsx)(El,{children:Wl[c].name}),(0,Yt.jsx)(Cl,{children:Wl[c].description})]}),(0,Yt.jsxs)(ml,{children:[(0,Yt.jsxs)(hl,{children:[(0,Yt.jsx)(bl,{children:(0,Yt.jsx)(ut,{size:24})}),"Neural Vision Feed"]}),(0,Yt.jsxs)(Ul,{connected:E,children:[E?(0,Yt.jsx)(Rr,{size:16}):(0,Yt.jsx)(_r,{size:16}),E?"AI Connected":"AI Disconnected"]}),C&&(0,Yt.jsxs)(Il,{matched:P,isStale:C.isStale,children:[(0,Yt.jsxs)(Dl,{matched:P,isStale:C.isStale,children:["Detected: ",C.sign,C.isStale&&" (previous)"]}),(0,Yt.jsx)(Fl,{children:(0,Yt.jsx)(Hl,{confidence:C.confidence})}),(0,Yt.jsxs)("div",{style:{fontSize:"0.875rem",marginTop:"8px",color:"var(--text-secondary)"},children:["Confidence: ",Math.round(100*C.confidence),"%",P&&R&&(0,Yt.jsx)("span",{style:{color:"var(--success-600)",marginLeft:"8px"},children:"\u2713 Match! Recording..."}),!z&&(0,Yt.jsxs)("div",{style:{color:"var(--primary-600)",marginTop:"4px"},children:['\ud83c\udfaf Auto-recording active: Perform "',Wl[c].name,'" sign (\u226550% confidence)',(0,Yt.jsx)("br",{}),'\ud83d\udca1 Or click "Record 3 Seconds" for manual recording']})]})]}),!C&&(0,Yt.jsxs)(Il,{children:[(0,Yt.jsxs)(Dl,{children:['\ud83c\udfaf Ready to detect "',Wl[c].name,'"']}),(0,Yt.jsx)("div",{style:{fontSize:"0.875rem",color:"var(--text-secondary)"},children:"Auto-recording is active. Perform the sign with \u226550% confidence to trigger recording."})]}),(0,Yt.jsxs)(yl,{children:[(0,Yt.jsx)(vl,{ref:k,audio:!1,screenshotFormat:"image/jpeg",videoConstraints:{width:640,height:480,facingMode:"user"}}),(0,Yt.jsx)(wl,{isRecording:z,children:z?(0,Yt.jsxs)(Yt.Fragment,{children:[(0,Yt.jsx)("div",{style:{width:"8px",height:"8px",borderRadius:"50%",backgroundColor:"white",marginRight:"4px"}}),"Recording"]}):(0,Yt.jsxs)(Yt.Fragment,{children:[(0,Yt.jsx)(zr,{size:16}),"Ready"]})})]})]})]}),(u||j)&&(0,Yt.jsx)(Rl,{type:(u||j).includes("error")?"error":(u||j).includes("success")?"success":"info",children:j||u}),b.length>0&&(0,Yt.jsxs)(_l,{children:[(0,Yt.jsx)(Ol,{children:"Your Practice Recordings"}),(0,Yt.jsx)(Tl,{children:b.map(e=>(0,Yt.jsxs)(Al,{children:[(0,Yt.jsx)(Nl,{children:e.sign}),(0,Yt.jsx)(Ll,{children:new Date(e.timestamp).toLocaleString()}),(0,Yt.jsxs)(Ml,{onClick:()=>(e=>{const n=document.createElement("a");n.href=e.url,n.download="sign_".concat(e.sign,"_").concat(e.timestamp,".webm"),n.click()})(e),children:[(0,Yt.jsx)(Or,{size:16}),"Download"]})]},e.id))})]})]})]})},Vl=ot("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),ql=ot("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]),Yl=ot("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);var Gl,Kl,Ql,$l,Zl,Xl,Jl,ec,nc,tc,rc,ic,ac,oc,sc;const lc=Yn.div(Gl||(Gl=r(["\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow-x: hidden;\n  \n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: \n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n"]))),cc=Yn.nav(Kl||(Kl=r(["\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: var(--bg-glass);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-neural);\n  padding: var(--space-4) 0;\n  transition: var(--transition-normal);\n"]))),dc=Yn.div(Ql||(Ql=r(["\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  \n  @media (max-width: 768px) {\n    padding: 0 var(--space-4);\n  }\n"]))),uc=Yn.div($l||($l=r(["\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  \n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n    gap: var(--space-2);\n  }\n"]))),fc=Yn.div(Zl||(Zl=r(["\n  width: 40px;\n  height: 40px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n  \n  @media (max-width: 768px) {\n    width: 36px;\n    height: 36px;\n  }\n"]))),pc=Yn.button(Xl||(Xl=r(["\n  background: rgba(59, 130, 246, 0.15);\n  color: #3b82f6;\n  border: 1px solid rgba(59, 130, 246, 0.3);\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-xl);\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  backdrop-filter: blur(10px);\n  \n  &:hover {\n    background: rgba(59, 130, 246, 0.25);\n    color: #2563eb;\n    border-color: rgba(59, 130, 246, 0.5);\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);\n  }\n\n  &:active {\n    transform: translateY(0);\n    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);\n  }\n"]))),gc=Yn.main(Jl||(Jl=r(["\n  padding: var(--space-24) var(--space-6) var(--space-20);\n  max-width: 1400px;\n  margin: 0 auto;\n  position: relative;\n  z-index: 1;\n  \n  @media (max-width: 768px) {\n    padding: var(--space-20) var(--space-4) var(--space-16);\n  }\n"]))),mc=Yn.h1(ec||(ec=r(["\n  font-family: var(--font-primary);\n  font-size: 3rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  text-align: center;\n  margin-bottom: var(--space-4);\n  letter-spacing: -0.02em;\n  \n  @media (max-width: 768px) {\n    font-size: 2.25rem;\n  }\n"]))),hc=Yn.p(nc||(nc=r(["\n  font-size: 1.25rem;\n  color: var(--text-secondary);\n  text-align: center;\n  margin-bottom: var(--space-16);\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n  \n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    margin-bottom: var(--space-12);\n  }\n"]))),bc=Yn.div(tc||(tc=r(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: var(--space-8);\n  margin-bottom: var(--space-20);\n  \n  @media (max-width: 768px) {\n    gap: var(--space-6);\n    margin-bottom: var(--space-16);\n  }\n"]))),yc=Yn.div(rc||(rc=r(["\n  background: var(--bg-glass);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-8);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-lg);\n  text-align: center;\n  transition: var(--transition-normal);\n  \n  &:hover {\n    transform: translateY(-4px);\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n    border-color: var(--primary-300);\n  }\n"]))),vc=Yn.div(ic||(ic=r(["\n  width: 64px;\n  height: 64px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-xl);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 0 auto var(--space-4);\n  box-shadow: var(--shadow-neural);\n"]))),wc=Yn.div(ac||(ac=r(["\n  font-size: 2rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin-bottom: var(--space-2);\n  font-family: var(--font-primary);\n"]))),kc=Yn.div(oc||(oc=r(["\n  font-size: 1rem;\n  color: var(--text-secondary);\n  font-weight: 500;\n"]))),xc=(Yn.h2(sc||(sc=r(["\n  font-family: var(--font-primary);\n  font-size: 2rem;\n  font-weight: 600;\n  color: var(--text-primary);\n  margin-bottom: var(--space-6);\n  text-align: center;\n\n  @media (max-width: 768px) {\n    font-size: 1.75rem;\n  }\n"]))),e=>{let{onBackToHome:n}=e;return(0,Yt.jsxs)(lc,{children:[(0,Yt.jsx)(cc,{children:(0,Yt.jsxs)(dc,{children:[(0,Yt.jsxs)(uc,{children:[(0,Yt.jsx)(fc,{children:(0,Yt.jsx)(st,{size:24})}),"ASL Neural"]}),(0,Yt.jsxs)(pc,{onClick:n,children:[(0,Yt.jsx)(Cr,{size:18}),"Back to Home"]})]})}),(0,Yt.jsxs)(gc,{children:[(0,Yt.jsx)(mc,{children:"About ASL Neural"}),(0,Yt.jsx)(hc,{children:"Pioneering the future of accessibility through advanced artificial intelligence and computer vision technology for sign language education"}),(0,Yt.jsxs)(bc,{children:[(0,Yt.jsxs)(yc,{children:[(0,Yt.jsx)(vc,{children:(0,Yt.jsx)(Vl,{size:28,color:"white"})}),(0,Yt.jsx)(wc,{children:"100K+"}),(0,Yt.jsx)(kc,{children:"Trained on videos"})]}),(0,Yt.jsxs)(yc,{children:[(0,Yt.jsx)(vc,{children:(0,Yt.jsx)(ft,{size:28,color:"white"})}),(0,Yt.jsx)(wc,{children:"2.5M"}),(0,Yt.jsx)(kc,{children:"Neural Patterns"})]}),(0,Yt.jsxs)(yc,{children:[(0,Yt.jsx)(vc,{children:(0,Yt.jsx)(ql,{size:28,color:"white"})}),(0,Yt.jsx)(wc,{children:"+88.7%"}),(0,Yt.jsx)(kc,{children:"AI Accuracy"})]}),(0,Yt.jsxs)(yc,{children:[(0,Yt.jsx)(vc,{children:(0,Yt.jsx)(Yl,{size:28,color:"white"})}),(0,Yt.jsx)(wc,{children:"15k+"}),(0,Yt.jsx)(kc,{children:"Users"})]})]})]})]})}),Sc=ot("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),Ec=ot("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),Cc=ot("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]),zc=ot("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),jc=ot("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);var Pc,Rc,_c,Oc,Tc,Ac,Nc,Lc,Mc,Ic,Dc,Fc,Hc,Uc,Wc,Bc,Vc,qc,Yc,Gc,Kc;const Qc=Yn.div(Pc||(Pc=r(["\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow-x: hidden;\n  \n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: \n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n"]))),$c=Yn.nav(Rc||(Rc=r(["\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: var(--bg-glass);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-neural);\n  padding: var(--space-4) 0;\n  transition: var(--transition-normal);\n"]))),Zc=Yn.div(_c||(_c=r(["\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  \n  @media (max-width: 768px) {\n    padding: 0 var(--space-4);\n  }\n"]))),Xc=Yn.div(Oc||(Oc=r(["\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n    gap: var(--space-2);\n  }\n"]))),Jc=Yn.div(Tc||(Tc=r(["\n  width: 40px;\n  height: 40px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n"]))),ed=Yn.button(Ac||(Ac=r(["\n  background: rgba(59, 130, 246, 0.15);\n  color: #3b82f6;\n  border: 1px solid rgba(59, 130, 246, 0.3);\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-xl);\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  backdrop-filter: blur(10px);\n  \n  &:hover {\n    background: rgba(59, 130, 246, 0.25);\n    color: #2563eb;\n    border-color: rgba(59, 130, 246, 0.5);\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);\n  }\n\n  &:active {\n    transform: translateY(0);\n    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);\n  }\n"]))),nd=Yn.main(Nc||(Nc=r(["\n  padding: var(--space-24) var(--space-6) var(--space-20);\n  max-width: 1200px;\n  margin: 0 auto;\n  position: relative;\n  z-index: 1;\n  \n  @media (max-width: 768px) {\n    padding: var(--space-20) var(--space-4) var(--space-16);\n  }\n"]))),td=Yn.h1(Lc||(Lc=r(["\n  font-family: var(--font-primary);\n  font-size: 2.75rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  text-align: center;\n  margin-bottom: var(--space-4);\n  letter-spacing: -0.02em;\n\n  @media (max-width: 768px) {\n    font-size: 2.25rem;\n  }\n"]))),rd=Yn.p(Mc||(Mc=r(["\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  text-align: center;\n  margin-bottom: var(--space-16);\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n\n  @media (max-width: 768px) {\n    font-size: 1rem;\n    margin-bottom: var(--space-12);\n  }\n"]))),id=Yn.div(Ic||(Ic=r(["\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--space-12);\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-8);\n  }\n"]))),ad=Yn.div(Dc||(Dc=r(["\n  background: var(--bg-glass);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-10);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-lg);\n"]))),od=Yn.form(Fc||(Fc=r(["\n  background: var(--bg-glass);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-10);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-lg);\n"]))),sd=Yn.h2(Hc||(Hc=r(["\n  font-family: var(--font-primary);\n  font-size: 1.75rem;\n  font-weight: 700;\n  color: var(--text-primary);\n  margin-bottom: var(--space-8);\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n"]))),ld=Yn.div(Uc||(Uc=r(["\n  display: flex;\n  align-items: center;\n  gap: var(--space-4);\n  margin-bottom: var(--space-6);\n  padding: var(--space-4);\n  border-radius: var(--radius-lg);\n  transition: var(--transition-normal);\n  \n  &:hover {\n    background: var(--primary-50);\n  }\n"]))),cd=Yn.div(Wc||(Wc=r(["\n  width: 48px;\n  height: 48px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n"]))),dd=Yn.div(Bc||(Bc=r(["\n  h3 {\n    font-size: 1.125rem;\n    font-weight: 600;\n    color: var(--text-primary);\n    margin-bottom: var(--space-1);\n  }\n  \n  p {\n    color: var(--text-secondary);\n    font-size: 0.9rem;\n  }\n"]))),ud=Yn.div(Vc||(Vc=r(["\n  margin-bottom: var(--space-6);\n"]))),fd=Yn.label(qc||(qc=r(["\n  display: block;\n  font-weight: 500;\n  color: var(--text-primary);\n  margin-bottom: var(--space-2);\n  font-size: 0.9rem;\n"]))),pd=Yn.input(Yc||(Yc=r(["\n  width: 100%;\n  padding: var(--space-4);\n  border: 2px solid var(--border-light);\n  border-radius: var(--radius-lg);\n  font-size: 1rem;\n  transition: var(--transition-normal);\n  background: var(--bg-primary);\n  \n  &:focus {\n    outline: none;\n    border-color: var(--primary-500);\n    box-shadow: var(--shadow-glow);\n  }\n"]))),gd=Yn.textarea(Gc||(Gc=r(["\n  width: 100%;\n  padding: var(--space-4);\n  border: 2px solid var(--border-light);\n  border-radius: var(--radius-lg);\n  font-size: 1rem;\n  transition: var(--transition-normal);\n  background: var(--bg-primary);\n  min-height: 120px;\n  resize: vertical;\n  \n  &:focus {\n    outline: none;\n    border-color: var(--primary-500);\n    box-shadow: var(--shadow-glow);\n  }\n"]))),md=Yn.button(Kc||(Kc=r(["\n  background: var(--bg-neural);\n  color: white;\n  border: none;\n  padding: var(--space-4) var(--space-8);\n  border-radius: var(--radius-xl);\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  box-shadow: var(--shadow-neural);\n  width: 100%;\n  justify-content: center;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n  }\n"]))),hd=n=>{let{onBackToHome:t}=n;const[r,i]=(0,e.useState)({name:"",email:"",subject:"",message:""}),a=e=>{i(Xn(Xn({},r),{},{[e.target.name]:e.target.value}))};return(0,Yt.jsxs)(Qc,{children:[(0,Yt.jsx)($c,{children:(0,Yt.jsxs)(Zc,{children:[(0,Yt.jsxs)(Xc,{children:[(0,Yt.jsx)(Jc,{children:(0,Yt.jsx)(st,{size:24})}),"ASL Neural"]}),(0,Yt.jsxs)(ed,{onClick:t,children:[(0,Yt.jsx)(Cr,{size:18}),"Back to Home"]})]})}),(0,Yt.jsxs)(nd,{children:[(0,Yt.jsx)(td,{children:"Contact Us"}),(0,Yt.jsx)(rd,{children:"Get in touch with our team to learn more about ASL Neural technology or discuss partnership opportunities"}),(0,Yt.jsxs)(id,{children:[(0,Yt.jsxs)(ad,{children:[(0,Yt.jsxs)(sd,{children:[(0,Yt.jsx)(Sc,{size:24}),"Get in Touch"]}),(0,Yt.jsxs)(ld,{children:[(0,Yt.jsx)(cd,{children:(0,Yt.jsx)(Ec,{size:20})}),(0,Yt.jsxs)(dd,{children:[(0,Yt.jsx)("h3",{children:"Email"}),(0,Yt.jsx)("p",{children:"<EMAIL>"})]})]}),(0,Yt.jsxs)(ld,{children:[(0,Yt.jsx)(cd,{children:(0,Yt.jsx)(Cc,{size:20})}),(0,Yt.jsxs)(dd,{children:[(0,Yt.jsx)("h3",{children:"Phone"}),(0,Yt.jsx)("p",{children:"+****************"})]})]}),(0,Yt.jsxs)(ld,{children:[(0,Yt.jsx)(cd,{children:(0,Yt.jsx)(zc,{size:20})}),(0,Yt.jsxs)(dd,{children:[(0,Yt.jsx)("h3",{children:"Address"}),(0,Yt.jsxs)("p",{children:["123 AI Innovation Drive",(0,Yt.jsx)("br",{}),"San Francisco, CA 94105"]})]})]}),(0,Yt.jsxs)(ld,{children:[(0,Yt.jsx)(cd,{children:(0,Yt.jsx)(pt,{size:20})}),(0,Yt.jsxs)(dd,{children:[(0,Yt.jsx)("h3",{children:"Website"}),(0,Yt.jsx)("p",{children:"www.aslneural.ai"})]})]})]}),(0,Yt.jsxs)(od,{onSubmit:e=>{e.preventDefault(),console.log("Form submitted:",r),alert("Thank you for your message! We'll get back to you soon."),i({name:"",email:"",subject:"",message:""})},children:[(0,Yt.jsxs)(sd,{children:[(0,Yt.jsx)(jc,{size:24}),"Send Message"]}),(0,Yt.jsxs)(ud,{children:[(0,Yt.jsx)(fd,{htmlFor:"name",children:"Name"}),(0,Yt.jsx)(pd,{type:"text",id:"name",name:"name",value:r.name,onChange:a,required:!0})]}),(0,Yt.jsxs)(ud,{children:[(0,Yt.jsx)(fd,{htmlFor:"email",children:"Email"}),(0,Yt.jsx)(pd,{type:"email",id:"email",name:"email",value:r.email,onChange:a,required:!0})]}),(0,Yt.jsxs)(ud,{children:[(0,Yt.jsx)(fd,{htmlFor:"subject",children:"Subject"}),(0,Yt.jsx)(pd,{type:"text",id:"subject",name:"subject",value:r.subject,onChange:a,required:!0})]}),(0,Yt.jsxs)(ud,{children:[(0,Yt.jsx)(fd,{htmlFor:"message",children:"Message"}),(0,Yt.jsx)(gd,{id:"message",name:"message",value:r.message,onChange:a,required:!0})]}),(0,Yt.jsxs)(md,{type:"submit",children:[(0,Yt.jsx)(jc,{size:18}),"Send Message"]})]})]})]})]})};var bd,yd;const vd=Yn.div(bd||(bd=r(["\n  min-height: 100vh;\n  background: var(--bg-primary);\n  font-family: var(--font-secondary);\n  overflow-x: hidden;\n  position: relative;\n\n  /* Subtle background pattern for visual interest */\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n"]))),wd=Yn.div(yd||(yd=r(["\n  position: relative;\n  z-index: 1;\n  min-height: 100vh;\n"])));const kd=function(){const[n,t]=(0,e.useState)("home"),r=()=>{t("training")},i=()=>{t("about")},a=()=>{t("contact")},o=()=>{t("home")};return(0,Yt.jsx)(vd,{children:(0,Yt.jsx)(wd,{children:(()=>{switch(n){case"training":return(0,Yt.jsx)(Bl,{onBackToHome:o});case"about":return(0,Yt.jsx)(xc,{onBackToHome:o});case"contact":return(0,Yt.jsx)(hd,{onBackToHome:o});default:return(0,Yt.jsx)(xr,{onStartTraining:r,onNavigateToAbout:i,onNavigateToContact:a})}})()})})},xd=e=>{e&&e instanceof Function&&t.e(453).then(t.bind(t,453)).then(n=>{let{getCLS:t,getFID:r,getFCP:i,getLCP:a,getTTFB:o}=n;t(e),r(e),i(e),a(e),o(e)})};n.createRoot(document.getElementById("root")).render((0,Yt.jsx)(e.StrictMode,{children:(0,Yt.jsx)(kd,{})})),xd()})()})();
//# sourceMappingURL=main.78e46417.js.map