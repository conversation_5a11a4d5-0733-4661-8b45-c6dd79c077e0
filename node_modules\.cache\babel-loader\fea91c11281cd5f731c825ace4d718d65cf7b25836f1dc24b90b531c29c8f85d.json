{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"20\",\n  height: \"16\",\n  x: \"2\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"18n3k1\"\n}], [\"path\", {\n  d: \"M6 8h.01\",\n  key: \"x9i8wu\"\n}], [\"path\", {\n  d: \"M10 8h.01\",\n  key: \"1r9ogq\"\n}], [\"path\", {\n  d: \"M14 8h.01\",\n  key: \"1primd\"\n}]];\nconst AppWindowMac = createLucideIcon(\"app-window-mac\", __iconNode);\nexport { __iconNode, AppWindowMac as default };", "map": {"version": 3, "names": ["__iconNode", "width", "height", "x", "y", "rx", "key", "d", "AppWindowMac", "createLucideIcon"], "sources": ["D:\\ASL\\training-frontend\\node_modules\\lucide-react\\src\\icons\\app-window-mac.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '20', height: '16', x: '2', y: '4', rx: '2', key: '18n3k1' }],\n  ['path', { d: 'M6 8h.01', key: 'x9i8wu' }],\n  ['path', { d: 'M10 8h.01', key: '1r9ogq' }],\n  ['path', { d: 'M14 8h.01', key: '1primd' }],\n];\n\n/**\n * @component @name AppWindowMac\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTYiIHg9IjIiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik02IDhoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xMCA4aC4wMSIgLz4KICA8cGF0aCBkPSJNMTQgOGguMDEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/app-window-mac\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst AppWindowMac = createLucideIcon('app-window-mac', __iconNode);\n\nexport default AppWindowMac;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAQ;EAAMC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAU,GAC5C;AAaM,MAAAE,YAAA,GAAeC,gBAAiB,mBAAkBT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}