{"ast": null, "code": "import _objectSpread from \"D:/ASL/training-frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"D:/ASL/training-frontend/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"color\", \"size\", \"strokeWidth\", \"absoluteStrokeWidth\", \"className\", \"children\", \"iconNode\"];\n/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport defaultAttributes from './defaultAttributes.js';\nimport { mergeClasses, hasA11yProp } from './shared/src/utils.js';\nconst Icon = forwardRef((_ref, ref) => {\n  let {\n      color = \"currentColor\",\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = \"\",\n      children,\n      iconNode\n    } = _ref,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  return createElement(\"svg\", _objectSpread(_objectSpread(_objectSpread({\n    ref\n  }, defaultAttributes), {}, {\n    width: size,\n    height: size,\n    stroke: color,\n    strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n    className: mergeClasses(\"lucide\", className)\n  }, !children && !hasA11yProp(rest) && {\n    \"aria-hidden\": \"true\"\n  }), rest), [...iconNode.map(_ref2 => {\n    let [tag, attrs] = _ref2;\n    return createElement(tag, attrs);\n  }), ...(Array.isArray(children) ? children : [children])]);\n});\nexport { Icon as default };", "map": {"version": 3, "names": ["Icon", "forwardRef", "_ref", "ref", "color", "size", "strokeWidth", "absoluteStrokeWidth", "className", "children", "iconNode", "rest", "_objectWithoutProperties", "_excluded", "createElement", "_objectSpread", "defaultAttributes", "width", "height", "stroke", "Number", "mergeClasses", "hasA11yProp", "map", "_ref2", "tag", "attrs", "Array", "isArray"], "sources": ["D:\\ASL\\training-frontend\\node_modules\\lucide-react\\src\\Icon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses, hasA11yProp } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) =>\n    createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...(!children && !hasA11yProp(rest) && { 'aria-hidden': 'true' }),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    ),\n);\n\nexport default Icon;\n"], "mappings": ";;;;;;;;;;;;;AAwBA,MAAMA,IAAO,GAAAC,UAAA,CACX,CAAAC,IAAA,EAWEC,GAEA;EAAA,IAZA;MACEC,KAAQ;MACRC,IAAO;MACPC,WAAc;MACdC,mBAAA;MACAC,SAAY;MACZC,QAAA;MACAC;IACG,IAAAR,IAAA;IAAAS,IAAA,GAAAC,wBAAA,CAAAV,IAAA,EAAAW,SAAA;EAAA,OAILC,aAAA,CACE,OAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA;IAEEZ;EAAA,GACGa,iBAAA;IACHC,KAAO,EAAAZ,IAAA;IACPa,MAAQ,EAAAb,IAAA;IACRc,MAAQ,EAAAf,KAAA;IACRE,WAAA,EAAaC,mBAAA,GAAuBa,MAAO,CAAAd,WAAW,IAAI,EAAM,GAAAc,MAAA,CAAOf,IAAI,CAAI,GAAAC,WAAA;IAC/EE,SAAA,EAAWa,YAAa,WAAUb,SAAS;EAAA,GACvC,CAACC,QAAY,KAACa,WAAA,CAAYX,IAAI,KAAK;IAAE,eAAe;EAAO,IAC5DA,IAAA,GAEL,CACE,GAAGD,QAAS,CAAAa,GAAA,CAAIC,KAAA;IAAA,IAAC,CAACC,GAAK,EAAAC,KAAK,CAAM,GAAAF,KAAA;IAAA,OAAAV,aAAA,CAAcW,GAAK,EAAAC,KAAK,CAAC;EAAA,IAC3D,IAAIC,KAAM,CAAAC,OAAA,CAAQnB,QAAQ,CAAI,GAAAA,QAAA,GAAW,CAACA,QAAQ,GACpD;AAAA,CAEN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}